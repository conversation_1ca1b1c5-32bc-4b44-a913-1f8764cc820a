/*
 * DTOs e validadores para o endpoint "Create Existing Contract" no NestJS
 * Autor: ChatGPT (o3)
 * Data: 14/04/2025
 *
 * <PERSON>stes DTOs replicam integralmente as regras de validação presentes nos schemas Zod
 * fornecidos pelo domínio da aplicação, utilizando os decorators do class‑validator
 * e as transformações do class‑transformer.
 *
 * Para funcionar, lembre‑se de habilitar o ValidationPipe global (ou por rota) no
 * seu bootstrap do NestJS:
 *   app.useGlobalPipes(new ValidationPipe({ transform: true }));
 *
 * Caso deseje internacionalizar as mensagens ou alterar a estratégia de erro,
 * recomenda‑se integrar o i18n ou criar um filtro de exceção customizado.
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsCNPJ, IsCPF } from 'brazilian-class-validator';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
  registerDecorator,
  ValidationOptions,
  IsEmail,
  ValidationArguments,
} from 'class-validator';
import { IsAfter } from 'src/shared/decorators/is-after.decorator';
import { IsValidName } from 'src/shared/decorators/is-valid-name.decorator';
import { ToBoolean } from 'src/shared/decorators/to-boolean.decorator';
import { ToNumber } from 'src/shared/decorators/to-number.decorator';
import { generateDate } from 'src/shared/functions/generate-date';

export enum ContractType {
  MUTUO = 'MUTUO',
  SCP = 'SCP',
}

/**
 * Valida se o investimento tem uma quantidade de cotas válida para contrato SCP
 * @param validationOptions
 * @returns
 */
function ValidateInvestmentWithContractType(
  validationOptions?: ValidationOptions,
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'validateInvestmentWithContractType',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(
          value: ExistingContractInvestmentDto,
          args: ValidationArguments,
        ) {
          const parent = args.object as CreateExistingContractDto;

          if (parent.contractType !== ContractType.SCP) return true;

          return (
            value?.quotaQuantity != null &&
            /^[1-9]\d*$/.test(value.quotaQuantity)
          );
        },
        defaultMessage() {
          return 'Quantidade de cotas é obrigatória para contrato SCP e deve ser um número inteiro maior que zero';
        },
      },
    });
  };
}

/**
 * Enums importados do domínio.
 * Se já existirem em outro módulo, substitua estes stubs por `import { … } from '…'`.
 */
export enum CompanyLegalType {
  MEI = 'MEI',
  EI = 'EI',
  EIRELI = 'EIRELI',
  LTDA = 'LTDA',
  SLU = 'SLU',
  SA = 'SA',
  SS = 'SS',
  CONSORCIO = 'CONSORCIO',
}

export enum InvestorProfile {
  CONSERVATIVE = 'conservative',
  MODERATE = 'moderate',
  AGGRESSIVE = 'aggressive',
}

export enum PaymentMethod {
  PIX = 'pix',
  BANK_TRANSFER = 'bank_transfer',
  BOLETO = 'boleto',
}

export enum PersonType {
  PF = 'PF',
  PJ = 'PJ',
}

export type UploadedFile = {
  mimetype: string;
  buffer: Buffer;
};

/**
 * -----------------------------
 * Decoradores customizados
 * -----------------------------
 */

/**
 * -----------------------------
 * DTOs aninhados
 * -----------------------------
 */

export class BankAccountDto {
  @ApiProperty({
    description: 'Nome do banco (ex: ICA Bank)',
    example: 'ICA Bank',
  })
  @IsNotEmpty({ message: 'Nome do banco é obrigatório' })
  bank!: string;

  @ApiProperty({
    description: 'Agência bancária (ex: 0001)',
    example: '0001',
  })
  @IsNotEmpty({ message: 'Agência bancária é obrigatória' })
  agency!: string;

  @ApiProperty({
    description: 'Conta bancária (ex: 1234567)',
    example: '1234567',
  })
  @IsNotEmpty({ message: 'Conta bancária é obrigatória' })
  account!: string;

  @ApiProperty({
    description: 'Tipo de conta (ex: corrente)',
    enum: ['corrente', 'poupanca', 'investimento'],
    example: 'corrente',
  })
  @IsOptional()
  @IsNotEmpty({ message: 'Tipo de conta é obrigatório' })
  accountType!: string;

  @ApiPropertyOptional({ description: 'Chave PIX (ex: <EMAIL>)' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      // If it's just DDD + number (e.g., ***********)
      if (value.match(/^\d{2}9\d{8}$/)) {
        return '+55' + value;
      }
      // If it has 55 but no + (e.g., *************)
      if (value.match(/^55\d{2}9\d{8}$/)) {
        return '+' + value;
      }
    }
    return value as string;
  })
  @Matches(
    /^(\+55\d{2}9\d{8}|[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|\d{11}|\d{14}|[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})$/,
    {
      message:
        'Chave PIX inválida. Use CPF (11 dígitos), CNPJ (14 dígitos), telefone (+55+DDD+Telefone), email ou chave aleatória.',
    },
  )
  pix?: string;
}

export class AddressDto {
  @ApiProperty({
    description: 'Rua (ex: Av. Paulista)',
    example: 'Av. Paulista',
  })
  @IsNotEmpty({ message: 'Rua é obrigatória' })
  street!: string;

  @ApiProperty({ description: 'Número (ex: 1000)', example: '1000' })
  @IsNotEmpty({ message: 'Número é obrigatório' })
  number!: string;

  @ApiProperty({ description: 'Cidade (ex: São Paulo)', example: 'São Paulo' })
  @IsNotEmpty({ message: 'Cidade é obrigatória' })
  city!: string;

  @ApiProperty({ description: 'Estado (ex: SP)', example: 'SP' })
  @Matches(/^[A-Z]{2}$/, {
    message: 'Estado inválido. Use o formato XX (ex: SP)',
  })
  state!: string;

  @ApiProperty({
    description: 'CEP (ex: 01310-100 ou 01310100)',
    example: '01310100',
    pattern: '^\\d{5}-?\\d{3}$',
  })
  @Matches(/^\d{8}$/, {
    message: 'CEP inválido. Use o formato XXXXXXXX (ex: 01310100)',
  })
  postalCode!: string;

  @ApiProperty({
    description: 'Bairro (ex: Bela Vista)',
    example: 'Bela Vista',
  })
  @IsNotEmpty({ message: 'Bairro é obrigatório' })
  neighborhood!: string;

  @ApiPropertyOptional({ description: 'Complemento (ex: Apt 101)' })
  @IsOptional()
  @IsString()
  complement?: string;
}

export class AdvisorAssignmentDto {
  @ApiProperty({
    description: 'ID do assessor (ex: advisor-uuid)',
    example: '',
  })
  @IsUUID('all', { message: 'ID do assessor inválido' })
  advisorId!: string;

  @ApiProperty({
    description: 'Porcentagem do assessor (%) (ex: 10)',
    example: 10,
  })
  @ToNumber()
  @IsNumber({ allowNaN: false }, { message: 'Porcentagem deve ser um número' })
  @Min(0.01, { message: 'Porcentagem deve ser maior que zero' })
  @Max(100, { message: 'Porcentagem deve ser menor ou igual a 100' })
  rate!: number;
}

export class IndividualDto {
  @ApiProperty({
    description: 'Nome completo (ex: João da Silva)',
    example: 'João da Silva',
  })
  @IsNotEmpty({ message: 'Nome completo é obrigatório' })
  @IsValidName({
    message:
      'Nome completo inválido. Use apenas letras, espaços, hífens e caracteres acentuados',
  })
  fullName!: string;

  @ApiProperty({ description: 'CPF (ex: 12345678900)', example: '' })
  @IsCPF({
    message: 'CPF inválido. Use o formato XXXXXXXXXXX (ex: 12345678900)',
  })
  cpf!: string;

  @ApiProperty({ description: 'RG (ex: 123456789)', example: '123456789' })
  @IsNotEmpty({ message: 'RG é obrigatório' })
  rg!: string;

  @ApiProperty({ description: 'Órgão emissor (ex: SSP)' })
  @IsNotEmpty({ message: 'Órgão emissor é obrigatório' })
  issuingAgency!: string;

  @ApiProperty({ description: 'Nacionalidade (ex: Brasileiro)' })
  @IsNotEmpty({ message: 'Nacionalidade é obrigatória' })
  nationality!: string;

  @ApiProperty({
    description: 'Ocupação (ex: Engenheiro Civil)',
    example: 'Engenheiro Civil',
  })
  @IsNotEmpty({ message: 'Ocupação é obrigatória' })
  @IsValidName({
    message:
      'Ocupação inválida. Use apenas letras, espaços, hífens e caracteres acentuados',
  })
  occupation!: string;

  @ApiProperty({
    description: 'Data de nascimento (ex: 1990-01-01)',
    example: generateDate(-(365 * 20)),
  })
  @IsDateString(
    {},
    { message: 'Data de nascimento inválida. Use o formato YYYY-MM-DD' },
  )
  birthDate!: string;

  @ApiProperty({
    description: 'Email (ex: <EMAIL>)',
    example: '<EMAIL>',
  })
  @IsEmail(
    {},
    { message: 'Email inválido. Use um formato válido como: <EMAIL>' },
  )
  email!: string;

  @ApiProperty({
    description: 'Telefone celular (ex: 5548999999999)',
    example: '5548999999999',
  })
  @Matches(/^55(\d{2})\d{8,9}$/, {
    message: 'Telefone inválido. (ex: 5548999999999)',
  })
  phone!: string;

  @ApiProperty({
    description: 'Nome da mãe (ex: Maria da Silva)',
    example: 'Maria da Silva',
  })
  @IsNotEmpty({ message: 'Nome da mãe é obrigatório' })
  @IsValidName({
    message:
      'Nome da mãe inválido. Use apenas letras, espaços, hífens e caracteres acentuados',
  })
  motherName!: string;

  @ApiProperty({
    description: 'Endereço',
    type: AddressDto,
    example: {
      street: 'Av. Paulista',
      number: '1000',
      city: 'São Paulo',
      state: 'SP',
      postalCode: '01310100',
      neighborhood: 'Bela Vista',
      complement: 'Apt 101',
    },
  })
  @ValidateNested()
  @Type(() => AddressDto)
  address!: AddressDto;
}

export class CompanyDto {
  @ApiProperty({
    description: 'Razão social (ex: ACME Ltda)',
    example: 'ACME Ltda',
  })
  @IsString({ message: 'Razão social é obrigatória' })
  @IsNotEmpty({ message: 'Razão social não pode estar vazia' })
  corporateName!: string;

  @ApiProperty({ description: 'CNPJ (ex: 12345678000199)' })
  @IsCNPJ({
    message: 'CNPJ inválido. Use o formato XXXXXXXXXXXXXX (ex: 12345678000199)',
  })
  cnpj!: string;

  @ApiProperty({
    description: 'Tipo jurídico da empresa',
    enum: CompanyLegalType,
    example: CompanyLegalType.LTDA,
  })
  @IsEnum(CompanyLegalType, { message: 'Tipo de empresa inválido' })
  type!: CompanyLegalType;

  @ApiProperty({ description: 'Endereço', type: AddressDto })
  @ValidateNested()
  @Type(() => AddressDto)
  address!: AddressDto;

  @ApiProperty({
    description: 'Representante legal',
    type: IndividualDto,
  })
  @ValidateNested()
  @Type(() => IndividualDto)
  representative!: IndividualDto;
}

export class ExistingContractInvestmentDto {
  @ApiProperty({
    description: 'Valor do investimento (ex: 10000)',
    example: 10000,
  })
  @ToNumber()
  @Min(1, {
    message: 'Valor do investimento deve ser um número maior que zero',
  })
  amount!: number;

  @ApiProperty({
    description: 'Taxa mensal (%) (ex: 1.5)',
    example: 1.5,
  })
  @ToNumber()
  @Min(0, { message: 'Taxa mensal deve ser um número maior ou igual a zero' })
  monthlyRate!: number;

  @ApiProperty({
    description: 'Método de pagamento',
    enum: PaymentMethod,
    example: PaymentMethod.PIX,
  })
  @IsEnum(PaymentMethod, {
    message:
      'Método de pagamento inválido. Tipos válidos: pix, bank_transfer, boleto',
  })
  paymentMethod!: PaymentMethod;

  @ApiProperty({
    description: 'Perfil do investidor',
    enum: InvestorProfile,
    example: InvestorProfile.MODERATE,
  })
  @IsEnum(InvestorProfile, {
    message:
      'Tipo de pessoa inválido. Tipos válidos: conservative, moderate e aggressive',
  })
  profile!: InvestorProfile;

  @ApiProperty({
    description: 'Data de início (ex: 2025-01-01)',
    example: generateDate(),
  })
  @IsDateString(
    {},
    { message: 'Data de início inválida. Use o formato YYYY-MM-DD' },
  )
  startDate!: string;

  @ApiProperty({
    description: 'Data de término (ex: 2026-01-01)',
    example: generateDate(365),
  })
  @IsDateString(
    {},
    { message: 'Data de término inválida. Use o formato YYYY-MM-DD' },
  )
  @IsAfter('startDate', {
    message: 'A data de término deve ser posterior à data de início',
  })
  endDate!: string;

  @ApiProperty({ description: 'Prazo em meses (ex: 12)', example: 12 })
  @ToNumber()
  @Min(1, { message: 'Prazo deve ser um número inteiro maior que zero' })
  durationInMonths!: number;

  @ApiProperty({
    description: 'É debênture? (ex: false)',
    example: false,
  })
  @ToBoolean()
  @IsBoolean({ message: 'Debenture deve ser true ou false' })
  isDebenture!: boolean;

  @ApiPropertyOptional({
    description: 'Quantidade de cotas (ex: 1)',
    example: '2',
  })
  @IsString({ message: 'Quantidade de cotas deve ser uma string' })
  quotaQuantity: string = '';
}

export function IsValidFileType(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidFileType',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: UploadedFile) {
          const allowedTypes = [
            'application/pdf',
            'image/png',
            'image/jpeg',
            'image/jpg',
          ];
          return (
            value &&
            typeof value === 'object' &&
            allowedTypes.includes(value.mimetype) &&
            Buffer.isBuffer(value.buffer)
          );
        },
        defaultMessage() {
          return 'O arquivo deve estar no formato PDF, PNG, JPEG ou JPG e conter um Buffer válido';
        },
      },
    });
  };
}

export class FileDto {
  @ApiProperty({
    description:
      'Arquivo (PDF, PNG, JPEG, JPG). Exemplo de mimetype: application/pdf',
    type: 'object',
    properties: {
      mimetype: {
        type: 'string',
        description: 'Tipo MIME do arquivo (ex: application/pdf)',
      },
      buffer: {
        type: 'string',
        description: 'Buffer do arquivo (ex: <Buffer>)',
      },
    },
    additionalProperties: false,
  })
  @IsValidFileType({
    message: 'O arquivo deve estar no formato PDF, PNG, JPEG ou JPG',
  })
  file!: { mimetype: string; buffer: Buffer };
}

/**
 * -----------------------------
 * DTO principal
 * -----------------------------
 */

export class CreateExistingContractDto {
  @ApiProperty({ description: 'ID da função (ex: role-uuid)', example: '' })
  @IsString({ message: 'Role é obrigatório' })
  role: string;

  @ApiProperty({
    description: 'Tipo de pessoa',
    enum: PersonType,
    example: PersonType.PF,
  })
  @IsEnum(PersonType, {
    message: 'Tipo de pessoa inválido. Tipos válidos: PF e PJ',
  })
  personType!: PersonType;

  @ApiProperty({
    description: 'Tipo de contrato',
    enum: ContractType,
    example: ContractType.MUTUO,
  })
  @IsEnum(ContractType, {
    message: 'Tipo de contrato inválido. Tipos válidos: MUTUO ou SCP',
  })
  contractType!: ContractType;

  @ApiPropertyOptional({
    description: 'ID do broker (ex: broker-uuid)',
    example: '',
  })
  @IsOptional()
  @IsUUID('all', { message: 'ID do broker inválido' })
  brokerId?: string;

  @ApiProperty({
    description: 'Investimento',
    type: ExistingContractInvestmentDto,
    example: {
      amount: 10000,
      monthlyRate: 1.5,
      paymentMethod: PaymentMethod.PIX,
      profile: InvestorProfile.MODERATE,
      startDate: generateDate(),
      endDate: generateDate(365),
      durationInMonths: 12,
      isDebenture: false,
      quotaQuantity: '2',
    },
  })
  @ValidateInvestmentWithContractType()
  @ValidateNested()
  @Type(() => ExistingContractInvestmentDto)
  investment!: ExistingContractInvestmentDto;

  @ApiProperty({
    description: 'Assessores',
    type: [AdvisorAssignmentDto],
    example: [{ advisorId: '', rate: 2 }],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AdvisorAssignmentDto)
  advisors: AdvisorAssignmentDto[] = [];

  @ApiProperty({
    description: 'Conta bancária',
    type: BankAccountDto,
    example: {
      bank: 'ICA Bank',
      agency: '0001',
      account: '123456-7',
      accountType: 'corrente',
      pix: '<EMAIL>',
    },
  })
  @ValidateNested()
  @Type(() => BankAccountDto)
  bankAccount!: BankAccountDto;

  @ApiProperty({
    description:
      'Dados do investidor pessoa física (obrigatório se personType for PF)',
    type: IndividualDto,
  })
  @ValidateIf((o: CreateExistingContractDto) => o.personType === PersonType.PF)
  @IsDefined({
    message: 'Os dados do investidor pessoa física são obrigatórios',
  })
  @ValidateNested()
  @Type(() => IndividualDto)
  individual?: IndividualDto;

  @ApiPropertyOptional({
    description: 'Dados da empresa (obrigatório se personType for PJ)',
  })
  @ValidateIf((o: CreateExistingContractDto) => o.personType === PersonType.PJ)
  @IsDefined({
    message: 'Os dados da empresa são obrigatórios para contratos PJ',
  })
  @ValidateNested()
  @Type(() => CompanyDto)
  company?: CompanyDto;

  @ApiProperty({
    description: 'Comprovante de pagamento',
    format: 'binary',
    type: 'string',
  })
  @ValidateNested()
  @Type(() => FileDto)
  proofOfPayment!: FileDto;

  @ApiProperty({
    description: 'Documento pessoal',
    format: 'binary',
    type: 'string',
  })
  @ValidateNested()
  @Type(() => FileDto)
  personalDocument!: FileDto;

  @ApiProperty({
    description: 'Comprovante de residência',
    format: 'binary',
    type: 'string',
  })
  @ValidateNested()
  @Type(() => FileDto)
  proofOfResidence!: FileDto;

  @ApiPropertyOptional({
    description: 'Documento da empresa',
    format: 'binary',
    type: 'string',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => FileDto)
  companyDocument?: FileDto;

  @ApiProperty({
    description: 'Arquivo do contrato',
    format: 'binary',
    type: 'string',
  })
  @ValidateNested()
  @Type(() => FileDto)
  contract!: FileDto;
}
