{"version": 3, "file": "get-contracts-growth-chart.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/get-contracts-growth-chart.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AAErC,4HAI8E;AAC9E,qFAA2E;AAC3E,+FAA2F;AAE3F,+FAAsF;AAG/E,IAAM,8BAA8B,GAApC,MAAM,8BAA+B,SAAQ,4EAAkC;IACpF,YAEmB,kBAA8C;QAE/D,KAAK,EAAE,CAAC;QAFS,uBAAkB,GAAlB,kBAAkB,CAA4B;IAGjE,CAAC;IAED,KAAK,CAAC,OAAO,CACX,eAA6B,MAAM,EACnC,YAA+B;QAE/B,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACxE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CACpC,SAAS,EACT,OAAO,EACP,OAAO,EACP,YAAY,CACb,CAAC;QACF,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,SAAe,EACf,OAAa,EACb,OAAsB,EACtB,YAA+B;QAE/B,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB;aAClC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,8BAA8B,EAC9B,EAAE,SAAS,EAAE,gCAAc,CAAC,YAAY,EAAE,CAC3C;aACA,MAAM,CACL,eAAe,OAAO,CAAC,WAAW,EAAE,sCAAsC,CAC3E;aACA,SAAS,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;aACjD,SAAS,CACR,mFAAmF,EACnF,YAAY,CACb;aACA,KAAK,CACJ,kGAAkG,EAClG,EAAE,SAAS,EAAE,OAAO,EAAE,CACvB;aACA,QAAQ,CAAC,2BAA2B,EAAE;YACrC,MAAM,EAAE,yCAAkB,CAAC,MAAM;SAClC,CAAC;aACD,QAAQ,CAAC,sCAAsC,EAAE;YAChD,WAAW,EAAE,IAAI,IAAI,EAAE;SACxB,CAAC,CAAC;QAEL,IAAI,YAAY,EAAE,CAAC;YACjB,KAAK,CAAC,QAAQ,CAAC,uBAAuB,EAAE;gBACtC,IAAI,EAAE,YAAY;aACnB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK;aACT,OAAO,CAAC,eAAe,OAAO,CAAC,WAAW,EAAE,4BAA4B,CAAC;aACzE,OAAO,CACN,eAAe,OAAO,CAAC,WAAW,EAAE,4BAA4B,EAChE,KAAK,CACN;aACA,UAAU,EAAE,CAAC;IAClB,CAAC;CACF,CAAA;AAtEY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCACI,oBAAU;GAHtC,8BAA8B,CAsE1C", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport {\r\n  DateGroupType,\r\n  GetContractsGrowthChartAbstraction,\r\n  PeriodFilter,\r\n} from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\nimport { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\nimport { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';\r\n\r\n@Injectable()\r\nexport class GetContractsGrowthChartService extends GetContractsGrowthChartAbstraction {\r\n  constructor(\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  async perform(\r\n    periodFilter: PeriodFilter = 'year',\r\n    contractType?: ContractTypeEnum,\r\n  ) {\r\n    const { startDate, endDate, groupBy } = this.getDateRange(periodFilter);\r\n    const data = await this.fetchChartData(\r\n      startDate,\r\n      endDate,\r\n      groupBy,\r\n      contractType,\r\n    );\r\n    return this.generateChartData(data, periodFilter);\r\n  }\r\n\r\n  private async fetchChartData(\r\n    startDate: Date,\r\n    endDate: Date,\r\n    groupBy: DateGroupType,\r\n    contractType?: ContractTypeEnum,\r\n  ) {\r\n    const query = this.contractRepository\r\n      .createQueryBuilder('contract')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :addStatus',\r\n        { addStatus: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select(\r\n        `DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract) AS period`,\r\n      )\r\n      .addSelect('COUNT(contract.id)', 'totalContracts')\r\n      .addSelect(\r\n        'COALESCE(SUM(signataries.investment_value), 0) + COALESCE(SUM(addendum.value), 0)',\r\n        'totalValue',\r\n      )\r\n      .where(\r\n        'CAST(contract.startContract AS DATE) BETWEEN CAST(:startDate AS DATE) AND CAST(:endDate AS DATE)',\r\n        { startDate, endDate },\r\n      )\r\n      .andWhere('contract.status = :status', {\r\n        status: ContractStatusEnum.ACTIVE,\r\n      })\r\n      .andWhere('contract.endContract >= :currentDate', {\r\n        currentDate: new Date(),\r\n      });\r\n\r\n    if (contractType) {\r\n      query.andWhere('contract.type = :type', {\r\n        type: contractType,\r\n      });\r\n    }\r\n\r\n    return query\r\n      .groupBy(`DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`)\r\n      .orderBy(\r\n        `DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`,\r\n        'ASC',\r\n      )\r\n      .getRawMany();\r\n  }\r\n}\r\n"]}