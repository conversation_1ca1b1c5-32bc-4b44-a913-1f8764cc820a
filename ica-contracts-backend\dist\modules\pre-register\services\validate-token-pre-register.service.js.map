{"version": 3, "file": "validate-token-pre-register.service.js", "sourceRoot": "/", "sources": ["modules/pre-register/services/validate-token-pre-register.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAC5C,+CAAsC;AA2B/B,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IAC1C,OAAO,CAAC,KAAa;QACnB,MAAM,OAAO,GAAG,IAAA,qBAAM,EAAC,KAAK,CAAa,CAAC;QAE1C,OAAO;YACL,KAAK,EAAE,OAAO,CAAC,aAAa;YAC5B,QAAQ,EAAE,OAAO,CAAC,gBAAgB;YAClC,KAAK,EAAE,OAAO,CAAC,gBAAgB;gBAC7B,CAAC,CAAC;oBACE,IAAI,EAAE,OAAO,CAAC,iBAAiB;oBAC/B,GAAG,EAAE,OAAO,CAAC,gBAAgB;iBAC9B;gBACH,CAAC,CAAC,SAAS;YACb,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO,CAAC,eAAe;gBAC9B,IAAI,EAAE,OAAO,CAAC,cAAc;gBAC5B,QAAQ,EAAE,OAAO,CAAC,kBAAkB;gBACpC,KAAK,EAAE,OAAO,CAAC,eAAe;gBAC9B,YAAY,EAAE,OAAO,CAAC,sBAAsB;gBAC5C,YAAY,EAAE,OAAO,CAAC,sBAAsB;gBAC5C,aAAa,EAAE,OAAO,CAAC,uBAAuB;gBAC9C,WAAW,EAAE,OAAO,CAAC,qBAAqB;gBAC1C,WAAW,EAAE,OAAO,CAAC,qBAAqB;gBAC1C,SAAS,EAAE,OAAO,CAAC,mBAAmB;gBACtC,YAAY,EAAE,OAAO,CAAC,sBAAsB;gBAC5C,6BAA6B,EAAE,OAAO,CAAC,6BAA6B;gBACpE,8BAA8B,EAAE,OAAO,CAAC,8BAA8B;aACvE;YACD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;SAC/B,CAAC;IACJ,CAAC;CACF,CAAA;AA/BY,0EAA+B;0CAA/B,+BAA+B;IAD3C,IAAA,mBAAU,GAAE;GACA,+BAA+B,CA+B3C", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { decode } from 'jsonwebtoken';\r\n\r\ninterface IDecoded {\r\n  adviserId: string;\r\n  investorEmail: string;\r\n  investorDocument: string;\r\n  investmentValue: number;\r\n  investmentTerm: string;\r\n  investmentModality: string;\r\n  investmentYield: number;\r\n  investmentPurchaseWith: string;\r\n  investmentAmountQuotes: number;\r\n  investmentStartContract: Date;\r\n  investmentEndContract: Date;\r\n  investmentGracePeriod: Date;\r\n  investmentDebenture: boolean;\r\n  investmentObservations: string;\r\n  investorOwnerCpf: string;\r\n  investorOwnerName: string;\r\n  signIca: string;\r\n  iat: number;\r\n  exp: number;\r\n  brokerParticipationPercentage?: string;\r\n  advisorParticipationPercentage?: string;\r\n}\r\n\r\n@Injectable()\r\nexport class ValidateTokenPreRegisterService {\r\n  perform(token: string) {\r\n    const decoded = decode(token) as IDecoded;\r\n\r\n    return {\r\n      email: decoded.investorEmail,\r\n      document: decoded.investorDocument,\r\n      owner: decoded.investorOwnerCpf\r\n        ? {\r\n            name: decoded.investorOwnerName,\r\n            cpf: decoded.investorOwnerCpf,\r\n          }\r\n        : undefined,\r\n      investment: {\r\n        value: decoded.investmentValue,\r\n        term: decoded.investmentTerm,\r\n        modality: decoded.investmentModality,\r\n        yield: decoded.investmentYield,\r\n        purchaseWith: decoded.investmentPurchaseWith,\r\n        amountQuotes: decoded.investmentAmountQuotes,\r\n        startContract: decoded.investmentStartContract,\r\n        endContract: decoded.investmentEndContract,\r\n        gracePeriod: decoded.investmentGracePeriod,\r\n        debenture: decoded.investmentDebenture,\r\n        observations: decoded.investmentObservations,\r\n        brokerParticipationPercentage: decoded.brokerParticipationPercentage,\r\n        advisorParticipationPercentage: decoded.advisorParticipationPercentage,\r\n      },\r\n      signIca: decoded.signIca ?? '',\r\n    };\r\n  }\r\n}\r\n"]}