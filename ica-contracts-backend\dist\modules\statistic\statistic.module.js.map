{"version": 3, "file": "statistic.module.js", "sourceRoot": "/", "sources": ["modules/statistic/statistic.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,8DAAwD;AAExD,6EAAyE;AACzE,kFAA6E;AAOtE,IAAM,cAAc,GAApB,MAAM,cAAc;CAAG,CAAA;AAAjB,wCAAc;yBAAd,cAAc;IAL1B,IAAA,eAAM,EAAC;QACN,WAAW,EAAE,CAAC,0CAAmB,CAAC;QAClC,OAAO,EAAE,CAAC,4BAAY,CAAC;QACvB,SAAS,EAAE,CAAC,iDAAsB,CAAC;KACpC,CAAC;GACW,cAAc,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { StatisticController } from './controllers/statistic.controller';\r\nimport { UpdateStatisticService } from './services/update-statistic.service';\r\n\r\n@Module({\r\n  controllers: [StatisticController],\r\n  imports: [SharedModule],\r\n  providers: [UpdateStatisticService],\r\n})\r\nexport class StatistcModule {}\r\n"]}