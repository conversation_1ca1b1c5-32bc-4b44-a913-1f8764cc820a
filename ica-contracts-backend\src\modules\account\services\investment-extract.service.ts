import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { InvestmentExtractResponse } from '../response/investmentExtract-response';

@Injectable()
export class InvestmentExtractService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountRepository: Repository<AccountEntity>,
  ) {}

  async execute(userId: string): Promise<InvestmentExtractResponse> {
    const account = await this.accountRepository.findOne({
      where: [
        {
          ownerId: Equal(userId),
        },
        {
          businessId: Equal(userId),
        },
      ],
      relations: {
        owner: true,
        business: true,
      },
    });

    if (!account) throw new NotFoundException('Usuario não possui conta ativa');

    const investmentInfo = account.owner ? account.owner : account.business;

    if (
      !(
        investmentInfo.startContract &&
        investmentInfo.endContract &&
        investmentInfo.yield &&
        investmentInfo.totalInvested
      )
    ) {
      return {
        totalInvested: 0,
        monthly: 0,
        weekly: 0,
        daily: 0,
      };
    }

    const profitabilityFee = investmentInfo.yield / 12;

    const monthly = investmentInfo.totalInvested * (profitabilityFee / 100);
    const daily = monthly / 30;
    const weekly = daily * 7;

    const taxRate = investmentInfo.taxRate ? investmentInfo.taxRate : null;
    const grossIncome = taxRate ? monthly * (taxRate / 100) : monthly;

    return {
      totalInvested: investmentInfo.totalInvested,
      grossIncome,
      monthly,
      weekly,
      daily,
    };
  }
}
