import { CacheModule } from '@nestjs/cache-manager';
import { Modu<PERSON> } from '@nestjs/common';

import { CreateTransactionsService } from './services/create-virtual-transaction.service';
import { GetAccountTransactionsService } from './services/get-account-transactions.service';
import { VirtualBalanceService } from './services/virtual-balance.service';
import { VirtualStatementService } from './services/virtual-statement.service';

@Module({
  imports: [CacheModule.register()],
  providers: [
    CreateTransactionsService,
    VirtualBalanceService,
    GetAccountTransactionsService,
    VirtualStatementService,
  ],
  exports: [
    CreateTransactionsService,
    VirtualBalanceService,
    GetAccountTransactionsService,
    VirtualStatementService,
  ],
})
export class IcaInvestCreditModule {}
