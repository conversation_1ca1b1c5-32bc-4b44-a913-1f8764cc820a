import { PinoLoggerAdapter } from './adapters/pino-logger.adapter'
import { app, setupRoutes } from './app'
import env from './config/env'
import { setupCron } from './cron'
import { setupIncomeReportWorker } from './workers/income-report.worker'
import '@/infrastructure/events/bootstrap-events'
import { setupProcessAuditSignatureWorker } from './workers/process-audit-signature.worker'
import { setupProcessAwaitingDepositWorker } from './workers/process-awaiting-deposit.worker'
import { setupProcessInvestorSignatureWorker } from './workers/process-investor.signature.worker'

async function bootstrap() {
  const logger = new PinoLoggerAdapter()

  try {
    setupRoutes()
    setupCron()

    const incomeReportWorker = await setupIncomeReportWorker(logger)

    const processInvestorSignatureWorker =
      await setupProcessInvestorSignatureWorker(logger)

    const processAuditSignatureWorker =
      await setupProcessAuditSignatureWorker(logger)

    const processAwaitingDepositWorker =
      await setupProcessAwaitingDepositWorker(logger)

    // Start the server
    await app.listen({ port: env.PORT, host: '0.0.0.0' })
    logger.info(`🚀 Server running at port ${env.PORT}`)

    // Handle graceful shutdown
    const shutdown = async () => {
      logger.info('Shutting down server...')
      await app.close()
      await incomeReportWorker.close()
      await processInvestorSignatureWorker.close()
      await processAuditSignatureWorker.close()
      await processAwaitingDepositWorker.close()
      process.exit(0)
    }

    process.on('SIGINT', shutdown)
    process.on('SIGTERM', shutdown)
  } catch (error) {
    logger.error(`❌ Error starting server: ${error}`)
    process.exit(1)
  }
}

bootstrap()
