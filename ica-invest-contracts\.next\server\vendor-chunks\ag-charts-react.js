"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ag-charts-react";
exports.ids = ["vendor-chunks/ag-charts-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/ag-charts-react/dist/package/index.esm.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/ag-charts-react/dist/package/index.esm.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgCharts: () => (/* binding */ AgCharts),\n/* harmony export */   AgFinancialCharts: () => (/* binding */ AgFinancialCharts),\n/* harmony export */   AgGauge: () => (/* binding */ AgGauge)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var ag_charts_community__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ag-charts-community */ \"(ssr)/./node_modules/ag-charts-community/dist/package/main.esm.mjs\");\n// packages/ag-charts-react/src/index.ts\n\n\nfunction getOptions(options, containerRef) {\n  return {\n    ...options,\n    container: containerRef.current\n  };\n}\nfunction ChartWithConstructor(ctor, displayName) {\n  const Component = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function AgChartsReact(props, ref) {\n    const { options, style, className } = props;\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const chartRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n      const chart = ctor(getOptions(options, containerRef));\n      chartRef.current = chart;\n      return () => {\n        chart.destroy();\n      };\n    }, []);\n    const unsafeIsInitialMount = chartRef.current === void 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n      if (!unsafeIsInitialMount) {\n        void chartRef.current?.update(getOptions(options, containerRef));\n      }\n    }, [options]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => chartRef.current, []);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n      return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"div\", {\n        ref: containerRef,\n        style,\n        className\n      });\n    }, [style, className]);\n  });\n  Component.displayName = displayName;\n  return Component;\n}\nvar AgCharts = /* @__PURE__ */ ChartWithConstructor(\n  (options) => ag_charts_community__WEBPACK_IMPORTED_MODULE_1__.AgCharts.create(options),\n  \"AgCharts\"\n);\nvar AgFinancialCharts = /* @__PURE__ */ ChartWithConstructor(\n  (options) => ag_charts_community__WEBPACK_IMPORTED_MODULE_1__.AgCharts.createFinancialChart(options),\n  \"AgFinancialCharts\"\n);\nvar AgGauge = /* @__PURE__ */ ChartWithConstructor(\n  (options) => ag_charts_community__WEBPACK_IMPORTED_MODULE_1__.AgCharts.createGauge(options),\n  \"AgGauge\"\n);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ag-charts-react/dist/package/index.esm.mjs\n");

/***/ })

};
;