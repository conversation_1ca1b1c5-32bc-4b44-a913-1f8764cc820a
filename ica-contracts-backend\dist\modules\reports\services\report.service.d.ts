import { FileEntity } from 'src/shared/database/typeorm/entities/files-entity';
import { ReportEntity } from 'src/shared/database/typeorm/entities/report.entity';
import { Repository } from 'typeorm';
import { ReportRegistryService } from './report-registry.service';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
export declare class ReportService {
    private readonly registry;
    private reportRepository;
    private ownerRoleRelationRepository;
    constructor(registry: ReportRegistryService, reportRepository: Repository<ReportEntity>, ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>);
    generateReport(type: string, data: any, roleId: string, userId: string): Promise<FileEntity | null>;
}
