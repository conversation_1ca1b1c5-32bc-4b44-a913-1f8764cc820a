"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidateAdminMiddleware = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const jsonwebtoken_1 = require("jsonwebtoken");
const typeorm_2 = require("typeorm");
const owner_role_relation_entity_1 = require("../database/typeorm/entities/owner-role-relation.entity");
let ValidateAdminMiddleware = class ValidateAdminMiddleware {
    constructor(perfilDb) {
        this.perfilDb = perfilDb;
    }
    async use(req, res, next) {
        const token = req.headers.authorization.split(' ')[1];
        const decodeJwt = (0, jsonwebtoken_1.decode)(token);
        const adviserId = req.body.adviserId ||
            req.body.roleId ||
            req.query.adviserId ||
            req.query.roleId;
        const owner = await this.perfilDb.findOne({
            relations: {
                role: true,
            },
            where: [
                {
                    ownerId: (0, typeorm_2.Equal)(decodeJwt.id),
                    id: (0, typeorm_2.Equal)(adviserId),
                    role: [{ name: 'admin' }],
                },
                {
                    businessId: (0, typeorm_2.Equal)(decodeJwt.id),
                    id: (0, typeorm_2.Equal)(adviserId),
                    role: [{ name: 'admin' }],
                },
            ],
        });
        if (!owner) {
            throw new common_1.ForbiddenException();
        }
        next();
    }
};
exports.ValidateAdminMiddleware = ValidateAdminMiddleware;
exports.ValidateAdminMiddleware = ValidateAdminMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ValidateAdminMiddleware);
//# sourceMappingURL=validate-admin.middleware.js.map