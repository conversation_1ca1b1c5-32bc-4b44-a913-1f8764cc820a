export declare class ValidateTokenPreRegisterService {
    perform(token: string): {
        email: string;
        document: string;
        owner: {
            name: string;
            cpf: string;
        };
        investment: {
            value: number;
            term: string;
            modality: string;
            yield: number;
            purchaseWith: string;
            amountQuotes: number;
            startContract: Date;
            endContract: Date;
            gracePeriod: Date;
            debenture: boolean;
            observations: string;
            brokerParticipationPercentage: string;
            advisorParticipationPercentage: string;
        };
        signIca: string;
    };
}
