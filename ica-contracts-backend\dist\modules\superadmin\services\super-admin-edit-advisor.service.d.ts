import { EditAdvisorService } from 'src/modules/advisor/services/edit-advisor.service';
import { EditBrokerDto } from 'src/modules/broker/dto/edit-broker.dto';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';
export declare class SuperAdminEditAdvisorService {
    private readonly ownerRoleRelationRepository;
    private readonly editAdvisorService;
    constructor(ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, editAdvisorService: EditAdvisorService);
    perform(payload: EditBrokerDto): Promise<void>;
}
