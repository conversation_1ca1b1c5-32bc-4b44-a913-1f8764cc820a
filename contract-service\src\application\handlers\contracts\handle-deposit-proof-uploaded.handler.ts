import { ContractStatus } from '@/domain/entities/contracts'
import type { DepositProofUploadedEvent } from '@/domain/events/contracts/deposit-proof-uploaded.event'
import type { IInvestmentContractRepository } from '@/domain/repositories'

export class HandleDepositProofUploaded {
  constructor(private readonly repo: IInvestmentContractRepository) {}

  async handle(event: DepositProofUploadedEvent): Promise<void> {
    const contract = await this.repo.findById(event.payload.contractId)

    if (contract.isLeft()) {
      return
    }

    if (!contract.value) {
      console.warn(`[WARN] Contract not found: ${event.payload.contractId}`)
      return
    }

    const currentStatus = contract.value.getStatus()

    if (currentStatus !== ContractStatus.AWAITING_DEPOSIT) {
      console.log(
        `[SKIP] Contract ${contract.value.id} not awaiting deposit. Current: ${currentStatus}`
      )
      return
    }

    if (!event.payload.contractId) {
      console.warn(
        `[WARN] Missing deposit proof for contract ${contract.value.id}`
      )
      return
    }

    await this.repo.save(contract.value)

    console.log(
      `[INFO] Deposit proof saved. Contract ${contract.value} is now AWAITING_AUDIT.`
    )

    // Opcional: publicar evento para disparar notificação à auditoria
    // DomainEventMediator.publish(new AuditorNotificationEvent(contract.id))
  }
}
