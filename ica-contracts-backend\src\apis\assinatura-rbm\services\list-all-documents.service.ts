import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import { IListDocumentsQuery } from '../requests/list-all-documents.request';
import { IListDocumentsResponse } from '../responses/list-all-documents.response';
import { AuthIntegrationService } from './auth.service';

@Injectable()
export class ListAllDocumentsService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly logger = new Logger(ListAllDocumentsService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authIntegrationService: AuthIntegrationService,
  ) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/v2/documentos`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async listDocuments(
    query?: IListDocumentsQuery,
  ): Promise<IListDocumentsResponse> {
    const authResponse =
      await this.authIntegrationService.authenticateIntegration();
    const { token } = authResponse.jwt;

    try {
      const response = await this.axiosInstance.get<IListDocumentsResponse>(
        '/list',
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          params: query,
        },
      );

      if (response.data.erro) {
        this.logger.error(`Error fetching documents: ${response.data.message}`);
        throw new InternalServerErrorException(response.data.message);
      }

      return response.data;
    } catch (error) {
      this.logger.error('Error fetching documents', error);

      if (error.response) {
        throw new InternalServerErrorException(
          `API error: ${error.response.status} - ${error.response.data.message || error.response.data}`,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'No response received from API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Request error: ${error.message}`,
        );
      }
    }
  }
}
