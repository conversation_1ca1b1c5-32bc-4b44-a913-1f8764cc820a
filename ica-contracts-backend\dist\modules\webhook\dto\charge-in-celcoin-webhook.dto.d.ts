export interface IChargeInCelcoinWebhookDto {
    entity: 'charge-in';
    createTimestamp: string;
    status: 'CANCELLED' | 'CONFIRMED' | 'ERROR';
    body: {
        externalId: string;
        transactionId: string;
        status: string;
        dataPagamento: string;
        dataVencimento: string;
        valorPago: number;
        valorOriginal: number;
        tipoPagamento: 'Pix' | 'Boleto';
        split: {
            amount: number;
            account: number;
        }[];
    };
}
