{"version": 3, "file": "pix.controller.js", "sourceRoot": "/", "sources": ["modules/pix/controllers/pix.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAewB;AACxB,gFAA8D;AAC9D,iEAAwD;AACxD,0EAAgE;AAChE,kEAAyD;AAGzD,8DAAyD;AACzD,gEAA2D;AAC3D,wDAAmD;AACnD,kEAAyD;AACzD,wFAAiF;AACjF,sFAA+E;AAC/E,kEAA4D;AAC5D,8EAAuE;AACvE,8EAAwE;AACxE,oEAA8D;AAC9D,gFAA8E;AAC9E,4EAAsE;AACtE,0FAAmF;AACnF,0FAAoF;AACpF,oFAA6E;AAC7E,8EAAwE;AACxE,oGAA6F;AAC7F,kGAA2F;AAC3F,8EAAwE;AACxE,0FAAmF;AACnF,wEAAkE;AAClE,0FAAoF;AACpF,gFAA0E;AAC1E,4FAAqF;AACrF,wFAAkF;AAG3E,IAAM,aAAa,GAAnB,MAAM,aAAa;IACxB,YAEmB,mBAAwC,EAExC,gBAAkC,EAElC,wBAAkD,EAElD,mBAAwC,EAExC,yBAAoD,EAEpD,yBAAuD,EAEvD,0BAAyD,EAEzD,yBAAoD,EAEpD,yBAAoD,EAEpD,iBAAuC,EAEvC,wBAAkD,EAClD,qBAA4C,EAC5C,wBAAkD;QAtBlD,wBAAmB,GAAnB,mBAAmB,CAAqB;QAExC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAElC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAElD,wBAAmB,GAAnB,mBAAmB,CAAqB;QAExC,8BAAyB,GAAzB,yBAAyB,CAA2B;QAEpD,8BAAyB,GAAzB,yBAAyB,CAA8B;QAEvD,+BAA0B,GAA1B,0BAA0B,CAA+B;QAEzD,8BAAyB,GAAzB,yBAAyB,CAA2B;QAEpD,8BAAyB,GAAzB,yBAAyB,CAA2B;QAEpD,sBAAiB,GAAjB,iBAAiB,CAAsB;QAEvC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,6BAAwB,GAAxB,wBAAwB,CAA0B;IAClE,CAAC;IAKE,AAAN,KAAK,CAAC,YAAY,CAEhB,IAAkB,EACP,OAAqB;QAEhC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7E,OAAO,MAAM,CAAC;IAChB,CAAC;IAMK,AAAN,KAAK,CAAC,UAAU,CAAY,OAAqB;QAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrE,OAAO,OAAO,CAAC;IACjB,CAAC;IAKK,AAAN,KAAK,CAAC,iBAAiB,CAErB,OAAqB,EAErB,KAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CACtD,KAAK,EACL,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,YAAY,CACP,KAAsB,EACpB,OAAqB;QAEhC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CACjD,KAAK,EACL,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,kBAAkB,CAEtB,OAAqB,EAErB,KAA4B;QAE5B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,cAAc,CAElB,OAAqB,EAErB,IAA8B;QAE9B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CACvD,IAAI,EACJ,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,eAAe,CAEnB,OAAqB,EAErB,IAA+B;QAE/B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CACxD,IAAI,EACJ,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,YAAY,CACL,OAAqB,EAEhC,IAAgC;QAEhC,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpE,OAAO,EAAE,MAAM,EAAE,sCAAsC,EAAE,CAAC;IAC5D,CAAC;IAKK,AAAN,KAAK,CAAC,YAAY,CACL,OAAqB,EACxB,KAAsB;QAE9B,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrE,OAAO,EAAE,MAAM,EAAE,sCAAsC,EAAE,CAAC;IAC5D,CAAC;IAGK,AAAN,KAAK,CAAC,UAAU,CAEd,IAAsB;QAEtB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,WAAW,CACJ,OAAqB,EACxB,KAAqB;QAE7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAC1D,KAAK,EACL,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKK,AAAN,KAAK,CAAC,QAAQ,CAAY,OAAqB,EAAU,KAAkB;QACzE,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7E,OAAO,GAAG,CAAC;IACb,CAAC;IAIK,AAAN,KAAK,CAAC,iBAAiB,CACV,OAAqB,EAEhC,IAA0B,EACF,cAAsB;QAE9C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CACtD,IAAI,EACJ,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,KAAK,EACL,cAAc,CACf,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAjPY,sCAAa;AA+BlB;IAHL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,iBAAQ,EAAC,mBAAU,CAAC,OAAO,CAAC;IAC5B,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADJ,iCAAY;;iDAKnB;AAMK;IAJL,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAEjB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;+CAG1B;AAKK;IAHL,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,cAAK,GAAE,CAAA;;6CACD,+CAAoB;;sDAc5B;AAKK;IAHL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAEhC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADM,oCAAe;;iDAehC;AAIK;IAFL,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,cAAK,GAAE,CAAA;;6CACD,gDAAqB;;uDAW7B;AAIK;IAFL,IAAA,aAAI,EAAC,eAAe,CAAC;IACrB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,uDAAwB;;mDAc/B;AAIK;IAFL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IACtB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,yDAAyB;;oDAchC;AAKK;IAHL,IAAA,aAAI,EAAC,eAAe,CAAC;IACrB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,QAAQ,EAAE,sBAAS,CAAC,OAAO,EAAE,sBAAS,CAAC,MAAM,CAAC;IAE5D,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,sDAA0B;;iDAIjC;AAKK;IAHL,IAAA,aAAI,EAAC,eAAe,CAAC;IACrB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,QAAQ,EAAE,sBAAS,CAAC,OAAO,EAAE,sBAAS,CAAC,MAAM,CAAC;IAE5D,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAQ,mCAAe;;iDAI/B;AAGK;IADL,IAAA,aAAI,EAAC,aAAa,CAAC;IAEjB,WAAA,IAAA,aAAI,GAAE,CAAA;;qCACD,sCAAgB;;+CAWvB;AAKK;IAHL,IAAA,aAAI,EAAC,cAAc,CAAC;IACpB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,QAAQ,EAAE,sBAAS,CAAC,OAAO,EAAE,sBAAS,CAAC,MAAM,CAAC;IAE5D,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAQ,iCAAc;;gDAQ9B;AAKK;IAHL,IAAA,aAAI,EAAC,WAAW,CAAC;IACjB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,QAAQ,EAAE,sBAAS,CAAC,OAAO,EAAE,sBAAS,CAAC,MAAM,CAAC;IAC/C,WAAA,IAAA,gBAAO,GAAE,CAAA;IAAyB,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAQ,2BAAW;;6CAG1E;AAIK;IAFL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,EAAC,IAAI,uBAAc,CAAC,EAAE,YAAY,EAAE,8CAAoB,EAAE,CAAC,CAAC,CAAA;IAEhE,WAAA,IAAA,gBAAO,EAAC,aAAa,CAAC,CAAA;;6CADjB,8CAAoB;;sDAiB3B;wBAhPU,aAAa;IADzB,IAAA,mBAAU,EAAC,KAAK,CAAC;IAGb,WAAA,IAAA,eAAM,EAAC,4CAAmB,CAAC,CAAA;IAE3B,WAAA,IAAA,eAAM,EAAC,sCAAgB,CAAC,CAAA;IAExB,WAAA,IAAA,eAAM,EAAC,uDAAwB,CAAC,CAAA;IAEhC,WAAA,IAAA,eAAM,EAAC,4CAAmB,CAAC,CAAA;IAE3B,WAAA,IAAA,eAAM,EAAC,wDAAyB,CAAC,CAAA;IAEjC,WAAA,IAAA,eAAM,EAAC,+DAA4B,CAAC,CAAA;IAEpC,WAAA,IAAA,eAAM,EAAC,iEAA6B,CAAC,CAAA;IAErC,WAAA,IAAA,eAAM,EAAC,yDAAyB,CAAC,CAAA;IAEjC,WAAA,IAAA,eAAM,EAAC,wDAAyB,CAAC,CAAA;IAEjC,WAAA,IAAA,eAAM,EAAC,8CAAoB,CAAC,CAAA;IAE5B,YAAA,IAAA,eAAM,EAAC,uDAAwB,CAAC,CAAA;qCAnBK,4CAAmB;QAEtB,sCAAgB;QAER,uDAAwB;QAE7B,4CAAmB;QAEb,wDAAyB;QAEzB,+DAA4B;QAE3B,iEAA6B;QAE9B,yDAAyB;QAEzB,wDAAyB;QAEjC,8CAAoB;QAEb,uDAAwB;QAC3B,iDAAqB;QAClB,sDAAwB;GAzB1D,aAAa,CAiPzB", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Post,\r\n  HttpCode,\r\n  HttpStatus,\r\n  Inject,\r\n  UseGuards,\r\n  Request,\r\n  Get,\r\n  Query,\r\n  HttpException,\r\n  Delete,\r\n  Headers,\r\n  ValidationPipe,\r\n} from '@nestjs/common';\r\nimport { Roles } from 'src/shared/decorators/roles.decorator';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { RoleGuard } from 'src/shared/guards/role.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport { ClaimCancelDto } from '../dto/claim-cancel.dto';\r\nimport { ClaimConfirmDto } from '../dto/claim-confirm.dto';\r\nimport { ClaimGetDto } from '../dto/claim-get.dto';\r\nimport { CreatePixDto } from '../dto/create-pix-key.dto';\r\nimport { CreatePixQRCodeDynamicDto } from '../dto/create-pix-qrcode-dynamic.dto';\r\nimport { CreatePixQRCodeStaticDto } from '../dto/create-pix-qrcode-static.dto';\r\nimport { DeletePixKeyDto } from '../dto/delete-pix-key.dto';\r\nimport { GetPixKeyExternalDto } from '../dto/get-pix-key-external.dto';\r\nimport { GetPixParticipantsDto } from '../dto/get-pix-participants.dto';\r\nimport { ReadQRCodePixDto } from '../dto/read-qrcode-pix.dto';\r\nimport { RequestClaimPixKeyInputDTO } from '../dto/request-claim-pix-key.dto';\r\nimport { TransactionPixKeyDto } from '../dto/transaction-pix-key.dto';\r\nimport { ClaimCancelPixKeyService } from '../service/claim-cancel-pix-key.service';\r\nimport { ClaimConfirmPixKeyService } from '../service/claim-confim-pix-key.service';\r\nimport { ClaimGetPixKeyService } from '../service/claim-get-pix-key.service';\r\nimport { CreatePixKeyService } from '../service/create-pix-key.service';\r\nimport { CreatePixQRCodeDynamicService } from '../service/create-pix-qrcode-dynamic.service';\r\nimport { CreatePixQRCodeStaticService } from '../service/create-pix-qrcode-static.service';\r\nimport { DeletePixKeyService } from '../service/delete-pix-key.service';\r\nimport { GetPixKeyExternalService } from '../service/get-pix-key-external.service';\r\nimport { GetPixKeyService } from '../service/get-pix-key.service';\r\nimport { GetPixParticipantsService } from '../service/get-pix-participants.service';\r\nimport { ReadQRCodePixService } from '../service/read-qrcode-pix.service';\r\nimport { RequestClaimPixKeyService } from '../service/request-claim-pix-key.service';\r\nimport { TransactionPixKeyService } from '../service/transaction-pix-key.service';\r\n\r\n@Controller('pix')\r\nexport class PixController {\r\n  constructor(\r\n    @Inject(CreatePixKeyService)\r\n    private readonly createPixKeyService: CreatePixKeyService,\r\n    @Inject(GetPixKeyService)\r\n    private readonly getPixKeyService: GetPixKeyService,\r\n    @Inject(GetPixKeyExternalService)\r\n    private readonly getPixKeyExternalService: GetPixKeyExternalService,\r\n    @Inject(DeletePixKeyService)\r\n    private readonly deletePixKeyService: DeletePixKeyService,\r\n    @Inject(GetPixParticipantsService)\r\n    private readonly getPixParticipantsService: GetPixParticipantsService,\r\n    @Inject(CreatePixQRCodeStaticService)\r\n    private readonly createQRCodeStaticService: CreatePixQRCodeStaticService,\r\n    @Inject(CreatePixQRCodeDynamicService)\r\n    private readonly createQRCodeDynamicService: CreatePixQRCodeDynamicService,\r\n    @Inject(RequestClaimPixKeyService)\r\n    private readonly requestClaimPixKeyService: RequestClaimPixKeyService,\r\n    @Inject(ClaimConfirmPixKeyService)\r\n    private readonly claimConfirmPixKeyService: ClaimConfirmPixKeyService,\r\n    @Inject(ReadQRCodePixService)\r\n    private readonly readQrCodeService: ReadQRCodePixService,\r\n    @Inject(ClaimCancelPixKeyService)\r\n    private readonly claimCancelPixKeyService: ClaimCancelPixKeyService,\r\n    private readonly claimGetPixKeyService: ClaimGetPixKeyService,\r\n    private readonly transactionPixKeyService: TransactionPixKeyService,\r\n  ) {}\r\n\r\n  @Post('create')\r\n  @HttpCode(HttpStatus.CREATED)\r\n  @UseGuards(JwtAuthGuard)\r\n  async createPixKey(\r\n    @Body()\r\n    body: CreatePixDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    const pixKey = await this.createPixKeyService.perform(body, request.user.id);\r\n    return pixKey;\r\n  }\r\n\r\n  @Get('get-all')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  // @Roles(RolesEnum.INVESTOR)\r\n  async getPixKeys(@Request() request: IRequestUser) {\r\n    const pixKeys = await this.getPixKeyService.perform(request.user.id);\r\n    return pixKeys;\r\n  }\r\n\r\n  @Get('get-external')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async getPixKeyExternal(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Query()\r\n    query: GetPixKeyExternalDto,\r\n  ) {\r\n    try {\r\n      const data = await this.getPixKeyExternalService.perform(\r\n        query,\r\n        request.user.id,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Delete('key')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  async deletePixKey(\r\n    @Query() query: DeletePixKeyDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    try {\r\n      const data = await this.deletePixKeyService.perform(\r\n        query,\r\n        request.user.id,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('participants')\r\n  @HttpCode(HttpStatus.OK)\r\n  async getPixParticipants(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Query()\r\n    query: GetPixParticipantsDto,\r\n  ) {\r\n    try {\r\n      const data = await this.getPixParticipantsService.execute(query);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('qrcode/static')\r\n  @UseGuards(JwtAuthGuard)\r\n  async createQRStatic(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Body()\r\n    body: CreatePixQRCodeStaticDto,\r\n  ) {\r\n    try {\r\n      const data = await this.createQRCodeStaticService.perform(\r\n        body,\r\n        request.user.id,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('qrcode/dynamic')\r\n  @UseGuards(JwtAuthGuard)\r\n  async createQRDynamic(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Body()\r\n    body: CreatePixQRCodeDynamicDto,\r\n  ) {\r\n    try {\r\n      const data = await this.createQRCodeDynamicService.perform(\r\n        body,\r\n        request.user.id,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('request-claim')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.INVESTOR, RolesEnum.ADVISOR, RolesEnum.BROKER)\r\n  async requestClaim(\r\n    @Request() request: IRequestUser,\r\n    @Body()\r\n    body: RequestClaimPixKeyInputDTO,\r\n  ): Promise<{ reason: string }> {\r\n    await this.requestClaimPixKeyService.perform(body, request.user.id);\r\n    return { reason: 'Reivindicação realizada com sucesso.' };\r\n  }\r\n\r\n  @Post('confirm-claim')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.INVESTOR, RolesEnum.ADVISOR, RolesEnum.BROKER)\r\n  async ConfirmClaim(\r\n    @Request() request: IRequestUser,\r\n    @Body() input: ClaimConfirmDto,\r\n  ): Promise<{ reason: string }> {\r\n    await this.claimConfirmPixKeyService.execute(input, request.user.id);\r\n    return { reason: 'Reivindicação realizada com sucesso.' };\r\n  }\r\n\r\n  @Post('qrcode/read')\r\n  async readQRCode(\r\n    @Body()\r\n    body: ReadQRCodePixDto,\r\n  ) {\r\n    try {\r\n      const data = await this.readQrCodeService.perform(body);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('cancel-claim')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.INVESTOR, RolesEnum.ADVISOR, RolesEnum.BROKER)\r\n  async CancelClaim(\r\n    @Request() request: IRequestUser,\r\n    @Body() input: ClaimCancelDto,\r\n  ): Promise<{ reason: string }> {\r\n    const response = await this.claimCancelPixKeyService.execute(\r\n      input,\r\n      request.user.id,\r\n    );\r\n\r\n    return response;\r\n  }\r\n\r\n  @Post('get-claim')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.INVESTOR, RolesEnum.ADVISOR, RolesEnum.BROKER)\r\n  async getClaim(@Request() request: IRequestUser, @Body() input: ClaimGetDto) {\r\n    const res = await this.claimGetPixKeyService.execute(input, request.user.id);\r\n    return res;\r\n  }\r\n\r\n  @Post('transaction')\r\n  @UseGuards(JwtAuthGuard)\r\n  async transactionPixKey(\r\n    @Request() request: IRequestUser,\r\n    @Body(new ValidationPipe({ expectedType: TransactionPixKeyDto }))\r\n    body: TransactionPixKeyDto,\r\n    @Headers('x-2fa-token') twoFactorToken: string,\r\n  ) {\r\n    try {\r\n      const data = await this.transactionPixKeyService.perform(\r\n        body,\r\n        request.user.id,\r\n        false,\r\n        twoFactorToken,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}