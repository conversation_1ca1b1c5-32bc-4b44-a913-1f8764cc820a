{"version": 3, "file": "update-statistic.service.js", "sourceRoot": "/", "sources": ["modules/statistic/services/update-statistic.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,iGAAwF;AACxF,qCAAqC;AAK9B,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACjC,YAEmB,mBAAgD;QAAhD,wBAAmB,GAAnB,mBAAmB,CAA6B;IAChE,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,SAA6B;QACzC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;QACxD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACtC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;QAC/C,CAAC;QACD,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACzC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;QACrD,CAAC;QACD,IAAI,SAAS,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACvC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACjD,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;CACF,CAAA;AAxBY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kCAAe,CAAC,CAAA;qCACI,oBAAU;GAHvC,sBAAsB,CAwBlC", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { StatisticEntity } from 'src/shared/database/typeorm/entities/statistic.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { UpdateStatisticDto } from '../dto/update-statistic.dto';\r\n\r\n@Injectable()\r\nexport class UpdateStatisticService {\r\n  constructor(\r\n    @InjectRepository(StatisticEntity)\r\n    private readonly statisticRepository: Repository<StatisticEntity>,\r\n  ) {}\r\n\r\n  async perform(updateDto: UpdateStatisticDto): Promise<StatisticEntity[]> {\r\n    const statistic = await this.statisticRepository.find();\r\n    if (!statistic) {\r\n      throw new NotFoundException('Nenhum dado encontrado.');\r\n    }\r\n\r\n    if (updateDto.investors !== undefined) {\r\n      statistic[0].investors = updateDto.investors;\r\n    }\r\n    if (updateDto.totalApplied !== undefined) {\r\n      statistic[0].totalApplied = updateDto.totalApplied;\r\n    }\r\n    if (updateDto.statesData !== undefined) {\r\n      statistic[0].statesData = updateDto.statesData;\r\n    }\r\n\r\n    return this.statisticRepository.save(statistic);\r\n  }\r\n}\r\n"]}