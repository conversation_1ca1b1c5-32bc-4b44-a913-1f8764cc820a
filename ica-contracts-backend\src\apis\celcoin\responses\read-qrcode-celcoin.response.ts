interface IMerchantAccountInformation {
  url: string | null;
  gui: string;
  key: string;
  additionalInformation: string;
  withdrawalServiceProvider: string | null;
}

export interface IReadQrCodeCelcoinResponse {
  type: string;
  collection: null;
  payloadFormatIndicator: string;
  merchantAccountInformation: IMerchantAccountInformation;
  merchantCategoryCode: number;
  transactionCurrency: number;
  transactionAmount: number;
  countryCode: string;
  merchantName: string;
  merchantCity: string;
  postalCode: string;
  initiationMethod: string | null;
  transactionIdentification: string;
}
