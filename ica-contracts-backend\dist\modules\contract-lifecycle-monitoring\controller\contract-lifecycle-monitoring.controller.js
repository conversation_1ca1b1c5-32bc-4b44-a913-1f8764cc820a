"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractLifecycleMonitoringController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const create_contract_event_dto_1 = require("../dto/create-contract-event.dto");
const finalize_contract_event_dto_1 = require("../dto/finalize-contract-event.dto");
const pagination_dto_1 = require("../dto/pagination.dto");
const send_to_retention_dto_1 = require("../dto/send-to-retention.dto");
const dashboard_service_1 = require("../services/dashboard.service");
const finalize_contract_event_service_1 = require("../services/finalize-contract-event.service");
const find_expiring_contracts_service_1 = require("../services/find-expiring-contracts.service");
const get_contracts_for_retention_service_1 = require("../services/get-contracts-for-retention.service");
const send_contract_to_retention_service_1 = require("../services/send-contract-to-retention.service");
const update_contract_from_retention_service_1 = require("../services/update-contract-from-retention.service");
let ContractLifecycleMonitoringController = class ContractLifecycleMonitoringController {
    constructor(contractLifecycleMonitoringService, sendContractToRetentionService, getContractsForRetentionService, updateContractFromRetentionService, finalizeContractEventService, dashboardService) {
        this.contractLifecycleMonitoringService = contractLifecycleMonitoringService;
        this.sendContractToRetentionService = sendContractToRetentionService;
        this.getContractsForRetentionService = getContractsForRetentionService;
        this.updateContractFromRetentionService = updateContractFromRetentionService;
        this.finalizeContractEventService = finalizeContractEventService;
        this.dashboardService = dashboardService;
    }
    async getExpiringContracts(query, request) {
        return this.contractLifecycleMonitoringService.perform(query, request.user.id);
    }
    async sendContractToRetention(body, request) {
        return this.sendContractToRetentionService.perform(body, request.user.id);
    }
    async getContractsForRetention(statuses, request, page = 1, limit = 10) {
        const statusArray = [];
        if (statuses) {
            if (Array.isArray(statuses)) {
                statusArray.push(...statuses);
            }
            else {
                statusArray.push(statuses);
            }
        }
        return this.getContractsForRetentionService.perform(request.user.id, statusArray, Number(page), Number(limit));
    }
    async dashboard(request) {
        return this.dashboardService.perform(request.user.id);
    }
    async updateContractFromRetention(contractId, createEventDto, attachments, request) {
        const event = await this.updateContractFromRetentionService.perform(contractId, createEventDto, request.user.id, attachments);
        return {
            message: 'Evento de contrato criado com sucesso',
            event,
        };
    }
    async finalizeContractEvent(contractId, finalizeEventDto, attachments, request) {
        const event = await this.finalizeContractEventService.perform(contractId, finalizeEventDto, request.user.id, attachments);
        return {
            message: 'Evento de contrato finalizado com sucesso',
            event,
        };
    }
};
exports.ContractLifecycleMonitoringController = ContractLifecycleMonitoringController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('expiring'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pagination_dto_1.PaginationDto, Object]),
    __metadata("design:returntype", Promise)
], ContractLifecycleMonitoringController.prototype, "getExpiringContracts", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('send-contract-to-retention'),
    (0, common_1.HttpCode)(204),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [send_to_retention_dto_1.SendToRetentionDto, Object]),
    __metadata("design:returntype", Promise)
], ContractLifecycleMonitoringController.prototype, "sendContractToRetention", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('retention'),
    __param(0, (0, common_1.Query)('statuses')),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], ContractLifecycleMonitoringController.prototype, "getContractsForRetention", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('dashboard'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContractLifecycleMonitoringController.prototype, "dashboard", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)(':contractId/retention/update'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('attachments')),
    __param(0, (0, common_1.Param)('contractId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFiles)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_contract_event_dto_1.CreateContractEventDto, Array, Object]),
    __metadata("design:returntype", Promise)
], ContractLifecycleMonitoringController.prototype, "updateContractFromRetention", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)(':contractId/finalize'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('attachments')),
    __param(0, (0, common_1.Param)('contractId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFiles)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, finalize_contract_event_dto_1.FinalizeContractEventDto, Array, Object]),
    __metadata("design:returntype", Promise)
], ContractLifecycleMonitoringController.prototype, "finalizeContractEvent", null);
exports.ContractLifecycleMonitoringController = ContractLifecycleMonitoringController = __decorate([
    (0, common_1.Controller)('contract-lifecycle-monitoring'),
    __metadata("design:paramtypes", [find_expiring_contracts_service_1.FindExpiringContractsService,
        send_contract_to_retention_service_1.SendContractToRetentionService,
        get_contracts_for_retention_service_1.GetContractsForRetentionService,
        update_contract_from_retention_service_1.UpdateContractFromRetentionService,
        finalize_contract_event_service_1.FinalizeContractEventService,
        dashboard_service_1.DashboardService])
], ContractLifecycleMonitoringController);
//# sourceMappingURL=contract-lifecycle-monitoring.controller.js.map