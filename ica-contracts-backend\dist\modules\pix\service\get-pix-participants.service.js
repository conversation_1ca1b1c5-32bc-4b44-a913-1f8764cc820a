"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPixParticipantsService = void 0;
const common_1 = require("@nestjs/common");
const pix_transaction_celcoin_service_1 = require("../../../apis/celcoin/services/pix-transaction-celcoin.service");
const logger_1 = require("../../../shared/logger");
let GetPixParticipantsService = class GetPixParticipantsService {
    constructor(celcoinService) {
        this.celcoinService = celcoinService;
    }
    async execute(data) {
        try {
            const pixParticipants = await this.celcoinService.getPixParticipants(data);
            if (!pixParticipants)
                throw new common_1.NotFoundException(`Nenhum participante encontrado. [payload: ${data}]`);
            return pixParticipants;
        }
        catch (error) {
            logger_1.logger.error('GetPixParticipants.execute() -> ', error);
            throw new common_1.BadRequestException(error);
        }
    }
};
exports.GetPixParticipantsService = GetPixParticipantsService;
exports.GetPixParticipantsService = GetPixParticipantsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(pix_transaction_celcoin_service_1.PixTransactionCelcoinService)),
    __metadata("design:paramtypes", [pix_transaction_celcoin_service_1.PixTransactionCelcoinService])
], GetPixParticipantsService);
//# sourceMappingURL=get-pix-participants.service.js.map