import { OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';
import { logger } from 'src/shared/logger';
import { Repository } from 'typeorm';

@Processor('transactions')
export class ProcessScheduledPixService {
  constructor(
    @InjectRepository(TransactionEntity)
    private transactionRepository: Repository<TransactionEntity>,
    @Inject(PixTransactionCelcoinService)
    private celcoinService: PixTransactionCelcoinService,
  ) {}

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} of type ${job.name}. Data: ${JSON.stringify(
        job.data,
      )}`,
    );
  }

  @Process({ concurrency: 20 })
  async processPix(job: Job<{ transactionId: string }>) {
    const { transactionId } = job.data;

    const transaction = await this.transactionRepository.findOne({
      where: { id: transactionId },
      relations: { account: true },
    });

    if (!transaction) return;

    if (transaction.type === 'PIX_MANUAL') {
      await this.processManualScheduledPix(transaction);
    } else {
      await this.processScheduledPix(transaction);
    }
  }

  private async processScheduledPix(transaction: TransactionEntity) {
    try {
      const { body: result } = await this.celcoinService.transactionPixKey({
        amount: Number(transaction.value),
        initiationType: 'DICT',
        paymentType: 'IMMEDIATE',
        remittanceInformation: transaction.description,
        transactionType: 'TRANSFER',
        urgency: 'HIGH',
        endToEndId: transaction.endToEndId,
        clientCode: transaction.code,
        creditParty: {
          key: transaction.destinyKey,
          account: transaction.destinyAccount,
          accountType: transaction.destinyAccountType,
          bank: transaction.destinyBank,
          branch: transaction.destinyBranch,
          name: transaction.destinyName,
          taxId: transaction.destinyDocument,
        },
        debitParty: {
          account: transaction.account.number,
        },
      });

      const transferMetadata = {
        creditParty: result.creditParty,
        debitParty: result.debitParty,
      };

      await this.transactionRepository.update(transaction.id, {
        ...transaction,
        transferMetadata: JSON.stringify(transferMetadata),
        status: TransactionStatusEnum.PENDENT,
      });
    } catch (error) {
      logger.error(
        `PixService.processScheduledPix() -> Error ao tentar realizar Pix agendado. [ transactionId: ${transaction.id} ]`,
        error,
      );

      await this.transactionRepository.update(transaction.id, {
        ...transaction,
        status: TransactionStatusEnum.ERROR,
      });
    }
  }

  private async processManualScheduledPix(transaction: TransactionEntity) {
    try {
      const { body: result } = await this.celcoinService.transactionPixKey({
        amount: Number(transaction.value),
        initiationType: 'MANUAL',
        paymentType: 'IMMEDIATE',
        remittanceInformation: transaction.description,
        transactionType: 'TRANSFER',
        urgency: 'HIGH',
        clientCode: transaction.code,
        creditParty: {
          account: transaction.destinyAccount,
          accountType: transaction.destinyAccountType,
          bank: transaction.destinyBank,
          branch: transaction.destinyBranch,
          name: transaction.destinyName,
          taxId: transaction.destinyDocument,
        },
        debitParty: {
          account: transaction.account.number,
        },
      });

      const transferMetadata = {
        creditParty: result.creditParty,
        debitParty: result.debitParty,
      };

      await this.transactionRepository.update(transaction.id, {
        ...transaction,
        transferMetadata: JSON.stringify(transferMetadata),
        status: TransactionStatusEnum.PENDENT,
      });
    } catch (error) {
      logger.error(
        `PixService.processManualScheduledPix() -> Error ao tentar realizar Pix manual. [ transactionId: ${transaction.id} ]`,
        error,
      );

      await this.transactionRepository.update(transaction.id, {
        ...transaction,
        status: TransactionStatusEnum.ERROR,
      });
    }
  }
}
