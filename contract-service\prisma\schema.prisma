generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model account {
  id                      String                    @id(map: "PK_54115ee388cdb6d86bb4bf5b2ea") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_id                String?                   @db.Uuid
  business_id             String?                   @db.Uuid
  number                  String?                   @db.VarChar
  branch                  String?                   @db.VarChar
  status                  String                    @db.VarChar
  type                    String                    @db.VarChar
  updated_at              DateTime                  @default(now()) @db.Timestamp(6)
  created_at              DateTime                  @default(now()) @db.Timestamp(6)
  deleted_at              DateTime?                 @db.Timestamp(6)
  external_id             String                    @db.VarChar
  profile_image           String?                   @db.VarChar
  transaction_password    String?                   @db.VarChar
  bank                    String?                   @db.VarChar
  is_external             Boolean                   @default(false)
  is_taxable              Boolean                   @default(true)
  two_factor_key          String?                   @db.VarChar
  have_2fa                Boolean                   @default(false)
  business                business?                 @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_1c4f6487028b13c8c6b7b17ee83")
  owner                   owner?                    @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_7e86daab9d155ec4cc3fd654454")
  account_transfer_limits account_transfer_limits[]
  billet                  billet[]
  document                document[]
  favorite_pix            favorite_pix[]
  favorite_ted            favorite_ted[]
  pix_key                 pix_key[]
  recharge                recharge[]
  service_fee             service_fee[]
  transaction             transaction[]
}

model account_transfer_limits {
  id                     String    @id(map: "PK_f9c66b7635fc8816ab0573356bc") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  account_id             String?   @db.Uuid
  updated_at             DateTime  @default(now()) @db.Timestamp(6)
  created_at             DateTime  @default(now()) @db.Timestamp(6)
  deleted_at             DateTime? @db.Timestamp(6)
  daily_limit            Decimal   @default(1000) @db.Decimal
  monthly_limit          Decimal   @default(1000) @db.Decimal
  daily_night_limit      Decimal   @default(1000) @db.Decimal
  general_transfer_limit Decimal?  @default(50000) @db.Decimal
  default_values         String?   @db.VarChar
  active                 Boolean   @default(false)
  account                account?  @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_51fc575749fb624f8debaab8c5c")

  @@map("account_transfer-limits")
}

model addendum {
  id_addendum                       Int                                 @id(map: "PK_831c7c3e13213217a199e22fbc1") @default(autoincrement())
  status                            addendum_status_enum                @default(DRAFT)
  application_date                  DateTime                            @db.Date
  addendum_value                    Decimal                             @db.Decimal(15, 2)
  reason                            String?
  notes                             String?
  created_at                        DateTime                            @default(now()) @db.Timestamp(6)
  updated_at                        DateTime                            @default(now()) @db.Timestamp(6)
  contract_id                       String                              @db.Uuid
  external_id                       String?                             @db.VarChar
  expires_in                        DateTime                            @default(now()) @db.Date
  yield_rate                        Decimal                             @db.Decimal(5, 2)
  contract                          contract                            @relation(fields: [contract_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_5e577f2ba8c17a9b5420dc8dbc0")
  addendum_files                    addendum_files[]
  income_payment_scheduled_addendum income_payment_scheduled_addendum[]
  notification                      notification[]
}

model addendum_files {
  id         String    @id(map: "PK_ad6c646753270b6d29913531a00") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  type       String    @db.VarChar(50)
  createdAt  DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt  DateTime  @default(now()) @db.Timestamptz(6)
  addendumId Int?
  fileId     String?   @db.Uuid
  addendum   addendum? @relation(fields: [addendumId], references: [id_addendum], onDelete: Cascade, onUpdate: NoAction, map: "FK_2629a7c65ba6d270c2bc2e8b8f2")
  files      files?    @relation(fields: [fileId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_3f6aab9a35432b7e79f6e6f554f")
}

model address {
  id           String    @id(map: "PK_d92de1f82754668b5f5f5dd4fd5") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_id     String?   @db.Uuid
  business_id  String?   @db.Uuid
  cep          String    @db.VarChar
  street       String    @db.VarChar
  neighborhood String    @db.VarChar
  number       String    @db.VarChar
  city         String    @db.VarChar
  state        String    @db.VarChar
  complement   String?   @db.VarChar
  updated_at   DateTime  @default(now()) @db.Timestamp(6)
  created_at   DateTime  @default(now()) @db.Timestamp(6)
  deleted_at   DateTime? @db.Timestamp(6)
  business     business? @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_ac5eea1bbea2cce5156e8f67c27")
  owner        owner?    @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_d507cd237dff1fc31f67551c2a1")
}

model admin {
  id            String      @id(map: "PK_e032310bcef831fb83101899b10") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  superadmin_id String?     @db.Uuid
  owner_id      String?     @db.Uuid
  business_id   String?     @db.Uuid
  business      business?   @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_08c71558c0fb298ca007dc58367")
  superadmin    superadmin? @relation(fields: [superadmin_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_758bd6ff951dc166552e14a9393")
  owner         owner?      @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_b8f113de6f23998118435fa3b98")
  broker        broker[]
}

model advisor {
  id          String     @id(map: "PK_41bd0367a08f2d8c34560a2785e") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  broker_id   String?    @db.Uuid
  owner_id    String?    @db.Uuid
  business_id String?    @db.Uuid
  broker      broker?    @relation(fields: [broker_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_158b4652b9a7c747359c9fe2f09")
  business    business?  @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_1b87021e9e988eb57b23557a779")
  owner       owner?     @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_498ad66ddc714e114a26f43533d")
  investor    investor[]
}

model advisor_goal {
  id                     String               @id(map: "PK_7ca80f8383ddb80051f4fff0132") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  date_from              DateTime             @db.Timestamp(6)
  date_to                DateTime             @db.Timestamp(6)
  targetAmount           Decimal              @db.Decimal(10, 2)
  perspective_amount     Decimal?             @db.Decimal(10, 2)
  status                 String               @db.VarChar
  observations           String               @db.VarChar
  name                   String               @db.VarChar
  createdAt              DateTime             @default(now()) @db.Timestamp(6)
  updatedAt              DateTime             @default(now()) @db.Timestamp(6)
  owner_role_relation_id String?              @db.Uuid
  broker_goal_id         String?              @db.Uuid
  broker_goal            broker_goal?         @relation(fields: [broker_goal_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_1dc1e0d28fa23237aaae2361fae")
  owner_role_relation    owner_role_relation? @relation(fields: [owner_role_relation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_885f599e8e2b179496db960c14d")
}

model app_version {
  id              String   @id(map: "PK_f2573b981a7eac664875e7483ac") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  android_version String   @default("0") @db.VarChar
  ios_version     String   @default("0") @db.VarChar
  created_at      DateTime @default(now()) @db.Timestamp(6)
  updated_at      DateTime @default(now()) @db.Timestamp(6)
  ios_url         String?  @db.VarChar
  android_url     String?  @db.VarChar
}

model billet {
  id              String    @id(map: "PK_9b069b774f7172ce2a6700ef4bb") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  account_id      String    @db.Uuid
  due_date        DateTime  @db.Timestamp(6)
  barcode         String    @db.VarChar
  updated_at      DateTime  @default(now()) @db.Timestamp(6)
  created_at      DateTime  @default(now()) @db.Timestamp(6)
  debtor_name     String    @db.VarChar
  debtor_document String    @db.VarChar
  deleted_at      DateTime? @db.Timestamp(6)
  status          String    @db.VarChar
  transaction_id  String?   @db.VarChar
  metadata        Json?
  amount          String    @db.VarChar
  account         account   @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_e67d9237513dfc3b7a02dd7ddb3")
}

model broker {
  id          String     @id(map: "PK_06617ad8cb3dc7339492a5c995d") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  admin_id    String?    @db.Uuid
  owner_id    String?    @db.Uuid
  business_id String?    @db.Uuid
  advisor     advisor[]
  business    business?  @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_4db78c5e0905ede9a579ed060bd")
  admin       admin?     @relation(fields: [admin_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_7d049f6cef07839db176c50774c")
  owner       owner?     @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_9b02767be2adfea38e3fdff47c9")
  investor    investor[]
}

model broker_goal {
  id                       String               @id(map: "PK_1c40d7ac97468846b4c23834f16") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  date_from                DateTime             @db.Timestamp(6)
  date_to                  DateTime             @db.Timestamp(6)
  target_amount            Decimal              @db.Decimal(10, 2)
  perspective_amount       Decimal?             @db.Decimal(10, 2)
  status                   String               @db.VarChar
  observations             String               @db.VarChar
  name                     String               @db.VarChar
  createdAt                DateTime             @default(now()) @db.Timestamp(6)
  updatedAt                DateTime             @default(now()) @db.Timestamp(6)
  owner_role_relation_id   String?              @db.Uuid
  adminOwnerRoleRelationId String?              @db.VarChar
  advisor_goal             advisor_goal[]
  owner_role_relation      owner_role_relation? @relation(fields: [owner_role_relation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_e91985024a98030c3a58c067972")
}

model business {
  id                      String                    @id(map: "PK_0bd850da8dafab992e2e9b058e5") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  company_name            String                    @db.VarChar
  fantasy_name            String                    @db.VarChar
  cnpj                    String                    @unique(map: "UQ_d871b59fcb850c9870d9d69c472") @db.VarChar
  type                    String                    @db.VarChar
  size                    String                    @db.VarChar
  email                   String                    @db.VarChar
  dt_opening              DateTime                  @db.Date
  updated_at              DateTime                  @default(now()) @db.Timestamp(6)
  created_at              DateTime                  @default(now()) @db.Timestamp(6)
  deleted_at              DateTime?                 @db.Timestamp(6)
  password                String                    @db.VarChar
  temporary_password      Boolean                   @default(true)
  avatar                  String?                   @db.VarChar
  refresh_token           String?                   @db.VarChar
  total_invest            Decimal?                  @db.Decimal
  yield                   Decimal?                  @db.Decimal
  start_contract          DateTime?                 @db.Date
  end_contract            DateTime?                 @db.Date
  tax_rate                Decimal?                  @db.Decimal
  owner_id                String?                   @db.Uuid
  account                 account[]
  address                 address[]
  admin                   admin[]
  advisor                 advisor[]
  broker                  broker[]
  owner                   owner?                    @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_da374bf24a4b84a4aa36dfd3e76")
  owner_business_relation owner_business_relation[]
  owner_role_relation     owner_role_relation[]
  permission              permission[]
  superadmin              superadmin[]
  uploads                 uploads[]
}

model city {
  id        String   @id(map: "PK_b222f51ce26f7e5ca86944a6739") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name      String   @db.VarChar
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now()) @db.Timestamp(6)
  updatedAt DateTime @default(now()) @db.Timestamp(6)
  state_id  String?  @db.Uuid
  state     state?   @relation(fields: [state_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_37ecd8addf395545dcb0242a593")
}

model contract {
  id                                                                    String                     @id(map: "PK_17c3a89f58a2997276084e706e8") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  external_id                                                           Int?
  createdAt                                                             DateTime                   @default(now()) @db.Timestamp(6)
  updatedAt                                                             DateTime                   @default(now()) @db.Timestamp(6)
  owner_role_relation                                                   String?                    @db.Uuid
  investor_id                                                           String?                    @db.Uuid
  start_contract                                                        DateTime                   @db.Date
  end_contract                                                          DateTime                   @db.Date
  contract_pdf                                                          String?                    @db.VarChar
  proof_payment                                                         String?                    @db.VarChar
  status                                                                String?                    @default("aberto") @db.VarChar
  old_contract_pdf                                                      String?                    @db.VarChar
  sign_investor                                                         String?                    @db.VarChar
  sign_ica                                                              String?                    @db.VarChar
  type                                                                  String                     @default("p2p") @db.VarChar
  broker_participation_percentage                                       Decimal?                   @db.Decimal(5, 2)
  advisor_participation_percentage                                      Decimal?                   @db.Decimal(5, 2)
  is_debenture                                                          Boolean                    @default(false)
  contract_number                                                       Int                        @default(1)
  duration_in_months                                                    Int?
  addendum                                                              addendum[]
  owner_role_relation_contract_investor_idToowner_role_relation         owner_role_relation?       @relation("contract_investor_idToowner_role_relation", fields: [investor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_2a761745cdcd4c816a99d2243bc")
  owner_role_relation_contract_owner_role_relationToowner_role_relation owner_role_relation?       @relation("contract_owner_role_relationToowner_role_relation", fields: [owner_role_relation], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_f405dde729bbe574001e86ff68a")
  contract_advisor                                                      contract_advisor[]
  contract_audit                                                        contract_audit[]
  contract_deletion                                                     contract_deletion[]
  contract_event                                                        contract_event[]
  income_payment_scheduled                                              income_payment_scheduled[]
  income_reports_contracts                                              income_reports_contracts[]
  notification                                                          notification[]
  pre_register                                                          pre_register[]
}

model contract_advisor {
  id                  String              @id(map: "PK_76d4fa3107ee65be1406e768a30") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  rate                Decimal             @db.Decimal(5, 2)
  is_active           Boolean             @default(true)
  created_at          DateTime            @default(now()) @db.Timestamp(6)
  updated_at          DateTime            @default(now()) @db.Timestamp(6)
  id_contract         String              @db.Uuid
  id_advisor          String              @db.Uuid
  contract            contract            @relation(fields: [id_contract], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_b3eb1d7d01d429b1fc2ae37c05b")
  owner_role_relation owner_role_relation @relation(fields: [id_advisor], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_cba07ac9ce330bc89efb1e82786")

  @@unique([id_contract, id_advisor], map: "UQ_57ddfe73a0f966bba89f031cc9d")
}

model contract_event {
  id                        String                      @id(map: "PK_a0a0fdb2918e838e546c3b5fd01") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  eventType                 String                      @db.VarChar(50)
  eventDate                 DateTime                    @db.Date
  status                    String?                     @db.VarChar(50)
  comment                   String?
  destinationArea           String?                     @db.VarChar(50)
  createdAt                 DateTime                    @default(now()) @db.Timestamp(6)
  updatedAt                 DateTime                    @default(now()) @db.Timestamp(6)
  contract_id               String?                     @db.Uuid
  responsible_id            String                      @db.Uuid
  owner_role_relation       owner_role_relation         @relation(fields: [responsible_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_196c2c26c5048d8f6760bbc281e")
  contract                  contract?                   @relation(fields: [contract_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_654c5b3eae51e91ad465ad7daa0")
  contract_event_attachment contract_event_attachment[]
}

model contract_event_attachment {
  id                String         @id(map: "PK_23b9222cd3d55cf4f3688b8baa1") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  file_url          String
  file_name         String         @db.VarChar(255)
  contract_event_id String         @db.Uuid
  contract_event    contract_event @relation(fields: [contract_event_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_fa9466d0f494941cf4bc83d3cc1")
}

model device_entity {
  id         String    @id(map: "PK_a75e1d635b3b07412a2ab3eb000") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_id   String?   @db.Uuid
  name       String    @db.VarChar
  model      String    @db.VarChar
  code       String    @db.VarChar
  token      String    @db.VarChar
  latitude   String    @db.VarChar
  longitude  String    @db.VarChar
  updated_at DateTime  @default(now()) @db.Timestamp(6)
  created_at DateTime  @default(now()) @db.Timestamp(6)
  deleted_at DateTime? @db.Timestamp(6)
  owner      owner?    @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_8ecb72968cbdf87be784fe6cf1d")
}

model document {
  id              String   @id(map: "PK_e57d3357f83f3cdc0acffc3d777") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  account_id      String?  @db.Uuid
  front           String   @db.VarChar
  back            String   @db.VarChar
  card_cnpj       String?  @db.VarChar
  updated_at      DateTime @default(now()) @db.Timestamp(6)
  created_at      DateTime @default(now()) @db.Timestamp(6)
  type            String   @default("CNH") @db.VarChar
  sent            Boolean  @default(false)
  social_contract String?  @db.VarChar
  account         account? @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_a05e62b6b25192e8ff52fd197ed")
}

model email {
  id                  String                @id(map: "PK_1e7ed8734ee054ef18002e29b1c") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  from                String                @db.VarChar
  reply               String?               @db.VarChar
  body                String?               @db.VarChar
  body_type           String?               @db.VarChar
  status              String?               @db.VarChar
  created_at          DateTime              @default(now()) @db.Timestamp(6)
  sent_at             DateTime?             @db.Timestamp(6)
  failed_at           DateTime?             @db.Timestamp(6)
  error_message       String?               @db.VarChar
  external_id         String?               @db.VarChar
  income_report_email income_report_email[]
}

model favorite_pix {
  id               String    @id(map: "PK_8036547b3ab2e4af317048ff597") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  account_id       String?   @db.Uuid
  account_document String    @db.VarChar
  name             String    @db.VarChar
  account_number   String    @db.VarChar
  account_branch   String    @db.VarChar
  account_type     String    @db.VarChar
  account_bank     String    @db.VarChar
  alias            String?   @db.VarChar
  updated_at       DateTime  @default(now()) @db.Timestamp(6)
  created_at       DateTime  @default(now()) @db.Timestamp(6)
  deleted_at       DateTime? @db.Timestamp(6)
  account          account?  @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_9986c36d5cfcf84bb0f072b62ea")
}

model favorite_ted {
  id             String    @id(map: "PK_61a15d3413bde23004a5644e57c") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  document       String?   @db.VarChar
  account_number String?   @db.VarChar
  account_branch String?   @db.VarChar
  account_bank   String?   @db.VarChar
  name           String?   @db.VarChar
  alias          String?   @db.VarChar
  account_id     String?   @db.Uuid
  updated_at     DateTime  @default(now()) @db.Timestamp(6)
  created_at     DateTime  @default(now()) @db.Timestamp(6)
  deleted_at     DateTime? @db.Timestamp(6)
  account        account?  @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_a9320fa074e03dc370b5cd2d773")
}

model files {
  id             String           @id(map: "PK_6c16b9093a142e0e7613b04a3d9") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  filename       String           @db.VarChar(255)
  url            String           @db.VarChar(500)
  type           String           @db.VarChar(50)
  createdAt      DateTime         @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime         @default(now()) @db.Timestamptz(6)
  addendum_files addendum_files[]
  income_payment income_payment[]
  income_report  income_report[]
  report         report[]
}

model income {
  id                                                                  String               @id(map: "PK_29a10f17b97568f70cee8586d58") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  income_day                                                          Decimal?             @db.Decimal
  payment_date                                                        DateTime?            @db.Timestamp(6)
  amount                                                              Int?
  percentage                                                          Decimal?             @db.Decimal
  number_contracts                                                    Int?
  number_clients                                                      Int?
  contract_type                                                       String?              @db.VarChar
  role                                                                String?              @db.VarChar
  createdAt                                                           DateTime             @default(now()) @db.Timestamp(6)
  updatedAt                                                           DateTime             @default(now()) @db.Timestamp(6)
  deleted_at                                                          DateTime?            @db.Timestamp(6)
  owner_role_relation                                                 String?              @db.Uuid
  owner_role_relation_income_owner_role_relationToowner_role_relation owner_role_relation? @relation("income_owner_role_relationToowner_role_relation", fields: [owner_role_relation], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_2542605a11a32fcc24bd4c14a58")
}

model income_payment {
  id                      String               @id(map: "PK_64e0650628dd1a2ae42ca28b0f7") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  payment_distribution_id String               @unique(map: "REL_69fd3a33405e78e32acfb3c4d0") @db.Uuid
  paid_at                 DateTime             @db.Date
  paid_amount             Decimal              @db.Decimal(12, 2)
  payment_method          String?              @db.VarChar
  transaction_reference   String?              @db.VarChar
  info                    String?              @db.VarChar
  createdAt               DateTime             @default(now()) @db.Timestamp(6)
  updatedAt               DateTime             @default(now()) @db.Timestamp(6)
  deleted_at              DateTime?            @db.Timestamp(6)
  file_id                 String?              @db.Uuid
  files                   files?               @relation(fields: [file_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_57052a1821ced5041c5b53bb578")
  payment_distribution    payment_distribution @relation(fields: [payment_distribution_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_69fd3a33405e78e32acfb3c4d0e")
}

model income_payment_scheduled {
  id                                String                              @id(map: "PK_0af6ec8d4cb3402f0b044e887cb") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  scheduled_date                    DateTime                            @db.Date
  amount                            Decimal                             @db.Decimal(12, 2)
  createdAt                         DateTime                            @default(now()) @db.Timestamp(6)
  updatedAt                         DateTime                            @default(now()) @db.Timestamp(6)
  deleted_at                        DateTime?                           @db.Timestamp(6)
  contract_id                       String?                             @db.Uuid
  contract                          contract?                           @relation(fields: [contract_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_7f6668dd01215fa2445bf8f240a")
  income_payment_scheduled_addendum income_payment_scheduled_addendum[]
  payment_distribution              payment_distribution[]
}

model income_payment_scheduled_addendum {
  id                          String                    @id(map: "PK_f1b9d433fc301d7d5672a6fc18f") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  proportional_days           Int
  income_payment_scheduled_id String?                   @db.Uuid
  addendum_id                 Int?
  addendum                    addendum?                 @relation(fields: [addendum_id], references: [id_addendum], onDelete: NoAction, onUpdate: NoAction, map: "FK_8fc4b7fc74077dd64629a3524b7")
  income_payment_scheduled    income_payment_scheduled? @relation(fields: [income_payment_scheduled_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_9a8c92a6289fd28b96c5ca54163")
}

model income_report {
  id                       String                     @id(map: "PK_e06bd728f480f95876d4add8d23") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  status                   String                     @db.VarChar
  reference_year           String                     @db.VarChar
  createdAt                DateTime                   @default(now()) @db.Timestamp(6)
  updatedAt                DateTime                   @default(now()) @db.Timestamp(6)
  deleted_at               DateTime?                  @db.Timestamp(6)
  file_id                  String?                    @db.Uuid
  investor_id              String?                    @db.Uuid
  files                    files?                     @relation(fields: [file_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_2def7d6bd0ca360289f2ffd89a7")
  owner_role_relation      owner_role_relation?       @relation(fields: [investor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_5b2a1a2fdd0ff2d8c8df13a0bb3")
  income_report_email      income_report_email[]
  income_reports_contracts income_reports_contracts[]
  income_reports_logs      income_reports_logs[]
}

model income_report_email {
  id               String         @id(map: "PK_4a05ae86e57e4ead80037cb48c7") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  email_id         String?        @db.Uuid
  income_report_id String?        @db.Uuid
  email            email?         @relation(fields: [email_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_59d42906dfe4004cc588a4bae1e")
  income_report    income_report? @relation(fields: [income_report_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_85c9e5c87f3c8c2b85eb6d7c433")
}

model income_reports_contracts {
  id               String        @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  contract_id      String        @db.Uuid
  income_report_id String        @db.Uuid
  contract         contract      @relation(fields: [contract_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_05a011630e3d27a8f2f6a2b51c3")
  income_report    income_report @relation(fields: [income_report_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_b2190dc255e9b15a0b92579b35b")

  @@id([id, contract_id, income_report_id], map: "PK_12f4bf964f8813142cea847a92d")
}

model income_reports_logs {
  id               String        @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  logs_id          String        @db.Uuid
  income_report_id String        @db.Uuid
  income_report    income_report @relation(fields: [income_report_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_2ea87012b66dc68db6338ddd98d")
  logs             logs          @relation(fields: [logs_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "FK_380c98d3ec2d6857734b49f0e42")

  @@id([id, logs_id, income_report_id], map: "PK_a7127431770f703634d09919495")
}

model investor {
  id         String   @id(map: "PK_c60a173349549955c39d3703551") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  broker_id  String?  @db.Uuid
  owner_id   String?  @db.Uuid
  advisor_id String?  @db.Uuid
  owner      owner?   @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_2f542bded5a8b34cc53d24d1c5c")
  advisor    advisor? @relation(fields: [advisor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_4a98ce7d73b154d63f0aac4889e")
  broker     broker?  @relation(fields: [broker_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_5aafdad076d9c769718a1a049a2")
}

model logs {
  id                  String                @id(map: "PK_fb1b805f2f7795de79fa69340ba") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  stack_trace         String?
  message             String?
  created_at          DateTime              @default(now()) @db.Timestamp(6)
  income_reports_logs income_reports_logs[]
}

model migrations {
  id        Int    @id(map: "PK_8c82d7f526340ab734260ea46be") @default(autoincrement())
  timestamp BigInt
  name      String @db.VarChar
}

model movement {
  operation_type      String               @db.VarChar
  amount              Decimal              @db.Decimal
  description         String
  updated_at          DateTime             @default(now()) @db.Timestamp(6)
  created_at          DateTime             @default(now()) @db.Timestamp(6)
  deleted_at          DateTime?            @db.Timestamp(6)
  investor_id         String?              @db.Uuid
  id                  String               @id(map: "PK_079f005d01ebda984e75c2d67ee") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_role_relation owner_role_relation? @relation(fields: [investor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_bd105c1eda01fd5a1bace33beed")
}

model notification {
  id                                                                                 String               @id(map: "PK_705b6c7cdf9b2c2ff7ac7872cb7") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  title                                                                              String               @db.VarChar
  viewed                                                                             Boolean              @default(false)
  createdAt                                                                          DateTime             @default(now()) @db.Timestamp(6)
  updatedAt                                                                          DateTime             @default(now()) @db.Timestamp(6)
  deleted_at                                                                         DateTime?            @db.Timestamp(6)
  type                                                                               String               @db.VarChar
  description                                                                        String               @db.VarChar
  admin_owner_role_relation_id                                                       String?              @db.Uuid
  contract_id                                                                        String               @db.Uuid
  contract_value                                                                     Decimal?             @db.Decimal
  addendum_id                                                                        Int?
  investor_id                                                                        String?              @db.Uuid
  broker_id                                                                          String?              @db.Uuid
  owner_role_relation_notification_admin_owner_role_relation_idToowner_role_relation owner_role_relation? @relation("notification_admin_owner_role_relation_idToowner_role_relation", fields: [admin_owner_role_relation_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_1786280b5e015f1c1a408b970b7")
  addendum                                                                           addendum?            @relation(fields: [addendum_id], references: [id_addendum], onDelete: NoAction, onUpdate: NoAction, map: "FK_3eeec94e4bf39770577b3b20ca8")
  owner_role_relation_notification_investor_idToowner_role_relation                  owner_role_relation? @relation("notification_investor_idToowner_role_relation", fields: [investor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_91d75bba992225527b598ea2bf7")
  owner_role_relation_notification_broker_idToowner_role_relation                    owner_role_relation? @relation("notification_broker_idToowner_role_relation", fields: [broker_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_e3ef26f3cdca8b42a45bb8f25ab")
  contract                                                                           contract             @relation(fields: [contract_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_f9cd0107e86bcd1d22a4ed7125b")
}

model otp {
  id         String    @id(map: "PK_32556d9d7b22031d7d0e1fd6723") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  code       String    @db.VarChar
  expires_at DateTime  @db.Timestamp(6)
  updated_at DateTime  @default(now()) @db.Timestamp(6)
  created_at DateTime  @default(now()) @db.Timestamp(6)
  deleted_at DateTime? @db.Timestamp(6)
  owner_id   String?   @db.Uuid
  owner      owner?    @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_32edb1f3da9f9b7cc25fd91d56f")
}

model owner {
  id                      String                    @id(map: "PK_8e86b6b9f94aece7d12d465dc0c") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name                    String                    @db.VarChar
  cpf                     String                    @unique(map: "UQ_4ba355186545479193a3ed22ba0") @db.VarChar
  email                   String                    @db.VarChar
  phone                   String                    @db.VarChar
  mother_name             String                    @db.VarChar
  dt_birth                DateTime                  @db.Date
  pep                     String                    @db.VarChar
  nickname                String?                   @db.VarChar
  avatar                  String?                   @db.VarChar
  updated_at              DateTime                  @default(now()) @db.Timestamp(6)
  created_at              DateTime                  @default(now()) @db.Timestamp(6)
  deleted_at              DateTime?                 @db.Timestamp(6)
  password                String?                   @db.VarChar
  temporary_password      Boolean                   @default(true)
  refresh_token           String?                   @db.VarChar
  total_invest            Decimal?                  @db.Decimal
  yield                   Decimal?                  @db.Decimal
  start_contract          DateTime?                 @db.Date
  end_contract            DateTime?                 @db.Date
  tax_rate                Decimal?                  @db.Decimal
  rg                      String                    @default("0000000") @db.VarChar
  occupation              String                    @default("") @db.VarChar
  nationality             String                    @default("brasileira") @db.VarChar
  issuingAgency           String                    @default("SSP") @db.VarChar
  account                 account[]
  address                 address[]
  admin                   admin[]
  advisor                 advisor[]
  broker                  broker[]
  business                business[]
  device_entity           device_entity[]
  investor                investor[]
  otp                     otp[]
  owner_business_relation owner_business_relation[]
  owner_role_relation     owner_role_relation[]
  permission              permission[]
  superadmin              superadmin[]
  uploads                 uploads[]
}

model owner_business_relation {
  id          String    @id(map: "PK_13fd114daf4ffe5012f5eb826b5") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_id    String    @db.Uuid
  business_id String    @db.Uuid
  role        String    @db.VarChar
  created_at  DateTime  @default(now()) @db.Timestamp(6)
  deleted_at  DateTime? @db.Timestamp(6)
  owner       owner     @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_989f4ac312bb211adab3d23b804")
  business    business  @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_e1609daa891183934b6005b2875")
}

model owner_role_relation {
  id                                                                          String                 @id(map: "PK_e39ae65ffce1e6831360fd87c6d") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_id                                                                    String?                @db.Uuid
  role_id                                                                     String                 @db.Uuid
  created_at                                                                  DateTime               @default(now()) @db.Timestamp(6)
  deleted_at                                                                  DateTime?              @db.Timestamp(6)
  business_id                                                                 String?                @db.Uuid
  part_percent                                                                Decimal?               @db.Decimal(5, 4)
  advisor_goal                                                                advisor_goal[]
  broker_goal                                                                 broker_goal[]
  contract_contract_investor_idToowner_role_relation                          contract[]             @relation("contract_investor_idToowner_role_relation")
  contract_contract_owner_role_relationToowner_role_relation                  contract[]             @relation("contract_owner_role_relationToowner_role_relation")
  contract_advisor                                                            contract_advisor[]
  contract_audit                                                              contract_audit[]
  contract_deletion                                                           contract_deletion[]
  contract_event                                                              contract_event[]
  income_income_owner_role_relationToowner_role_relation                      income[]               @relation("income_owner_role_relationToowner_role_relation")
  income_report                                                               income_report[]
  movement                                                                    movement[]
  notification_notification_admin_owner_role_relation_idToowner_role_relation notification[]         @relation("notification_admin_owner_role_relation_idToowner_role_relation")
  notification_notification_investor_idToowner_role_relation                  notification[]         @relation("notification_investor_idToowner_role_relation")
  notification_notification_broker_idToowner_role_relation                    notification[]         @relation("notification_broker_idToowner_role_relation")
  role                                                                        role                   @relation(fields: [role_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_235ec492b335d8e8883f4997a70")
  business                                                                    business?              @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_469c060f976a087f2668fec5d04")
  owner                                                                       owner?                 @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_622c8e2dabdd04a18f9bc656570")
  payment_distribution                                                        payment_distribution[]
  permission                                                                  permission[]
  pre_register                                                                pre_register[]
  report_report_owner_role_relationToowner_role_relation                      report[]               @relation("report_owner_role_relationToowner_role_relation")
  wallets_views_wallets_views_bottom_idToowner_role_relation                  wallets_views[]        @relation("wallets_views_bottom_idToowner_role_relation")
  wallets_views_wallets_views_upper_idToowner_role_relation                   wallets_views[]        @relation("wallets_views_upper_idToowner_role_relation")
}

model payment_distribution {
  id                       String                   @id(map: "PK_d9a717d5c89f5f8cda298ffc6cf") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  planned_amount           Decimal                  @db.Decimal(12, 2)
  created_at               DateTime                 @default(now()) @db.Timestamp(6)
  scheduled_payment_id     String                   @db.Uuid
  participant_id           String                   @db.Uuid
  income_payment           income_payment?
  owner_role_relation      owner_role_relation      @relation(fields: [participant_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_2f69b8fe5016246b895ac497ea3")
  income_payment_scheduled income_payment_scheduled @relation(fields: [scheduled_payment_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_678ded15de538df35bff86369dd")
}

model permission {
  id                  String              @id(map: "PK_3b8b97af9d9d8807e41e6f48362") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_id            String?             @db.Uuid
  business_id         String?             @db.Uuid
  owner_role_id       String              @db.Uuid
  created_at          DateTime            @default(now()) @db.Timestamp(6)
  deleted_at          DateTime?           @db.Timestamp(6)
  role_id             String?             @db.Uuid
  role                role?               @relation(fields: [role_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_383892d758d08d346f837d3d8b7")
  owner               owner?              @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_3c2f72152c8b1d79c0879c20506")
  business            business?           @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_80c78d5396e006f3a2ae7800c12")
  owner_role_relation owner_role_relation @relation(fields: [owner_role_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_f697f57ebfc670aa2f6d82ded7c")
}

model pix_key {
  id             String    @id(map: "PK_97b3a249aca9b0fd61ae0d877ee") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  account_id     String    @db.Uuid
  type_key       String    @db.VarChar
  key            String    @db.VarChar
  updated_at     DateTime  @default(now()) @db.Timestamp(6)
  created_at     DateTime  @default(now()) @db.Timestamp(6)
  deleted_at     DateTime? @db.Timestamp(6)
  claim_metadata String?   @db.VarChar
  status         String    @default("PENDENT") @db.VarChar
  claim_status   String?   @db.VarChar
  account        account   @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_28c2c42359e273bc3552c8879b5")

  @@map("pix-key")
}

model pix_qrcode {
  id             String    @id(map: "PK_4088b442d7790b5b98f986a7076") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  account_id     String    @db.VarChar
  external_id    String    @db.VarChar
  transaction_id String    @db.VarChar
  type           String    @db.VarChar
  emv            String    @db.VarChar
  updated_at     DateTime  @default(now()) @db.Timestamp(6)
  created_at     DateTime  @default(now()) @db.Timestamp(6)
  deleted_at     DateTime? @db.Timestamp(6)
}

model pre_register {
  id                  String               @id(map: "PK_da786d4c6f01e1cbb0be67607a6") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name                String               @db.VarChar
  rg                  String?              @db.VarChar
  document            String               @db.VarChar
  phone_number        String               @db.VarChar
  dt_birth            DateTime             @db.Date
  email               String               @db.VarChar
  zip_code            String               @db.VarChar
  neighborhood        String               @db.VarChar
  city                String               @db.VarChar
  address_complement  String?              @db.VarChar
  address_number      String               @db.VarChar
  investment_value    Decimal              @db.Decimal
  investment_term     String?              @db.VarChar
  investment_yield    Decimal              @db.Decimal
  investment_modality String?              @db.VarChar
  observations        String?              @db.VarChar
  status              String               @db.VarChar
  adviser_id          String?              @db.Uuid
  purchase_with       String?              @db.VarChar
  amount_quotes       Int?
  grace_period        DateTime             @db.Date
  created_at          DateTime             @default(now()) @db.Timestamp(6)
  contract_id         String?              @db.Uuid
  bank                String?              @db.VarChar
  agency              String?              @db.VarChar
  account             String?              @db.VarChar
  pix                 String?              @db.VarChar
  state               String?              @db.VarChar
  mother_name         String?              @db.VarChar
  contract            contract?            @relation(fields: [contract_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_2a142caa100d41db710a3f56734")
  owner_role_relation owner_role_relation? @relation(fields: [adviser_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_db44bd7010cefe5ae4a9bb5506f")
}

model recharge {
  id          String   @id(map: "PK_5cc2dad79b630f05df9d7c0df82") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  account_id  String   @db.Uuid
  external_id String   @db.VarChar
  value       String   @db.VarChar
  status      String   @db.VarChar
  created_at  DateTime @default(now()) @db.Timestamp(6)
  account     account  @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_73c4afda905a8765218a1ffa1c6")
}

model report {
  id                                                                  String              @id(map: "PK_99e4d0bea58cba73c57f935a546") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  type                                                                String              @db.VarChar
  createdAt                                                           DateTime            @default(now()) @db.Timestamp(6)
  updatedAt                                                           DateTime            @default(now()) @db.Timestamp(6)
  deleted_at                                                          DateTime?           @db.Timestamp(6)
  owner_role_relation                                                 String              @db.Uuid
  file_id                                                             String              @db.Uuid
  files                                                               files               @relation(fields: [file_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_1ee07d7eee68a3ea0a9a73e1454")
  owner_role_relation_report_owner_role_relationToowner_role_relation owner_role_relation @relation("report_owner_role_relationToowner_role_relation", fields: [owner_role_relation], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_266246ede9f575c5ef3c1c079ec")
}

model role {
  id                  String                @id(map: "PK_b36bcfe02fc8de3c57a8b2391c2") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name                String                @db.VarChar
  owner_role_relation owner_role_relation[]
  permission          permission[]
}

model service_fee {
  id                      String    @id(map: "PK_70d082964aaa4bf193c2b1276e9") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  external_id             String?   @db.VarChar
  monthly_fee             Decimal   @db.Decimal
  fee_accumulation_period Decimal   @db.Decimal
  last_payment_date       DateTime? @db.Date
  payment_status          String    @db.VarChar(50)
  debt_amount             Decimal?  @db.Decimal
  created_at              DateTime  @default(now()) @db.Timestamp(6)
  updated_at              DateTime  @default(now()) @db.Timestamp(6)
  account_id              String?   @db.Uuid
  account                 account?  @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_b1553b09af9ce6028995889dffe")
}

model state {
  id           String   @id(map: "PK_549ffd046ebab1336c3a8030a12") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  name         String   @db.VarChar
  abbreviation String   @db.VarChar
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now()) @db.Timestamp(6)
  updatedAt    DateTime @default(now()) @db.Timestamp(6)
  city         city[]
}

model statistic {
  id            String    @id(map: "PK_e3e6fd496e1988019d8a46749ae") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  investors     Int
  total_applied Decimal   @db.Decimal
  updated_at    DateTime  @default(now()) @db.Timestamp(6)
  created_at    DateTime  @default(now()) @db.Timestamp(6)
  deleted_at    DateTime? @db.Timestamp(6)
  states_data   Json
}

model superadmin {
  id          String    @id(map: "PK_34da9117b572e9b32a8d829ae84") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  owner_id    String?   @db.Uuid
  business_id String?   @db.Uuid
  admin       admin[]
  owner       owner?    @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_a8f475d250f5bb4ab10977e4ea2")
  business    business? @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_de7c99eb422a8e5e506ed99460e")
}

model transaction {
  id                   String    @id(map: "PK_89eadb93a89810556e1cbcd6ab9") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  value                String    @db.VarChar
  account_id           String    @db.Uuid
  code                 String    @db.VarChar
  type                 String    @db.VarChar
  description          String?   @db.VarChar
  destiny_account      String?   @db.VarChar
  destiny_branch       String?   @db.VarChar
  destiny_bank         String?   @db.VarChar
  destiny_document     String?   @db.VarChar
  destiny_name         String?   @db.VarChar
  updated_at           DateTime  @default(now()) @db.Timestamp(6)
  created_at           DateTime  @default(now()) @db.Timestamp(6)
  deleted_at           DateTime? @db.Timestamp(6)
  end_to_end_id        String?   @db.VarChar
  tranferMetada        String?   @db.VarChar
  destiny_account_type String?   @db.VarChar
  transfer_date        DateTime? @db.Timestamp(6)
  destiny_key          String?   @db.VarChar
  status               String    @default("PENDENT") @db.VarChar
  account              account   @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_e2652fa8c16723c83a00fb9b17e")
}

model uploads {
  id          String    @id(map: "PK_d1781d1eedd7459314f60f39bd3") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  type        String    @db.VarChar
  url         String    @db.VarChar
  owner_id    String?   @db.Uuid
  business_id String?   @db.Uuid
  updated_at  DateTime  @default(now()) @db.Timestamp(6)
  created_at  DateTime  @default(now()) @db.Timestamp(6)
  owner       owner?    @relation(fields: [owner_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_4dfa98b9e12204ea0f0f712f30c")
  business    business? @relation(fields: [business_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_78d9afe9be70c9109d6824b4b58")
}

model wallets_views {
  id                                                               String              @id(map: "PK_e339cce0810748d2b6ffa38133d") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  upper_id                                                         String              @db.Uuid
  bottom_id                                                        String              @db.Uuid
  owner_role_relation_wallets_views_bottom_idToowner_role_relation owner_role_relation @relation("wallets_views_bottom_idToowner_role_relation", fields: [bottom_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_7cde3a0398069bbf43680cd8d12")
  owner_role_relation_wallets_views_upper_idToowner_role_relation  owner_role_relation @relation("wallets_views_upper_idToowner_role_relation", fields: [upper_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_834b2d2f9cd1d6ffaf3214968c6")

  @@map("wallets-views")
}

model contract_audit {
  id                  String                       @id(map: "PK_9d297ce3bd10f69e3da1eb0f67f") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  contract_id         String                       @db.Uuid
  auditor_id          String                       @db.Uuid
  decision            contract_audit_decision_enum
  comments            String?
  rejection_reasons   Json?
  created_at          DateTime                     @default(now()) @db.Timestamp(6)
  updated_at          DateTime                     @default(now()) @db.Timestamp(6)
  contract            contract                     @relation(fields: [contract_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_896afab3853dbb209938a2cd0a5")
  owner_role_relation owner_role_relation          @relation(fields: [auditor_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_a413e0b00469e3ac43ef85f6642")
}

model contract_deletion {
  id            String              @id(map: "PK_c2540b8ae09ef4084ad579c41b4") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  contract_id   String              @db.Uuid
  deleted_by_id String              @db.Uuid
  reason        String
  deleted_at    DateTime            @default(now()) @db.Timestamp(6)
  updated_at    DateTime            @default(now()) @db.Timestamp(6)
  contract      contract            @relation(fields: [contract_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_6709416e6166af82ffb1a094fc1")
  deleted_by    owner_role_relation @relation(fields: [deleted_by_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_f7a4a72bfa952689750c9db0399")
}

model logs_entity {
  id          String   @id(map: "PK_26b383487872c5230371ef07a95") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  stack_trace String?
  message     String?
  created_at  DateTime @default(now()) @db.Timestamp(6)
}

enum addendum_status_enum {
  DRAFT
  SENT_FOR_SIGNATURE
  PENDING_INVESTOR_SIGNATURE
  FULLY_SIGNED
  CANCELED
  EXPIRED
}

enum contract_audit_decision_enum {
  APPROVED
  REJECTED
}
