import { AccountTransferTypeEnum } from 'src/shared/enums/account-transfer-type.enum';
import { FinalityTedEnum } from 'src/shared/enums/finality-ted.enum';
import { PersonTypeTedEnum } from 'src/shared/enums/person-type-ted.enum';
declare class Account {
    number: string;
    type: AccountTransferTypeEnum;
    branch: string;
    bank: string;
}
declare class External {
    account: Account;
    document: string;
    name: string;
    person: PersonTypeTedEnum;
}
export declare class SendTedDto {
    accountId: string;
    amount: string;
    transactionPassword: string;
    finality: FinalityTedEnum;
    external: External;
    description: string;
}
export {};
