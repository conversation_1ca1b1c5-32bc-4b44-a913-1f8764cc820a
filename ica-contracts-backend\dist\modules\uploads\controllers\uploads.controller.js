"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const add_new_document_dto_1 = require("../dto/add-new-document.dto");
const get_documents_dto_1 = require("../dto/get-documents.dto");
const add_new_document_service_1 = require("../services/add-new-document.service");
const get_documents_service_1 = require("../services/get-documents.service");
let UploadsController = class UploadsController {
    constructor(addDocService, getDocService) {
        this.addDocService = addDocService;
        this.getDocService = getDocService;
    }
    async uploadDocuments(body, file) {
        try {
            const response = this.addDocService.perform(body, file);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response.data || error.message }, error.status || error.response.statusCode);
        }
    }
    async getDocuments(body) {
        try {
            const response = this.getDocService.perform(body);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response.data || error.message }, error.status || error.response.statusCode);
        }
    }
};
exports.UploadsController = UploadsController;
__decorate([
    (0, common_1.Post)('documents'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [add_new_document_dto_1.AddNewDocumentDto, Object]),
    __metadata("design:returntype", Promise)
], UploadsController.prototype, "uploadDocuments", null);
__decorate([
    (0, common_1.Get)('documents'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_documents_dto_1.GetDocumentsDto]),
    __metadata("design:returntype", Promise)
], UploadsController.prototype, "getDocuments", null);
exports.UploadsController = UploadsController = __decorate([
    (0, common_1.Controller)('uploads'),
    __param(0, (0, common_1.Inject)(add_new_document_service_1.AddNewDocumentService)),
    __param(1, (0, common_1.Inject)(get_documents_service_1.GetDocumentsService)),
    __metadata("design:paramtypes", [add_new_document_service_1.AddNewDocumentService,
        get_documents_service_1.GetDocumentsService])
], UploadsController);
//# sourceMappingURL=uploads.controller.js.map