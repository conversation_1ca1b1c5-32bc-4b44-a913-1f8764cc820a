{"version": 3, "file": "query.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-single-broker-with-active-contracts/query.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAkF;AAGlF,oFAAuE;AACvE,yDAAyC;AAEzC,MAAa,0CAA0C;IAAvD;QAcE,SAAI,GAAY,CAAC,CAAC;QAMlB,UAAK,GAAY,EAAE,CAAC;IAKtB,CAAC;CAAA;AAzBD,gGAyBC;AArBC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4EACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0EACK;AAMhB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;wEACU;AAMlB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;yEACY;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qCAAgB,CAAC;;gFACM", "sourcesContent": ["import { IsEnum, IsInt, IsOptional, IsPositive, IsString } from 'class-validator';\r\n\r\nimport { IDateRangeQuery, IPaginationQuery } from '../../helpers/pagination-query';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class GetSingleBrokerWithActiveContractsQueryDto\r\n  implements IDateRangeQuery, IPaginationQuery {\r\n  @IsOptional()\r\n  @IsString()\r\n  dateFrom?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  dateTo?: string;\r\n\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  page?: number = 1;\r\n\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  limit?: number = 10;\r\n\r\n  @IsOptional()\r\n  @IsEnum(ContractTypeEnum)\r\n  contractType: ContractTypeEnum;\r\n}\r\n"]}