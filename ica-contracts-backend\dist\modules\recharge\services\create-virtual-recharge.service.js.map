{"version": 3, "file": "create-virtual-recharge.service.js", "sourceRoot": "/", "sources": ["modules/recharge/services/create-virtual-recharge.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,mIAAkH;AAClH,6FAAoF;AACpF,qCAAqC;AAK9B,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IACvC,YACmB,yBAAoD,EAE7D,SAAoC;QAF3B,8BAAyB,GAAzB,yBAAyB,CAA2B;QAE7D,cAAS,GAAT,SAAS,CAA2B;IAC3C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,WAAqC;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;aACf;YACD,KAAK,EAAE;gBACL,EAAE,EAAE,WAAW,CAAC,SAAS;aAC1B;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,gBAAgB,GACpB,MAAM,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC;YACrD,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,IAAI,EAAE,WAAW,CAAC,IAAI;SACvB,CAAC,CAAC;QAEL,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF,CAAA;AA9BY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCADY,8DAAyB;QAElD,oBAAU;GAJpB,4BAA4B,CA8BxC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { CreateTransactionsService } from 'src/apis/icainvest-credit/services/create-virtual-transaction.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { CreateVirtualRechargeDto } from '../dto/create-virtual-recharge.dto';\r\n\r\n@Injectable()\r\nexport class CreateVirtualRechargeService {\r\n  constructor(\r\n    private readonly createTransactionsService: CreateTransactionsService,\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n  ) {}\r\n\r\n  async perform(transaction: CreateVirtualRechargeDto) {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        recharge: true,\r\n      },\r\n      where: {\r\n        id: transaction.accountId,\r\n      },\r\n    });\r\n\r\n    if (!account) {\r\n      throw new BadRequestException('Conta não encontrada para recarga.');\r\n    }\r\n\r\n    const externalResponse =\r\n      await this.createTransactionsService.createTransaction({\r\n        accountId: transaction.accountId,\r\n        amount: transaction.amount,\r\n        type: transaction.type,\r\n      });\r\n\r\n    return externalResponse;\r\n  }\r\n}\r\n"]}