import { AdminEntity } from 'src/shared/database/typeorm/entities/admin.entity';
import { Repository } from 'typeorm';
import { GetAdminSuperAdminDto } from '../dto/get-admin-super-admin-request.dto';
export declare class GetAdminSuperAdminService {
    private adminRepository;
    constructor(adminRepository: Repository<AdminEntity>);
    perform(input: GetAdminSuperAdminDto): Promise<{
        admin: {
            adminId: string;
            ownerId: string;
            broker: any[];
        };
    }>;
}
