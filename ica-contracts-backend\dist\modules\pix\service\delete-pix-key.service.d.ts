import { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';
import { Repository } from 'typeorm';
import { DeletePixKeyDto } from '../dto/delete-pix-key.dto';
export declare class DeletePixKeyService {
    private accountDb;
    private pixKeyDb;
    private celcoinService;
    constructor(accountDb: Repository<AccountEntity>, pixKeyDb: Repository<PixKeyEntity>, celcoinService: PixKeyCelcoinService);
    perform(data: DeletePixKeyDto, id: string): Promise<void>;
}
