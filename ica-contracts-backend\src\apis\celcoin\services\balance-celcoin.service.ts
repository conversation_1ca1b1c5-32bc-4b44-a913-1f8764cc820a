import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios from 'axios';
import * as https from 'https';

import { IGetBalanceApiRequest } from '../requests/get-balance-celcoin.request';
import { IGetBalanceApiResponse } from '../responses/get-balance-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class BalanceCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async getBalance(
    data: IGetBalanceApiRequest,
  ): Promise<IGetBalanceApiResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/baas-walletreports/v1/wallet/balance`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      params: {
        DocumentNumber: data.DocumentNumber,
        Account: data.Account,
      },
      httpsAgent,
    };

    try {
      const response = await axios.get<IGetBalanceApiResponse>(url, config);

      return response.data;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }
  async getRealBalance(
    data: IGetBalanceApiRequest,
  ): Promise<IGetBalanceApiResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/baas-walletreports/v1/wallet/balance`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      params: {
        DocumentNumber: data.DocumentNumber,
        Account: data.Account,
      },
      httpsAgent,
    };

    try {
      // Fetch the balance from the Celcoin API
      const response = await axios.get<IGetBalanceApiResponse>(url, config);

      return response.data;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }
}
