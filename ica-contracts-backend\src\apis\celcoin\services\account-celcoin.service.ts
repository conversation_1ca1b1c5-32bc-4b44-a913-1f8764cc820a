import {
  HttpException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios from 'axios';
import * as https from 'https';

import { IChangeAccountStatusCelcoinRequest } from '../requests/change-account-status-celcoin.request';
import { ICloseAccountCelcoinRequest } from '../requests/close-account-celcoin.request';
import { ICreateAccountPFCelcoinRequest } from '../requests/create-account-pf-celcoin.request';
import { ICreateAccountPJCelcoinRequest } from '../requests/create-account-pj-celcoin.request';
import { IGetAccountCelcoinRequest } from '../requests/get-account-celcoin.request';
import { IUpdateCustomerApiRequest } from '../requests/update-account-pf-celcoin.request';
import { IUpdatedBusinessApiRequest } from '../requests/update-account-pj-celcoin.request';
import { ICreateAccountPFCelcoinResponse } from '../responses/create-account-pf-celcoin.response';
import { ICreateAccountPJCelcoinResponse } from '../responses/create-account-pj-celcoin.response';
import { IGetAccountCelcoinResponse } from '../responses/get-account-celcoin.response';
import { ICustomerUpdateApiResponse } from '../responses/update-account-pf-celcoin.response';
import { IBusinessUpdateApiResponse } from '../responses/update-account-pj-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class AccountCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async createPF(
    data: ICreateAccountPFCelcoinRequest,
  ): Promise<ICreateAccountPFCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();

    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    return axios
      .post(
        `${process.env.CELCOIN_URL}/baas-onboarding/v1/account/natural-person/create`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
          httpsAgent,
        },
      )
      .then((response: { data: ICreateAccountPFCelcoinResponse }) => {
        return response.data;
      })
      .catch((error) => {
        throw new InternalServerErrorException(error.response.data);
      });
  }

  async createPJ(
    data: ICreateAccountPJCelcoinRequest,
  ): Promise<ICreateAccountPJCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    return axios
      .post(
        `${process.env.CELCOIN_URL}/baas-onboarding/v1/account/business/create`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
          httpsAgent,
        },
      )
      .then((response: { data: ICreateAccountPJCelcoinResponse }) => {
        return response.data;
      })
      .catch((error) => {
        throw new InternalServerErrorException(error.response.data);
      });
  }

  async getAccount(
    data: IGetAccountCelcoinRequest,
    type: string,
  ): Promise<IGetAccountCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const fetch = type === 'physical' ? 'fetch' : 'fetch-business';

    return axios
      .get(
        `${process.env.CELCOIN_URL}/baas-accountmanager/v1/account/${fetch}`,
        {
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
          params: data,
          httpsAgent,
        },
      )
      .then((response: { data: IGetAccountCelcoinResponse }) => {
        return response.data;
      })
      .catch((error) => {
        throw new InternalServerErrorException(error.response.data);
      });
  }

  async changeAccountStatus(data: IChangeAccountStatusCelcoinRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/baas-accountmanager/v1/account/status?${new URLSearchParams(
      {
        account: data.account,
        DocumentNumber: data.document,
      },
    )}`;

    return axios
      .put(
        url,
        {
          status: data.status,
          reason: data.reason,
        },
        {
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
          httpsAgent,
        },
      )
      .catch((error) => {
        throw new InternalServerErrorException(error.response.data);
      });
  }

  async closeAccount(data: ICloseAccountCelcoinRequest): Promise<{
    version: string;
    status: string;
  }> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/baas-accountmanager/v1/account/close?${new URLSearchParams(
      {
        account: data.account,
        DocumentNumber: data.document,
        reason: data.reason,
      },
    )}`;

    return axios
      .delete(url, {
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
        },
        httpsAgent,
      })
      .then((response: { data: { version: string; status: string } }) => {
        return response.data;
      })
      .catch((error) => {
        throw new InternalServerErrorException(error.response.data);
      });
  }

  async updatePF(
    data: IUpdateCustomerApiRequest,
  ): Promise<ICustomerUpdateApiResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/baas-accountmanager/v1/account/natural-person`;

    const params = {
      Account: data.Account,
      DocumentNumber: data.DocumentNumber,
    };

    try {
      const response = await axios.put(url, data, {
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
        },
        params,
        httpsAgent,
      });
      return response.data;
    } catch (error) {
      throw new HttpException(error.response.data, 400);
    }
  }

  async updatePJ(
    data: IUpdatedBusinessApiRequest,
  ): Promise<IBusinessUpdateApiResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/baas-accountmanager/v1/account/business`;
    const params = {
      Account: data.Account,
      DocumentNumber: data.DocumentNumber,
    };

    const headers = {
      accept: 'application/json',
      'content-type': 'application/json',
      Authorization: `Bearer ${token.accessToken}`,
    };

    try {
      const response = await axios.put(url, data, {
        params,
        headers,
        httpsAgent,
      });
      return response.data;
    } catch (error) {
      throw new HttpException(error.response.data, 400);
    }
  }
}
