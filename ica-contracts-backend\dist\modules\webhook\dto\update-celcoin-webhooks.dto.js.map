{"version": 3, "file": "update-celcoin-webhooks.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/update-celcoin-webhooks.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsD;AAEtD,MAAa,uBAAuB;CAQnC;AARD,0DAQC;AALC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;oDACC;AAIZ;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;uDACI", "sourcesContent": ["import { IsString, IsDefined } from 'class-validator';\r\n\r\nexport class UpdateCelcoinWebhookDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  url: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  entity: string;\r\n}\r\n"]}