import type { HttpClient } from '@/application/interfaces/http-client'
import type { Either } from '@/domain/shared'

export interface IJwtResponse {
  type: string
  token: string
  refreshToken: string
}

export interface IAuthRbmResponse {
  erro: boolean
  jwt?: IJwtResponse
  user?: unknown
  message?: string
}

export interface IAuthResult {
  token: string
  refreshToken: string
}

export interface IAuthRbm {
  authenticateIntegration(
    httpClient: HttpClient
  ): Promise<Either<Error, IAuthResult>>
}
