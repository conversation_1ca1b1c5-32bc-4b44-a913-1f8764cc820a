{"version": 3, "file": "register-celcoin-webhook.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/register-celcoin-webhook.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAkE;AAElE,MAAa,yBAAyB;CAgBrC;AAhBD,8DAgBC;AAbC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sDACC;AAIZ;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACG", "sourcesContent": ["import { IsString, IsDefined, IsOptional } from 'class-validator';\r\n\r\nexport class RegisterCelcoinWebhookDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  url: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  entity: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  password: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  login: string;\r\n}\r\n"]}