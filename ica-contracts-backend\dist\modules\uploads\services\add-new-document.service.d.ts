import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { UploadsEntity } from 'src/shared/database/typeorm/entities/uploads.entity';
import { Repository } from 'typeorm';
import { AddNewDocumentDto } from '../dto/add-new-document.dto';
export declare class AddNewDocumentService {
    private ownerRoleRelationRepository;
    private uploadsDb;
    private readonly s3;
    constructor(ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, uploadsDb: Repository<UploadsEntity>);
    perform(data: AddNewDocumentDto, file: Express.Multer.File): Promise<{
        message: string;
    }>;
    private uploadToS3;
}
