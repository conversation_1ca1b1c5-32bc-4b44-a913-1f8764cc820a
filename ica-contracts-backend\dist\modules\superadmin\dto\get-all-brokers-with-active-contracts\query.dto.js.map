{"version": 3, "file": "query.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-all-brokers-with-active-contracts/query.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAOyB;AAMzB,yDAAyC;AACzC,oFAAuE;AAEvE,MAAa,wCAAwC;IAArD;QAME,SAAI,GAAY,CAAC,CAAC;QAMlB,UAAK,GAAY,EAAE,CAAC;IAatB,CAAC;CAAA;AAzBD,4FAyBC;AAnBC;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;sEACU;AAMlB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;uEACY;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0EACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wEACK;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qCAAgB,CAAC;;8EACM", "sourcesContent": ["import {\r\n  IsEnum,\r\n  IsInt,\r\n  <PERSON>N<PERSON>ber,\r\n  IsOptional,\r\n  IsPositive,\r\n  IsString,\r\n} from 'class-validator';\r\n\r\nimport {\r\n  IPaginationQuery,\r\n  IDateRangeQuery,\r\n} from '../../helpers/pagination-query';\r\nimport { Type } from 'class-transformer';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\n\r\nexport class GetAllBrokersWithActiveContractsQueryDto\r\n  implements IPaginationQuery, IDateRangeQuery {\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  page?: number = 1;\r\n\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  limit?: number = 10;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  dateFrom?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  dateTo?: string;\r\n\r\n  @IsOptional()\r\n  @IsEnum(ContractTypeEnum)\r\n  contractType: ContractTypeEnum;\r\n}\r\n"]}