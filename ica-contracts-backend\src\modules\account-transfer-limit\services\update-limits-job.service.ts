import { Inject, Injectable } from '@nestjs/common';
// import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { format } from 'date-fns';
import { IGetAccountTransactionsResponse } from 'src/apis/icainvest-credit/responses/get-account-transactions.response';
import { GetAccountTransactionsService } from 'src/apis/icainvest-credit/services/get-account-transactions.service';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { Repository } from 'typeorm';

@Injectable()
export class UpdateAccountLimitsJobService {
  constructor(
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
    @Inject(GetAccountTransactionsService)
    private getAccountTransactionsService: GetAccountTransactionsService,
  ) {}

  // */5 5-20 * * *
  // @Cron('0 6 * * 1-5', { timeZone: 'America/Sao_Paulo' })
  async handleCron() {
    console.log('Executando UpdateAccountLimitsJob');
    const registersCount = await this.accountLimitRepo.count();
    try {
      const accountLimits = await this.accountLimitRepo.find();

      const accountLimitToUpdates = accountLimits.map(async (accountL) => {
        const day = new Date().getDay();
        let defaultValues = JSON.parse(accountL.defaultValues);

        const accountTransactions: IGetAccountTransactionsResponse[] =
          await this.getAccountTransactionsService.perform({
            id: accountL.accountId,
          });

        const today = format(new Date(), 'yyyy-MM-dd');

        const rechargesToday = accountTransactions.filter(
          (transaction) =>
            transaction.type === 'RECHARGE' &&
            transaction.date.startsWith(today),
        );

        const totalAmount = rechargesToday.reduce((sum, transaction) => {
          return sum + parseFloat(transaction.amount);
        }, 0);

        if (!defaultValues)
          defaultValues = {
            monthlyLimit: 10000,
            dailyNightLimit: 1000,
          };

        const { monthlyLimit, dailyNightLimit } = defaultValues;

        const updatedAccount = { ...accountL };

        if (day === 1) {
          updatedAccount.monthlyLimit = monthlyLimit;
        }

        updatedAccount.dailyLimit = totalAmount;
        updatedAccount.dailyNightLimit = dailyNightLimit;

        return this.accountLimitRepo.save(updatedAccount);
      });

      await Promise.allSettled(accountLimitToUpdates);
      console.log(`Job Executou. [Registros encontrados: ${registersCount}]`);
    } catch (error) {
      console.log('Erro ao atualizar limites de transferencia', error);
    }
  }
}
