{"version": 3, "file": "billpayment-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/billpayment-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qGAA4F;AAC5F,2FAAiF;AACjF,qCAAqC;AAK9B,IAAM,gCAAgC,GAAtC,MAAM,gCAAgC;IAC3C,YAEmB,qBAAoD;QAApD,0BAAqB,GAArB,qBAAqB,CAA+B;IACpE,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,KAAoC;QAChD,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;QAEjE,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEhE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;aACpB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAC1C,OAAO;QACT,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAErC,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YAErE,MAAM,mBAAmB,GAAG;gBAC1B,GAAG,mBAAmB;gBACtB,eAAe,EAAE,KAAK;aACvB,CAAC;YAEF,OAAO,CAAC,GAAG,CACT,wCAAwC,EACxC,mBAAmB,CACpB,CAAC;YAEF,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CACrC,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,EACtB;gBACE,MAAM,EAAE,+CAAqB,CAAC,KAAK;gBACnC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;aACtD,CACF,CAAC;YAEF,OAAO;QACT,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAEpC,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;gBAErE,MAAM,mBAAmB,GAAG;oBAC1B,GAAG,mBAAmB;oBACtB,eAAe,EAAE,KAAK;iBACvB,CAAC;gBACF,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CACrC,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,EACtB;oBACE,MAAM,EAAE,+CAAqB,CAAC,IAAI;oBAClC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;iBACtD,CACF,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAnEY,4EAAgC;2CAAhC,gCAAgC;IAD5C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;qCACI,oBAAU;GAHzC,gCAAgC,CAmE5C", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IBillPaymentCelcoinWebhookDto } from '../dto/billpayment-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class BillPaymentCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(TransactionEntity)\r\n    private readonly transactionRepository: Repository<TransactionEntity>,\r\n  ) {}\r\n  async perform(input: IBillPaymentCelcoinWebhookDto) {\r\n    console.log('BillPaymentCelcoinWebhookService payload: ', input);\r\n\r\n    console.log('Buscando Transaction com o code: ', input.body.id);\r\n\r\n    const transaction = await this.transactionRepository.findOne({\r\n      where: {\r\n        code: input.body.id,\r\n      },\r\n    });\r\n\r\n    if (!transaction) {\r\n      console.log('Transaction não encontrada');\r\n      return;\r\n    }\r\n\r\n    if (input.status === 'ERROR') {\r\n      console.log('Webhook retornou erro');\r\n\r\n      const transactionMetadata = JSON.parse(transaction.transferMetadata);\r\n\r\n      const newTransferMetadata = {\r\n        ...transactionMetadata,\r\n        webhookResponse: input,\r\n      };\r\n\r\n      console.log(\r\n        'atualizar metadata Transaction para : ',\r\n        newTransferMetadata,\r\n      );\r\n\r\n      await this.transactionRepository.update(\r\n        { id: transaction.id },\r\n        {\r\n          status: TransactionStatusEnum.ERROR,\r\n          transferMetadata: JSON.stringify(newTransferMetadata),\r\n        },\r\n      );\r\n\r\n      return;\r\n    }\r\n\r\n    if (input.status === 'CONFIRMED') {\r\n      console.log('Pagamento confirmado');\r\n\r\n      if (transaction) {\r\n        const transactionMetadata = JSON.parse(transaction.transferMetadata);\r\n\r\n        const newTransferMetadata = {\r\n          ...transactionMetadata,\r\n          webhookResponse: input,\r\n        };\r\n        await this.transactionRepository.update(\r\n          { id: transaction.id },\r\n          {\r\n            status: TransactionStatusEnum.DONE,\r\n            transferMetadata: JSON.stringify(newTransferMetadata),\r\n          },\r\n        );\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}