import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';
import { Repository } from 'typeorm';
import { SendPreRegisterDto } from '../dto/send-pre-register.dto';
export declare class SendPreRegisterService {
    private preRegisterDb;
    private accountDb;
    private readonly contractRepository;
    constructor(preRegisterDb: Repository<PreRegisterEntity>, accountDb: Repository<AccountEntity>, contractRepository: Repository<ContractEntity>);
    perform(data: SendPreRegisterDto): Promise<{
        token: string;
    }>;
}
