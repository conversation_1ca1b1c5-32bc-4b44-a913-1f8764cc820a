"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yup";
exports.ids = ["vendor-chunks/yup"];
exports.modules = {

/***/ "(ssr)/./node_modules/yup/index.js":
/*!***********************************!*\
  !*** ./node_modules/yup/index.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar propertyExpr = __webpack_require__(/*! property-expr */ \"(ssr)/./node_modules/property-expr/index.js\");\nvar tinyCase = __webpack_require__(/*! tiny-case */ \"(ssr)/./node_modules/tiny-case/index.js\");\nvar toposort = __webpack_require__(/*! toposort */ \"(ssr)/./node_modules/toposort/index.js\");\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar toposort__default = /*#__PURE__*/_interopDefaultLegacy(toposort);\n\nconst toString = Object.prototype.toString;\nconst errorToString = Error.prototype.toString;\nconst regExpToString = RegExp.prototype.toString;\nconst symbolToString = typeof Symbol !== 'undefined' ? Symbol.prototype.toString : () => '';\nconst SYMBOL_REGEXP = /^Symbol\\((.*)\\)(.*)$/;\nfunction printNumber(val) {\n  if (val != +val) return 'NaN';\n  const isNegativeZero = val === 0 && 1 / val < 0;\n  return isNegativeZero ? '-0' : '' + val;\n}\nfunction printSimpleValue(val, quoteStrings = false) {\n  if (val == null || val === true || val === false) return '' + val;\n  const typeOf = typeof val;\n  if (typeOf === 'number') return printNumber(val);\n  if (typeOf === 'string') return quoteStrings ? `\"${val}\"` : val;\n  if (typeOf === 'function') return '[Function ' + (val.name || 'anonymous') + ']';\n  if (typeOf === 'symbol') return symbolToString.call(val).replace(SYMBOL_REGEXP, 'Symbol($1)');\n  const tag = toString.call(val).slice(8, -1);\n  if (tag === 'Date') return isNaN(val.getTime()) ? '' + val : val.toISOString(val);\n  if (tag === 'Error' || val instanceof Error) return '[' + errorToString.call(val) + ']';\n  if (tag === 'RegExp') return regExpToString.call(val);\n  return null;\n}\nfunction printValue(value, quoteStrings) {\n  let result = printSimpleValue(value, quoteStrings);\n  if (result !== null) return result;\n  return JSON.stringify(value, function (key, value) {\n    let result = printSimpleValue(this[key], quoteStrings);\n    if (result !== null) return result;\n    return value;\n  }, 2);\n}\n\nfunction toArray(value) {\n  return value == null ? [] : [].concat(value);\n}\n\nlet _Symbol$toStringTag, _Symbol$hasInstance, _Symbol$toStringTag2;\nlet strReg = /\\$\\{\\s*(\\w+)\\s*\\}/g;\n_Symbol$toStringTag = Symbol.toStringTag;\nclass ValidationErrorNoStack {\n  constructor(errorOrErrors, value, field, type) {\n    this.name = void 0;\n    this.message = void 0;\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = void 0;\n    this.inner = void 0;\n    this[_Symbol$toStringTag] = 'Error';\n    this.name = 'ValidationError';\n    this.value = value;\n    this.path = field;\n    this.type = type;\n    this.errors = [];\n    this.inner = [];\n    toArray(errorOrErrors).forEach(err => {\n      if (ValidationError.isError(err)) {\n        this.errors.push(...err.errors);\n        const innerErrors = err.inner.length ? err.inner : [err];\n        this.inner.push(...innerErrors);\n      } else {\n        this.errors.push(err);\n      }\n    });\n    this.message = this.errors.length > 1 ? `${this.errors.length} errors occurred` : this.errors[0];\n  }\n}\n_Symbol$hasInstance = Symbol.hasInstance;\n_Symbol$toStringTag2 = Symbol.toStringTag;\nclass ValidationError extends Error {\n  static formatError(message, params) {\n    // Attempt to make the path more friendly for error message interpolation.\n    const path = params.label || params.path || 'this';\n    // Store the original path under `originalPath` so it isn't lost to custom\n    // message functions; e.g., ones provided in `setLocale()` calls.\n    params = Object.assign({}, params, {\n      path,\n      originalPath: params.path\n    });\n    if (typeof message === 'string') return message.replace(strReg, (_, key) => printValue(params[key]));\n    if (typeof message === 'function') return message(params);\n    return message;\n  }\n  static isError(err) {\n    return err && err.name === 'ValidationError';\n  }\n  constructor(errorOrErrors, value, field, type, disableStack) {\n    const errorNoStack = new ValidationErrorNoStack(errorOrErrors, value, field, type);\n    if (disableStack) {\n      return errorNoStack;\n    }\n    super();\n    this.value = void 0;\n    this.path = void 0;\n    this.type = void 0;\n    this.params = void 0;\n    this.errors = [];\n    this.inner = [];\n    this[_Symbol$toStringTag2] = 'Error';\n    this.name = errorNoStack.name;\n    this.message = errorNoStack.message;\n    this.type = errorNoStack.type;\n    this.value = errorNoStack.value;\n    this.path = errorNoStack.path;\n    this.errors = errorNoStack.errors;\n    this.inner = errorNoStack.inner;\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ValidationError);\n    }\n  }\n  static [_Symbol$hasInstance](inst) {\n    return ValidationErrorNoStack[Symbol.hasInstance](inst) || super[Symbol.hasInstance](inst);\n  }\n}\n\nlet mixed = {\n  default: '${path} is invalid',\n  required: '${path} is a required field',\n  defined: '${path} must be defined',\n  notNull: '${path} cannot be null',\n  oneOf: '${path} must be one of the following values: ${values}',\n  notOneOf: '${path} must not be one of the following values: ${values}',\n  notType: ({\n    path,\n    type,\n    value,\n    originalValue\n  }) => {\n    const castMsg = originalValue != null && originalValue !== value ? ` (cast from the value \\`${printValue(originalValue, true)}\\`).` : '.';\n    return type !== 'mixed' ? `${path} must be a \\`${type}\\` type, ` + `but the final value was: \\`${printValue(value, true)}\\`` + castMsg : `${path} must match the configured type. ` + `The validated value was: \\`${printValue(value, true)}\\`` + castMsg;\n  }\n};\nlet string = {\n  length: '${path} must be exactly ${length} characters',\n  min: '${path} must be at least ${min} characters',\n  max: '${path} must be at most ${max} characters',\n  matches: '${path} must match the following: \"${regex}\"',\n  email: '${path} must be a valid email',\n  url: '${path} must be a valid URL',\n  uuid: '${path} must be a valid UUID',\n  datetime: '${path} must be a valid ISO date-time',\n  datetime_precision: '${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits',\n  datetime_offset: '${path} must be a valid ISO date-time with UTC \"Z\" timezone',\n  trim: '${path} must be a trimmed string',\n  lowercase: '${path} must be a lowercase string',\n  uppercase: '${path} must be a upper case string'\n};\nlet number = {\n  min: '${path} must be greater than or equal to ${min}',\n  max: '${path} must be less than or equal to ${max}',\n  lessThan: '${path} must be less than ${less}',\n  moreThan: '${path} must be greater than ${more}',\n  positive: '${path} must be a positive number',\n  negative: '${path} must be a negative number',\n  integer: '${path} must be an integer'\n};\nlet date = {\n  min: '${path} field must be later than ${min}',\n  max: '${path} field must be at earlier than ${max}'\n};\nlet boolean = {\n  isValue: '${path} field must be ${value}'\n};\nlet object = {\n  noUnknown: '${path} field has unspecified keys: ${unknown}',\n  exact: '${path} object contains unknown properties: ${properties}'\n};\nlet array = {\n  min: '${path} field must have at least ${min} items',\n  max: '${path} field must have less than or equal to ${max} items',\n  length: '${path} must have ${length} items'\n};\nlet tuple = {\n  notType: params => {\n    const {\n      path,\n      value,\n      spec\n    } = params;\n    const typeLen = spec.types.length;\n    if (Array.isArray(value)) {\n      if (value.length < typeLen) return `${path} tuple value has too few items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n      if (value.length > typeLen) return `${path} tuple value has too many items, expected a length of ${typeLen} but got ${value.length} for value: \\`${printValue(value, true)}\\``;\n    }\n    return ValidationError.formatError(mixed.notType, params);\n  }\n};\nvar locale = Object.assign(Object.create(null), {\n  mixed,\n  string,\n  number,\n  date,\n  object,\n  array,\n  boolean,\n  tuple\n});\n\nconst isSchema = obj => obj && obj.__isYupSchema__;\n\nclass Condition {\n  static fromOptions(refs, config) {\n    if (!config.then && !config.otherwise) throw new TypeError('either `then:` or `otherwise:` is required for `when()` conditions');\n    let {\n      is,\n      then,\n      otherwise\n    } = config;\n    let check = typeof is === 'function' ? is : (...values) => values.every(value => value === is);\n    return new Condition(refs, (values, schema) => {\n      var _branch;\n      let branch = check(...values) ? then : otherwise;\n      return (_branch = branch == null ? void 0 : branch(schema)) != null ? _branch : schema;\n    });\n  }\n  constructor(refs, builder) {\n    this.fn = void 0;\n    this.refs = refs;\n    this.refs = refs;\n    this.fn = builder;\n  }\n  resolve(base, options) {\n    let values = this.refs.map(ref =>\n    // TODO: ? operator here?\n    ref.getValue(options == null ? void 0 : options.value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context));\n    let schema = this.fn(values, base, options);\n    if (schema === undefined ||\n    // @ts-ignore this can be base\n    schema === base) {\n      return base;\n    }\n    if (!isSchema(schema)) throw new TypeError('conditions must return a schema object');\n    return schema.resolve(options);\n  }\n}\n\nconst prefixes = {\n  context: '$',\n  value: '.'\n};\nfunction create$9(key, options) {\n  return new Reference(key, options);\n}\nclass Reference {\n  constructor(key, options = {}) {\n    this.key = void 0;\n    this.isContext = void 0;\n    this.isValue = void 0;\n    this.isSibling = void 0;\n    this.path = void 0;\n    this.getter = void 0;\n    this.map = void 0;\n    if (typeof key !== 'string') throw new TypeError('ref must be a string, got: ' + key);\n    this.key = key.trim();\n    if (key === '') throw new TypeError('ref must be a non-empty string');\n    this.isContext = this.key[0] === prefixes.context;\n    this.isValue = this.key[0] === prefixes.value;\n    this.isSibling = !this.isContext && !this.isValue;\n    let prefix = this.isContext ? prefixes.context : this.isValue ? prefixes.value : '';\n    this.path = this.key.slice(prefix.length);\n    this.getter = this.path && propertyExpr.getter(this.path, true);\n    this.map = options.map;\n  }\n  getValue(value, parent, context) {\n    let result = this.isContext ? context : this.isValue ? value : parent;\n    if (this.getter) result = this.getter(result || {});\n    if (this.map) result = this.map(result);\n    return result;\n  }\n\n  /**\n   *\n   * @param {*} value\n   * @param {Object} options\n   * @param {Object=} options.context\n   * @param {Object=} options.parent\n   */\n  cast(value, options) {\n    return this.getValue(value, options == null ? void 0 : options.parent, options == null ? void 0 : options.context);\n  }\n  resolve() {\n    return this;\n  }\n  describe() {\n    return {\n      type: 'ref',\n      key: this.key\n    };\n  }\n  toString() {\n    return `Ref(${this.key})`;\n  }\n  static isRef(value) {\n    return value && value.__isYupRef;\n  }\n}\n\n// @ts-ignore\nReference.prototype.__isYupRef = true;\n\nconst isAbsent = value => value == null;\n\nfunction createValidation(config) {\n  function validate({\n    value,\n    path = '',\n    options,\n    originalValue,\n    schema\n  }, panic, next) {\n    const {\n      name,\n      test,\n      params,\n      message,\n      skipAbsent\n    } = config;\n    let {\n      parent,\n      context,\n      abortEarly = schema.spec.abortEarly,\n      disableStackTrace = schema.spec.disableStackTrace\n    } = options;\n    function resolve(item) {\n      return Reference.isRef(item) ? item.getValue(value, parent, context) : item;\n    }\n    function createError(overrides = {}) {\n      const nextParams = Object.assign({\n        value,\n        originalValue,\n        label: schema.spec.label,\n        path: overrides.path || path,\n        spec: schema.spec,\n        disableStackTrace: overrides.disableStackTrace || disableStackTrace\n      }, params, overrides.params);\n      for (const key of Object.keys(nextParams)) nextParams[key] = resolve(nextParams[key]);\n      const error = new ValidationError(ValidationError.formatError(overrides.message || message, nextParams), value, nextParams.path, overrides.type || name, nextParams.disableStackTrace);\n      error.params = nextParams;\n      return error;\n    }\n    const invalid = abortEarly ? panic : next;\n    let ctx = {\n      path,\n      parent,\n      type: name,\n      from: options.from,\n      createError,\n      resolve,\n      options,\n      originalValue,\n      schema\n    };\n    const handleResult = validOrError => {\n      if (ValidationError.isError(validOrError)) invalid(validOrError);else if (!validOrError) invalid(createError());else next(null);\n    };\n    const handleError = err => {\n      if (ValidationError.isError(err)) invalid(err);else panic(err);\n    };\n    const shouldSkip = skipAbsent && isAbsent(value);\n    if (shouldSkip) {\n      return handleResult(true);\n    }\n    let result;\n    try {\n      var _result;\n      result = test.call(ctx, value, ctx);\n      if (typeof ((_result = result) == null ? void 0 : _result.then) === 'function') {\n        if (options.sync) {\n          throw new Error(`Validation test of type: \"${ctx.type}\" returned a Promise during a synchronous validate. ` + `This test will finish after the validate call has returned`);\n        }\n        return Promise.resolve(result).then(handleResult, handleError);\n      }\n    } catch (err) {\n      handleError(err);\n      return;\n    }\n    handleResult(result);\n  }\n  validate.OPTIONS = config;\n  return validate;\n}\n\nfunction getIn(schema, path, value, context = value) {\n  let parent, lastPart, lastPartDebug;\n\n  // root path: ''\n  if (!path) return {\n    parent,\n    parentPath: path,\n    schema\n  };\n  propertyExpr.forEach(path, (_part, isBracket, isArray) => {\n    let part = isBracket ? _part.slice(1, _part.length - 1) : _part;\n    schema = schema.resolve({\n      context,\n      parent,\n      value\n    });\n    let isTuple = schema.type === 'tuple';\n    let idx = isArray ? parseInt(part, 10) : 0;\n    if (schema.innerType || isTuple) {\n      if (isTuple && !isArray) throw new Error(`Yup.reach cannot implicitly index into a tuple type. the path part \"${lastPartDebug}\" must contain an index to the tuple element, e.g. \"${lastPartDebug}[0]\"`);\n      if (value && idx >= value.length) {\n        throw new Error(`Yup.reach cannot resolve an array item at index: ${_part}, in the path: ${path}. ` + `because there is no value at that index. `);\n      }\n      parent = value;\n      value = value && value[idx];\n      schema = isTuple ? schema.spec.types[idx] : schema.innerType;\n    }\n\n    // sometimes the array index part of a path doesn't exist: \"nested.arr.child\"\n    // in these cases the current part is the next schema and should be processed\n    // in this iteration. For cases where the index signature is included this\n    // check will fail and we'll handle the `child` part on the next iteration like normal\n    if (!isArray) {\n      if (!schema.fields || !schema.fields[part]) throw new Error(`The schema does not contain the path: ${path}. ` + `(failed at: ${lastPartDebug} which is a type: \"${schema.type}\")`);\n      parent = value;\n      value = value && value[part];\n      schema = schema.fields[part];\n    }\n    lastPart = part;\n    lastPartDebug = isBracket ? '[' + _part + ']' : '.' + _part;\n  });\n  return {\n    schema,\n    parent,\n    parentPath: lastPart\n  };\n}\nfunction reach(obj, path, value, context) {\n  return getIn(obj, path, value, context).schema;\n}\n\nclass ReferenceSet extends Set {\n  describe() {\n    const description = [];\n    for (const item of this.values()) {\n      description.push(Reference.isRef(item) ? item.describe() : item);\n    }\n    return description;\n  }\n  resolveAll(resolve) {\n    let result = [];\n    for (const item of this.values()) {\n      result.push(resolve(item));\n    }\n    return result;\n  }\n  clone() {\n    return new ReferenceSet(this.values());\n  }\n  merge(newItems, removeItems) {\n    const next = this.clone();\n    newItems.forEach(value => next.add(value));\n    removeItems.forEach(value => next.delete(value));\n    return next;\n  }\n}\n\n// tweaked from https://github.com/Kelin2025/nanoclone/blob/0abeb7635bda9b68ef2277093f76dbe3bf3948e1/src/index.js\nfunction clone(src, seen = new Map()) {\n  if (isSchema(src) || !src || typeof src !== 'object') return src;\n  if (seen.has(src)) return seen.get(src);\n  let copy;\n  if (src instanceof Date) {\n    // Date\n    copy = new Date(src.getTime());\n    seen.set(src, copy);\n  } else if (src instanceof RegExp) {\n    // RegExp\n    copy = new RegExp(src);\n    seen.set(src, copy);\n  } else if (Array.isArray(src)) {\n    // Array\n    copy = new Array(src.length);\n    seen.set(src, copy);\n    for (let i = 0; i < src.length; i++) copy[i] = clone(src[i], seen);\n  } else if (src instanceof Map) {\n    // Map\n    copy = new Map();\n    seen.set(src, copy);\n    for (const [k, v] of src.entries()) copy.set(k, clone(v, seen));\n  } else if (src instanceof Set) {\n    // Set\n    copy = new Set();\n    seen.set(src, copy);\n    for (const v of src) copy.add(clone(v, seen));\n  } else if (src instanceof Object) {\n    // Object\n    copy = {};\n    seen.set(src, copy);\n    for (const [k, v] of Object.entries(src)) copy[k] = clone(v, seen);\n  } else {\n    throw Error(`Unable to clone ${src}`);\n  }\n  return copy;\n}\n\n// If `CustomSchemaMeta` isn't extended with any keys, we'll fall back to a\n// loose Record definition allowing free form usage.\nclass Schema {\n  constructor(options) {\n    this.type = void 0;\n    this.deps = [];\n    this.tests = void 0;\n    this.transforms = void 0;\n    this.conditions = [];\n    this._mutate = void 0;\n    this.internalTests = {};\n    this._whitelist = new ReferenceSet();\n    this._blacklist = new ReferenceSet();\n    this.exclusiveTests = Object.create(null);\n    this._typeCheck = void 0;\n    this.spec = void 0;\n    this.tests = [];\n    this.transforms = [];\n    this.withMutation(() => {\n      this.typeError(mixed.notType);\n    });\n    this.type = options.type;\n    this._typeCheck = options.check;\n    this.spec = Object.assign({\n      strip: false,\n      strict: false,\n      abortEarly: true,\n      recursive: true,\n      disableStackTrace: false,\n      nullable: false,\n      optional: true,\n      coerce: true\n    }, options == null ? void 0 : options.spec);\n    this.withMutation(s => {\n      s.nonNullable();\n    });\n  }\n\n  // TODO: remove\n  get _type() {\n    return this.type;\n  }\n  clone(spec) {\n    if (this._mutate) {\n      if (spec) Object.assign(this.spec, spec);\n      return this;\n    }\n\n    // if the nested value is a schema we can skip cloning, since\n    // they are already immutable\n    const next = Object.create(Object.getPrototypeOf(this));\n\n    // @ts-expect-error this is readonly\n    next.type = this.type;\n    next._typeCheck = this._typeCheck;\n    next._whitelist = this._whitelist.clone();\n    next._blacklist = this._blacklist.clone();\n    next.internalTests = Object.assign({}, this.internalTests);\n    next.exclusiveTests = Object.assign({}, this.exclusiveTests);\n\n    // @ts-expect-error this is readonly\n    next.deps = [...this.deps];\n    next.conditions = [...this.conditions];\n    next.tests = [...this.tests];\n    next.transforms = [...this.transforms];\n    next.spec = clone(Object.assign({}, this.spec, spec));\n    return next;\n  }\n  label(label) {\n    let next = this.clone();\n    next.spec.label = label;\n    return next;\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n  withMutation(fn) {\n    let before = this._mutate;\n    this._mutate = true;\n    let result = fn(this);\n    this._mutate = before;\n    return result;\n  }\n  concat(schema) {\n    if (!schema || schema === this) return this;\n    if (schema.type !== this.type && this.type !== 'mixed') throw new TypeError(`You cannot \\`concat()\\` schema's of different types: ${this.type} and ${schema.type}`);\n    let base = this;\n    let combined = schema.clone();\n    const mergedSpec = Object.assign({}, base.spec, combined.spec);\n    combined.spec = mergedSpec;\n    combined.internalTests = Object.assign({}, base.internalTests, combined.internalTests);\n\n    // manually merge the blacklist/whitelist (the other `schema` takes\n    // precedence in case of conflicts)\n    combined._whitelist = base._whitelist.merge(schema._whitelist, schema._blacklist);\n    combined._blacklist = base._blacklist.merge(schema._blacklist, schema._whitelist);\n\n    // start with the current tests\n    combined.tests = base.tests;\n    combined.exclusiveTests = base.exclusiveTests;\n\n    // manually add the new tests to ensure\n    // the deduping logic is consistent\n    combined.withMutation(next => {\n      schema.tests.forEach(fn => {\n        next.test(fn.OPTIONS);\n      });\n    });\n    combined.transforms = [...base.transforms, ...combined.transforms];\n    return combined;\n  }\n  isType(v) {\n    if (v == null) {\n      if (this.spec.nullable && v === null) return true;\n      if (this.spec.optional && v === undefined) return true;\n      return false;\n    }\n    return this._typeCheck(v);\n  }\n  resolve(options) {\n    let schema = this;\n    if (schema.conditions.length) {\n      let conditions = schema.conditions;\n      schema = schema.clone();\n      schema.conditions = [];\n      schema = conditions.reduce((prevSchema, condition) => condition.resolve(prevSchema, options), schema);\n      schema = schema.resolve(options);\n    }\n    return schema;\n  }\n  resolveOptions(options) {\n    var _options$strict, _options$abortEarly, _options$recursive, _options$disableStack;\n    return Object.assign({}, options, {\n      from: options.from || [],\n      strict: (_options$strict = options.strict) != null ? _options$strict : this.spec.strict,\n      abortEarly: (_options$abortEarly = options.abortEarly) != null ? _options$abortEarly : this.spec.abortEarly,\n      recursive: (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive,\n      disableStackTrace: (_options$disableStack = options.disableStackTrace) != null ? _options$disableStack : this.spec.disableStackTrace\n    });\n  }\n\n  /**\n   * Run the configured transform pipeline over an input value.\n   */\n\n  cast(value, options = {}) {\n    let resolvedSchema = this.resolve(Object.assign({\n      value\n    }, options));\n    let allowOptionality = options.assert === 'ignore-optionality';\n    let result = resolvedSchema._cast(value, options);\n    if (options.assert !== false && !resolvedSchema.isType(result)) {\n      if (allowOptionality && isAbsent(result)) {\n        return result;\n      }\n      let formattedValue = printValue(value);\n      let formattedResult = printValue(result);\n      throw new TypeError(`The value of ${options.path || 'field'} could not be cast to a value ` + `that satisfies the schema type: \"${resolvedSchema.type}\". \\n\\n` + `attempted value: ${formattedValue} \\n` + (formattedResult !== formattedValue ? `result of cast: ${formattedResult}` : ''));\n    }\n    return result;\n  }\n  _cast(rawValue, options) {\n    let value = rawValue === undefined ? rawValue : this.transforms.reduce((prevValue, fn) => fn.call(this, prevValue, rawValue, this), rawValue);\n    if (value === undefined) {\n      value = this.getDefault(options);\n    }\n    return value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      path,\n      originalValue = _value,\n      strict = this.spec.strict\n    } = options;\n    let value = _value;\n    if (!strict) {\n      value = this._cast(value, Object.assign({\n        assert: false\n      }, options));\n    }\n    let initialTests = [];\n    for (let test of Object.values(this.internalTests)) {\n      if (test) initialTests.push(test);\n    }\n    this.runTests({\n      path,\n      value,\n      originalValue,\n      options,\n      tests: initialTests\n    }, panic, initialErrors => {\n      // even if we aren't ending early we can't proceed further if the types aren't correct\n      if (initialErrors.length) {\n        return next(initialErrors, value);\n      }\n      this.runTests({\n        path,\n        value,\n        originalValue,\n        options,\n        tests: this.tests\n      }, panic, next);\n    });\n  }\n\n  /**\n   * Executes a set of validations, either schema, produced Tests or a nested\n   * schema validate result.\n   */\n  runTests(runOptions, panic, next) {\n    let fired = false;\n    let {\n      tests,\n      value,\n      originalValue,\n      path,\n      options\n    } = runOptions;\n    let panicOnce = arg => {\n      if (fired) return;\n      fired = true;\n      panic(arg, value);\n    };\n    let nextOnce = arg => {\n      if (fired) return;\n      fired = true;\n      next(arg, value);\n    };\n    let count = tests.length;\n    let nestedErrors = [];\n    if (!count) return nextOnce([]);\n    let args = {\n      value,\n      originalValue,\n      path,\n      options,\n      schema: this\n    };\n    for (let i = 0; i < tests.length; i++) {\n      const test = tests[i];\n      test(args, panicOnce, function finishTestRun(err) {\n        if (err) {\n          Array.isArray(err) ? nestedErrors.push(...err) : nestedErrors.push(err);\n        }\n        if (--count <= 0) {\n          nextOnce(nestedErrors);\n        }\n      });\n    }\n  }\n  asNestedTest({\n    key,\n    index,\n    parent,\n    parentPath,\n    originalParent,\n    options\n  }) {\n    const k = key != null ? key : index;\n    if (k == null) {\n      throw TypeError('Must include `key` or `index` for nested validations');\n    }\n    const isIndex = typeof k === 'number';\n    let value = parent[k];\n    const testOptions = Object.assign({}, options, {\n      // Nested validations fields are always strict:\n      //    1. parent isn't strict so the casting will also have cast inner values\n      //    2. parent is strict in which case the nested values weren't cast either\n      strict: true,\n      parent,\n      value,\n      originalValue: originalParent[k],\n      // FIXME: tests depend on `index` being passed around deeply,\n      //   we should not let the options.key/index bleed through\n      key: undefined,\n      // index: undefined,\n      [isIndex ? 'index' : 'key']: k,\n      path: isIndex || k.includes('.') ? `${parentPath || ''}[${isIndex ? k : `\"${k}\"`}]` : (parentPath ? `${parentPath}.` : '') + key\n    });\n    return (_, panic, next) => this.resolve(testOptions)._validate(value, testOptions, panic, next);\n  }\n  validate(value, options) {\n    var _options$disableStack2;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let disableStackTrace = (_options$disableStack2 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack2 : schema.spec.disableStackTrace;\n    return new Promise((resolve, reject) => schema._validate(value, options, (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      reject(error);\n    }, (errors, validated) => {\n      if (errors.length) reject(new ValidationError(errors, validated, undefined, undefined, disableStackTrace));else resolve(validated);\n    }));\n  }\n  validateSync(value, options) {\n    var _options$disableStack3;\n    let schema = this.resolve(Object.assign({}, options, {\n      value\n    }));\n    let result;\n    let disableStackTrace = (_options$disableStack3 = options == null ? void 0 : options.disableStackTrace) != null ? _options$disableStack3 : schema.spec.disableStackTrace;\n    schema._validate(value, Object.assign({}, options, {\n      sync: true\n    }), (error, parsed) => {\n      if (ValidationError.isError(error)) error.value = parsed;\n      throw error;\n    }, (errors, validated) => {\n      if (errors.length) throw new ValidationError(errors, value, undefined, undefined, disableStackTrace);\n      result = validated;\n    });\n    return result;\n  }\n  isValid(value, options) {\n    return this.validate(value, options).then(() => true, err => {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    });\n  }\n  isValidSync(value, options) {\n    try {\n      this.validateSync(value, options);\n      return true;\n    } catch (err) {\n      if (ValidationError.isError(err)) return false;\n      throw err;\n    }\n  }\n  _getDefault(options) {\n    let defaultValue = this.spec.default;\n    if (defaultValue == null) {\n      return defaultValue;\n    }\n    return typeof defaultValue === 'function' ? defaultValue.call(this, options) : clone(defaultValue);\n  }\n  getDefault(options\n  // If schema is defaulted we know it's at least not undefined\n  ) {\n    let schema = this.resolve(options || {});\n    return schema._getDefault(options);\n  }\n  default(def) {\n    if (arguments.length === 0) {\n      return this._getDefault();\n    }\n    let next = this.clone({\n      default: def\n    });\n    return next;\n  }\n  strict(isStrict = true) {\n    return this.clone({\n      strict: isStrict\n    });\n  }\n  nullability(nullable, message) {\n    const next = this.clone({\n      nullable\n    });\n    next.internalTests.nullable = createValidation({\n      message,\n      name: 'nullable',\n      test(value) {\n        return value === null ? this.schema.spec.nullable : true;\n      }\n    });\n    return next;\n  }\n  optionality(optional, message) {\n    const next = this.clone({\n      optional\n    });\n    next.internalTests.optionality = createValidation({\n      message,\n      name: 'optionality',\n      test(value) {\n        return value === undefined ? this.schema.spec.optional : true;\n      }\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  defined(message = mixed.defined) {\n    return this.optionality(false, message);\n  }\n  nullable() {\n    return this.nullability(true);\n  }\n  nonNullable(message = mixed.notNull) {\n    return this.nullability(false, message);\n  }\n  required(message = mixed.required) {\n    return this.clone().withMutation(next => next.nonNullable(message).defined(message));\n  }\n  notRequired() {\n    return this.clone().withMutation(next => next.nullable().optional());\n  }\n  transform(fn) {\n    let next = this.clone();\n    next.transforms.push(fn);\n    return next;\n  }\n\n  /**\n   * Adds a test function to the schema's queue of tests.\n   * tests can be exclusive or non-exclusive.\n   *\n   * - exclusive tests, will replace any existing tests of the same name.\n   * - non-exclusive: can be stacked\n   *\n   * If a non-exclusive test is added to a schema with an exclusive test of the same name\n   * the exclusive test is removed and further tests of the same name will be stacked.\n   *\n   * If an exclusive test is added to a schema with non-exclusive tests of the same name\n   * the previous tests are removed and further tests of the same name will replace each other.\n   */\n\n  test(...args) {\n    let opts;\n    if (args.length === 1) {\n      if (typeof args[0] === 'function') {\n        opts = {\n          test: args[0]\n        };\n      } else {\n        opts = args[0];\n      }\n    } else if (args.length === 2) {\n      opts = {\n        name: args[0],\n        test: args[1]\n      };\n    } else {\n      opts = {\n        name: args[0],\n        message: args[1],\n        test: args[2]\n      };\n    }\n    if (opts.message === undefined) opts.message = mixed.default;\n    if (typeof opts.test !== 'function') throw new TypeError('`test` is a required parameters');\n    let next = this.clone();\n    let validate = createValidation(opts);\n    let isExclusive = opts.exclusive || opts.name && next.exclusiveTests[opts.name] === true;\n    if (opts.exclusive) {\n      if (!opts.name) throw new TypeError('Exclusive tests must provide a unique `name` identifying the test');\n    }\n    if (opts.name) next.exclusiveTests[opts.name] = !!opts.exclusive;\n    next.tests = next.tests.filter(fn => {\n      if (fn.OPTIONS.name === opts.name) {\n        if (isExclusive) return false;\n        if (fn.OPTIONS.test === validate.OPTIONS.test) return false;\n      }\n      return true;\n    });\n    next.tests.push(validate);\n    return next;\n  }\n  when(keys, options) {\n    if (!Array.isArray(keys) && typeof keys !== 'string') {\n      options = keys;\n      keys = '.';\n    }\n    let next = this.clone();\n    let deps = toArray(keys).map(key => new Reference(key));\n    deps.forEach(dep => {\n      // @ts-ignore readonly array\n      if (dep.isSibling) next.deps.push(dep.key);\n    });\n    next.conditions.push(typeof options === 'function' ? new Condition(deps, options) : Condition.fromOptions(deps, options));\n    return next;\n  }\n  typeError(message) {\n    let next = this.clone();\n    next.internalTests.typeError = createValidation({\n      message,\n      name: 'typeError',\n      skipAbsent: true,\n      test(value) {\n        if (!this.schema._typeCheck(value)) return this.createError({\n          params: {\n            type: this.schema.type\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  oneOf(enums, message = mixed.oneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._whitelist.add(val);\n      next._blacklist.delete(val);\n    });\n    next.internalTests.whiteList = createValidation({\n      message,\n      name: 'oneOf',\n      skipAbsent: true,\n      test(value) {\n        let valids = this.schema._whitelist;\n        let resolved = valids.resolveAll(this.resolve);\n        return resolved.includes(value) ? true : this.createError({\n          params: {\n            values: Array.from(valids).join(', '),\n            resolved\n          }\n        });\n      }\n    });\n    return next;\n  }\n  notOneOf(enums, message = mixed.notOneOf) {\n    let next = this.clone();\n    enums.forEach(val => {\n      next._blacklist.add(val);\n      next._whitelist.delete(val);\n    });\n    next.internalTests.blacklist = createValidation({\n      message,\n      name: 'notOneOf',\n      test(value) {\n        let invalids = this.schema._blacklist;\n        let resolved = invalids.resolveAll(this.resolve);\n        if (resolved.includes(value)) return this.createError({\n          params: {\n            values: Array.from(invalids).join(', '),\n            resolved\n          }\n        });\n        return true;\n      }\n    });\n    return next;\n  }\n  strip(strip = true) {\n    let next = this.clone();\n    next.spec.strip = strip;\n    return next;\n  }\n\n  /**\n   * Return a serialized description of the schema including validations, flags, types etc.\n   *\n   * @param options Provide any needed context for resolving runtime schema alterations (lazy, when conditions, etc).\n   */\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const {\n      label,\n      meta,\n      optional,\n      nullable\n    } = next.spec;\n    const description = {\n      meta,\n      label,\n      optional,\n      nullable,\n      default: next.getDefault(options),\n      type: next.type,\n      oneOf: next._whitelist.describe(),\n      notOneOf: next._blacklist.describe(),\n      tests: next.tests.map(fn => ({\n        name: fn.OPTIONS.name,\n        params: fn.OPTIONS.params\n      })).filter((n, idx, list) => list.findIndex(c => c.name === n.name) === idx)\n    };\n    return description;\n  }\n}\n// @ts-expect-error\nSchema.prototype.__isYupSchema__ = true;\nfor (const method of ['validate', 'validateSync']) Schema.prototype[`${method}At`] = function (path, value, options = {}) {\n  const {\n    parent,\n    parentPath,\n    schema\n  } = getIn(this, path, value, options.context);\n  return schema[method](parent && parent[parentPath], Object.assign({}, options, {\n    parent,\n    path\n  }));\n};\nfor (const alias of ['equals', 'is']) Schema.prototype[alias] = Schema.prototype.oneOf;\nfor (const alias of ['not', 'nope']) Schema.prototype[alias] = Schema.prototype.notOneOf;\n\nconst returnsTrue = () => true;\nfunction create$8(spec) {\n  return new MixedSchema(spec);\n}\nclass MixedSchema extends Schema {\n  constructor(spec) {\n    super(typeof spec === 'function' ? {\n      type: 'mixed',\n      check: spec\n    } : Object.assign({\n      type: 'mixed',\n      check: returnsTrue\n    }, spec));\n  }\n}\ncreate$8.prototype = MixedSchema.prototype;\n\nfunction create$7() {\n  return new BooleanSchema();\n}\nclass BooleanSchema extends Schema {\n  constructor() {\n    super({\n      type: 'boolean',\n      check(v) {\n        if (v instanceof Boolean) v = v.valueOf();\n        return typeof v === 'boolean';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (ctx.spec.coerce && !ctx.isType(value)) {\n          if (/^(true|1)$/i.test(String(value))) return true;\n          if (/^(false|0)$/i.test(String(value))) return false;\n        }\n        return value;\n      });\n    });\n  }\n  isTrue(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'true'\n      },\n      test(value) {\n        return isAbsent(value) || value === true;\n      }\n    });\n  }\n  isFalse(message = boolean.isValue) {\n    return this.test({\n      message,\n      name: 'is-value',\n      exclusive: true,\n      params: {\n        value: 'false'\n      },\n      test(value) {\n        return isAbsent(value) || value === false;\n      }\n    });\n  }\n  default(def) {\n    return super.default(def);\n  }\n  defined(msg) {\n    return super.defined(msg);\n  }\n  optional() {\n    return super.optional();\n  }\n  required(msg) {\n    return super.required(msg);\n  }\n  notRequired() {\n    return super.notRequired();\n  }\n  nullable() {\n    return super.nullable();\n  }\n  nonNullable(msg) {\n    return super.nonNullable(msg);\n  }\n  strip(v) {\n    return super.strip(v);\n  }\n}\ncreate$7.prototype = BooleanSchema.prototype;\n\n/**\n * This file is a modified version of the file from the following repository:\n * Date.parse with progressive enhancement for ISO 8601 <https://github.com/csnover/js-iso8601>\n * NON-CONFORMANT EDITION.\n * © 2011 Colin Snover <http://zetafleet.com>\n * Released under MIT license.\n */\n\n// prettier-ignore\n//                1 YYYY                2 MM        3 DD              4 HH     5 mm        6 ss           7 msec         8 Z 9 ±   10 tzHH    11 tzmm\nconst isoReg = /^(\\d{4}|[+-]\\d{6})(?:-?(\\d{2})(?:-?(\\d{2}))?)?(?:[ T]?(\\d{2}):?(\\d{2})(?::?(\\d{2})(?:[,.](\\d{1,}))?)?(?:(Z)|([+-])(\\d{2})(?::?(\\d{2}))?)?)?$/;\nfunction parseIsoDate(date) {\n  const struct = parseDateStruct(date);\n  if (!struct) return Date.parse ? Date.parse(date) : Number.NaN;\n\n  // timestamps without timezone identifiers should be considered local time\n  if (struct.z === undefined && struct.plusMinus === undefined) {\n    return new Date(struct.year, struct.month, struct.day, struct.hour, struct.minute, struct.second, struct.millisecond).valueOf();\n  }\n  let totalMinutesOffset = 0;\n  if (struct.z !== 'Z' && struct.plusMinus !== undefined) {\n    totalMinutesOffset = struct.hourOffset * 60 + struct.minuteOffset;\n    if (struct.plusMinus === '+') totalMinutesOffset = 0 - totalMinutesOffset;\n  }\n  return Date.UTC(struct.year, struct.month, struct.day, struct.hour, struct.minute + totalMinutesOffset, struct.second, struct.millisecond);\n}\nfunction parseDateStruct(date) {\n  var _regexResult$7$length, _regexResult$;\n  const regexResult = isoReg.exec(date);\n  if (!regexResult) return null;\n\n  // use of toNumber() avoids NaN timestamps caused by “undefined”\n  // values being passed to Date constructor\n  return {\n    year: toNumber(regexResult[1]),\n    month: toNumber(regexResult[2], 1) - 1,\n    day: toNumber(regexResult[3], 1),\n    hour: toNumber(regexResult[4]),\n    minute: toNumber(regexResult[5]),\n    second: toNumber(regexResult[6]),\n    millisecond: regexResult[7] ?\n    // allow arbitrary sub-second precision beyond milliseconds\n    toNumber(regexResult[7].substring(0, 3)) : 0,\n    precision: (_regexResult$7$length = (_regexResult$ = regexResult[7]) == null ? void 0 : _regexResult$.length) != null ? _regexResult$7$length : undefined,\n    z: regexResult[8] || undefined,\n    plusMinus: regexResult[9] || undefined,\n    hourOffset: toNumber(regexResult[10]),\n    minuteOffset: toNumber(regexResult[11])\n  };\n}\nfunction toNumber(str, defaultValue = 0) {\n  return Number(str) || defaultValue;\n}\n\n// Taken from HTML spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address\nlet rEmail =\n// eslint-disable-next-line\n/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\nlet rUrl =\n// eslint-disable-next-line\n/^((https?|ftp):)?\\/\\/(((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:)*@)?(((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]))|((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.?)(:\\d*)?)(\\/((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)+(\\/(([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)*)*)?)?(\\?((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|[\\uE000-\\uF8FF]|\\/|\\?)*)?(\\#((([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(%[\\da-f]{2})|[!\\$&'\\(\\)\\*\\+,;=]|:|@)|\\/|\\?)*)?$/i;\n\n// eslint-disable-next-line\nlet rUUID = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;\nlet yearMonthDay = '^\\\\d{4}-\\\\d{2}-\\\\d{2}';\nlet hourMinuteSecond = '\\\\d{2}:\\\\d{2}:\\\\d{2}';\nlet zOrOffset = '(([+-]\\\\d{2}(:?\\\\d{2})?)|Z)';\nlet rIsoDateTime = new RegExp(`${yearMonthDay}T${hourMinuteSecond}(\\\\.\\\\d+)?${zOrOffset}$`);\nlet isTrimmed = value => isAbsent(value) || value === value.trim();\nlet objStringTag = {}.toString();\nfunction create$6() {\n  return new StringSchema();\n}\nclass StringSchema extends Schema {\n  constructor() {\n    super({\n      type: 'string',\n      check(value) {\n        if (value instanceof String) value = value.valueOf();\n        return typeof value === 'string';\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce || ctx.isType(value)) return value;\n\n        // don't ever convert arrays\n        if (Array.isArray(value)) return value;\n        const strValue = value != null && value.toString ? value.toString() : value;\n\n        // no one wants plain objects converted to [Object object]\n        if (strValue === objStringTag) return value;\n        return strValue;\n      });\n    });\n  }\n  required(message) {\n    return super.required(message).withMutation(schema => schema.test({\n      message: message || mixed.required,\n      name: 'required',\n      skipAbsent: true,\n      test: value => !!value.length\n    }));\n  }\n  notRequired() {\n    return super.notRequired().withMutation(schema => {\n      schema.tests = schema.tests.filter(t => t.OPTIONS.name !== 'required');\n      return schema;\n    });\n  }\n  length(length, message = string.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message = string.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = string.max) {\n    return this.test({\n      name: 'max',\n      exclusive: true,\n      message,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  matches(regex, options) {\n    let excludeEmptyString = false;\n    let message;\n    let name;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          excludeEmptyString = false,\n          message,\n          name\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.test({\n      name: name || 'matches',\n      message: message || string.matches,\n      params: {\n        regex\n      },\n      skipAbsent: true,\n      test: value => value === '' && excludeEmptyString || value.search(regex) !== -1\n    });\n  }\n  email(message = string.email) {\n    return this.matches(rEmail, {\n      name: 'email',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  url(message = string.url) {\n    return this.matches(rUrl, {\n      name: 'url',\n      message,\n      excludeEmptyString: true\n    });\n  }\n  uuid(message = string.uuid) {\n    return this.matches(rUUID, {\n      name: 'uuid',\n      message,\n      excludeEmptyString: false\n    });\n  }\n  datetime(options) {\n    let message = '';\n    let allowOffset;\n    let precision;\n    if (options) {\n      if (typeof options === 'object') {\n        ({\n          message = '',\n          allowOffset = false,\n          precision = undefined\n        } = options);\n      } else {\n        message = options;\n      }\n    }\n    return this.matches(rIsoDateTime, {\n      name: 'datetime',\n      message: message || string.datetime,\n      excludeEmptyString: true\n    }).test({\n      name: 'datetime_offset',\n      message: message || string.datetime_offset,\n      params: {\n        allowOffset\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || allowOffset) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return !!struct.z;\n      }\n    }).test({\n      name: 'datetime_precision',\n      message: message || string.datetime_precision,\n      params: {\n        precision\n      },\n      skipAbsent: true,\n      test: value => {\n        if (!value || precision == undefined) return true;\n        const struct = parseDateStruct(value);\n        if (!struct) return false;\n        return struct.precision === precision;\n      }\n    });\n  }\n\n  //-- transforms --\n  ensure() {\n    return this.default('').transform(val => val === null ? '' : val);\n  }\n  trim(message = string.trim) {\n    return this.transform(val => val != null ? val.trim() : val).test({\n      message,\n      name: 'trim',\n      test: isTrimmed\n    });\n  }\n  lowercase(message = string.lowercase) {\n    return this.transform(value => !isAbsent(value) ? value.toLowerCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toLowerCase()\n    });\n  }\n  uppercase(message = string.uppercase) {\n    return this.transform(value => !isAbsent(value) ? value.toUpperCase() : value).test({\n      message,\n      name: 'string_case',\n      exclusive: true,\n      skipAbsent: true,\n      test: value => isAbsent(value) || value === value.toUpperCase()\n    });\n  }\n}\ncreate$6.prototype = StringSchema.prototype;\n\n//\n// String Interfaces\n//\n\nlet isNaN$1 = value => value != +value;\nfunction create$5() {\n  return new NumberSchema();\n}\nclass NumberSchema extends Schema {\n  constructor() {\n    super({\n      type: 'number',\n      check(value) {\n        if (value instanceof Number) value = value.valueOf();\n        return typeof value === 'number' && !isNaN$1(value);\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        if (!ctx.spec.coerce) return value;\n        let parsed = value;\n        if (typeof parsed === 'string') {\n          parsed = parsed.replace(/\\s/g, '');\n          if (parsed === '') return NaN;\n          // don't use parseFloat to avoid positives on alpha-numeric strings\n          parsed = +parsed;\n        }\n\n        // null -> NaN isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (ctx.isType(parsed) || parsed === null) return parsed;\n        return parseFloat(parsed);\n      });\n    });\n  }\n  min(min, message = number.min) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message = number.max) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(max);\n      }\n    });\n  }\n  lessThan(less, message = number.lessThan) {\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        less\n      },\n      skipAbsent: true,\n      test(value) {\n        return value < this.resolve(less);\n      }\n    });\n  }\n  moreThan(more, message = number.moreThan) {\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        more\n      },\n      skipAbsent: true,\n      test(value) {\n        return value > this.resolve(more);\n      }\n    });\n  }\n  positive(msg = number.positive) {\n    return this.moreThan(0, msg);\n  }\n  negative(msg = number.negative) {\n    return this.lessThan(0, msg);\n  }\n  integer(message = number.integer) {\n    return this.test({\n      name: 'integer',\n      message,\n      skipAbsent: true,\n      test: val => Number.isInteger(val)\n    });\n  }\n  truncate() {\n    return this.transform(value => !isAbsent(value) ? value | 0 : value);\n  }\n  round(method) {\n    var _method;\n    let avail = ['ceil', 'floor', 'round', 'trunc'];\n    method = ((_method = method) == null ? void 0 : _method.toLowerCase()) || 'round';\n\n    // this exists for symemtry with the new Math.trunc\n    if (method === 'trunc') return this.truncate();\n    if (avail.indexOf(method.toLowerCase()) === -1) throw new TypeError('Only valid options for round() are: ' + avail.join(', '));\n    return this.transform(value => !isAbsent(value) ? Math[method](value) : value);\n  }\n}\ncreate$5.prototype = NumberSchema.prototype;\n\n//\n// Number Interfaces\n//\n\nlet invalidDate = new Date('');\nlet isDate = obj => Object.prototype.toString.call(obj) === '[object Date]';\nfunction create$4() {\n  return new DateSchema();\n}\nclass DateSchema extends Schema {\n  constructor() {\n    super({\n      type: 'date',\n      check(v) {\n        return isDate(v) && !isNaN(v.getTime());\n      }\n    });\n    this.withMutation(() => {\n      this.transform((value, _raw, ctx) => {\n        // null -> InvalidDate isn't useful; treat all nulls as null and let it fail on\n        // nullability check vs TypeErrors\n        if (!ctx.spec.coerce || ctx.isType(value) || value === null) return value;\n        value = parseIsoDate(value);\n\n        // 0 is a valid timestamp equivalent to 1970-01-01T00:00:00Z(unix epoch) or before.\n        return !isNaN(value) ? new Date(value) : DateSchema.INVALID_DATE;\n      });\n    });\n  }\n  prepareParam(ref, name) {\n    let param;\n    if (!Reference.isRef(ref)) {\n      let cast = this.cast(ref);\n      if (!this._typeCheck(cast)) throw new TypeError(`\\`${name}\\` must be a Date or a value that can be \\`cast()\\` to a Date`);\n      param = cast;\n    } else {\n      param = ref;\n    }\n    return param;\n  }\n  min(min, message = date.min) {\n    let limit = this.prepareParam(min, 'min');\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      test(value) {\n        return value >= this.resolve(limit);\n      }\n    });\n  }\n  max(max, message = date.max) {\n    let limit = this.prepareParam(max, 'max');\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value <= this.resolve(limit);\n      }\n    });\n  }\n}\nDateSchema.INVALID_DATE = invalidDate;\ncreate$4.prototype = DateSchema.prototype;\ncreate$4.INVALID_DATE = invalidDate;\n\n// @ts-expect-error\nfunction sortFields(fields, excludedEdges = []) {\n  let edges = [];\n  let nodes = new Set();\n  let excludes = new Set(excludedEdges.map(([a, b]) => `${a}-${b}`));\n  function addNode(depPath, key) {\n    let node = propertyExpr.split(depPath)[0];\n    nodes.add(node);\n    if (!excludes.has(`${key}-${node}`)) edges.push([key, node]);\n  }\n  for (const key of Object.keys(fields)) {\n    let value = fields[key];\n    nodes.add(key);\n    if (Reference.isRef(value) && value.isSibling) addNode(value.path, key);else if (isSchema(value) && 'deps' in value) value.deps.forEach(path => addNode(path, key));\n  }\n  return toposort__default[\"default\"].array(Array.from(nodes), edges).reverse();\n}\n\nfunction findIndex(arr, err) {\n  let idx = Infinity;\n  arr.some((key, ii) => {\n    var _err$path;\n    if ((_err$path = err.path) != null && _err$path.includes(key)) {\n      idx = ii;\n      return true;\n    }\n  });\n  return idx;\n}\nfunction sortByKeyOrder(keys) {\n  return (a, b) => {\n    return findIndex(keys, a) - findIndex(keys, b);\n  };\n}\n\nconst parseJson = (value, _, ctx) => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  let parsed = value;\n  try {\n    parsed = JSON.parse(value);\n  } catch (err) {\n    /* */\n  }\n  return ctx.isType(parsed) ? parsed : value;\n};\n\n// @ts-ignore\nfunction deepPartial(schema) {\n  if ('fields' in schema) {\n    const partial = {};\n    for (const [key, fieldSchema] of Object.entries(schema.fields)) {\n      partial[key] = deepPartial(fieldSchema);\n    }\n    return schema.setFields(partial);\n  }\n  if (schema.type === 'array') {\n    const nextArray = schema.optional();\n    if (nextArray.innerType) nextArray.innerType = deepPartial(nextArray.innerType);\n    return nextArray;\n  }\n  if (schema.type === 'tuple') {\n    return schema.optional().clone({\n      types: schema.spec.types.map(deepPartial)\n    });\n  }\n  if ('optional' in schema) {\n    return schema.optional();\n  }\n  return schema;\n}\nconst deepHas = (obj, p) => {\n  const path = [...propertyExpr.normalizePath(p)];\n  if (path.length === 1) return path[0] in obj;\n  let last = path.pop();\n  let parent = propertyExpr.getter(propertyExpr.join(path), true)(obj);\n  return !!(parent && last in parent);\n};\nlet isObject = obj => Object.prototype.toString.call(obj) === '[object Object]';\nfunction unknown(ctx, value) {\n  let known = Object.keys(ctx.fields);\n  return Object.keys(value).filter(key => known.indexOf(key) === -1);\n}\nconst defaultSort = sortByKeyOrder([]);\nfunction create$3(spec) {\n  return new ObjectSchema(spec);\n}\nclass ObjectSchema extends Schema {\n  constructor(spec) {\n    super({\n      type: 'object',\n      check(value) {\n        return isObject(value) || typeof value === 'function';\n      }\n    });\n    this.fields = Object.create(null);\n    this._sortErrors = defaultSort;\n    this._nodes = [];\n    this._excludedEdges = [];\n    this.withMutation(() => {\n      if (spec) {\n        this.shape(spec);\n      }\n    });\n  }\n  _cast(_value, options = {}) {\n    var _options$stripUnknown;\n    let value = super._cast(_value, options);\n\n    //should ignore nulls here\n    if (value === undefined) return this.getDefault(options);\n    if (!this._typeCheck(value)) return value;\n    let fields = this.fields;\n    let strip = (_options$stripUnknown = options.stripUnknown) != null ? _options$stripUnknown : this.spec.noUnknown;\n    let props = [].concat(this._nodes, Object.keys(value).filter(v => !this._nodes.includes(v)));\n    let intermediateValue = {}; // is filled during the transform below\n    let innerOptions = Object.assign({}, options, {\n      parent: intermediateValue,\n      __validating: options.__validating || false\n    });\n    let isChanged = false;\n    for (const prop of props) {\n      let field = fields[prop];\n      let exists = (prop in value);\n      if (field) {\n        let fieldValue;\n        let inputValue = value[prop];\n\n        // safe to mutate since this is fired in sequence\n        innerOptions.path = (options.path ? `${options.path}.` : '') + prop;\n        field = field.resolve({\n          value: inputValue,\n          context: options.context,\n          parent: intermediateValue\n        });\n        let fieldSpec = field instanceof Schema ? field.spec : undefined;\n        let strict = fieldSpec == null ? void 0 : fieldSpec.strict;\n        if (fieldSpec != null && fieldSpec.strip) {\n          isChanged = isChanged || prop in value;\n          continue;\n        }\n        fieldValue = !options.__validating || !strict ?\n        // TODO: use _cast, this is double resolving\n        field.cast(value[prop], innerOptions) : value[prop];\n        if (fieldValue !== undefined) {\n          intermediateValue[prop] = fieldValue;\n        }\n      } else if (exists && !strip) {\n        intermediateValue[prop] = value[prop];\n      }\n      if (exists !== prop in intermediateValue || intermediateValue[prop] !== value[prop]) {\n        isChanged = true;\n      }\n    }\n    return isChanged ? intermediateValue : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let {\n      from = [],\n      originalValue = _value,\n      recursive = this.spec.recursive\n    } = options;\n    options.from = [{\n      schema: this,\n      value: originalValue\n    }, ...from];\n    // this flag is needed for handling `strict` correctly in the context of\n    // validation vs just casting. e.g strict() on a field is only used when validating\n    options.__validating = true;\n    options.originalValue = originalValue;\n    super._validate(_value, options, panic, (objectErrors, value) => {\n      if (!recursive || !isObject(value)) {\n        next(objectErrors, value);\n        return;\n      }\n      originalValue = originalValue || value;\n      let tests = [];\n      for (let key of this._nodes) {\n        let field = this.fields[key];\n        if (!field || Reference.isRef(field)) {\n          continue;\n        }\n        tests.push(field.asNestedTest({\n          options,\n          key,\n          parent: value,\n          parentPath: options.path,\n          originalParent: originalValue\n        }));\n      }\n      this.runTests({\n        tests,\n        value,\n        originalValue,\n        options\n      }, panic, fieldErrors => {\n        next(fieldErrors.sort(this._sortErrors).concat(objectErrors), value);\n      });\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    next.fields = Object.assign({}, this.fields);\n    next._nodes = this._nodes;\n    next._excludedEdges = this._excludedEdges;\n    next._sortErrors = this._sortErrors;\n    return next;\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n    let nextFields = next.fields;\n    for (let [field, schemaOrRef] of Object.entries(this.fields)) {\n      const target = nextFields[field];\n      nextFields[field] = target === undefined ? schemaOrRef : target;\n    }\n    return next.withMutation(s =>\n    // XXX: excludes here is wrong\n    s.setFields(nextFields, [...this._excludedEdges, ...schema._excludedEdges]));\n  }\n  _getDefault(options) {\n    if ('default' in this.spec) {\n      return super._getDefault(options);\n    }\n\n    // if there is no default set invent one\n    if (!this._nodes.length) {\n      return undefined;\n    }\n    let dft = {};\n    this._nodes.forEach(key => {\n      var _innerOptions;\n      const field = this.fields[key];\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      dft[key] = field && 'getDefault' in field ? field.getDefault(innerOptions) : undefined;\n    });\n    return dft;\n  }\n  setFields(shape, excludedEdges) {\n    let next = this.clone();\n    next.fields = shape;\n    next._nodes = sortFields(shape, excludedEdges);\n    next._sortErrors = sortByKeyOrder(Object.keys(shape));\n    // XXX: this carries over edges which may not be what you want\n    if (excludedEdges) next._excludedEdges = excludedEdges;\n    return next;\n  }\n  shape(additions, excludes = []) {\n    return this.clone().withMutation(next => {\n      let edges = next._excludedEdges;\n      if (excludes.length) {\n        if (!Array.isArray(excludes[0])) excludes = [excludes];\n        edges = [...next._excludedEdges, ...excludes];\n      }\n\n      // XXX: excludes here is wrong\n      return next.setFields(Object.assign(next.fields, additions), edges);\n    });\n  }\n  partial() {\n    const partial = {};\n    for (const [key, schema] of Object.entries(this.fields)) {\n      partial[key] = 'optional' in schema && schema.optional instanceof Function ? schema.optional() : schema;\n    }\n    return this.setFields(partial);\n  }\n  deepPartial() {\n    const next = deepPartial(this);\n    return next;\n  }\n  pick(keys) {\n    const picked = {};\n    for (const key of keys) {\n      if (this.fields[key]) picked[key] = this.fields[key];\n    }\n    return this.setFields(picked, this._excludedEdges.filter(([a, b]) => keys.includes(a) && keys.includes(b)));\n  }\n  omit(keys) {\n    const remaining = [];\n    for (const key of Object.keys(this.fields)) {\n      if (keys.includes(key)) continue;\n      remaining.push(key);\n    }\n    return this.pick(remaining);\n  }\n  from(from, to, alias) {\n    let fromGetter = propertyExpr.getter(from, true);\n    return this.transform(obj => {\n      if (!obj) return obj;\n      let newObj = obj;\n      if (deepHas(obj, from)) {\n        newObj = Object.assign({}, obj);\n        if (!alias) delete newObj[from];\n        newObj[to] = fromGetter(obj);\n      }\n      return newObj;\n    });\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n\n  /**\n   * Similar to `noUnknown` but only validates that an object is the right shape without stripping the unknown keys\n   */\n  exact(message) {\n    return this.test({\n      name: 'exact',\n      exclusive: true,\n      message: message || object.exact,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return unknownKeys.length === 0 || this.createError({\n          params: {\n            properties: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n  }\n  stripUnknown() {\n    return this.clone({\n      noUnknown: true\n    });\n  }\n  noUnknown(noAllow = true, message = object.noUnknown) {\n    if (typeof noAllow !== 'boolean') {\n      message = noAllow;\n      noAllow = true;\n    }\n    let next = this.test({\n      name: 'noUnknown',\n      exclusive: true,\n      message: message,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return !noAllow || unknownKeys.length === 0 || this.createError({\n          params: {\n            unknown: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n    next.spec.noUnknown = noAllow;\n    return next;\n  }\n  unknown(allow = true, message = object.noUnknown) {\n    return this.noUnknown(!allow, message);\n  }\n  transformKeys(fn) {\n    return this.transform(obj => {\n      if (!obj) return obj;\n      const result = {};\n      for (const key of Object.keys(obj)) result[fn(key)] = obj[key];\n      return result;\n    });\n  }\n  camelCase() {\n    return this.transformKeys(tinyCase.camelCase);\n  }\n  snakeCase() {\n    return this.transformKeys(tinyCase.snakeCase);\n  }\n  constantCase() {\n    return this.transformKeys(key => tinyCase.snakeCase(key).toUpperCase());\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.fields = {};\n    for (const [key, value] of Object.entries(next.fields)) {\n      var _innerOptions2;\n      let innerOptions = options;\n      if ((_innerOptions2 = innerOptions) != null && _innerOptions2.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[key]\n        });\n      }\n      base.fields[key] = value.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$3.prototype = ObjectSchema.prototype;\n\nfunction create$2(type) {\n  return new ArraySchema(type);\n}\nclass ArraySchema extends Schema {\n  constructor(type) {\n    super({\n      type: 'array',\n      spec: {\n        types: type\n      },\n      check(v) {\n        return Array.isArray(v);\n      }\n    });\n\n    // `undefined` specifically means uninitialized, as opposed to \"no subtype\"\n    this.innerType = void 0;\n    this.innerType = type;\n  }\n  _cast(_value, _opts) {\n    const value = super._cast(_value, _opts);\n\n    // should ignore nulls here\n    if (!this._typeCheck(value) || !this.innerType) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = value.map((v, idx) => {\n      const castElement = this.innerType.cast(v, Object.assign({}, _opts, {\n        path: `${_opts.path || ''}[${idx}]`\n      }));\n      if (castElement !== v) {\n        isChanged = true;\n      }\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    var _options$recursive;\n    // let sync = options.sync;\n    // let path = options.path;\n    let innerType = this.innerType;\n    // let endEarly = options.abortEarly ?? this.spec.abortEarly;\n    let recursive = (_options$recursive = options.recursive) != null ? _options$recursive : this.spec.recursive;\n    options.originalValue != null ? options.originalValue : _value;\n    super._validate(_value, options, panic, (arrayErrors, value) => {\n      var _options$originalValu2;\n      if (!recursive || !innerType || !this._typeCheck(value)) {\n        next(arrayErrors, value);\n        return;\n      }\n\n      // #950 Ensure that sparse array empty slots are validated\n      let tests = new Array(value.length);\n      for (let index = 0; index < value.length; index++) {\n        var _options$originalValu;\n        tests[index] = innerType.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(arrayErrors), value));\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    return next;\n  }\n\n  /** Parse an input JSON string to an object */\n  json() {\n    return this.transform(parseJson);\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n\n    // @ts-expect-error readonly\n    next.innerType = this.innerType;\n    if (schema.innerType)\n      // @ts-expect-error readonly\n      next.innerType = next.innerType ?\n      // @ts-expect-error Lazy doesn't have concat and will break\n      next.innerType.concat(schema.innerType) : schema.innerType;\n    return next;\n  }\n  of(schema) {\n    // FIXME: this should return a new instance of array without the default to be\n    let next = this.clone();\n    if (!isSchema(schema)) throw new TypeError('`array.of()` sub-schema must be a valid yup schema not: ' + printValue(schema));\n\n    // @ts-expect-error readonly\n    next.innerType = schema;\n    next.spec = Object.assign({}, next.spec, {\n      types: schema\n    });\n    return next;\n  }\n  length(length, message = array.length) {\n    return this.test({\n      message,\n      name: 'length',\n      exclusive: true,\n      params: {\n        length\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length === this.resolve(length);\n      }\n    });\n  }\n  min(min, message) {\n    message = message || array.min;\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      skipAbsent: true,\n      // FIXME(ts): Array<typeof T>\n      test(value) {\n        return value.length >= this.resolve(min);\n      }\n    });\n  }\n  max(max, message) {\n    message = message || array.max;\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      skipAbsent: true,\n      test(value) {\n        return value.length <= this.resolve(max);\n      }\n    });\n  }\n  ensure() {\n    return this.default(() => []).transform((val, original) => {\n      // We don't want to return `null` for nullable schema\n      if (this._typeCheck(val)) return val;\n      return original == null ? [] : [].concat(original);\n    });\n  }\n  compact(rejector) {\n    let reject = !rejector ? v => !!v : (v, i, a) => !rejector(v, i, a);\n    return this.transform(values => values != null ? values.filter(reject) : values);\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    if (next.innerType) {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[0]\n        });\n      }\n      base.innerType = next.innerType.describe(innerOptions);\n    }\n    return base;\n  }\n}\ncreate$2.prototype = ArraySchema.prototype;\n\n// @ts-ignore\nfunction create$1(schemas) {\n  return new TupleSchema(schemas);\n}\nclass TupleSchema extends Schema {\n  constructor(schemas) {\n    super({\n      type: 'tuple',\n      spec: {\n        types: schemas\n      },\n      check(v) {\n        const types = this.spec.types;\n        return Array.isArray(v) && v.length === types.length;\n      }\n    });\n    this.withMutation(() => {\n      this.typeError(tuple.notType);\n    });\n  }\n  _cast(inputValue, options) {\n    const {\n      types\n    } = this.spec;\n    const value = super._cast(inputValue, options);\n    if (!this._typeCheck(value)) {\n      return value;\n    }\n    let isChanged = false;\n    const castArray = types.map((type, idx) => {\n      const castElement = type.cast(value[idx], Object.assign({}, options, {\n        path: `${options.path || ''}[${idx}]`\n      }));\n      if (castElement !== value[idx]) isChanged = true;\n      return castElement;\n    });\n    return isChanged ? castArray : value;\n  }\n  _validate(_value, options = {}, panic, next) {\n    let itemTypes = this.spec.types;\n    super._validate(_value, options, panic, (tupleErrors, value) => {\n      var _options$originalValu2;\n      // intentionally not respecting recursive\n      if (!this._typeCheck(value)) {\n        next(tupleErrors, value);\n        return;\n      }\n      let tests = [];\n      for (let [index, itemSchema] of itemTypes.entries()) {\n        var _options$originalValu;\n        tests[index] = itemSchema.asNestedTest({\n          options,\n          index,\n          parent: value,\n          parentPath: options.path,\n          originalParent: (_options$originalValu = options.originalValue) != null ? _options$originalValu : _value\n        });\n      }\n      this.runTests({\n        value,\n        tests,\n        originalValue: (_options$originalValu2 = options.originalValue) != null ? _options$originalValu2 : _value,\n        options\n      }, panic, innerTypeErrors => next(innerTypeErrors.concat(tupleErrors), value));\n    });\n  }\n  describe(options) {\n    const next = (options ? this.resolve(options) : this).clone();\n    const base = super.describe(options);\n    base.innerType = next.spec.types.map((schema, index) => {\n      var _innerOptions;\n      let innerOptions = options;\n      if ((_innerOptions = innerOptions) != null && _innerOptions.value) {\n        innerOptions = Object.assign({}, innerOptions, {\n          parent: innerOptions.value,\n          value: innerOptions.value[index]\n        });\n      }\n      return schema.describe(innerOptions);\n    });\n    return base;\n  }\n}\ncreate$1.prototype = TupleSchema.prototype;\n\nfunction create(builder) {\n  return new Lazy(builder);\n}\nfunction catchValidationError(fn) {\n  try {\n    return fn();\n  } catch (err) {\n    if (ValidationError.isError(err)) return Promise.reject(err);\n    throw err;\n  }\n}\nclass Lazy {\n  constructor(builder) {\n    this.type = 'lazy';\n    this.__isYupSchema__ = true;\n    this.spec = void 0;\n    this._resolve = (value, options = {}) => {\n      let schema = this.builder(value, options);\n      if (!isSchema(schema)) throw new TypeError('lazy() functions must return a valid schema');\n      if (this.spec.optional) schema = schema.optional();\n      return schema.resolve(options);\n    };\n    this.builder = builder;\n    this.spec = {\n      meta: undefined,\n      optional: false\n    };\n  }\n  clone(spec) {\n    const next = new Lazy(this.builder);\n    next.spec = Object.assign({}, this.spec, spec);\n    return next;\n  }\n  optionality(optional) {\n    const next = this.clone({\n      optional\n    });\n    return next;\n  }\n  optional() {\n    return this.optionality(true);\n  }\n  resolve(options) {\n    return this._resolve(options.value, options);\n  }\n  cast(value, options) {\n    return this._resolve(value, options).cast(value, options);\n  }\n  asNestedTest(config) {\n    let {\n      key,\n      index,\n      parent,\n      options\n    } = config;\n    let value = parent[index != null ? index : key];\n    return this._resolve(value, Object.assign({}, options, {\n      value,\n      parent\n    })).asNestedTest(config);\n  }\n  validate(value, options) {\n    return catchValidationError(() => this._resolve(value, options).validate(value, options));\n  }\n  validateSync(value, options) {\n    return this._resolve(value, options).validateSync(value, options);\n  }\n  validateAt(path, value, options) {\n    return catchValidationError(() => this._resolve(value, options).validateAt(path, value, options));\n  }\n  validateSyncAt(path, value, options) {\n    return this._resolve(value, options).validateSyncAt(path, value, options);\n  }\n  isValid(value, options) {\n    try {\n      return this._resolve(value, options).isValid(value, options);\n    } catch (err) {\n      if (ValidationError.isError(err)) {\n        return Promise.resolve(false);\n      }\n      throw err;\n    }\n  }\n  isValidSync(value, options) {\n    return this._resolve(value, options).isValidSync(value, options);\n  }\n  describe(options) {\n    return options ? this.resolve(options).describe(options) : {\n      type: 'lazy',\n      meta: this.spec.meta,\n      label: undefined\n    };\n  }\n  meta(...args) {\n    if (args.length === 0) return this.spec.meta;\n    let next = this.clone();\n    next.spec.meta = Object.assign(next.spec.meta || {}, args[0]);\n    return next;\n  }\n}\n\nfunction setLocale(custom) {\n  Object.keys(custom).forEach(type => {\n    // @ts-ignore\n    Object.keys(custom[type]).forEach(method => {\n      // @ts-ignore\n      locale[type][method] = custom[type][method];\n    });\n  });\n}\n\nfunction addMethod(schemaType, name, fn) {\n  if (!schemaType || !isSchema(schemaType.prototype)) throw new TypeError('You must provide a yup schema constructor function');\n  if (typeof name !== 'string') throw new TypeError('A Method name must be provided');\n  if (typeof fn !== 'function') throw new TypeError('Method function must be provided');\n  schemaType.prototype[name] = fn;\n}\n\nexports.ArraySchema = ArraySchema;\nexports.BooleanSchema = BooleanSchema;\nexports.DateSchema = DateSchema;\nexports.LazySchema = Lazy;\nexports.MixedSchema = MixedSchema;\nexports.NumberSchema = NumberSchema;\nexports.ObjectSchema = ObjectSchema;\nexports.Schema = Schema;\nexports.StringSchema = StringSchema;\nexports.TupleSchema = TupleSchema;\nexports.ValidationError = ValidationError;\nexports.addMethod = addMethod;\nexports.array = create$2;\nexports.bool = create$7;\nexports.boolean = create$7;\nexports.date = create$4;\nexports.defaultLocale = locale;\nexports.getIn = getIn;\nexports.isSchema = isSchema;\nexports.lazy = create;\nexports.mixed = create$8;\nexports.number = create$5;\nexports.object = create$3;\nexports.printValue = printValue;\nexports.reach = reach;\nexports.ref = create$9;\nexports.setLocale = setLocale;\nexports.string = create$6;\nexports.tuple = create$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yup/index.js\n");

/***/ })

};
;