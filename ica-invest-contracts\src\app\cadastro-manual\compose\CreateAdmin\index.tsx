
import SelectCustom from "@/components/SelectCustom"
import { useState } from "react";
import SelectSearch from "@/components/SelectSearch";
import { getUserProfile } from "@/functions/getUserData";
import {  useQuery } from "@tanstack/react-query";
import api from "@/core/api";
import AdminRegisterPj from "./registerForms/AdminRegisterPJ";
import AdminRegisterPf from "./registerForms/AdminRegisterPF";



export const AdminCreate = () => {
    const [typeAccount, setTypeAccount] = useState('pf')
    const userProfile = getUserProfile();


    return (
        <div>
            <div className="m-3">
            <p className="text-xl text-white mb-5">Cadastro de Admin / Gestor de carteira</p>
                <div className=' mb-5 flex items-center gap-4'>
                    <div className="mb-5">
                        <p className="text-white mb-1">Tipo de conta</p>
                        <SelectCustom
                            value={typeAccount}
                            onChange={({ target }) => setTypeAccount(target.value)}
                        >
                            <option value={'pf'}>Pessoa Física</option>
                            <option value={'pj'}>Pessoa Jurídica</option>
                        </SelectCustom>
                    </div>
                </div>
                
            </div>

            {typeAccount === 'pj' ? <AdminRegisterPj typeCreate="admin" /> : <AdminRegisterPf  typeCreate="admin" />}
        </div>
    );

}