import type {
  HttpClient,
  HttpResponse,
} from '@/application/interfaces/http-client'

export class FetchHttpClient implements HttpClient {
  private buildQueryParams(params?: Record<string, string | number>): string {
    if (!params) return ''
    const query = new URLSearchParams()
    for (const [key, value] of Object.entries(params)) {
      query.append(key, value.toString())
    }
    return `?${query.toString()}`
  }

  private async parseResponse<T>(
    response: Response,
    responseType = 'json'
  ): Promise<HttpResponse<T>> {
    let data: any

    switch (responseType) {
      case 'arraybuffer':
        data = Buffer.from(await response.arrayBuffer())
        break
      case 'text':
        data = await response.text()
        break
      case 'json':
        data = await response.json()
        break
      default:
        data = await response.json()
        break
    }

    return {
      statusCode: response.status,
      data: data as T,
      headers: Object.fromEntries(response.headers.entries()),
    }
  }

  async get<TResult>(
    url: string,
    options?: {
      headers?: Record<string, string>
      params?: Record<string, string | number>
    }
  ): Promise<HttpResponse<TResult>> {
    const fullUrl = url + this.buildQueryParams(options?.params)
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: options?.headers,
    })

    return this.parseResponse<TResult>(response)
  }

  async post<TData, TResult>(
    url: string,
    body: TData,
    options?: { headers?: Record<string, string>; responseType?: string }
  ): Promise<HttpResponse<TResult>> {
    const requestBody =
      body instanceof FormData ||
      body instanceof Blob ||
      typeof body === 'string'
        ? body
        : JSON.stringify(body)

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        ...options?.headers,
      },
      body: requestBody,
    })

    return this.parseResponse<TResult>(response, options?.responseType)
  }

  async put<TData, TResult>(
    url: string,
    body: TData,
    options?: { headers?: Record<string, string> }
  ): Promise<HttpResponse<TResult>> {
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      body: JSON.stringify(body),
    })

    return this.parseResponse<TResult>(response)
  }

  async patch<TData, TResult>(
    url: string,
    body: TData,
    options?: { headers?: Record<string, string> }
  ): Promise<HttpResponse<TResult>> {
    const response = await fetch(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      body: JSON.stringify(body),
    })

    return this.parseResponse<TResult>(response)
  }

  async delete<TResult>(
    url: string,
    options?: {
      headers?: Record<string, string>
      params?: Record<string, string | number>
    }
  ): Promise<HttpResponse<TResult>> {
    const fullUrl = url + this.buildQueryParams(options?.params)

    const response = await fetch(fullUrl, {
      method: 'DELETE',
      headers: options?.headers,
    })

    return this.parseResponse<TResult>(response)
  }
}
