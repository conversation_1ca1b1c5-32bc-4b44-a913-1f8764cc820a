"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAccountCelcoinWebhookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const AWS = require("aws-sdk");
const kyc_celcoin_service_1 = require("../../../apis/celcoin/services/kyc-celcoin.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const document_entity_1 = require("../../../shared/database/typeorm/entities/document.entity");
const account_status_enum_1 = require("../../../shared/enums/account-status.enum");
const typeorm_2 = require("typeorm");
let CreateAccountCelcoinWebhookService = class CreateAccountCelcoinWebhookService {
    constructor(accountDb, documentDb, apiCelcoin) {
        this.accountDb = accountDb;
        this.documentDb = documentDb;
        this.apiCelcoin = apiCelcoin;
    }
    async perform(data) {
        if (data.status === 'CONFIRMED') {
            await this.accountDb.update({
                externalId: data.body.onboardingId,
            }, {
                number: data.body.account.account,
                branch: data.body.account.branch,
                status: account_status_enum_1.AccountStatusEnum.KYC_PENDING,
            });
            const account = await this.accountDb.findOne({
                relations: {
                    owner: true,
                    business: {
                        ownerBusinessRelation: {
                            owner: true,
                        },
                    },
                },
                where: {
                    externalId: data.body.onboardingId,
                },
            });
            const sendDocument = await this.documentDb.findOne({
                where: {
                    accountId: account.id,
                    sent: false,
                },
            });
            const s3 = new AWS.S3({
                accessKeyId: process.env.AWS_ACCESS_KEY,
                secretAccessKey: process.env.AWS_SECRET_KEY,
                region: process.env.AWS_REGION,
            });
            const document = account.type === 'physical'
                ? account.owner.cpf
                : account.business.ownerBusinessRelation[0].owner.cpf;
            const front = await s3
                .getObject({
                Bucket: process.env.S3_BUCKET,
                Key: sendDocument.front,
            })
                .promise();
            const back = await s3
                .getObject({
                Bucket: process.env.S3_BUCKET,
                Key: sendDocument.back,
            })
                .promise();
            await this.apiCelcoin
                .sendDocuments({
                documentnumber: document,
                filetype: sendDocument.type,
                front: front.Body,
                verse: back.Body,
                cnpj: account.business ? account.business.cnpj : undefined,
            })
                .catch(async () => {
                await this.accountDb.update(account.id, {
                    status: account_status_enum_1.AccountStatusEnum.REPROVED,
                });
            });
            if (account.type === 'business') {
                const socialContract = await s3
                    .getObject({
                    Bucket: process.env.S3_BUCKET,
                    Key: sendDocument.socialContract,
                })
                    .promise();
                await this.apiCelcoin.sendDocuments({
                    documentnumber: account.business.cnpj,
                    filetype: 'CONTRATO_SOCIAL',
                    front: socialContract.Body,
                    cnpj: account.business.cnpj,
                });
                if (sendDocument.cardCNPJ) {
                    const cardCnpj = await s3
                        .getObject({
                        Bucket: process.env.S3_BUCKET,
                        Key: sendDocument.cardCNPJ,
                    })
                        .promise();
                    await this.apiCelcoin.sendDocuments({
                        documentnumber: account.business.cnpj,
                        filetype: 'CARTAO_CNPJ',
                        front: cardCnpj.Body,
                        cnpj: account.business.cnpj,
                    });
                }
            }
            await this.documentDb.update(sendDocument.id, {
                sent: true,
            });
        }
    }
};
exports.CreateAccountCelcoinWebhookService = CreateAccountCelcoinWebhookService;
exports.CreateAccountCelcoinWebhookService = CreateAccountCelcoinWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(document_entity_1.DocumentEntity)),
    __param(2, (0, common_1.Inject)(kyc_celcoin_service_1.KYCCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        kyc_celcoin_service_1.KYCCelcoinService])
], CreateAccountCelcoinWebhookService);
//# sourceMappingURL=create-account-celcoin-webhook.service.js.map