{"version": 3, "file": "find-admin-super-admin.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/find-admin-super-admin.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,yFAAgF;AAChF,qCAAqC;AAK9B,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,YAEU,eAAwC;QAAxC,oBAAe,GAAf,eAAe,CAAyB;IAC/C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAA4B;QACxC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YAC/C,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE;YACjC,SAAS,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;SACvC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,0BAAiB,CAAC,iBAAiB,KAAK,CAAC,OAAO,aAAa,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,QAAQ,GAAG;YACf,KAAK,EAAE;gBACL,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,MAAM,EAAE,EAAE;aACX;SACF,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG;gBAChB,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,IAAI;gBAChC,GAAG,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,IAAI,IAAI;gBAC9B,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,IAAI,IAAI;gBAClC,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,IAAI,IAAI;gBAClC,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,SAAS,IAAI,IAAI;aAC3C,CAAC;YAEF,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAxCY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;qCACL,oBAAU;GAH1B,yBAAyB,CAwCrC", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AdminEntity } from 'src/shared/database/typeorm/entities/admin.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { GetAdminSuperAdminDto } from '../dto/get-admin-super-admin-request.dto';\r\n\r\n@Injectable()\r\nexport class GetAdminSuperAdminService {\r\n  constructor(\r\n    @InjectRepository(AdminEntity)\r\n    private adminRepository: Repository<AdminEntity>,\r\n  ) {}\r\n\r\n  async perform(input: GetAdminSuperAdminDto) {\r\n    const admin = await this.adminRepository.findOne({\r\n      where: { ownerId: input.ownerId },\r\n      relations: { broker: { owner: true } },\r\n    });\r\n\r\n    if (!admin) {\r\n      throw new NotFoundException(`Admin with ID ${input.ownerId} not found.`);\r\n    }\r\n\r\n    const response = {\r\n      admin: {\r\n        adminId: admin.id,\r\n        ownerId: admin.ownerId,\r\n        broker: [],\r\n      },\r\n    };\r\n\r\n    for (const broker of admin.broker) {\r\n      const brokerObj = {\r\n        brokerId: broker.id,\r\n        ownerId: broker.ownerId,\r\n        name: broker.owner?.name || null,\r\n        cpf: broker.owner?.cpf || null,\r\n        phone: broker.owner?.phone || null,\r\n        email: broker.owner?.email || null,\r\n        createdAt: broker.owner?.createdAt || null,\r\n      };\r\n\r\n      response.admin.broker.push(brokerObj);\r\n    }\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}