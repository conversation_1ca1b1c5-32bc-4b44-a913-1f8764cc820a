import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';
import { ExportReportRechargesDto } from '../dto/export-report-recharges.dto';
interface ITableData {
    name: string;
    amount: string;
    date: string;
    document: string;
}
export declare class ExportReportRechargesService {
    private accountDb;
    constructor(accountDb: Repository<AccountEntity>);
    perform(data: ExportReportRechargesDto): Promise<string>;
    generateHtmlTable(data: ITableData[]): string;
}
export {};
