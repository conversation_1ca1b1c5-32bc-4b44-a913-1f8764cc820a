/* eslint-disable no-param-reassign */
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';

import { IUpdateAccountLimitDto } from '../dto/update-account-limit.dto';

@Injectable()
export class UpdateAccountLimitService {
  constructor(
    @InjectRepository(AccountEntity)
    private readonly accountRepo: Repository<AccountEntity>,
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
  ) {}

  async execute(input: IUpdateAccountLimitDto, ownerId: string) {
    this.validateInput(input);

    const account = await this.accountRepo.findOne({
      where: [{ businessId: ownerId }, { ownerId }],
    });

    if (!account) throw new NotFoundException('Conta não encontrada!');

    let accountLimits: AccountTransferLimitEntity;
    // eslint-disable-next-line prefer-const
    accountLimits = await this.accountLimitRepo.findOneBy({
      accountId: account.id,
    });

    if (!accountLimits)
      Object.assign(accountLimits, {} as AccountTransferLimitEntity);

    if (input.dailyLimit) accountLimits.dailyLimit = input.dailyLimit;
    if (input.dailyNightLimit)
      accountLimits.dailyNightLimit = input.dailyNightLimit;
    if (input.monthlyLimit) accountLimits.monthlyLimit = input.monthlyLimit;

    accountLimits.defaultValues = JSON.stringify({
      dailyLimit: input.dailyLimit,
      dailyNightLimit: input.dailyNightLimit,
      monthlyLimit: input.monthlyLimit,
    });

    await this.accountLimitRepo.save(accountLimits);
  }

  private async updateAccountLimits(
    accountLimits: AccountTransferLimitEntity,
    input: IUpdateAccountLimitDto,
  ): Promise<void> {
    if (input.dailyLimit && input.dailyLimit > accountLimits.dailyLimit)
      accountLimits.dailyLimit = input.dailyLimit;

    if (
      input.dailyNightLimit &&
      input.dailyNightLimit > accountLimits.dailyNightLimit
    )
      accountLimits.dailyNightLimit = input.dailyNightLimit;
    if (input.monthlyLimit && input.monthlyLimit > accountLimits.monthlyLimit)
      accountLimits.monthlyLimit = input.monthlyLimit;

    await this.accountLimitRepo.update(accountLimits.id, {
      ...accountLimits,
    });
  }

  private validateInput({
    dailyLimit,
    dailyNightLimit,
    monthlyLimit,
  }: IUpdateAccountLimitDto): void {
    if (dailyLimit && dailyNightLimit && monthlyLimit) {
      if (dailyNightLimit > monthlyLimit)
        throw new BadRequestException(
          'Limite noturno não pode ser maior que limite mensal!',
        );
      if (dailyLimit > monthlyLimit)
        throw new BadRequestException(
          'Limite diario não pode ser maior que limite mensal!',
        );

      if (dailyNightLimit > dailyLimit)
        throw new BadRequestException(
          'Limite noturno não pode ser maior que limite diario!',
        );
    }
  }
}
