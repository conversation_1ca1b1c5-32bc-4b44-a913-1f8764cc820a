"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuperAdminEditInvestorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const get_contract_by_investor_service_1 = require("../../contract/services/get-contract-by-investor.service");
const pre_register_entity_1 = require("../../../shared/database/typeorm/entities/pre-register.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const edit_investor_service_1 = require("../../investor/services/edit-investor.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
let SuperAdminEditInvestorService = class SuperAdminEditInvestorService {
    constructor(ownerRoleRelationRepository, preRegisterRepository, accountRepository, getContractsByInvestorService, editInvestorService) {
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.preRegisterRepository = preRegisterRepository;
        this.accountRepository = accountRepository;
        this.getContractsByInvestorService = getContractsByInvestorService;
        this.editInvestorService = editInvestorService;
        this.updateBank = async (payload) => {
            const userProfile = await this.ownerRoleRelationRepository.findOne({
                where: { id: payload.ownerRoleRelationId },
                relations: {
                    owner: { address: true, account: true },
                    business: { address: true, account: true },
                    role: true,
                },
            });
            if (!userProfile) {
                throw new common_1.NotFoundException(`Usuario nāo encontrado.`);
            }
            const account = userProfile.owner.account[0];
            if (!account) {
                throw new common_1.NotFoundException(`Conta bancária não encontrada.`);
            }
            await this.accountRepository.update(account.id, {
                bank: payload.bank.bank,
                number: payload.bank.accountNumber,
                branch: payload.bank.branch,
            });
            return {
                message: 'Conta bancária atualizada com sucesso',
            };
        };
    }
    async perform(payload, userId) {
        if (payload.bank) {
            await this.updateBank(payload);
        }
        return this.editInvestorService.perform(payload, userId);
    }
};
exports.SuperAdminEditInvestorService = SuperAdminEditInvestorService;
exports.SuperAdminEditInvestorService = SuperAdminEditInvestorService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(pre_register_entity_1.PreRegisterEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        get_contract_by_investor_service_1.GetContractsByInvestorService,
        edit_investor_service_1.EditInvestorService])
], SuperAdminEditInvestorService);
//# sourceMappingURL=super-admin-edit-investor.service.js.map