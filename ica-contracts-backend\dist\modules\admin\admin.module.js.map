{"version": 3, "file": "admin.module.js", "sourceRoot": "/", "sources": ["modules/admin/admin.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,8DAAwD;AAExD,iEAA6D;AAC7D,oEAAgE;AAChE,oEAAqE;AACrE,sEAAiE;AACjE,oEAA+D;AAC/D,kHAA0G;AAC1G,gFAAgF;AAChF,sFAAgF;AAChF,4FAAsF;AACtF,sFAAgF;AAChF,4FAAsF;AACtF,6CAAgD;AAChD,qEAAiE;AACjE,8EAA8E;AAmBvE,IAAM,WAAW,GAAjB,MAAM,WAAW;CAAG,CAAA;AAAd,kCAAW;sBAAX,WAAW;IAlBvB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,4BAAY;YACZ,gCAAc;YACd,4BAAa;YACb,uBAAa,CAAC,UAAU,CAAC,CAAC,0BAAW,EAAE,gCAAc,EAAE,0BAAW,EAAE,gCAAc,CAAC,CAAC;SACrF;QACD,SAAS,EAAE;YACT,mCAAe;YACf,yCAAqB;YACrB,qCAAgB;YAChB,8EAAmC;YACnC,oDAA0B;YAC1B,kDAAyB;SAC1B;QACD,WAAW,EAAE,CAAC,kCAAe,CAAC;QAC9B,OAAO,EAAE,EAAE;KACZ,CAAC;GACW,WAAW,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { InvestorModule } from '../investor/investor.module';\r\nimport { AdminController } from './controller/admin.controller';\r\nimport { AdminDashboardService } from './services/dashboard.service';\r\nimport { EditAdminService } from './services/edit-admin.service';\r\nimport { GetAdminService } from './services/get-admin.service';\r\nimport { GetAdminContractsGrowthChartService } from './services/get-admin-contracts-growth-chart.service';\r\nimport { ChangePasswordAdminService } from './services/change-password.service';\r\nimport { EmailEntity } from 'src/shared/database/typeorm/entities/email.entity';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { EmailNgModule } from 'src/shared/email-ng/email.module';\r\nimport { ListContractsAdminService } from './services/list-contracts.service';\r\n@Module({\r\n  imports: [\r\n    SharedModule,\r\n    InvestorModule,\r\n    EmailNgModule,\r\n    TypeOrmModule.forFeature([OwnerEntity, BusinessEntity, EmailEntity, ContractEntity]),\r\n  ],\r\n  providers: [\r\n    GetAdminService,\r\n    AdminDashboardService,\r\n    EditAdminService,\r\n    GetAdminContractsGrowthChartService,\r\n    ChangePasswordAdminService,\r\n    ListContractsAdminService,\r\n  ],\r\n  controllers: [AdminController],\r\n  exports: [],\r\n})\r\nexport class AdminModule {}\r\n"]}