import { type Either, left, right } from '@/domain/shared'

export class ContractType {
  private static readonly allowedValues = ['mutuo', 'scp'] as const
  private constructor(
    private readonly value: (typeof ContractType.allowedValues)[number]
  ) {}

  static create(value: string): Either<Error, ContractType> {
    const normalized = value.toLowerCase()

    if (!ContractType.allowedValues.includes(normalized as any)) {
      return left(new Error(`Invalid contract type: ${value}`))
    }

    return right(
      new ContractType(
        normalized as (typeof ContractType.allowedValues)[number]
      )
    )
  }

  static mutuo(): ContractType {
    return new ContractType('mutuo')
  }

  static scp(): ContractType {
    return new ContractType('scp')
  }

  isMutuo(): boolean {
    return this.value === 'mutuo'
  }

  isScp(): boolean {
    return this.value === 'scp'
  }

  toString(): string {
    return this.value
  }

  get valueAsString(): string {
    return this.value
  }
}
