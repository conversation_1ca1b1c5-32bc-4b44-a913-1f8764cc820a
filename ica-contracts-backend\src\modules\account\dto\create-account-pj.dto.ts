import { <PERSON><PERSON>NPJ } from 'brazilian-class-validator';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsDefined,
  IsEmail,
  IsEnum,
  IsObject,
  IsOptional,
  IsPhoneNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { RolesEnum } from 'src/shared/enums/roles.enum';

class Address {
  @IsDefined()
  @IsString()
  cep: string;

  @IsDefined()
  @IsString()
  city: string;

  @IsDefined()
  @IsString()
  state: string;

  @IsDefined()
  @IsString()
  neighborhood: string;

  @IsDefined()
  @IsString()
  street: string;

  @IsOptional()
  @IsString()
  complement?: string;

  @IsDefined()
  @IsString()
  number: string;

  @IsOptional()
  @IsString()
  longitude: string;

  @IsOptional()
  @IsString()
  latitude: string;
}

class Owner {
  @IsDefined()
  @IsDateString()
  birthDate: Date;

  @IsDefined()
  @IsString()
  socialName: string;

  @IsDefined()
  @IsString()
  fullName: string;

  @IsDefined()
  @IsString()
  cpf: string;

  @IsDefined()
  @IsString()
  @IsEmail()
  email: string;

  @IsDefined()
  @IsString()
  @IsPhoneNumber()
  phoneNumber: string;

  @IsDefined()
  @IsString()
  motherName: string;

  @IsDefined()
  @IsBoolean()
  pep: boolean;

  @IsDefined()
  @IsObject()
  @ValidateNested()
  @Type(() => Address)
  address: Address;
}

export class CreateAccountPjDto {
  @IsDefined()
  @IsString()
  fantasyName: string;

  @IsDefined()
  @IsString()
  @IsEnum(RolesEnum)
  role: RolesEnum;

  @IsDefined()
  @IsString()
  @IsCNPJ()
  cnpj: string;

  @IsDefined()
  @IsString()
  companyName: string;

  @IsDefined()
  @IsBoolean()
  isTaxable: boolean;

  @IsDefined()
  @IsString()
  @IsPhoneNumber()
  phoneNumber: string;

  @IsDefined()
  @IsDateString()
  dtOpening: Date;

  @IsDefined()
  @IsString()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  password: string;

  @IsDefined()
  @IsString()
  type: string;

  @IsDefined()
  @IsString()
  size: string;

  @IsDefined()
  @IsObject()
  @ValidateNested()
  @Type(() => Address)
  address: Address;

  @IsDefined()
  @IsObject()
  @ValidateNested()
  @Type(() => Owner)
  owner: Owner;

  @IsDefined()
  @IsBoolean()
  registerPixKey: boolean;
}
