import { GenerateReportDto } from '../dto/generate-report.dto';
import { ReportService } from '../services/report.service';
import type { IRequestUser } from 'src/shared/interfaces/request-user.interface';
export declare class ReportsController {
    private readonly reportService;
    constructor(reportService: ReportService);
    generateReport(queryParams: GenerateReportDto, roleId: string, request: IRequestUser): Promise<{
        url: string;
    }>;
}
