import {
  InvestmentContract,
  type InvestmentContractProps,
} from '@/domain/entities/contracts'
import { type Either, left } from '@/domain/shared'
import { BankAccount, PaymentMethod } from '@/domain/value-objects'
import { ContractType } from '@/domain/value-objects/contract-type.value-object'
import { Money } from '@/domain/value-objects/money.value-object'
import { Percentage } from '@/domain/value-objects/percentage.value-object'
import { Profile } from '@/domain/value-objects/profile.value-object'
import type { Prisma } from '@prisma/client'
import { PrismaAddendumMapper } from './addendum.mapper'
import { AdvisorMapper } from './advisor.mapper'
import { BrokerMapper } from './broker.mapper'
import { InvestorMapper } from './investor.mapper'

export type ContractWithRelations = Prisma.contractGetPayload<{
  include: {
    pre_register: true
    contract_advisor: {
      include: {
        owner_role_relation: {
          include: {
            owner: {
              include: { address: true; account: true }
            }
            business: {
              include: {
                address: true
                account: true
                owner_business_relation: {
                  include: {
                    owner: {
                      include: { address: true }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    owner_role_relation_contract_investor_idToowner_role_relation: {
      include: {
        income_report: {
          include: {
            files: true
          }
        }
        pre_register: true
        owner: {
          include: { address: true; account: true }
        }
        business: {
          include: {
            address: true
            account: true
            owner_business_relation: {
              include: {
                owner: {
                  include: { address: true }
                }
              }
            }
          }
        }
      }
    }
    owner_role_relation_contract_owner_role_relationToowner_role_relation: {
      include: {
        owner: {
          include: { address: true; account: true }
        }
        business: {
          include: {
            address: true
            account: true
            owner_business_relation: {
              include: {
                owner: {
                  include: { address: true }
                }
              }
            }
          }
        }
      }
    }
    addendum: {
      include: {
        income_payment_scheduled_addendum: {
          include: {
            income_payment_scheduled: {
              select: {
                scheduled_date: true
              }
            }
          }
        }
      }
      where: {
        status: 'FULLY_SIGNED'
      }
    }
  }
}>
export class DetailsIncomeReportMapper {
  static toDomain(
    raw: ContractWithRelations
  ): Either<Error, InvestmentContract> {
    // 🔢 Dados do investimento (pega o primeiro pre_register)
    const investment = raw.pre_register?.[0]
    if (!investment)
      return left(new Error('Contract missing pre_register data'))

    const amountResult = Money.create(Number(investment.investment_value))
    const yieldResult = Percentage.create(Number(investment.investment_yield))

    if (amountResult.isLeft()) return left(amountResult.value)
    if (yieldResult.isLeft()) return left(yieldResult.value)

    if (!raw.owner_role_relation_contract_investor_idToowner_role_relation) {
      return left(new Error('Contrato sem investidor'))
    }

    // 🧑 Investor
    const investorResult = InvestorMapper.toDomain(
      raw.owner_role_relation_contract_investor_idToowner_role_relation
    )
    if (investorResult.isLeft()) return left(investorResult.value)

    if (
      !raw.owner_role_relation_contract_owner_role_relationToowner_role_relation
    ) {
      return left(new Error('Contrato sem broker'))
    }

    // 💼 Broker
    const brokerResult = BrokerMapper.toDomain(
      raw.owner_role_relation_contract_owner_role_relationToowner_role_relation
    )
    if (brokerResult.isLeft()) return left(brokerResult.value)

    // 🧠 Advisors
    const advisorsRaw = raw.contract_advisor.map(ca => ca.owner_role_relation)
    const advisorsResult = AdvisorMapper.toDomainList(
      advisorsRaw,
      brokerResult.value.id
    )
    if (advisorsResult.isLeft()) return left(advisorsResult.value)

    // 📄 Tipo do contrato
    const contractTypeResult =
      raw.type === 'scp' ? ContractType.scp() : ContractType.mutuo()

    // 🔢 Aditivos referente ao contrato
    const addendums = PrismaAddendumMapper.toDomainAddendum(raw.addendum)

    if (!investment.bank) return left(new Error('Banco não encontrado'))
    if (!investment.agency) return left(new Error('Agência não encontrada'))
    if (!investment.account) return left(new Error('Conta não encontrada'))

    // 🔢 Conta do investidor
    const bankAccount = BankAccount.create(
      investment.bank,
      investment.agency,
      investment.account,
      investment.pix ?? undefined
    )

    const incomeReportOwner =
      raw.owner_role_relation_contract_investor_idToowner_role_relation.income_report
        .filter(ir => !!ir.files?.url)
        .map(ir => ({
          year: Number(ir.reference_year),
          // biome-ignore lint/style/noNonNullAssertion: <explanation>
          fileUrl: ir.files!.url,
        }))
        .filter(item => !Number.isNaN(item.year))

    if (bankAccount.isLeft()) return left(bankAccount.value)

    const props: InvestmentContractProps = {
      investor: investorResult.value,
      broker: brokerResult.value,
      advisors: advisorsResult.value,
      amount: amountResult.value,
      type: contractTypeResult,
      profitability: yieldResult.value,
      paymentMethod: PaymentMethod.pix(), // ⚠️ substituir se necessário
      startDate: raw.start_contract,
      endDate: raw.end_contract,
      profile: Profile.conservative(), // ⚠️ pode vir do investment.investment_modality
      signatureRequestId: raw.external_id?.toString(),
      addendums,
      incomeReport: incomeReportOwner,
      contractPdf: raw.contract_pdf,
      proofPayment: raw.proof_payment,
    }

    return InvestmentContract.createFromExisting(props, raw.id)
  }
}
