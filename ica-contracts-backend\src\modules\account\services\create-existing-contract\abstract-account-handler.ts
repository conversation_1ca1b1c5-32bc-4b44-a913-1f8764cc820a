import { Logger } from '@nestjs/common';

import { IAccountHandler } from './account-handler.dto';

export abstract class AbstractAccountHandler<Request, Result>
  implements IAccountHandler<Request, Result>
{
  protected readonly logger = new Logger(this.constructor.name);
  private nextHandler: IAccountHandler<Request, Result> | null = null;

  public setNext(
    handler: IAccount<PERSON>andler<Request, Result>,
  ): IAccount<PERSON>andler<Request, Result> {
    this.nextHandler = handler;
    return handler;
  }

  public async handle(request: Request): Promise<Result> {
    this.logger.debug(`Handling in ${this.constructor.name}...`);

    if (this.nextHandler) {
      return this.nextHandler.handle(request);
    }

    return null as unknown as Result;
  }
}
