{"version": 3, "file": "update-statistic.dto.js", "sourceRoot": "/", "sources": ["modules/statistic/dto/update-statistic.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yDAAyC;AACzC,qDAKyB;AAGzB,MAAa,YAAY;CAMxB;AAND,oCAMC;AAJC;IADC,IAAA,0BAAQ,GAAE;;2CACG;AAGd;IADC,IAAA,0BAAQ,GAAE;;2CACG;AAGhB,MAAa,kBAAkB;CAa9B;AAbD,gDAaC;AAVC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACQ;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACW;AAKtB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,YAAY,CAAC;;sDACD", "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport {\r\n  IsNumber,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { IStateData } from 'src/shared/database/typeorm/entities/statistic.entity';\r\n\r\nexport class StateDataDto {\r\n  @IsString()\r\n  state: string;\r\n\r\n  @IsNumber()\r\n  value: number;\r\n}\r\n\r\nexport class UpdateStatisticDto {\r\n  @IsOptional()\r\n  @IsNumber()\r\n  investors?: number;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  totalApplied?: number;\r\n\r\n  @IsOptional()\r\n  @ValidateNested()\r\n  @Type(() => StateDataDto)\r\n  statesData?: IStateData;\r\n}\r\n"]}