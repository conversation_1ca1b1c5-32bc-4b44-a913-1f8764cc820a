{"version": 3, "file": "pagination-query.js", "sourceRoot": "/", "sources": ["modules/superadmin/helpers/pagination-query.ts"], "names": [], "mappings": ";;;AAiBA,MAAa,oBAAoB;IAC/B,MAAM,CAAC,mBAAmB,CAAC,KAAuB;QAChD,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;QACzB,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;QAC3B,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC/B,CAAC;IACD,MAAM,CAAC,kBAAkB,CAAC,KAAsB;QAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ;YAC1B,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC1B,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;QAEjE,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM;YACtB,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACxB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAErE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;IACxB,CAAC;IAED,MAAM,CAAC,uBAAuB,CAC5B,IAAS,EACT,KAAa,EACb,IAAY,EACZ,KAAa;QAEb,OAAO;YACL,IAAI;YACJ,IAAI,EAAE;gBACJ,KAAK;gBACL,IAAI;gBACJ,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBAClC,OAAO,EAAE,KAAK;aACf;SACF,CAAC;IACJ,CAAC;CACF;AApCD,oDAoCC", "sourcesContent": ["import { PaginationMeta } from './pagination-meta.dto';\r\n\r\nexport interface IPaginationQuery {\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\nexport interface IDateRangeQuery {\r\n  dateFrom?: string;\r\n  dateTo?: string;\r\n}\r\n\r\nexport interface IPaginatedResult<T> {\r\n  data: T[];\r\n  meta: PaginationMeta;\r\n}\r\n\r\nexport class PaginatedQueryHelper {\r\n  static getPaginationParams(query: IPaginationQuery) {\r\n    const page = +query.page;\r\n    const limit = +query.limit;\r\n    const skip = (page - 1) * limit;\r\n\r\n    return { page, limit, skip };\r\n  }\r\n  static getDateRangeParams(query: IDateRangeQuery) {\r\n    const start = query.dateFrom\r\n      ? new Date(query.dateFrom)\r\n      : new Date(new Date().getFullYear(), new Date().getMonth(), 1);\r\n\r\n    const end = query.dateTo\r\n      ? new Date(query.dateTo)\r\n      : new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);\r\n\r\n    return { start, end };\r\n  }\r\n\r\n  static createPaginatedResponse<T>(\r\n    data: T[],\r\n    total: number,\r\n    page: number,\r\n    limit: number,\r\n  ): IPaginatedResult<T> {\r\n    return {\r\n      data,\r\n      meta: {\r\n        total,\r\n        page,\r\n        lastPage: Math.ceil(total / limit),\r\n        perPage: limit,\r\n      },\r\n    };\r\n  }\r\n}\r\n"]}