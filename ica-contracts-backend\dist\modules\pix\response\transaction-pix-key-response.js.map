{"version": 3, "file": "transaction-pix-key-response.js", "sourceRoot": "/", "sources": ["modules/pix/response/transaction-pix-key-response.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface ITransactionPixKeyResponse {\r\n  id: string;\r\n  status: string;\r\n  creditParty: {\r\n    account: string;\r\n    name: string;\r\n    document: string;\r\n  };\r\n  debitParty: {\r\n    account: string;\r\n    name: string;\r\n    document: string;\r\n  };\r\n  createdAt: Date;\r\n}\r\n"]}