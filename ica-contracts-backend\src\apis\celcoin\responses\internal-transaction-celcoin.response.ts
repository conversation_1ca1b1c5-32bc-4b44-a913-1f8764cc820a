export interface IInternalTransferResponse {
  status: string;
  version: string;
  body: {
    id: string;
    amount: number;
    clientRequestId: string;
    endToEndId: string;
    debitParty: {
      account: string;
      taxId: string;
      name: string;
      branch: string;
      bank: string;
    };
    creditParty: {
      account: string;
      taxId: string;
      name: string;
      branch: string;
      bank: string;
    };
    description: string;
  };
}
