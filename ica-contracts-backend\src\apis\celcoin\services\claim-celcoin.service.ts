import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';

import { IClaimConfirmCelcoinRequest } from '../requests/claim-confirm-celcoin.request';
import {
  IBodyResponse,
  IClaimConfirmCelcoinResponse,
} from '../responses/claim-confirm-celcoin.response';
import { IGetPixClaimResponse } from '../responses/get-claim-pix-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class ClaimCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async claimConfirm(
    input: IClaimConfirmCelcoinRequest,
  ): Promise<IBodyResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/claim/confirm`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IClaimConfirmCelcoinResponse> =
        await axios.post(url, input, config);
      return data.body;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async cancelClaim(
    input: IClaimConfirmCelcoinRequest,
  ): Promise<IBodyResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/claim/cancel`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IClaimConfirmCelcoinResponse> =
        await axios.post(url, input, config);
      return data.body;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async getClaim(claimId: string): Promise<IGetPixClaimResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/claim/${claimId}`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };
    try {
      const response = await axios.get(url, config);
      return response.data;
    } catch (error) {
      console.error(error.response);
      throw new InternalServerErrorException(error.response.data);
    }
  }
}
