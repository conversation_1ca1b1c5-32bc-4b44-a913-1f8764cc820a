import { Cache } from 'cache-manager';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { Repository } from 'typeorm';
import { GetAllBrokersWithActiveContractsQueryDto } from '../dto/get-all-brokers-with-active-contracts/query.dto';
import { GetAllBrokersWithActiveContractsResponseDto } from '../dto/get-all-brokers-with-active-contracts/response.dto';
export declare class GetAllBrokersWithActiveContractsService {
    private contractsDb;
    private cacheManager;
    constructor(contractsDb: Repository<ContractEntity>, cacheManager: Cache);
    perform(query: GetAllBrokersWithActiveContractsQueryDto): Promise<GetAllBrokersWithActiveContractsResponseDto>;
}
