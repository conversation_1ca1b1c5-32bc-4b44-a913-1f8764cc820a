type TransactionParty = {
  bank: string;
  key: string;
  account: string;
  branch: string;
  taxId: string;
  name: string;
  accountType: string;
};

export interface IGetPixTransactionStatusCelcoinResponse {
  status: string;
  version: string;
  body: {
    id: string;
    amount: number;
    clientCode: string;
    transactionIdentification: string;
    endToEndId: string;
    initiationType: string;
    paymentType: string;
    urgency: string;
    transactionType: string;
    debitParty: TransactionParty;
    creditParty: TransactionParty;
    remittanceInformation: string;
    error: Record<string, string>;
  };
}
