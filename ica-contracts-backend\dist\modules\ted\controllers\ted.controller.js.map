{"version": 3, "file": "ted.controller.js", "sourceRoot": "/", "sources": ["modules/ted/controllers/ted.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAewB;AACxB,0EAAgE;AAGhE,4FAAqF;AACrF,oDAA+C;AAC/C,wFAAiF;AACjF,sDAAiD;AACjD,yGAAkG;AAClG,mGAA4F;AAC5F,iFAA2E;AAC3E,iEAA4D;AAC5D,qGAA8F;AAC9F,mEAA8D;AAGvD,IAAM,aAAa,GAAnB,MAAM,aAAa;IACxB,YAEU,cAA8B,EAE9B,aAA4B,EAE5B,oBAA0C,EAEjC,6BAA4D,EAE5D,4BAA0D,EAE1D,+BAAgE;QAVzE,mBAAc,GAAd,cAAc,CAAgB;QAE9B,kBAAa,GAAb,aAAa,CAAe;QAE5B,yBAAoB,GAApB,oBAAoB,CAAsB;QAEjC,kCAA6B,GAA7B,6BAA6B,CAA+B;QAE5D,iCAA4B,GAA5B,4BAA4B,CAA8B;QAE1D,oCAA+B,GAA/B,+BAA+B,CAAiC;IAChF,CAAC;IAKE,AAAN,KAAK,CAAC,OAAO,CAEX,IAAgB,EACL,OAAqB,EACR,cAAsB;QAE9C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAC5C,IAAI,EACJ,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,cAAc,CACf,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,MAAM,CAEV,KAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,SAAS,CAAY,OAAqB;QAC9C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,sBAAsB,CAE1B,IAA+B,EACpB,OAAqB;QAEhC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,qBAAqB,CAAY,OAAqB;QAC1D,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,wBAAwB,CAE5B,MAAmC,EACxB,OAAqB;QAEhC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA1HY,sCAAa;AAmBlB;IAHL,IAAA,aAAI,EAAC,MAAM,CAAC;IACZ,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,gBAAO,EAAC,aAAa,CAAC,CAAA;;qCAFjB,yBAAU;;4CAiBjB;AAIK;IAFL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,cAAK,GAAE,CAAA;;qCACD,uBAAS;;2CAWjB;AAKK;IAHL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACP,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;8CAUzB;AAKK;IAHL,IAAA,aAAI,EAAC,WAAW,CAAC;IACjB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,OAAO,CAAC;IAC5B,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADJ,yDAAyB;;2DAWhC;AAKK;IAHL,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACK,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;0DAUrC;AAKK;IAHL,IAAA,eAAM,EAAC,sBAAsB,CAAC;IAC9B,IAAA,iBAAQ,EAAC,mBAAU,CAAC,OAAO,CAAC;IAC5B,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,cAAK,GAAE,CAAA;IAEP,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADF,6DAA2B;;6DAWpC;wBAzHU,aAAa;IADzB,IAAA,mBAAU,EAAC,KAAK,CAAC;IAGb,WAAA,IAAA,eAAM,EAAC,iCAAc,CAAC,CAAA;IAEtB,WAAA,IAAA,eAAM,EAAC,+BAAa,CAAC,CAAA;IAErB,WAAA,IAAA,eAAM,EAAC,8CAAoB,CAAC,CAAA;IAE5B,WAAA,IAAA,eAAM,EAAC,iEAA6B,CAAC,CAAA;IAErC,WAAA,IAAA,eAAM,EAAC,+DAA4B,CAAC,CAAA;IAEpC,WAAA,IAAA,eAAM,EAAC,qEAA+B,CAAC,CAAA;qCAThB,iCAAc;QAEf,+BAAa;QAEN,8CAAoB;QAEF,iEAA6B;QAE9B,+DAA4B;QAEzB,qEAA+B;GAbxE,aAAa,CA0HzB", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Delete,\r\n  Get,\r\n  HttpCode,\r\n  HttpException,\r\n  HttpStatus,\r\n  Inject,\r\n  Param,\r\n  Post,\r\n  Query,\r\n  Request,\r\n  UseGuards,\r\n  Headers,\r\n} from '@nestjs/common';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport { DeleteFavoriteTedContactDto } from '../dto/delete-favorite-ted-contact.dto';\r\nimport { GetTedDto } from '../dto/get-ted.dto';\r\nimport { SaveFavoriteTedContactDto } from '../dto/save-favorite-ted-contact.dto';\r\nimport { SendTedDto } from '../dto/send-ted.dto';\r\nimport { DeleteFavoriteTedContactService } from '../services/delete-favorite-ted-contact.service';\r\nimport { GetFavoriteTedContactService } from '../services/get-favorite-ted-contact.service';\r\nimport { GetRecentTedsService } from '../services/get-recent-teds.service';\r\nimport { GetTedService } from '../services/get-ted.service';\r\nimport { SaveFavoriteTedContactService } from '../services/save-favorite-ted-contact.service';\r\nimport { SendTedService } from '../services/send-ted.service';\r\n\r\n@Controller('ted')\r\nexport class TedController {\r\n  constructor(\r\n    @Inject(SendTedService)\r\n    private sendTedService: SendTedService,\r\n    @Inject(GetTedService)\r\n    private getTedService: GetTedService,\r\n    @Inject(GetRecentTedsService)\r\n    private getRecentTedsService: GetRecentTedsService,\r\n    @Inject(SaveFavoriteTedContactService)\r\n    private readonly saveFavoriteTedContactService: SaveFavoriteTedContactService,\r\n    @Inject(GetFavoriteTedContactService)\r\n    private readonly getFavoriteTedContactService: GetFavoriteTedContactService,\r\n    @Inject(DeleteFavoriteTedContactService)\r\n    private readonly deleteFavoriteTedContactService: DeleteFavoriteTedContactService,\r\n  ) {}\r\n\r\n  @Post('send')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async sendTed(\r\n    @Body()\r\n    body: SendTedDto,\r\n    @Request() request: IRequestUser,\r\n    @Headers('x-2fa-token') twoFactorToken: string,\r\n  ) {\r\n    try {\r\n      const data = await this.sendTedService.perform(\r\n        body,\r\n        request.user.id,\r\n        twoFactorToken,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('get')\r\n  @UseGuards(JwtAuthGuard)\r\n  async getTed(\r\n    @Query()\r\n    query: GetTedDto,\r\n  ) {\r\n    try {\r\n      const data = await this.getTedService.perform(query);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('recent')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async getRecent(@Request() request: IRequestUser) {\r\n    try {\r\n      const data = await this.getRecentTedsService.perform(request.user.id);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('/favorite')\r\n  @HttpCode(HttpStatus.CREATED)\r\n  @UseGuards(JwtAuthGuard)\r\n  async saveFavoriteTedContact(\r\n    @Body()\r\n    body: SaveFavoriteTedContactDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    try {\r\n      await this.saveFavoriteTedContactService.execute(body, request.user.id);\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('/favorite')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async getFavoriteTedContact(@Request() request: IRequestUser) {\r\n    try {\r\n      const data = await this.getFavoriteTedContactService.execute(request.user.id);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Delete('/favorite/:contactId')\r\n  @HttpCode(HttpStatus.CREATED)\r\n  @UseGuards(JwtAuthGuard)\r\n  async deleteFavoriteTedContact(\r\n    @Param()\r\n    params: DeleteFavoriteTedContactDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    try {\r\n      await this.deleteFavoriteTedContactService.execute(params, request.user.id);\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}