"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateStatisticService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const statistic_entity_1 = require("../../../shared/database/typeorm/entities/statistic.entity");
const typeorm_2 = require("typeorm");
let UpdateStatisticService = class UpdateStatisticService {
    constructor(statisticRepository) {
        this.statisticRepository = statisticRepository;
    }
    async perform(updateDto) {
        const statistic = await this.statisticRepository.find();
        if (!statistic) {
            throw new common_1.NotFoundException('Nenhum dado encontrado.');
        }
        if (updateDto.investors !== undefined) {
            statistic[0].investors = updateDto.investors;
        }
        if (updateDto.totalApplied !== undefined) {
            statistic[0].totalApplied = updateDto.totalApplied;
        }
        if (updateDto.statesData !== undefined) {
            statistic[0].statesData = updateDto.statesData;
        }
        return this.statisticRepository.save(statistic);
    }
};
exports.UpdateStatisticService = UpdateStatisticService;
exports.UpdateStatisticService = UpdateStatisticService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(statistic_entity_1.StatisticEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UpdateStatisticService);
//# sourceMappingURL=update-statistic.service.js.map