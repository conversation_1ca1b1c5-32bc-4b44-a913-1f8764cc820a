import type { ITransactionContext } from "@/application/interfaces";
import { Investor } from "@/domain/entities/user";
import type { IInvestorRepository } from "@/domain/repositories/investor.repository";
import { type Either, left, right } from "@/domain/shared/either";
import { PinoLoggerAdapter } from "@/main/adapters/pino-logger.adapter";
import prisma from "../client";
import { InvestorMapper } from "../mappers/investor.mapper";
import { hasGetClient } from "./broker.repository";

type OwnerWithAddressAndAccount = Awaited<
  ReturnType<IInvestorRepository["findOwnerByCpf"]>
>;
type BusinessWithAddressAndAccount = Awaited<
  ReturnType<IInvestorRepository["findBusinessByCnpj"]>
>;
type CreatedOwnerWithAddress = Awaited<
  ReturnType<IInvestorRepository["createOwner"]>
>;
type CreatedBusinessWithAddress = Awaited<
  ReturnType<IInvestorRepository["createBusiness"]>
>;

export class PrismaInvestorRepository implements IInvestorRepository {
  private readonly logger = new PinoLoggerAdapter();
  private readonly INVESTOR_ROLE = "investor";

  /**
   * Busca um investidor por documento (CPF ou CNPJ)
   * @param document - CPF ou CNPJ para buscar
   * @param tx - Contexto de transação opcional
   * @param pixFromDto - PIX key do DTO original (opcional)
   * @returns Either com Investor ou undefined se não encontrado
   */
  async findByDocument(
    document: string,
    tx?: ITransactionContext,
    pixFromDto?: string
  ): Promise<Either<Error, Investor | undefined>> {
    this.logger.info(
      `Buscando investidor existente com documento: ${document}`
    );

    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    // Busca por investidor existente com papel de investidor
    const investorFound = await client.owner_role_relation.findFirst({
      where: {
        role: { name: "investor" },
        OR: [{ owner: { cpf: document } }, { business: { cnpj: document } }],
      },
      include: {
        pre_register: true,
        owner: {
          include: { address: true, account: true },
        },
        business: {
          include: {
            address: true,
            account: true,
            owner_business_relation: {
              include: {
                owner: {
                  include: { address: true },
                },
              },
            },
          },
        },
      },
    });

    if (!investorFound) {
      this.logger.info(`Investidor não encontrado com documento: ${document}`);
      return right(undefined);
    }

    const investor = InvestorMapper.toDomainForContract(
      investorFound,
      pixFromDto
    );
    if (investor.isLeft()) {
      this.logger.error(
        `Erro ao mapear investidor com documento: ${document}. Detalhes: ${investor.value}`
      );
      return left(investor.value);
    }

    this.logger.info(`Investidor encontrado com documento: ${document}`);
    return right(investor.value);
  }

  /**
   * Busca um proprietário por CPF
   * @param cpf - CPF para buscar
   * @param tx - Contexto de transação opcional
   * @returns Dados do proprietário ou null
   */
  async findOwnerByCpf(
    cpf: string,
    tx?: ITransactionContext
  ): Promise<OwnerWithAddressAndAccount | null> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    return await client.owner.findFirst({
      where: { cpf },
      include: { address: true, account: true },
    });
  }

  /**
   * Busca uma empresa por CNPJ
   * @param cnpj - CNPJ para buscar
   * @param tx - Contexto de transação opcional
   * @returns Dados da empresa ou null
   */
  async findBusinessByCnpj(
    cnpj: string,
    tx?: ITransactionContext
  ): Promise<BusinessWithAddressAndAccount | null> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    return await client.business.findFirst({
      where: { cnpj },
      include: {
        address: true,
        account: true,
        owner_business_relation: {
          include: {
            owner: {
              include: { address: true },
            },
          },
        },
      },
    });
  }

  /**
   * Cria um novo registro de proprietário
   * @param data - Dados do proprietário para criar
   * @param tx - Contexto de transação opcional
   * @returns Dados do proprietário criado com endereço
   */
  async createOwner(
    data: Parameters<IInvestorRepository["createOwner"]>[0],
    tx?: ITransactionContext
  ): Promise<CreatedOwnerWithAddress> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    this.logger.info(`Criando registro de proprietário. id: ${data.id}`);

    const owner = await client.owner.create({
      data: {
        id: data.id,
        cpf: data.cpf,
        name: data.name,
        dt_birth: data.birthDate,
        email: data.email,
        mother_name: data.motherName,
        pep: data.pep,
        rg: data.rg,
        occupation: data.occupation,
        phone: data.phone,
        address: {
          create: {
            cep: data.address.postalCode,
            city: data.address.city,
            neighborhood: data.address.neighborhood,
            number: data.address.number,
            state: data.address.state,
            street: data.address.street,
            complement: data.address.complement || "",
          },
        },
      },
      include: {
        address: true,
        account: true,
      },
    });

    this.logger.info(`Proprietário criado com sucesso. ownerId: ${owner.id}`);
    return owner;
  }

  /**
   * Cria um novo registro de empresa
   * @param data - Dados da empresa para criar
   * @param tx - Contexto de transação opcional
   * @returns Dados da empresa criada com endereço
   */
  async createBusiness(
    data: Parameters<IInvestorRepository["createBusiness"]>[0],
    tx?: ITransactionContext
  ): Promise<CreatedBusinessWithAddress> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    this.logger.info(`Criando registro de empresa. id: ${data.id}`);

    const business = await client.business.create({
      data: {
        id: data.id,
        cnpj: data.cnpj,
        company_name: data.companyName,
        dt_opening: new Date(),
        email: data.email,
        password: "",
        fantasy_name: data.companyName,
        size: data.size,
        owner_id: data.ownerId,
        type: data.type,
        address: {
          create: {
            cep: data.address.postalCode,
            city: data.address.city,
            neighborhood: data.address.neighborhood,
            number: data.address.number,
            state: data.address.state,
            street: data.address.street,
            complement: data.address.complement || "",
          },
        },
      },
      include: {
        address: true,
        account: true,
      },
    });

    this.logger.info(`Empresa criada com sucesso. businessId: ${business.id}`);
    return business;
  }

  /**
   * Cria uma conta bancária para um proprietário
   * @param ownerId - ID do proprietário
   * @param accountData - Dados da conta bancária
   * @param tx - Contexto de transação opcional
   */
  async createOwnerAccount(
    ownerId: string,
    accountData: Parameters<IInvestorRepository["createOwnerAccount"]>[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    await client.account.create({
      data: {
        owner_id: ownerId,
        external_id: "",
        status: "",
        type: accountData.type,
        bank: accountData.bank,
        number: accountData.number,
        branch: accountData.branch,
        is_external: false,
      },
    });
    this.logger.info(`Conta criada para proprietário. ownerId: ${ownerId}`);
  }

  /**
   * Cria uma conta bancária para uma empresa
   * @param businessId - ID da empresa
   * @param accountData - Dados da conta bancária
   * @param tx - Contexto de transação opcional
   */
  async createBusinessAccount(
    businessId: string,
    accountData: Parameters<IInvestorRepository["createBusinessAccount"]>[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    await client.account.create({
      data: {
        business_id: businessId,
        external_id: "",
        status: "",
        type: accountData.type,
        bank: accountData.bank,
        number: accountData.number,
        branch: accountData.branch,
        is_external: false,
      },
    });
    this.logger.info(`Conta criada para empresa. businessId: ${businessId}`);
  }

  /**
   * Cria relação de papel de investidor
   * @param investorId - ID do investidor
   * @param ownerId - ID do proprietário (opcional)
   * @param businessId - ID da empresa (opcional)
   * @param tx - Contexto de transação opcional
   */
  async createInvestorRoleRelation(
    investorId: string,
    ownerId?: string,
    businessId?: string,
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    const role = await client.role.findFirst({
      where: { name: this.INVESTOR_ROLE },
    });

    if (!role) {
      throw new Error("Perfil 'investor' não encontrado");
    }

    await client.owner_role_relation.create({
      data: {
        id: investorId,
        owner_id: ownerId,
        business_id: businessId,
        role_id: role.id,
      },
    });

    this.logger.info(`Relação de investidor criada. investorId: ${investorId}`);
  }

  /**
   * Cria relação proprietário-empresa
   * @param businessId - ID da empresa
   * @param ownerId - ID do proprietário
   * @param tx - Contexto de transação opcional
   */
  async createOwnerBusinessRelation(
    businessId: string,
    ownerId: string,
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    this.logger.info(
      `Iniciando criação da relação owner_business_relation. businessId: ${businessId}, ownerId: ${ownerId}`
    );

    const role = await client.role.findFirst({
      where: { name: this.INVESTOR_ROLE },
    });

    if (role) {
      await client.owner_business_relation.create({
        data: {
          business_id: businessId,
          owner_id: ownerId,
          role: role.id,
        },
      });
      this.logger.info(
        `Relação owner-business criada com sucesso. businessId: ${businessId}, ownerId: ${ownerId}, roleId: ${role.id}`
      );
    } else {
      this.logger.error(
        `Role 'investor' não encontrada para criar relação owner_business_relation`
      );
      throw new Error("Role 'investor' não encontrada");
    }
  }

  /**
   * Salva uma entidade investidor no banco de dados (DEPRECATED)
   * @param entity - Entidade investidor para salvar
   * @param tx - Contexto de transação opcional
   * @returns Either com void ou Error
   */
  async save(
    entity: Investor,
    tx?: ITransactionContext
  ): Promise<Either<Error, void>> {
    this.logger.info(
      "InvestorRepository.save() está obsoleto. Use Caso de Uso para lógica de criação de investidor."
    );
    return right(undefined);
  }

  /**
   * Atualiza a senha do investidor
   * @param investorId - ID do investidor
   * @param password - Nova senha
   * @returns Either com void ou Error
   */
  async updatePassword(
    investorId: string,
    password: string
  ): Promise<Either<Error, void>> {
    try {
      this.logger.info(`Atualizando senha para o investidor ${investorId}`);

      // Buscar o owner_id ou business_id relacionado ao investidor
      const relation = await prisma.owner_role_relation.findUnique({
        where: { id: investorId },
        select: { owner_id: true, business_id: true },
      });

      if (!relation) {
        this.logger.error(
          `Relação de investidor não encontrada para ${investorId}`
        );
        return left(new Error(`Investidor não encontrado: ${investorId}`));
      }

      // Atualizar a senha do owner ou business
      if (relation.owner_id) {
        await prisma.owner.update({
          where: { id: relation.owner_id },
          data: {
            password,
            temporary_password: false,
          },
        });
        this.logger.info(
          `Senha atualizada com sucesso para o investidor individual ${investorId}`
        );
      } else if (relation.business_id) {
        await prisma.business.update({
          where: { id: relation.business_id },
          data: {
            password,
            temporary_password: false,
          },
        });
        this.logger.info(
          `Senha atualizada com sucesso para o investidor empresa ${investorId}`
        );
      } else {
        this.logger.error(
          `Tipo de investidor não reconhecido para ${investorId}`
        );
        return left(
          new Error(`Tipo de investidor não reconhecido: ${investorId}`)
        );
      }

      return right(undefined);
    } catch (error) {
      this.logger.error(`Erro ao atualizar senha do investidor: ${error}`);
      return left(error as Error);
    }
  }

  /**
   * Busca todos os investidores com filtros opcionais
   * @param filters - Filtros opcionais
   * @param page - Número da página
   * @param limit - Limite de registros por página
   * @returns Either com array de investidores ou Error
   */
  findAll(
    filters?: unknown,
    page?: number,
    limit?: number
  ): Promise<Either<Error, Investor[]>> {
    throw new Error("Método não implementado.");
  }

  /**
   * Remove um investidor por ID
   * @param id - ID do investidor
   * @returns Either com void ou Error
   */
  delete(id: string): Promise<Either<Error, void>> {
    throw new Error("Método não implementado.");
  }

  /**
   * Busca um investidor por ID
   * @param id - ID do investidor
   * @returns Either com investidor ou null se não encontrado
   */
  async findById(id: string): Promise<Either<Error, Investor | null>> {
    this.logger.info(`Buscando investidor por ID: ${id}`);
    try {
      const raw = await prisma.owner_role_relation.findUnique({
        where: { id },
        include: {
          pre_register: true,
          owner: {
            include: { address: true, account: true },
          },
          business: {
            include: {
              address: true,
              account: true,
              owner_business_relation: {
                include: {
                  owner: {
                    include: { address: true },
                  },
                },
              },
            },
          },
        },
      });

      if (!raw) {
        this.logger.info(`Investidor não encontrado com ID: ${id}`);
        return right(null);
      }

      const investor = InvestorMapper.toDomain(raw);
      if (investor.isLeft()) {
        this.logger.error(
          `Erro ao mapear investidor com ID: ${id}. Detalhes: ${investor.value}`
        );
        return left(investor.value);
      }

      this.logger.info(`Investidor encontrado com ID: ${id}`);
      return right(investor.value);
    } catch (err) {
      this.logger.error(
        `Erro ao buscar investidor com ID: ${id}. Detalhes: ${
          (err as Error).message
        }`
      );
      return left(
        new Error(`Erro ao buscar investidor: ${(err as Error).message}`)
      );
    }
  }

  /**
   * Obtém os IDs do proprietário e empresa por ID da relação
   * @param relationId - ID da relação
   * @returns Objeto com ownerId e businessId (um será null)
   */
  async getPartyIdsByRelationId(
    relationId: string
  ): Promise<{ ownerId: string | null; businessId: string | null }> {
    const relation = await prisma.owner_role_relation.findUnique({
      where: { id: relationId },
      select: { owner_id: true, business_id: true },
    });
    return {
      ownerId: relation?.owner_id ?? null,
      businessId: relation?.business_id ?? null,
    };
  }

  /**
   * Verifica se uma parte tem outros papéis além do atual
   * @param relationId - ID da relação
   * @returns true se tem outros papéis, false caso contrário
   */
  async hasOtherRolesForParty(relationId: string): Promise<boolean> {
    const { ownerId, businessId } = await this.getPartyIdsByRelationId(
      relationId
    );
    if (ownerId) {
      const relations = await prisma.owner_role_relation.findMany({
        where: { owner_id: ownerId },
        take: 2,
        select: { id: true },
      });
      return relations.length > 1;
    }
    if (businessId) {
      const relations = await prisma.owner_role_relation.findMany({
        where: { business_id: businessId },
        take: 2,
        select: { id: true },
      });
      return relations.length > 1;
    }
    return false;
  }

  /**
   * Atualiza dados pessoais de um proprietário (apenas campos vazios)
   * @param ownerId - ID do proprietário
   * @param data - Dados para atualizar
   * @param tx - Contexto de transação opcional
   */
  async updateOwnerPersonalDataIfNeeded(
    ownerId: string,
    data: Parameters<IInvestorRepository["updateOwnerPersonalDataIfNeeded"]>[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    // Buscar o proprietário existente para verificar campos vazios
    const existingOwner = await client.owner.findUnique({
      where: { id: ownerId },
    });

    if (!existingOwner) {
      this.logger.error(
        `Proprietário não encontrado para atualização. ownerId: ${ownerId}`
      );
      return;
    }

    this.logger.info(
      `Iniciando atualização de dados pessoais do proprietário. ownerId: ${ownerId}, nome existente: ${existingOwner.name}`
    );

    // Atualizar apenas campos vazios
    await client.owner.update({
      where: { id: ownerId },
      data: {
        name: existingOwner.name || data.name,
        email: existingOwner.email || data.email,
        phone: existingOwner.phone || data.phone,
        rg: existingOwner.rg || data.rg,
        occupation: existingOwner.occupation || data.occupation,
        issuingAgency: existingOwner.issuingAgency || data.issuingAgency,
        nationality: existingOwner.nationality || data.nationality,
        mother_name: existingOwner.mother_name || data.motherName,
      },
    });

    this.logger.info(
      `Dados pessoais atualizados se necessário para proprietário. ownerId: ${ownerId}`
    );
  }

  /**
   * Cria um endereço para um proprietário
   * @param ownerId - ID do proprietário
   * @param addressData - Dados do endereço
   * @param tx - Contexto de transação opcional
   */
  async createOwnerAddress(
    ownerId: string,
    addressData: Parameters<IInvestorRepository["createOwnerAddress"]>[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    await client.address.create({
      data: {
        owner_id: ownerId,
        cep: addressData.postalCode,
        city: addressData.city,
        neighborhood: addressData.neighborhood,
        number: addressData.number,
        state: addressData.state,
        street: addressData.street,
        complement: addressData.complement || "",
      },
    });

    this.logger.info(`Endereço criado para proprietário. ownerId: ${ownerId}`);
  }

  /**
   * Atualiza um endereço existente de um proprietário (apenas campos vazios)
   * @param ownerId - ID do proprietário
   * @param addressData - Dados do endereço para atualizar
   * @param tx - Contexto de transação opcional
   */
  async updateOwnerAddressIfNeeded(
    ownerId: string,
    addressData: Parameters<
      IInvestorRepository["updateOwnerAddressIfNeeded"]
    >[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    // Buscar o endereço existente do proprietário
    const existingAddress = await client.address.findFirst({
      where: { owner_id: ownerId },
    });

    if (!existingAddress) {
      this.logger.error(
        `Endereço não encontrado para proprietário. ownerId: ${ownerId}`
      );
      return;
    }

    // Atualizar apenas campos vazios
    await client.address.update({
      where: { id: existingAddress.id },
      data: {
        cep: existingAddress.cep || addressData.postalCode,
        city: existingAddress.city || addressData.city,
        neighborhood: existingAddress.neighborhood || addressData.neighborhood,
        number: existingAddress.number || addressData.number,
        state: existingAddress.state || addressData.state,
        street: existingAddress.street || addressData.street,
        complement: existingAddress.complement || addressData.complement || "",
      },
    });

    this.logger.info(
      `Endereço atualizado para proprietário. ownerId: ${ownerId}`
    );
  }

  /**
   * Verifica se um proprietário já possui conta bancária
   * @param ownerId - ID do proprietário
   * @param tx - Contexto de transação opcional
   * @returns true se possui conta, false caso contrário
   */
  async hasOwnerAccount(
    ownerId: string,
    tx?: ITransactionContext
  ): Promise<boolean> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    const account = await client.account.findFirst({
      where: { owner_id: ownerId },
    });

    return !!account;
  }

  /**
   * Verifica se uma empresa já possui conta bancária
   * @param businessId - ID da empresa
   * @param tx - Contexto de transação opcional
   * @returns true se possui conta, false caso contrário
   */
  async hasBusinessAccount(
    businessId: string,
    tx?: ITransactionContext
  ): Promise<boolean> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    const account = await client.account.findFirst({
      where: { business_id: businessId },
    });

    return !!account;
  }

  /**
   * Atualiza uma conta bancária de proprietário (apenas campos vazios)
   */
  async updateOwnerAccountIfNeeded(
    ownerId: string,
    accountData: Parameters<
      IInvestorRepository["updateOwnerAccountIfNeeded"]
    >[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;
    const existingAccount = await client.account.findFirst({
      where: { owner_id: ownerId },
    });
    if (!existingAccount) return;

    await client.account.update({
      where: { id: existingAccount.id },
      data: {
        bank: existingAccount.bank || accountData.bank,
        number: existingAccount.number || accountData.number,
        branch: existingAccount.branch || accountData.branch,
        type: existingAccount.type || accountData.type,
      },
    });
  }

  /**
   * Atualiza uma conta bancária de empresa (apenas campos vazios)
   */
  async updateBusinessAccountIfNeeded(
    businessId: string,
    accountData: Parameters<
      IInvestorRepository["updateBusinessAccountIfNeeded"]
    >[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;
    const existingAccount = await client.account.findFirst({
      where: { business_id: businessId },
    });
    if (!existingAccount) return;

    await client.account.update({
      where: { id: existingAccount.id },
      data: {
        bank: existingAccount.bank || accountData.bank,
        number: existingAccount.number || accountData.number,
        branch: existingAccount.branch || accountData.branch,
        type: existingAccount.type || accountData.type,
      },
    });
  }

  /**
   * Atualiza dados de uma empresa existente se necessário
   */
  async updateBusinessIfNeeded(
    businessId: string,
    data: Parameters<IInvestorRepository["updateBusinessIfNeeded"]>[1],
    tx?: ITransactionContext
  ): Promise<void> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    const existingBusiness = await client.business.findUnique({
      where: { id: businessId },
    });

    if (!existingBusiness) {
      this.logger.error(
        `Empresa não encontrada para atualização. businessId: ${businessId}`
      );
      return;
    }

    // Atualizar apenas campos vazios
    await client.business.update({
      where: { id: businessId },
      data: {
        company_name: existingBusiness.company_name || data.companyName,
        fantasy_name: existingBusiness.fantasy_name || data.companyName,
        email: existingBusiness.email || data.email,
      },
    });

    this.logger.info(
      `Dados da empresa atualizados se necessário. businessId: ${businessId}`
    );
  }

  /**
   * Verifica se uma relação owner_business_relation já existe
   * @param businessId - ID da empresa
   * @param ownerId - ID do proprietário
   * @param tx - Contexto de transação opcional
   * @returns true se existe, false caso contrário
   */
  async hasOwnerBusinessRelation(
    businessId: string,
    ownerId: string,
    tx?: ITransactionContext
  ): Promise<boolean> {
    const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

    const relation = await client.owner_business_relation.findFirst({
      where: {
        business_id: businessId,
        owner_id: ownerId,
      },
    });

    return !!relation;
  }
}
