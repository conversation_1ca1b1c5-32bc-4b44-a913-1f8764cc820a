{"version": 3, "file": "transaction-recharge.dto.js", "sourceRoot": "/", "sources": ["modules/recharge/dto/transaction-recharge.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8E;AAE9E,MAAa,sBAAsB;CASlC;AATD,wDASC;AALC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;kDACE;AAIX;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;sDACF", "sourcesContent": ["import { IsDefined, IsNumberString, IsString, IsUUID } from 'class-validator';\r\n\r\nexport class TransactionRechargeDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsUUID()\r\n  id: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  amount: string;\r\n}\r\n"]}