{"version": 3, "file": "uploads.controller.js", "sourceRoot": "/", "sources": ["modules/uploads/controllers/uploads.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAWwB;AACxB,+DAA2D;AAC3D,0EAAgE;AAEhE,sEAAgE;AAChE,gEAA2D;AAC3D,mFAA6E;AAC7E,6EAAwE;AAGjE,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YAEU,aAAoC,EAEpC,aAAkC;QAFlC,kBAAa,GAAb,aAAa,CAAuB;QAEpC,kBAAa,GAAb,aAAa,CAAqB;IACzC,CAAC;IAKE,AAAN,KAAK,CAAC,eAAe,CAEnB,IAAuB,EAEvB,IAAyB;QAEzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACxD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE,EAC/C,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAC1C,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,YAAY,CAEhB,IAAqB;QAErB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE,EAC/C,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAC1C,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA3CY,8CAAiB;AAWtB;IAHL,IAAA,aAAI,EAAC,WAAW,CAAC;IACjB,IAAA,wBAAe,EAAC,IAAA,kCAAe,EAAC,MAAM,CAAC,CAAC;IACxC,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,qBAAY,GAAE,CAAA;;qCADT,wCAAiB;;wDAaxB;AAGK;IADL,IAAA,YAAG,EAAC,WAAW,CAAC;IAEd,WAAA,IAAA,cAAK,GAAE,CAAA;;qCACF,mCAAe;;qDAWtB;4BA1CU,iBAAiB;IAD7B,IAAA,mBAAU,EAAC,SAAS,CAAC;IAGjB,WAAA,IAAA,eAAM,EAAC,gDAAqB,CAAC,CAAA;IAE7B,WAAA,IAAA,eAAM,EAAC,2CAAmB,CAAC,CAAA;qCADL,gDAAqB;QAErB,2CAAmB;GALjC,iBAAiB,CA2C7B", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Get,\r\n  HttpException,\r\n  Inject,\r\n  Post,\r\n  Query,\r\n  UploadedFile,\r\n  UseGuards,\r\n  UseInterceptors,\r\n} from '@nestjs/common';\r\nimport { FileInterceptor } from '@nestjs/platform-express';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\n\r\nimport { AddNewDocumentDto } from '../dto/add-new-document.dto';\r\nimport { GetDocumentsDto } from '../dto/get-documents.dto';\r\nimport { AddNewDocumentService } from '../services/add-new-document.service';\r\nimport { GetDocumentsService } from '../services/get-documents.service';\r\n\r\n@Controller('uploads')\r\nexport class UploadsController {\r\n  constructor(\r\n    @Inject(AddNewDocumentService)\r\n    private addDocService: AddNewDocumentService,\r\n    @Inject(GetDocumentsService)\r\n    private getDocService: GetDocumentsService,\r\n  ) {}\r\n\r\n  @Post('documents')\r\n  @UseInterceptors(FileInterceptor('file'))\r\n  @UseGuards(JwtAuthGuard)\r\n  async uploadDocuments(\r\n    @Body()\r\n    body: AddNewDocumentDto,\r\n    @UploadedFile()\r\n    file: Express.Multer.File,\r\n  ) {\r\n    try {\r\n      const response = this.addDocService.perform(body, file);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response.data || error.message },\r\n        error.status || error.response.statusCode,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('documents')\r\n  async getDocuments(\r\n    @Query()\r\n    body: GetDocumentsDto,\r\n  ) {\r\n    try {\r\n      const response = this.getDocService.perform(body);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response.data || error.message },\r\n        error.status || error.response.statusCode,\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}