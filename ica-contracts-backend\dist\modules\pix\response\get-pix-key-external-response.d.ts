import { AccountTypeEnum } from 'src/shared/enums/account-type.enum';
import { KeyTypeEnum } from 'src/shared/enums/key-type.enum';
export interface IGetPixKeyExternalResponse {
    keyType: KeyTypeEnum;
    key: string;
    endToEndId: string;
    account: {
        branch: string;
        number: string;
        type: string;
        bank: string;
    };
    owner: {
        name: string;
        document: string;
        type: AccountTypeEnum;
    };
}
