import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import { IAddSignatoriesRequest } from '../requests/add-many-signatories.request';
import { IAddSignatoriesResponse } from '../responses/add-many-signatories.response';
import { AuthIntegrationService } from './auth.service';

@Injectable()
export class AddManySignatoriesService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly logger = new Logger(AddManySignatoriesService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authIntegrationService: AuthIntegrationService,
  ) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/documentos`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async addSignatories(
    id: string,
    data: IAddSignatoriesRequest,
  ): Promise<IAddSignatoriesResponse> {
    const authResponse =
      await this.authIntegrationService.authenticateIntegration();
    const { token } = authResponse.jwt;

    try {
      const response = await this.axiosInstance.post<IAddSignatoriesResponse>(
        `/signatarios/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data;
    } catch (error) {
      this.logger.error('Error adding signatories', error);

      if (error.response) {
        throw new InternalServerErrorException(
          `${error.response.status} - ${error.response.data.payload.status[0].message}`,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'No response received from API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Request error: ${error.message}`,
        );
      }
    }
  }
}
