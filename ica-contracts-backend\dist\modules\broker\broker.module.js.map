{"version": 3, "file": "broker.module.js", "sourceRoot": "/", "sources": ["modules/broker/broker.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,8DAAwD;AAExD,iEAA6D;AAC7D,wDAAoD;AACpD,uEAAmE;AACnE,4EAAuE;AACvE,wEAAmE;AACnE,wFAAkF;AAClF,0FAAmF;AACnF,sEAAiE;AACjE,oEAAsE;AACtE,oGAA8F;AAC9F,oFAA+E;AAC/E,0HAAiH;AACjH,kGAA2F;AAC3F,+EAAwE;AACxE,8FAAoG;AACpG,mFAA8E;AAC9E,wHAAgH;AAChH,sGAAgG;AAChG,mHAA4G;AAC5G,oHAA4G;AAC5G,8EAAqF;AACrF,gFAA4E;AAwCrE,IAAM,YAAY,GAAlB,MAAM,YAAY;CAAG,CAAA;AAAf,oCAAY;uBAAZ,YAAY;IAtCxB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,4BAAY;YACZ,0BAAW;YACX,gCAAc;YACd,mCAAe;YACf,2CAAmB;YACnB,0CAAmB;SACpB;QACD,WAAW,EAAE,CAAC,oCAAgB,CAAC;QAC/B,SAAS,EAAE;YACT,2CAAmB;YACnB,uDAAwB;YACxB,sDAAwB;YACxB,uCAAiB;YACjB,qCAAgB;YAChB,0CAAsB;YACtB,kEAA8B;YAC9B,mDAAuB;YACvB,qFAAsC;YACtC,+DAA4B;YAC5B,wEAAuC;YACvC,oFAAsC;YACtC,oEAA+B;YAC/B,mEAA8B;YAC9B,gFAAoC;YACpC,yDAAgC;SACjC;QACD,OAAO,EAAE;YACP,uDAAwB;YACxB,sDAAwB;YACxB,uCAAiB;YACjB,mDAAuB;YACvB,qFAAsC;YACtC,+DAA4B;YAC5B,oFAAsC;SACvC;KACF,CAAC;GACW,YAAY,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { InvestorModule } from '../investor/investor.module';\r\nimport { OwnerModule } from '../owner/owner.module';\r\nimport { BrokerController } from './controllers/broker.controller';\r\nimport { CreateBrokerService } from './services/create-broker.service';\r\nimport { EditBrokerService } from './services/edit-broker.service';\r\nimport { GetBrokerAdvisorsService } from './services/get-broker-advisors.service';\r\nimport { GetBrokerInvestorService } from './services/get-broker-investors.service';\r\nimport { GetBrokerService } from './services/get-broker.service';\r\nimport { BrokerDashboardService } from './services/dashboard.service';\r\nimport { ContractsMonthlyCaptureService } from './services/contracts-monthly-capture.service';\r\nimport { ContractsCaptureService } from './services/contracts-capture.service';\r\nimport { GetOneScheduledPaymentForBrokerService } from './services/get-one-scheduled-payment-for-broker.service';\r\nimport { GeneratePaymentReportService } from './services/generate-payment-reports.service';\r\nimport { IcaReportModule } from 'src/apis/ica-report/ica-report.module';\r\nimport { ListIncomePaymentScheduledBrokerService } from './services/list-payment-scheduled.service';\r\nimport { IncomePaymentModule } from '../income-payment/income-payment.module';\r\nimport { GenerateScheduledPaymentsReportService } from './services/generate-scheduled-payments-reports.service';\r\nimport { GenerateContractsReportsService } from './services/generate-contracts-reports.service';\r\nimport { GetAcquisitionPerPeriodService } from '../acquisition/services/get-acquisition-per-period.service';\r\nimport { GetBrokerContractsGrowthChartService } from './services/get-broker-contracts-growth-chart.service';\r\nimport { ListActiveInvestorsBrokerService } from './services/list-investors.service';\r\nimport { IncomeReportsModule } from '../income-report/income-report.module';\r\n\r\n@Module({\r\n  imports: [\r\n    SharedModule,\r\n    OwnerModule,\r\n    InvestorModule,\r\n    IcaReportModule,\r\n    IncomePaymentModule,\r\n    IncomeReportsModule,\r\n  ],\r\n  controllers: [BrokerController],\r\n  providers: [\r\n    CreateBrokerService,\r\n    GetBrokerInvestorService,\r\n    GetBrokerAdvisorsService,\r\n    EditBrokerService,\r\n    GetBrokerService,\r\n    BrokerDashboardService,\r\n    ContractsMonthlyCaptureService,\r\n    ContractsCaptureService,\r\n    GetOneScheduledPaymentForBrokerService,\r\n    GeneratePaymentReportService,\r\n    ListIncomePaymentScheduledBrokerService,\r\n    GenerateScheduledPaymentsReportService,\r\n    GenerateContractsReportsService,\r\n    GetAcquisitionPerPeriodService,\r\n    GetBrokerContractsGrowthChartService,\r\n    ListActiveInvestorsBrokerService,\r\n  ],\r\n  exports: [\r\n    GetBrokerInvestorService,\r\n    GetBrokerAdvisorsService,\r\n    EditBrokerService,\r\n    ContractsCaptureService,\r\n    GetOneScheduledPaymentForBrokerService,\r\n    GeneratePaymentReportService,\r\n    GenerateScheduledPaymentsReportService,\r\n  ],\r\n})\r\nexport class BrokerModule {}\r\n"]}