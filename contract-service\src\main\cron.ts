import { makeProcessAuditSignatureJob } from './factories/jobs/process-audit-signature.factory';
import { makeProcessAwaitingDepositJob } from './factories/jobs/process-awaiting-deposit.factory';
import { makeProcessInvestorSignatureJob } from './factories/jobs/process-investor-signature.factory';

export function setupCron() {
  makeProcessInvestorSignatureJob().start();
  makeProcessAuditSignatureJob().start();
  makeProcessAwaitingDepositJob().start();
}
