import axios, { AxiosResponse, isAxiosError } from 'axios';
import { Injectable, HttpException, BadRequestException } from '@nestjs/common';
import { CreateExistingContractRequest } from '../request/create-existing-contract.request';
import { CreateExistingContractResponseDto } from '../request/create-existing-contract.response';
import { mapMimeTypeToType } from 'src/shared/functions/map-mimetype';

@Injectable()
export class CreateExistingContractApiService {
  async perform(
    dto: CreateExistingContractRequest,
  ): Promise<CreateExistingContractResponseDto> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/existing`;

      const formData = new FormData();
      
      if (dto.individual) {
        this.appendFormData(formData, dto.individual, 'individual');
      }

      if (dto.company) {
        this.appendFormData(formData, dto.company, 'company');
      }

      formData.append('bankAccount[bank]', dto.bankAccount.bank);
      formData.append('bankAccount[agency]', dto.bankAccount.agency);
      formData.append('bankAccount[account]', dto.bankAccount.account);
      formData.append('bankAccount[accountType]', dto.bankAccount.accountType);

      if (dto.bankAccount.pix) {
        formData.append('bankAccount[pix]', dto.bankAccount.pix);
      }

      // Adiciona os campos simples (texto, números, enums) no form-data.
      formData.append('personType', dto.personType);
      formData.append('contractType', dto.contractType);
      formData.append('brokerId', dto.brokerId);

      formData.append('investment[amount]', dto.investment.amount.toString());
      formData.append(
        'investment[monthlyRate]',
        dto.investment.monthlyRate.toString(),
      );
      formData.append(
        'investment[paymentMethod]',
        dto.investment.paymentMethod,
      );
      formData.append('investment[profile]', dto.investment.profile);
      formData.append('investment[startDate]', dto.investment.startDate);
      formData.append('investment[endDate]', dto.investment.endDate);
      

      if (dto.contractType === 'SCP' && dto.investment.quotaQuantity) {
        formData.append('investment[quotaQuantity]', dto.investment.quotaQuantity);
      }

      formData.append(
        'investment[durationInMonths]',
        dto.investment.durationInMonths.toString(),
      );
      formData.append(
        'investment[isDebenture]',
        dto.investment.isDebenture.toString(),
      ); 

      // Se houver arrays (como advisors), pode ser necessário iterar:
      if (dto.advisors && dto.advisors.length > 0) {
        dto.advisors.forEach((advisor, index) => {
          formData.append(`advisors[${index}][advisorId]`, advisor.advisorId);
          formData.append(`advisors[${index}][rate]`, advisor.rate.toString());
        });
      }

      const proofOfPaymentBlob = new Blob([dto.proofOfPayment.buffer], {
        type: dto.proofOfPayment.mimetype,
      });
      const proofOfResidenceBlob = new Blob([dto.proofOfResidence.buffer], {
        type: dto.proofOfResidence.mimetype,
      });
      const personalDocumentBlob = new Blob([dto.personalDocument.buffer], {
        type: dto.personalDocument.mimetype,
      });
      const contractBlob = new Blob([dto.contract.buffer], {
        type: dto.contract.mimetype,
      });

      formData.append(
        'proofOfPayment',
        proofOfPaymentBlob,
        `proofOfPayment.${mapMimeTypeToType(dto.proofOfPayment.mimetype)}`,
      );
      formData.append(
        'proofOfResidence',
        proofOfResidenceBlob,
        `proofOfResidence.${mapMimeTypeToType(dto.proofOfResidence.mimetype)}`,
      );
      formData.append(
        'personalDocument',
        personalDocumentBlob,
        `personalDocument.${mapMimeTypeToType(dto.personalDocument.mimetype)}`,
      );

      formData.append(
        'contract',
        contractBlob,
        `contract.${mapMimeTypeToType(dto.contract.mimetype)}`,
      );

      if (dto.companyDocument) {
        const companyDocumentBlob = new Blob([dto.companyDocument.buffer], {
          type: dto.companyDocument.mimetype,
        });
        formData.append(
          'companyDocument',
          companyDocumentBlob,
          `companyDocument.${mapMimeTypeToType(dto.companyDocument.mimetype)}`,
        );
      }  
      
      const { data } = await axios.post<
      FormData,
      AxiosResponse<CreateExistingContractResponseDto>
      >(url, formData, { headers: { 'Content-Type': 'multipart/form-data' } });
      
      return data;
    } catch (error) {
      console.log('error', error);

      if (isAxiosError(error)) {
        if (error.status > 399 && error.status < 499) {
          throw new BadRequestException(error.response.data.message.at(1));
        }

        throw new HttpException(
          error.response.data.message,
          error.response.data.statusCode,
        );
      }
      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }

  /**
   * Função utilitária que percorre recursivamente um objeto e adiciona cada propriedade ao FormData.
   * @param formData - Instância de FormData onde os dados serão adicionados.
   * @param data - Objeto a ser adicionado.
   * @param parentKey - Chave pai, utilizada para montar a chave composta.
   */
  private appendFormData(
    formData: FormData,
    data: any,
    parentKey?: string,
  ): void {
    if (
      data &&
      typeof data === 'object' &&
      !(data instanceof Buffer) &&
      !(data instanceof Blob)
    ) {
      Object.keys(data).forEach((key) => {
        const value = data[key];
        const fullKey = parentKey ? `${parentKey}[${key}]` : key;

        // Se for um objeto, faça a chamada recursiva.
        if (
          value &&
          typeof value === 'object' &&
          !(value instanceof Buffer) &&
          !(value instanceof Blob)
        ) {
          this.appendFormData(formData, value, fullKey);
        } else {
          // Caso o valor seja nulo ou indefinido, pode ser convertido para string ou ignorado.
          formData.append(
            fullKey,
            value !== null && value !== undefined ? value.toString() : '',
          );
        }
      });
    } else if (parentKey) {
      formData.append(
        parentKey,
        data !== null && data !== undefined ? data.toString() : '',
      );
    }
  }
}
