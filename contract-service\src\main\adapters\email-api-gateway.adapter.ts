import env from '../config/env';
import { left, right, Either } from '@/domain/shared/either';
import { HttpClient } from '@/application/interfaces/http-client';
import { IEmailGateway } from '@/domain/gateways/email-send-gateway';
import { IncomeReportEmailPayload } from '@/contexts/income-report/domain/entities/email-income-report';

export interface EmailMessage {
  from_email?: string;
  to_email: string;
  template: string;
  template_data: TemplateData;
  subject: string;
}
export interface TemplateData {
  calendar_year: number;
  name: string;
  download_link: string;
  telephone?: string;
  email?: string;
}

export interface ResponseEmail {
  id: string;
  status: string;
  created_at: Date;
}

export interface ResponseEmailData {
  id: string;
  status: 'SENT' | 'DELIVERED' | 'FAILED';
}

export class EmailGateway implements IEmailGateway {
  private readonly apiBaseUrl: string;
  private readonly apiKey: string;

  constructor(private readonly httpClient: HttpClient) {
    this.apiBaseUrl = env.EMAIL_API_URL;
    this.apiKey = env.EMAIL_API_KEY;
  }

  async sendIncomeReportEmail(
    data: IncomeReportEmailPayload,
  ): Promise<Either<Error, any>> {
    try {
      const dataEmail: EmailMessage = {
        to_email: data.body.to_email,
        template: 'income',
        template_data: {
          calendar_year: data.body.template_data.calendar_year,
          download_link: data.body.template_data.download_link,
          name: data.body.template_data.name,
        },
        subject: 'Seu Informe de Rendimentos está Disponível',
      };

      const response = await this.httpClient.post<EmailMessage, ResponseEmail>(
        `${this.apiBaseUrl}/api/v1/email`,
        dataEmail,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Api-Key': this.apiKey,
          },
        },
      );

      return right(response.data);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      return left(new Error(`Falha ao gerar relatório: ${errorMessage}`));
    }
  }
}
