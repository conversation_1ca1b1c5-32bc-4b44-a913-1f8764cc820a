import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import generateAndSendReport from 'src/shared/functions/report/generate-send-report';
import { Equal, Repository } from 'typeorm';

@Injectable()
export class SendInvestmentExtractService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountRepository: Repository<AccountEntity>,
  ) {}

  async execute(userId: string, month: number): Promise<void> {
    const account = await this.accountRepository.findOne({
      where: [
        {
          ownerId: Equal(userId),
        },
        {
          businessId: Equal(userId),
        },
        {
          id: Equal(userId),
        },
      ],
      relations: {
        owner: true,
        business: true,
      },
    });

    if (!account) throw new NotFoundException('Usuario não possui conta ativa');

    const investmentInfo = account.owner ? account.owner : account.business;

    if (
      !(
        investmentInfo.startContract &&
        investmentInfo.endContract &&
        investmentInfo.yield &&
        investmentInfo.totalInvested
      )
    ) {
      throw new BadRequestException('Usuario não possui investimentos!');
    }

    const profitabilityFee = investmentInfo.yield / 12;

    const monthly = investmentInfo.totalInvested * (profitabilityFee / 100);

    const taxRate = investmentInfo.taxRate ? investmentInfo.taxRate : null;
    const grossIncome = taxRate ? monthly * (taxRate / 100) : monthly;

    const monthName = new Intl.DateTimeFormat('pt-BR', {
      month: 'short',
    }).format(new Date(new Date().getFullYear(), month, new Date().getDay()));

    generateAndSendReport(investmentInfo.email, {
      name: account.owner.name || account.business.companyName || '',
      document: account.owner.cpf || account.business.cnpj,
      totalValue: new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(investmentInfo.totalInvested),
      transactionDate: new Intl.DateTimeFormat('pt-BR', {
        dateStyle: 'short',
        timeStyle: 'short',
        timeZone: 'America/Sao_Paulo',
      }).format(new Date()),
      month: monthName,
      totalIncome: new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(grossIncome),
    });
  }
}
