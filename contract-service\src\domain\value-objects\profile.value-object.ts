import { type Either, left, right } from '@/domain/shared'

export class Profile {
  private static readonly validProfiles = [
    'conservative',
    'moderate',
    'aggressive',
  ] as const

  private constructor(
    private readonly _value: (typeof Profile.validProfiles)[number]
  ) {}

  static create(value: string): Either<Error, Profile> {
    const normalized = value.toLowerCase()

    if (!Profile.validProfiles.includes(normalized as any)) {
      return left(new Error(`Invalid profile: ${value}`))
    }

    return right(
      new Profile(normalized as (typeof Profile.validProfiles)[number])
    )
  }

  get value(): 'conservative' | 'moderate' | 'aggressive' {
    return this._value
  }

  toString(): string {
    return this._value
  }

  isConservative(): boolean {
    return this._value === 'conservative'
  }

  isModerate(): boolean {
    return this._value === 'moderate'
  }

  isAggressive(): boolean {
    return this._value === 'aggressive'
  }

  static conservative(): Profile {
    return new Profile('conservative')
  }

  static moderate(): Profile {
    return new Profile('moderate')
  }

  static aggressive(): Profile {
    return new Profile('aggressive')
  }

  formatProfile() {
    switch (this._value) {
      case 'conservative':
        return 'conservador'
      case 'moderate':
        return 'moderado'
      case 'aggressive':
        return 'agresivo'
      default:
        return 'conservador'
    }
  }
}
