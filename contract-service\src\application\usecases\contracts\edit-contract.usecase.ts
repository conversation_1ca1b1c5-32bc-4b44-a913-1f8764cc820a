import type { EditContractDTO } from "@/application/dtos/contracts/edit-contract.dto";
import type {
  ITransactionContext,
  IUnitOfWork,
} from "@/application/interfaces";
import type { IUseCase } from "@/application/interfaces/usecase";
import {
  ContractStatus,
  InvestmentContract,
} from "@/domain/entities/contracts";
import { Company, Individual } from "@/domain/entities/parties";
import type { LoggerGateway } from "@/domain/gateways/logger.gateway";
import type { IInvestmentContractRepository } from "@/domain/repositories";
import { DomainEventMediator, type Either, left, right } from "@/domain/shared";
import { ContractType } from "@/domain/value-objects/contract-type.value-object";
import { Money } from "@/domain/value-objects/money.value-object";
import { Percentage } from "@/domain/value-objects/percentage.value-object";
import { PaymentMethod } from "@/domain/value-objects/payment-method.value-object";
import { Profile } from "@/domain/value-objects/profile.value-object";
import { CompanyType } from "@/domain/value-objects/company-type.value-object";
import { SignatureAdapter } from "@/main/adapters/rbm/signature.adapter";
import { ContractCreatedEvent } from "@/domain/events/contracts/contract-created.event";

interface EditContractResponse {
  id: string;
  status: ContractStatus;
}

export class EditContractUseCase
  implements
    IUseCase<EditContractDTO, Promise<Either<Error, EditContractResponse>>>
{
  constructor(
    private readonly unitOfWork: IUnitOfWork,
    private readonly contractRepository: IInvestmentContractRepository,
    private readonly rmbApi: SignatureAdapter, // Nao remover, necessario para o atualizar o contrato para a assinatura
    private readonly logger: LoggerGateway
  ) {}

  async execute(
    data: EditContractDTO
  ): Promise<Either<Error, EditContractResponse>> {
    try {
      this.logger.info(`Iniciando edição do contrato ${data.contractId}`);

      const result = await this.unitOfWork.runInTransaction(
        async (
          tx: ITransactionContext
        ): Promise<Either<Error, EditContractResponse>> => {
          // Passo 1: Buscar e validar contrato
          const contractResult = await this.findAndValidateContract(
            data.contractId,
            tx
          );
          if (contractResult.isLeft()) {
            return left(contractResult.value);
          }
          const contract = contractResult.value;

          // Passo 2: Atualizar dados do investidor
          const investorUpdateResult = await this.updateInvestorData(
            data,
            contract
          );
          if (investorUpdateResult.isLeft()) {
            return left(investorUpdateResult.value);
          }

          // Passo 3: Atualizar dados do contrato
          const contractUpdateResult = await this.updateContractData(
            data,
            contract
          );
          if (contractUpdateResult.isLeft()) {
            return left(contractUpdateResult.value);
          }

          // Passo 4: Atualizar dados bancários
          const bankUpdateResult = await this.updateBankAccountData(
            data,
            contract
          );
          if (bankUpdateResult.isLeft()) {
            return left(bankUpdateResult.value);
          }

          // Passo 5: Salvar alterações no repositório
          const saveResult = await this.contractRepository.editContract(
            contract.id,
            contract,
            tx
          );
          if (saveResult.isLeft()) {
            this.logger.error(
              `Erro ao salvar contrato: ${saveResult.value.message}`
            );
            return left(saveResult.value);
          }

          // Passo 6: Verificar e cancelar contrato no serviço externo se necessário
          const externalCancellationResult =
            await this.handleExternalContractCancellation(contract);
          if (externalCancellationResult.isLeft()) {
            this.logger.error(
              `Erro ao cancelar contrato no serviço externo: ${externalCancellationResult.value.message}`
            );
            return left(externalCancellationResult.value);
          }

          // Passo 7: Registrar evento de domínio para geração do novo PDF e envio para signatários
          tx.registerAfterCommit(() => {
            DomainEventMediator.dispatch(new ContractCreatedEvent(contract.id));
            this.logger.info(
              `Evento ContractCreatedEvent registrado para disparo pós-commit. contractId: ${contract.id}`
            );
          });

          this.logger.info(
            `Contrato ${
              contract.id
            } editado com sucesso: ${contract.getStatus()}`
          );
          return right({
            id: contract.id,
            status: contract.getStatus(),
          });
        }
      );

      if (result.isLeft()) {
        this.logger.error(`Erro na transação: ${result.value.message}`);
        return left(result.value);
      }

      return right(result.value);
    } catch (error) {
      this.logger.error(`Erro inesperado ao editar contrato: ${error}`);
      return left(new Error(`Erro ao editar contrato: ${error}`));
    }
  }

  /**
   * Busca e valida o contrato para edição
   */
  private async findAndValidateContract(
    contractId: string,
    tx: ITransactionContext
  ): Promise<Either<Error, InvestmentContract>> {
    this.logger.info(`Buscando contrato ${contractId} para validação`);

    const contractResult = await this.contractRepository.findById(
      contractId,
      tx
    );
    if (contractResult.isLeft()) {
      this.logger.error(
        `Erro ao buscar contrato ${contractId}: ${contractResult.value.message}`
      );
      return left(contractResult.value);
    }

    const contract = contractResult.value;
    if (!contract) {
      this.logger.error(`Contrato ${contractId} não encontrado`);
      return left(new Error("Contrato não encontrado"));
    }

    this.logger.info(
      `Contrato ${contract.id} encontrado com status ${contract.getStatus()}`
    );

    const allowedStatuses = [ContractStatus.AWAITING_INVESTOR_SIGNATURE];
    if (!allowedStatuses.includes(contract.getStatus())) {
      this.logger.error(
        `Contrato ${contract.id} não está em status permitido para edição`
      );
      return left(
        new Error("Contrato não está aguardando assinatura do investidor")
      );
    }

    return right(contract);
  }

  /**
   * Atualiza os dados do investidor (PF ou PJ)
   */
  private async updateInvestorData(
    data: EditContractDTO,
    contract: InvestmentContract
  ): Promise<Either<Error, void>> {
    this.logger.info(
      `Atualizando dados do investidor para contrato ${contract.id}`
    );

    if (data.company && contract.getInvestor().getParty() instanceof Company) {
      return this.updateCompanyInvestorData(data.company, contract);
    }

    if (
      data.individual &&
      contract.getInvestor().getParty() instanceof Individual
    ) {
      return this.updateIndividualInvestorData(data.individual, contract);
    }

    return right(undefined);
  }

  /**
   * Atualiza dados do investidor pessoa jurídica
   */
  private async updateCompanyInvestorData(
    companyData: any,
    contract: InvestmentContract
  ): Promise<Either<Error, void>> {
    this.logger.info(
      `Atualizando dados da empresa para contrato ${contract.id}`
    );

    const companyTypeResult = CompanyType.create(companyData.type);
    if (companyTypeResult.isLeft()) {
      this.logger.error(
        `Erro ao criar tipo de empresa: ${companyTypeResult.value.message}`
      );
      return left(companyTypeResult.value);
    }

    const repDTO = companyData.representative;
    const company = contract.getInvestor().getParty() as Company;
    const legalRepResult = Individual.create(
      company.getLegalRepresentative().id,
      repDTO.fullName,
      {
        cpf: repDTO.cpf,
        email: repDTO.email,
        phone: repDTO.phone,
        birthDate: new Date(repDTO.birthDate),
        motherName: repDTO.motherName,
        rg: repDTO.rg,
        address: repDTO.address,
        occupation: repDTO.occupation,
        nationality: repDTO.nationality,
        issuingAgency: repDTO.issuingAgency,
      }
    );

    if (legalRepResult.isLeft()) {
      this.logger.error(
        `Erro ao criar representante legal: ${legalRepResult.value.message}`
      );
      return left(legalRepResult.value);
    }

    const updateResult = contract.editContractData(
      companyData.corporateName,
      repDTO.email,
      repDTO.phone,
      companyData.address,
      {
        cpf: repDTO.cpf,
        cnpj: companyData.cnpj,
        birthDate: new Date(repDTO.birthDate),
        motherName: repDTO.motherName,
        occupation: repDTO.occupation,
        nationality: repDTO.nationality,
        rg: repDTO.rg,
        issuingAgency: repDTO.issuingAgency,
        companyType: companyTypeResult.value,
        legalRepresentative: legalRepResult.value,
      }
    );

    if (updateResult.isLeft()) {
      this.logger.error(
        `Erro ao atualizar dados da empresa: ${updateResult.value.message}`
      );
      return left(updateResult.value);
    }

    this.logger.info(
      `Dados da empresa atualizados com sucesso para contrato ${contract.id}`
    );
    return right(undefined);
  }

  /**
   * Atualiza dados do investidor pessoa física
   */
  private async updateIndividualInvestorData(
    individualData: any,
    contract: InvestmentContract
  ): Promise<Either<Error, void>> {
    this.logger.info(
      `Atualizando dados do indivíduo para contrato ${contract.id}`
    );

    const updateResult = contract.editContractData(
      individualData.fullName,
      individualData.email,
      individualData.phone,
      individualData.address,
      {
        cpf: individualData.cpf,
        birthDate: new Date(individualData.birthDate),
        motherName: individualData.motherName,
        occupation: individualData.occupation,
        nationality: individualData.nationality,
        issuingAgency: individualData.issuingAgency,
        rg: individualData.rg,
      }
    );

    if (updateResult.isLeft()) {
      this.logger.error(
        `Erro ao atualizar dados do indivíduo: ${updateResult.value.message}`
      );
      return left(updateResult.value);
    }

    this.logger.info(
      `Dados do indivíduo atualizados com sucesso para contrato ${contract.id}`
    );
    return right(undefined);
  }

  /**
   * Atualiza os dados do contrato
   */
  private async updateContractData(
    data: EditContractDTO,
    contract: InvestmentContract
  ): Promise<Either<Error, void>> {
    if (
      !data.contractType ||
      !data.investment?.startDate ||
      !data.investment?.endDate
    ) {
      this.logger.info(`Dados do contrato não fornecidos, pulando atualização`);
      return right(undefined);
    }

    this.logger.info(`Atualizando dados do contrato ${contract.id}`);

    // Criar value objects para os dados do investimento
    const valueObjectsResult = await this.createInvestmentValueObjects(data);
    if (valueObjectsResult.isLeft()) {
      return left(valueObjectsResult.value);
    }

    const {
      contractType,
      amount,
      monthlyRate,
      paymentMethod,
      profile,
      quotaQuantity,
    } = valueObjectsResult.value;

    const updateResult = contract.updateContractDetails(
      contractType,
      data.investment.startDate
        ? new Date(data.investment.startDate)
        : contract.getStartDate(),
      new Date(data.investment.endDate),
      data.investment.isDebenture,
      amount,
      monthlyRate,
      paymentMethod,
      profile,
      undefined, // proofPaymentUrl - não aplicável para edição
      undefined, // contractUrl - não aplicável para edição
      quotaQuantity,
      data.investment.durationInMonths
    );

    if (updateResult.isLeft()) {
      this.logger.error(
        `Erro ao atualizar dados do contrato: ${updateResult.value.message}`
      );
      return left(updateResult.value);
    }

    this.logger.info(
      `Dados do contrato atualizados com sucesso para contrato ${contract.id}`
    );
    return right(undefined);
  }

  /**
   * Cria os value objects necessários para atualização do contrato
   */
  private async createInvestmentValueObjects(data: EditContractDTO): Promise<
    Either<
      Error,
      {
        contractType: ContractType;
        amount?: Money;
        monthlyRate?: Percentage;
        paymentMethod?: PaymentMethod;
        profile?: Profile;
        quotaQuantity?: number;
      }
    >
  > {
    // Tipo de contrato
    if (!data.contractType) {
      return left(new Error("Tipo de contrato não fornecido"));
    }

    const contractTypeResult = ContractType.create(
      data.contractType.toString().toLowerCase()
    );
    if (contractTypeResult.isLeft()) {
      this.logger.error(
        `Erro ao criar tipo de contrato: ${contractTypeResult.value.message}`
      );
      return left(contractTypeResult.value);
    }

    const result: any = { contractType: contractTypeResult.value };

    // Valor do investimento
    if (data.investment?.amount) {
      const amountResult = Money.create(data.investment.amount);
      if (amountResult.isLeft()) {
        this.logger.error(
          `Erro ao criar valor do investimento: ${amountResult.value.message}`
        );
        return left(amountResult.value);
      }
      result.amount = amountResult.value;
    }

    // Taxa mensal
    if (data.investment?.monthlyRate) {
      const monthlyRateResult = Percentage.create(data.investment.monthlyRate);
      if (monthlyRateResult.isLeft()) {
        this.logger.error(
          `Erro ao criar taxa mensal: ${monthlyRateResult.value.message}`
        );
        return left(monthlyRateResult.value);
      }
      result.monthlyRate = monthlyRateResult.value;
    }

    // Método de pagamento
    if (data.investment?.paymentMethod) {
      const paymentMethodResult = PaymentMethod.create(
        data.investment.paymentMethod.toString()
      );
      if (paymentMethodResult.isLeft()) {
        this.logger.error(
          `Erro ao criar método de pagamento: ${paymentMethodResult.value.message}`
        );
        return left(paymentMethodResult.value);
      }
      result.paymentMethod = paymentMethodResult.value;
    }

    // Perfil
    if (data.investment?.profile) {
      const profileResult = Profile.create(data.investment.profile.toString());
      if (profileResult.isLeft()) {
        this.logger.error(
          `Erro ao criar perfil: ${profileResult.value.message}`
        );
        return left(profileResult.value);
      }
      result.profile = profileResult.value;
    }

    // Quantidade de cotas (apenas para SCP)
    if (contractTypeResult.value.isScp() && data.investment?.quotaQuantity) {
      result.quotaQuantity = Number(data.investment.quotaQuantity);
    }

    return right(result);
  }

  /**
   * Atualiza dados bancários
   */
  private async updateBankAccountData(
    data: EditContractDTO,
    contract: InvestmentContract
  ): Promise<Either<Error, void>> {
    if (!data.bankAccount) {
      this.logger.info(`Dados bancários não fornecidos, pulando atualização`);
      return right(undefined);
    }

    this.logger.info(
      `Atualizando dados bancários para contrato ${contract.id}`
    );

    const investor = contract.getInvestor();

    const updateResult = investor.updateBankAccount(
      data.bankAccount.bank,
      data.bankAccount.agency,
      data.bankAccount.account,
      data.bankAccount.pix
    );

    if (updateResult.isLeft()) {
      this.logger.error(
        `Erro ao atualizar dados bancários: ${updateResult.value.message}`
      );
      return left(updateResult.value);
    }

    this.logger.info(
      `Dados bancários atualizados com sucesso para contrato ${contract.id}`
    );
    return right(undefined);
  }

  /**
   * Verifica e cancela o contrato no serviço externo se existir externalId
   */
  private async handleExternalContractCancellation(
    contract: InvestmentContract
  ): Promise<Either<Error, void>> {
    const externalId = contract.getSignatureRequestId();

    if (!externalId) {
      this.logger.info(
        `Contrato ${contract.id} não possui externalId, pulando cancelamento externo`
      );
      return right(undefined);
    }

    this.logger.info(
      `Verificando existência do documento ${externalId} no serviço externo`
    );

    const documentExistResult = await this.rmbApi.documentExist(externalId);
    if (documentExistResult.isLeft()) {
      this.logger.error(
        `Erro ao verificar documento na Dimensa ${contract.id}: ${documentExistResult.value.message}`
      );
      return left(documentExistResult.value);
    }

    if (!documentExistResult.value) {
      this.logger.info(
        `Documento ${externalId} não existe no serviço externo, pulando cancelamento`
      );
      return right(undefined);
    }

    this.logger.info(`Cancelando contrato ${externalId} no serviço externo`);

    const deleteResult = await this.rmbApi.deleteContract(externalId);
    if (deleteResult.isLeft()) {
      this.logger.error(
        `Erro ao cancelar contrato na Dimensa ${contract.id}: ${deleteResult.value.message}`
      );
      return left(deleteResult.value);
    }

    this.logger.info(
      `Contrato ${externalId} cancelado com sucesso no serviço externo`
    );
    return right(undefined);
  }
}
