import type { RequestInvestorCredentialsUseCase } from '@/application/usecases/contracts/request-investor-credentials.usecase'
import { badRequest, ok, serverError } from '../../helpers/http-helper'
import type { HttpRequest, HttpResponse, IController } from '../../protocols'

export class RequestInvestorCredentialsController implements IController {
  constructor(
    private readonly requestInvestorCredentialsUseCase: RequestInvestorCredentialsUseCase
  ) {}

  async handle(httpRequest: HttpRequest): Promise<HttpResponse> {
    try {
      if (!httpRequest) {
        return badRequest(new Error('Requisição não informada.'))
      }

      if (!httpRequest.params) {
        return badRequest(new Error('Parâmetros da requisição não informados.'))
      }
      if (!httpRequest.params.contractId) {
        return badRequest(
          new Error(
            'ID do contrato não informado nos parâmetros da requisição.'
          )
        )
      }

      const result = await this.requestInvestorCredentialsUseCase.execute({
        contractId: httpRequest.params.contractId,
      })

      // 3. Retornar Resposta
      if (result.isLeft()) {
        return badRequest(result.value)
      }

      return ok(undefined) // Retorna 200 OK sem corpo em caso de sucesso
    } catch (error: unknown) {
      return serverError(error as Error)
    }
  }
}
