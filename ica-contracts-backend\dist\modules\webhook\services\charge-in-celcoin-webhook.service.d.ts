import { BilletEntity } from 'src/shared/database/typeorm/entities/billet.entity';
import { Repository } from 'typeorm';
import { IChargeInCelcoinWebhookDto } from '../dto/charge-in-celcoin-webhook.dto';
export declare class ChargeInCelcoinWebhookService {
    private readonly billetEntityRepository;
    constructor(billetEntityRepository: Repository<BilletEntity>);
    perform(input: IChargeInCelcoinWebhookDto): Promise<void>;
}
