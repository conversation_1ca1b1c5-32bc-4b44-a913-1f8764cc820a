{"version": 3, "file": "send-pre-register.service.js", "sourceRoot": "/", "sources": ["modules/pre-register/services/send-pre-register.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,+CAAoC;AACpC,6FAAoF;AACpF,+FAAsF;AACtF,uGAA6F;AAC7F,qCAA4C;AAKrC,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACjC,YAEU,aAA4C,EAE5C,SAAoC,EAE3B,kBAA8C;QAJvD,kBAAa,GAAb,aAAa,CAA+B;QAE5C,cAAS,GAAT,SAAS,CAA2B;QAE3B,uBAAkB,GAAlB,kBAAkB,CAA4B;IAC9D,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAwB;QACpC,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC;QAE5B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;YACrD,QAAQ,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;SAC/B,CAAC,CAAC;QAEH,IAAI,WAAW;YACb,MAAM,IAAI,4BAAmB,CAC3B,+CAA+C,CAChD,CAAC;QAEJ,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;aACf;YACD,KAAK,EAAE;gBACL;oBACE,KAAK,EAAE;wBACL,GAAG,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;qBAC1B;iBACF;gBACD;oBACE,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;qBAC3B;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,OAAO;YACT,MAAM,IAAI,4BAAmB,CAAC,yCAAyC,CAAC,CAAC;QAE3E,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC1D,KAAK,EAAE;gBACL,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE;4BACL,GAAG,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;yBAC1B;qBACF;oBACD;wBACE,QAAQ,EAAE;4BACR,IAAI,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;yBAC3B;qBACF;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,aAAa;YACf,MAAM,IAAI,4BAAmB,CAC3B,iDAAiD,CAClD,CAAC;QAEJ,MAAM,KAAK,GAAG,IAAA,mBAAI,EAChB;YACE,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,KAAK;YACzB,gBAAgB,EAAE,IAAI,CAAC,QAAQ;YAC/B,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;YACtC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YACpC,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ;YAC5C,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;YACtC,sBAAsB,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;YACpD,sBAAsB,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY;YACrD,uBAAuB,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa;YACtD,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW;YAClD,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW;YAClD,mBAAmB,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS;YAC9C,sBAAsB,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;YACpD,gBAAgB,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG;YACjC,mBAAmB,EAAE,IAAI,CAAC,WAAW;YACrC,iBAAiB,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,6BAA6B,EAC3B,IAAI,CAAC,UAAU,EAAE,6BAA6B;YAChD,8BAA8B,EAC5B,IAAI,CAAC,UAAU,EAAE,8BAA8B;SAClD,EACD,SAAS,EACT;YACE,SAAS,EAAE,cAAc;SAC1B,CACF,CAAC;QAEF,OAAO;YACL,KAAK;SACN,CAAC;IACJ,CAAC;CACF,CAAA;AArGY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCAHV,oBAAU;QAEd,oBAAU;QAEQ,oBAAU;GAPtC,sBAAsB,CAqGlC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { sign } from 'jsonwebtoken';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';\r\nimport { Repository, Equal } from 'typeorm';\r\n\r\nimport { SendPreRegisterDto } from '../dto/send-pre-register.dto';\r\n\r\n@Injectable()\r\nexport class SendPreRegisterService {\r\n  constructor(\r\n    @InjectRepository(PreRegisterEntity)\r\n    private preRegisterDb: Repository<PreRegisterEntity>,\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n  ) {}\r\n  async perform(data: SendPreRegisterDto) {\r\n    const secretKey = process.env.PRE_REGISTER_TOKEN;\r\n    const expirationTime = '1d';\r\n\r\n    const preRegister = await this.preRegisterDb.findOneBy({\r\n      document: Equal(data.document),\r\n    });\r\n\r\n    if (preRegister)\r\n      throw new BadRequestException(\r\n        'Ja existe um pre contrato para este documento',\r\n      );\r\n\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        owner: true,\r\n        business: true,\r\n      },\r\n      where: [\r\n        {\r\n          owner: {\r\n            cpf: Equal(data.document),\r\n          },\r\n        },\r\n        {\r\n          business: {\r\n            cnpj: Equal(data.document),\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (account)\r\n      throw new BadRequestException('Ja existe uma conta para este documento');\r\n\r\n    const existContract = await this.contractRepository.findOne({\r\n      where: {\r\n        investor: [\r\n          {\r\n            owner: {\r\n              cpf: Equal(data.document),\r\n            },\r\n          },\r\n          {\r\n            business: {\r\n              cnpj: Equal(data.document),\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    });\r\n\r\n    if (existContract)\r\n      throw new BadRequestException(\r\n        'Já existe um contrato ativo para este documento',\r\n      );\r\n\r\n    const token = sign(\r\n      {\r\n        adviserId: data.adviserId,\r\n        investorEmail: data.email,\r\n        investorDocument: data.document,\r\n        investmentValue: data.investment.value,\r\n        investmentTerm: data.investment.term,\r\n        investmentModality: data.investment.modality,\r\n        investmentYield: data.investment.yield,\r\n        investmentPurchaseWith: data.investment.purchaseWith,\r\n        investmentAmountQuotes: data.investment?.amountQuotes,\r\n        investmentStartContract: data.investment.startContract,\r\n        investmentEndContract: data.investment.endContract,\r\n        investmentGracePeriod: data.investment.gracePeriod,\r\n        investmentDebenture: data.investment.debenture,\r\n        investmentObservations: data.investment.observations,\r\n        investorOwnerCpf: data.owner?.cpf,\r\n        investorAccountType: data.accountType,\r\n        investorOwnerName: data.owner?.name,\r\n        signIca: data.signIca,\r\n        brokerParticipationPercentage:\r\n          data.investment?.brokerParticipationPercentage,\r\n        advisorParticipationPercentage:\r\n          data.investment?.advisorParticipationPercentage,\r\n      },\r\n      secretKey,\r\n      {\r\n        expiresIn: expirationTime,\r\n      },\r\n    );\r\n\r\n    return {\r\n      token,\r\n    };\r\n  }\r\n}\r\n"]}