{"version": 3, "file": "pix.module.js", "sourceRoot": "/", "sources": ["modules/pix/pix.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,wDAAkD;AAClD,uEAAiE;AACjE,8DAAwD;AAExD,2GAAqG;AACrG,sFAAgF;AAChF,wFAAkF;AAClF,mFAA8E;AAC9E,iEAA6D;AAC7D,yFAAoF;AACpF,iFAA2E;AAC3E,yFAAkF;AAClF,yFAAmF;AACnF,mFAA4E;AAC5E,6EAAuE;AACvE,mGAA4F;AAC5F,iGAA0F;AAC1F,uFAAiF;AACjF,6EAAuE;AACvE,iGAA0F;AAC1F,yGAAkG;AAClG,yFAAkF;AAClF,uEAAiE;AACjE,yFAAmF;AACnF,yFAAoF;AACpF,2FAA8E;AAC9E,+EAAyE;AACzE,2FAAoF;AACpF,yEAAoE;AACpE,uFAAiF;AACjF,6FAAuF;AACvF,6FAAuF;AAqChF,IAAM,SAAS,GAAf,MAAM,SAAS;CAAG,CAAA;AAAZ,8BAAS;oBAAT,SAAS;IAnCrB,IAAA,eAAM,EAAC;QACN,WAAW,EAAE,CAAC,8BAAa,EAAE,qDAAwB,EAAE,+CAAqB,CAAC;QAC7E,OAAO,EAAE;YACP,4BAAY;YACZ,wBAAU;YACV,kCAAe;YACf,0DAA0B;YAC1B,4CAAmB;SACpB;QACD,SAAS,EAAE;YACT,4CAAmB;YACnB,sCAAgB;YAChB,uDAAwB;YACxB,sDAAwB;YACxB,4CAAmB;YACnB,4DAA2B;YAC3B,wDAAyB;YACzB,uEAAgC;YAChC,+DAA4B;YAC5B,yDAA0B;YAC1B,yDAAyB;YACzB,iEAA6B;YAC7B,wDAAyB;YACzB,8CAAoB;YACpB,4DAA2B;YAC3B,yCAAkB;YAClB,uDAAwB;YACxB,iDAAqB;YACrB,mDAAmB;YACnB,gDAAqB;YACrB,+DAA4B;YAC5B,sDAAwB;YACxB,8CAAoB;SACrB;KACF,CAAC;GACW,SAAS,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { ApisModule } from 'src/apis/apis.module';\r\nimport { MessagingModule } from 'src/messaging/messaging.module';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { AccountTransferLimitModule } from '../account-transfer-limit/account-transfer-limit.module';\r\nimport { TwoFactorAuthModule } from '../two-factor-auth/two-factor-auth.module';\r\nimport { TwoFactorAuthService } from '../two-factor-auth/two-factor-auth.service';\r\nimport { FavoritePixController } from './controllers/favorite-pix.controller';\r\nimport { PixController } from './controllers/pix.controller';\r\nimport { TransactionPixController } from './controllers/transaction-pix.controller';\r\nimport { AddFavoritePixService } from './service/add-favorite-pix.service';\r\nimport { ClaimCancelPixKeyService } from './service/claim-cancel-pix-key.service';\r\nimport { ClaimConfirmPixKeyService } from './service/claim-confim-pix-key.service';\r\nimport { ClaimGetPixKeyService } from './service/claim-get-pix-key.service';\r\nimport { CreatePixKeyService } from './service/create-pix-key.service';\r\nimport { CreatePixQRCodeDynamicService } from './service/create-pix-qrcode-dynamic.service';\r\nimport { CreatePixQRCodeStaticService } from './service/create-pix-qrcode-static.service';\r\nimport { DeleteFavoritePixService } from './service/delete-favorite-pix.service';\r\nimport { DeletePixKeyService } from './service/delete-pix-key.service';\r\nimport { GetFavoritePixAccountService } from './service/get-favorite-pix-account.service';\r\nimport { GetInternalPixTransactionService } from './service/get-internal-pix-transaction.service';\r\nimport { GetPixKeyExternalService } from './service/get-pix-key-external.service';\r\nimport { GetPixKeyService } from './service/get-pix-key.service';\r\nimport { GetPixParticipantsService } from './service/get-pix-participants.service';\r\nimport { InternalTransactionService } from './service/internal-transaction.service';\r\nimport { ProcessScheduledPix } from './service/process-scheduled-pix.service';\r\nimport { ReadQRCodePixService } from './service/read-qrcode-pix.service';\r\nimport { RequestClaimPixKeyService } from './service/request-claim-pix-key.service';\r\nimport { ReversalPixService } from './service/reversal-pix.service';\r\nimport { TransactionPixKeyService } from './service/transaction-pix-key.service';\r\nimport { TransactionPixManualService } from './service/transaction-pix-manual.service';\r\nimport { TransactionPixStatusService } from './service/transaction-pix-status.service';\r\n\r\n@Module({\r\n  controllers: [PixController, TransactionPixController, FavoritePixController],\r\n  imports: [\r\n    SharedModule,\r\n    ApisModule,\r\n    MessagingModule,\r\n    AccountTransferLimitModule,\r\n    TwoFactorAuthModule,\r\n  ],\r\n  providers: [\r\n    CreatePixKeyService,\r\n    GetPixKeyService,\r\n    GetPixKeyExternalService,\r\n    TransactionPixKeyService,\r\n    DeletePixKeyService,\r\n    TransactionPixStatusService,\r\n    GetPixParticipantsService,\r\n    GetInternalPixTransactionService,\r\n    CreatePixQRCodeStaticService,\r\n    InternalTransactionService,\r\n    RequestClaimPixKeyService,\r\n    CreatePixQRCodeDynamicService,\r\n    ClaimConfirmPixKeyService,\r\n    ReadQRCodePixService,\r\n    TransactionPixManualService,\r\n    ReversalPixService,\r\n    ClaimCancelPixKeyService,\r\n    ClaimGetPixKeyService,\r\n    ProcessScheduledPix,\r\n    AddFavoritePixService,\r\n    GetFavoritePixAccountService,\r\n    DeleteFavoritePixService,\r\n    TwoFactorAuthService,\r\n  ],\r\n})\r\nexport class PixModule {}\r\n"]}