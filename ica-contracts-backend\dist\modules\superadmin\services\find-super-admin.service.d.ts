import { SuperadminEntity } from 'src/shared/database/typeorm/entities/superadmin.entity';
import { Repository } from 'typeorm';
import { SuperAdminDto } from '../dto/get-super-admin-request.dto';
export declare class SuperAdminService {
    private superAdminRepository;
    constructor(superAdminRepository: Repository<SuperadminEntity>);
    perform(input: SuperAdminDto): Promise<{
        superadmin: {
            superAdminId: string;
            ownerId: string;
            admin: any[];
        };
    }>;
}
