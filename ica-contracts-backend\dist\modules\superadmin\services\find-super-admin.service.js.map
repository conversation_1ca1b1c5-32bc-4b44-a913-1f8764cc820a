{"version": 3, "file": "find-super-admin.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/find-super-admin.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,mGAA0F;AAC1F,qCAAqC;AAK9B,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YAEU,oBAAkD;QAAlD,yBAAoB,GAApB,oBAAoB,CAA8B;IACzD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAoB;QAChC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,IAAI,EAAE;YACzC,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;SACtC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CACzB,sBAAsB,KAAK,CAAC,OAAO,aAAa,CACjD,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG;YACf,UAAU,EAAE;gBACV,YAAY,EAAE,UAAU,CAAC,EAAE;gBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,KAAK,EAAE,EAAE;aACV;SACF,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG;gBACf,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,IAAI,IAAI;gBAC/B,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,IAAI,IAAI;gBAC7B,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,IAAI,IAAI;gBACjC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,IAAI,IAAI;gBACjC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,SAAS,IAAI,IAAI;aAC1C,CAAC;YAEF,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AA1CY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oCAAgB,CAAC,CAAA;qCACL,oBAAU;GAH/B,iBAAiB,CA0C7B", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { SuperadminEntity } from 'src/shared/database/typeorm/entities/superadmin.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { SuperAdminDto } from '../dto/get-super-admin-request.dto';\r\n\r\n@Injectable()\r\nexport class SuperAdminService {\r\n  constructor(\r\n    @InjectRepository(SuperadminEntity)\r\n    private superAdminRepository: Repository<SuperadminEntity>,\r\n  ) {}\r\n\r\n  async perform(input: SuperAdminDto) {\r\n    const superAdmin = await this.superAdminRepository.findOne({\r\n      where: { ownerId: input.ownerId || null },\r\n      relations: { admin: { owner: true } },\r\n    });\r\n\r\n    if (!superAdmin) {\r\n      throw new NotFoundException(\r\n        `Superadmin with ID ${input.ownerId} not found.`,\r\n      );\r\n    }\r\n\r\n    const response = {\r\n      superadmin: {\r\n        superAdminId: superAdmin.id,\r\n        ownerId: superAdmin.ownerId,\r\n        admin: [],\r\n      },\r\n    };\r\n\r\n    for (const admin of superAdmin.admin) {\r\n      const adminObj = {\r\n        adminId: admin.id,\r\n        ownerId: admin.ownerId,\r\n        name: admin.owner?.name || null,\r\n        cpf: admin.owner?.cpf || null,\r\n        phone: admin.owner?.phone || null,\r\n        email: admin.owner?.email || null,\r\n        createdAt: admin.owner?.createdAt || null,\r\n      };\r\n\r\n      response.superadmin.admin.push(adminObj);\r\n    }\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}