import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories';
import { QueueNames } from '@/main/config/queue-names';
import { createQueueAdapter } from '../bullmq';
import { AwaitingAuditSignature } from '@/infrastructure/jobs/node-cron/awaiting-audit-signature.job';

export function makeProcessAuditSignatureJob() {
  const investorRepository = new PrismaInvestmentContractRepository();

  const queueGateway = createQueueAdapter(QueueNames.PROCESS_AUDIT_SIGNATURE);

  const awaitingInvestorSignature = new AwaitingAuditSignature(
    investorRepository,
    queueGateway
  );

  return awaitingInvestorSignature;
}
