import { IGetAccountTransactionsResponse } from 'src/apis/icainvest-credit/responses/get-account-transactions.response';
import { GetAccountTransactionsService } from 'src/apis/icainvest-credit/services/get-account-transactions.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';
import { GetAccountRechargesDTO } from '../dto/get-account-recharges.dto';
export declare class GetAccountRecharges {
    private accountDb;
    private getAccountTransactionsService;
    constructor(accountDb: Repository<AccountEntity>, getAccountTransactionsService: GetAccountTransactionsService);
    perform(data: GetAccountRechargesDTO): Promise<{
        debits: IGetAccountTransactionsResponse[];
        credits: IGetAccountTransactionsResponse[];
        recharges: IGetAccountTransactionsResponse[];
    }>;
    private filterTransactionsByDate;
    private groupTransactionsByType;
}
