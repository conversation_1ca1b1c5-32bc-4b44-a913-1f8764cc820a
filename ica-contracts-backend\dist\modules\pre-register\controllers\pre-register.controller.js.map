{"version": 3, "file": "pre-register.controller.js", "sourceRoot": "/", "sources": ["modules/pre-register/controllers/pre-register.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAUwB;AACxB,gFAA8D;AAC9D,iEAAwD;AACxD,0EAAgE;AAEhE,sEAAgE;AAChE,oFAA6E;AAC7E,oFAA6E;AAC7E,8EAAuE;AACvE,wEAAkE;AAClE,mFAA6E;AAC7E,iGAA0F;AAC1F,iGAA0F;AAC1F,2FAAoF;AACpF,qFAA+E;AAC/E,yGAAkG;AAG3F,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEU,UAAiC,EAEjC,gBAA6C,EAE7C,aAAuC,EAEvC,WAAmC,EAEnC,gBAA6C,EAE7C,oBAAqD;QAVrD,eAAU,GAAV,UAAU,CAAuB;QAEjC,qBAAgB,GAAhB,gBAAgB,CAA6B;QAE7C,kBAAa,GAAb,aAAa,CAA0B;QAEvC,gBAAW,GAAX,WAAW,CAAwB;QAEnC,qBAAgB,GAAhB,gBAAgB,CAA6B;QAE7C,yBAAoB,GAApB,oBAAoB,CAAiC;IAC5D,CAAC;IAKE,AAAN,KAAK,CAAC,GAAG,CAEP,IAAuB,EACZ,OAAY;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACpE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,MAAM,CAEV,KAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACzD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,SAAS,CAEb,KAA8B;QAE9B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,IAAI,CAER,IAAwB;QAExB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,SAAS,CAEb,IAA6B,EAClB,OAAY;QAEvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACpE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,aAAa,CAEjB,OAAY;QAEZ,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAClE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAnHY,sDAAqB;AAmB1B;IAHL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,EAAE,sBAAS,CAAC,OAAO,CAAC;IAC1C,IAAA,aAAI,EAAC,KAAK,CAAC;IAET,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADJ,wCAAiB;;gDAYxB;AAGK;IADL,IAAA,YAAG,EAAC,KAAK,CAAC;IAER,WAAA,IAAA,cAAK,GAAE,CAAA;;qCACD,+CAAoB;;mDAW5B;AAGK;IADL,IAAA,YAAG,EAAC,QAAQ,CAAC;IAEX,WAAA,IAAA,cAAK,GAAE,CAAA;;qCACD,qDAAuB;;sDAW/B;AAGK;IADL,IAAA,aAAI,EAAC,MAAM,CAAC;IAEV,WAAA,IAAA,aAAI,GAAE,CAAA;;qCACD,0CAAkB;;iDAWzB;AAGK;IADL,IAAA,aAAI,EAAC,YAAY,CAAC;IAEhB,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADJ,qDAAuB;;sDAY9B;AAGK;IADL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IAEnB,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;0DAYX;gCAlHU,qBAAqB;IADjC,IAAA,mBAAU,EAAC,cAAc,CAAC;IAGtB,WAAA,IAAA,eAAM,EAAC,gDAAqB,CAAC,CAAA;IAE7B,WAAA,IAAA,eAAM,EAAC,6DAA2B,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,uDAAwB,CAAC,CAAA;IAEhC,WAAA,IAAA,eAAM,EAAC,kDAAsB,CAAC,CAAA;IAE9B,WAAA,IAAA,eAAM,EAAC,6DAA2B,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,qEAA+B,CAAC,CAAA;qCATpB,gDAAqB;QAEf,6DAA2B;QAE9B,uDAAwB;QAE1B,kDAAsB;QAEjB,6DAA2B;QAEvB,qEAA+B;GAbpD,qBAAqB,CAmHjC", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Get,\r\n  Headers,\r\n  HttpException,\r\n  Inject,\r\n  Post,\r\n  Query,\r\n  UseGuards,\r\n} from '@nestjs/common';\r\nimport { Roles } from 'src/shared/decorators/roles.decorator';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\n\r\nimport { AddPreRegisterDto } from '../dto/add-pre-register.dto';\r\nimport { DirectAddPreRegisterDto } from '../dto/direct-add-pre-register.dto';\r\nimport { GetFilterPreRegisterDto } from '../dto/get-filter-pre-register.dto';\r\nimport { GetOnePreRegisterDto } from '../dto/get-one-pre-register.dto';\r\nimport { SendPreRegisterDto } from '../dto/send-pre-register.dto';\r\nimport { AddPreRegisterService } from '../services/add-pre-register.service';\r\nimport { DirectAddPreRegisterService } from '../services/direct-add-pre-register.service';\r\nimport { GetFilterPreRegisterService } from '../services/get-filter-pre-register.service';\r\nimport { GetOnePreRegisterService } from '../services/get-one-pre-register.service';\r\nimport { SendPreRegisterService } from '../services/send-pre-register.service';\r\nimport { ValidateTokenPreRegisterService } from '../services/validate-token-pre-register.service';\r\n\r\n@Controller('pre-register')\r\nexport class PreRegisterController {\r\n  constructor(\r\n    @Inject(AddPreRegisterService)\r\n    private addService: AddPreRegisterService,\r\n    @Inject(GetFilterPreRegisterService)\r\n    private getFilterService: GetFilterPreRegisterService,\r\n    @Inject(GetOnePreRegisterService)\r\n    private getOneService: GetOnePreRegisterService,\r\n    @Inject(SendPreRegisterService)\r\n    private sendService: SendPreRegisterService,\r\n    @Inject(DirectAddPreRegisterService)\r\n    private directAddService: DirectAddPreRegisterService,\r\n    @Inject(ValidateTokenPreRegisterService)\r\n    private validateTokenService: ValidateTokenPreRegisterService,\r\n  ) {}\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR)\r\n  @Post('add')\r\n  async add(\r\n    @Body()\r\n    body: AddPreRegisterDto,\r\n    @Headers() headers: any,\r\n  ) {\r\n    try {\r\n      const response = await this.addService.perform(body, headers.token);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('one')\r\n  async getOne(\r\n    @Query()\r\n    query: GetOnePreRegisterDto,\r\n  ) {\r\n    try {\r\n      const response = await this.getOneService.perform(query);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('filter')\r\n  async getFilter(\r\n    @Query()\r\n    query: GetFilterPreRegisterDto,\r\n  ) {\r\n    try {\r\n      const response = await this.getFilterService.perform(query);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('send')\r\n  async send(\r\n    @Body()\r\n    body: SendPreRegisterDto,\r\n  ) {\r\n    try {\r\n      const response = await this.sendService.perform(body);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('direct-add')\r\n  async directAdd(\r\n    @Body()\r\n    body: DirectAddPreRegisterDto,\r\n    @Headers() headers: any,\r\n  ) {\r\n    try {\r\n      const response = this.directAddService.perform(body, headers.token);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('token-validate')\r\n  async tokenValidate(\r\n    @Headers()\r\n    headers: any,\r\n  ) {\r\n    try {\r\n      const response = this.validateTokenService.perform(headers.token);\r\n      return response;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}