{"version": 3, "file": "internal-transaction.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/internal-transaction.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAKyB;AAEzB,MAAa,sBAAsB;CAgBlC;AAhBD,wDAgBC;AAbC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;sDACF;AAIf;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;mEACgB;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACS;AAIpB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;6DACW", "sourcesContent": ["import {\r\n  IsDefined,\r\n  IsNumberString,\r\n  IsOptional,\r\n  IsString,\r\n} from 'class-validator';\r\n\r\nexport class InternalTransactionDto {\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  amount: string;\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  transactionPassword?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  description: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  creditAccount: string;\r\n}\r\n"]}