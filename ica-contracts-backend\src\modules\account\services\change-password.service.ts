import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as brcypt from 'bcrypt';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Equal, Repository } from 'typeorm';

import { ChangePasswordDto } from '../dto/change-password.dto';

@Injectable()
export class ChangePasswordService {
  constructor(
    @InjectRepository(OwnerEntity)
    private ownerRepo: Repository<OwnerEntity>,
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(BusinessEntity)
    private businessRepo: Repository<BusinessEntity>,
  ) {}

  async perform(data: ChangePasswordDto, id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    const hash = await brcypt.hash(data.newPassword, 10);

    if (account.type === 'physical') {
      await this.ownerRepo.update(account.ownerId, {
        password: hash,
        temporaryPassword: false,
      });
    }

    if (account.type === 'business') {
      await this.businessRepo.update(account.businessId, {
        password: hash,
        temporaryPassword: false,
      });
    }
  }
}
