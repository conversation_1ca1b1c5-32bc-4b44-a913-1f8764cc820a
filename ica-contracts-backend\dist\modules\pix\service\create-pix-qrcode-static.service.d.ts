import { PixQRCodeCelcoinService } from 'src/apis/celcoin/services/pix-qrcode-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { PixQRCodeEntity } from 'src/shared/database/typeorm/entities/pix-qrcode.entity';
import { Repository } from 'typeorm';
import { CreatePixQRCodeStaticDto } from '../dto/create-pix-qrcode-static.dto';
import { ICreatePixQRCodeStaticResponse } from '../response/create-pix-qrcode-static.response';
export declare class CreatePixQRCodeStaticService {
    private accountDb;
    private pixQRCodeRepo;
    private pixQRCodeCelcoin;
    constructor(accountDb: Repository<AccountEntity>, pixQRCodeRepo: Repository<PixQRCodeEntity>, pixQRCodeCelcoin: PixQRCodeCelcoinService);
    perform(data: CreatePixQRCodeStaticDto, id: string): Promise<ICreatePixQRCodeStaticResponse>;
}
