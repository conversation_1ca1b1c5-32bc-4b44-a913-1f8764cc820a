import { Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { IClient } from 'src/apis/ica-contract/requests/ica-contract.request';

@Injectable()
export class IcaContractService {
  private readonly axiosInstance: AxiosInstance;
  private readonly url =
    'https://contract-pdf.prodbr01.icabank.org/v2/generate_pdf';

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: this.url,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async gerarPdf(cliente: any, type?: string) {
    try {
      
      const response = await this.axiosInstance.post(
        this.url,
        { ...cliente },
        {
          responseType: 'arraybuffer',
        },
      );

      

      
      
      return Buffer.from(response.data, 'binary');
    } catch (error) {
      
      throw new Error(`Erro ao gerar PDF: ${error.message}`);
    }
  }
}
