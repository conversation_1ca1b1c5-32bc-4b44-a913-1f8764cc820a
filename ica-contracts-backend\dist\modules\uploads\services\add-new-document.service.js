"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddNewDocumentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const AWS = require("aws-sdk");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const uploads_entity_1 = require("../../../shared/database/typeorm/entities/uploads.entity");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
let AddNewDocumentService = class AddNewDocumentService {
    constructor(ownerRoleRelationRepository, uploadsDb) {
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.uploadsDb = uploadsDb;
        this.s3 = new AWS.S3({
            accessKeyId: process.env.AWS_ACCESS_KEY,
            secretAccessKey: process.env.AWS_SECRET_KEY,
            region: process.env.AWS_REGION,
        });
    }
    async perform(data, file) {
        const ownerRole = await this.ownerRoleRelationRepository.findOne({
            where: [
                { id: (0, typeorm_2.Equal)(data.id) },
                { owner: { account: { id: (0, typeorm_2.Equal)(data.id) } } },
                { business: { account: { id: (0, typeorm_2.Equal)(data.id) } } },
            ],
            relations: { owner: true, business: true },
        });
        if (!ownerRole)
            throw new common_1.BadRequestException('Nenhum perfil encontrado');
        const url = await this.uploadToS3(file, ownerRole.business?.cnpj || ownerRole.owner?.cpf);
        const upload = await this.uploadsDb.findOne({
            where: {
                type: data.type,
                businessId: ownerRole.businessId,
                ownerId: ownerRole.ownerId,
            },
        });
        if (upload) {
            await this.uploadsDb.update(upload.id, {
                url,
            });
            return {
                message: 'Documento atualizado',
            };
        }
        const create = this.uploadsDb.create({
            businessId: ownerRole.businessId,
            ownerId: ownerRole.ownerId,
            type: data.type,
            url,
        });
        await this.uploadsDb.save(create);
        return {
            message: 'Documento criado',
        };
    }
    async uploadToS3(file, folder) {
        const fileName = `${folder}/${(0, uuid_1.v4)()}-${file.originalname}`;
        const params = {
            Bucket: process.env.S3_BUCKET,
            Key: fileName,
            Body: file.buffer,
            ContentType: file.mimetype,
            ACL: 'public-read',
        };
        try {
            const uploadResult = await this.s3.upload(params).promise();
            return uploadResult.Location;
        }
        catch (error) {
            throw new common_1.BadRequestException('Erro ao fazer upload do arquivo para o S3');
        }
    }
};
exports.AddNewDocumentService = AddNewDocumentService;
exports.AddNewDocumentService = AddNewDocumentService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(uploads_entity_1.UploadsEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AddNewDocumentService);
//# sourceMappingURL=add-new-document.service.js.map