import { PaginationMeta } from '../../helpers/pagination-meta.dto';
import { IPaginatedResult } from '../../helpers/pagination-query';
export declare class AdvisorInfoResponse {
    advisorId: string;
    rank: number;
    name: string;
    avatar: string;
    totalValue: number;
}
export declare class GetAllAdvisorsWithActiveContractsResponseDto implements IPaginatedResult<AdvisorInfoResponse> {
    data: AdvisorInfoResponse[];
    meta: PaginationMeta;
}
