export interface IDocument {
  id: string;
  titulo: string;
  status: string;
  tag?: string;
  numero_externo?: string;
  data_criacao: string;
  url_documento: string;
  signatarios: any;
}

export interface IListDocumentsResponse {
  erro: boolean;
  message: string;
  payload: {
    documentos: IDocument[];
    total: number;
    page: number;
    perPage: number;
    num_arquivos: number;
    lastPage: number;
  };
}
