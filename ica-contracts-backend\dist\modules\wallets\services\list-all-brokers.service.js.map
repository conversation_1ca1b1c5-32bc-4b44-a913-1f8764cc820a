{"version": 3, "file": "list-all-brokers.service.js", "sourceRoot": "/", "sources": ["modules/wallets/services/list-all-brokers.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qHAA0G;AAC1G,yGAA+F;AAC/F,iEAAwD;AACxD,qCAAqC;AAG9B,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEU,SAAyC,EAEzC,QAA6C;QAF7C,cAAS,GAAT,SAAS,CAAgC;QAEzC,aAAQ,GAAR,QAAQ,CAAqC;IACpD,CAAC;IACJ,KAAK,CAAC,OAAO;QACX,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACvC,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE;oBACL,OAAO,EAAE,IAAI;iBACd;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,IAAI;iBACd;aACF;YACD,MAAM,EAAE;gBACN,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI;iBACX;gBACD,QAAQ,EAAE;oBACR,WAAW,EAAE,IAAI;iBAClB;aACF;YACD,KAAK,EAAE;gBACL,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,MAAM;iBACvB;aACF;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACrB,IAAI,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5D,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE,WAAW;oBACxD,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,SAAS,EACP,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;iBAC/D,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAhDY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,yCAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADvB,oBAAU;QAEX,oBAAU;GALnB,qBAAqB,CAgDjC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\n@Injectable()\r\nexport class ListAllBrokersService {\r\n  constructor(\r\n    @InjectRepository(WalletsViewsEntity)\r\n    private walletsDb: Repository<WalletsViewsEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private perfilDb: Repository<OwnerRoleRelationEntity>,\r\n  ) {}\r\n  async perform() {\r\n    const response = [];\r\n\r\n    const brokers = await this.perfilDb.find({\r\n      relations: {\r\n        role: true,\r\n        owner: {\r\n          account: true,\r\n        },\r\n        business: {\r\n          account: true,\r\n        },\r\n      },\r\n      select: {\r\n        owner: {\r\n          name: true,\r\n        },\r\n        business: {\r\n          companyName: true,\r\n        },\r\n      },\r\n      where: {\r\n        role: {\r\n          name: RolesEnum.BROKER,\r\n        },\r\n      },\r\n    });\r\n\r\n    brokers.map((broker) => {\r\n      if (broker.owner?.account[0] || broker.business?.account[0]) {\r\n        response.push({\r\n          name: broker.owner?.name || broker.business?.companyName,\r\n          id: broker.id,\r\n          accountId:\r\n            broker.owner?.account[0].id || broker.business.account[0].id,\r\n        });\r\n      }\r\n    });\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}