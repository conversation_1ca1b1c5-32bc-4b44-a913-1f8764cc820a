import { Injectable, HttpException } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';

import { ICreateTransactionsRequest } from '../requests/create-transaction.request';
import { ICreateTransactionResponse } from '../responses/create-transaction.response';

@Injectable()
export class CreateTransactionsService {
  async createTransaction(
    transaction: ICreateTransactionsRequest,
  ): Promise<ICreateTransactionResponse> {
    try {
      const url = `${process.env.TRANSACTIONS_URL}/transactions`;

      const { data }: AxiosResponse = await axios.post(url, transaction, {
        headers: {
          'X-Api-Key': 'c9e0d900-edd3-45f7-9e69-abd96edbf724',
          'Content-Type': 'application/json',
        },
      });

      return data;
    } catch (error) {
      console.log(error);

      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }
}
