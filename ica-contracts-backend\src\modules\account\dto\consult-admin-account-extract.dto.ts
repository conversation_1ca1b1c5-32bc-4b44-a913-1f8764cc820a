import {
  IsDefined,
  IsE<PERSON>,
  IsISO86<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsS<PERSON>,
  IsUUI<PERSON>,
} from 'class-validator';
import { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';

export class IConsultAdminAccountExtract {
  @IsDefined()
  @IsISO8601(
    { strict: true },
    { message: 'A data de início deve estar no formato ISO 8601' },
  )
  DateFrom: string;

  @IsDefined()
  @IsISO8601(
    { strict: true },
    { message: 'A data de início deve estar no formato ISO 8601' },
  )
  DateTo: string;

  @IsDefined()
  @IsString()
  LimitPerPage?: string;

  @IsDefined()
  @IsString()
  Page?: string;

  @IsOptional()
  @IsEnum(TransactionMovementTypeEnum)
  transactionType?: TransactionMovementTypeEnum;

  @IsDefined()
  @IsUUID()
  accountId: string;
}
