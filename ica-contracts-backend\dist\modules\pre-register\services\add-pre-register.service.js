"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPreRegisterService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const jsonwebtoken_1 = require("jsonwebtoken");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const pre_register_entity_1 = require("../../../shared/database/typeorm/entities/pre-register.entity");
const pre_register_status_enum_1 = require("../../../shared/enums/pre-register-status.enum");
const typeorm_2 = require("typeorm");
let AddPreRegisterService = class AddPreRegisterService {
    constructor(preRegisterDb, accountDb) {
        this.preRegisterDb = preRegisterDb;
        this.accountDb = accountDb;
    }
    async perform(data, token) {
        const decoded = (0, jsonwebtoken_1.decode)(token);
        const preRegister = await this.preRegisterDb.findOne({
            where: {
                document: (0, typeorm_2.Equal)(data.document.trim()),
                status: pre_register_status_enum_1.PreRegisterStatusEnum.ACTIVE,
            },
        });
        if (preRegister) {
            throw new common_1.BadRequestException('Ja existe um pre registro ativo para este documento');
        }
        const account = await this.accountDb.findOne({
            where: [
                {
                    owner: {
                        cpf: (0, typeorm_2.Equal)(data.document),
                    },
                },
                {
                    business: {
                        cnpj: (0, typeorm_2.Equal)(data.document),
                    },
                },
            ],
        });
        if (account) {
            throw new common_1.BadRequestException('Ja existe uma conta com este documento');
        }
        const create = this.preRegisterDb.create({
            adviserId: decoded.adviserId,
            addressComplement: data.address.complement,
            addressNumber: data.address.number,
            neighborhood: data.address.neighborhood,
            zipCode: data.address.zipCode,
            city: data.address.city,
            document: data.document,
            dtBirth: data.dtBirth,
            email: data.email,
            investmentModality: data.investment.modality,
            investmentTerm: data.investment.term,
            investmentValue: data.investment.value,
            investmentYield: data.investment.yield,
            name: data.name,
            phoneNumber: data.phoneNumber,
            rg: data.rg,
            observations: data.observations,
            status: pre_register_status_enum_1.PreRegisterStatusEnum.ACTIVE,
            amountQuotes: data.investment?.amountQuotes,
            gracePeriod: data.investment.gracePeriod,
            purchaseWith: data.investment.purchaseWith,
            state: data.address.state,
            motherName: data.motherName,
        });
        await this.preRegisterDb.save(create);
    }
};
exports.AddPreRegisterService = AddPreRegisterService;
exports.AddPreRegisterService = AddPreRegisterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(pre_register_entity_1.PreRegisterEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AddPreRegisterService);
//# sourceMappingURL=add-pre-register.service.js.map