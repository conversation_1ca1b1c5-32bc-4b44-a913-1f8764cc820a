"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetRecentTedsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ted_celcoin_service_1 = require("../../../apis/celcoin/services/ted-celcoin.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const typeorm_2 = require("typeorm");
let GetRecentTedsService = class GetRecentTedsService {
    constructor(transactionRepo, accountRepo, celcoinService) {
        this.transactionRepo = transactionRepo;
        this.accountRepo = accountRepo;
        this.celcoinService = celcoinService;
    }
    async perform(ownerId) {
        const account = await this.accountRepo.findOne({
            where: [{ ownerId: (0, typeorm_2.Equal)(ownerId) }, { businessId: (0, typeorm_2.Equal)(ownerId) }],
        });
        if (!account)
            throw new common_1.NotFoundException('Conta não encontrada.');
        const transactions = await this.transactionRepo.find({
            where: {
                type: (0, typeorm_2.Like)('TED%'),
                accountId: account.id,
            },
            order: { createdAt: 'DESC' },
            take: 5,
        });
        const output = await Promise.all(transactions.map(async (transaction) => {
            const { body } = await this.celcoinService.getTed({
                id: transaction.code,
            });
            return {
                id: body.id,
                amount: body.amount,
                clientCode: body.clientCode,
                description: body.description,
                creditParty: body.creditParty,
                debitParty: body.debitParty,
            };
        }));
        return {
            transactions: output,
            transactionsCount: output.length,
        };
    }
};
exports.GetRecentTedsService = GetRecentTedsService;
exports.GetRecentTedsService = GetRecentTedsService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(2, (0, common_1.Inject)(ted_celcoin_service_1.TedCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        ted_celcoin_service_1.TedCelcoinService])
], GetRecentTedsService);
//# sourceMappingURL=get-recent-teds.service.js.map