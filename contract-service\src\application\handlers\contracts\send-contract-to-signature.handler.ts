import type { ISignatureAPI } from '@/application/interfaces/signature-api'
import type { ContractPdfGeneratedEvent } from '@/domain/events/contracts/contract-pdf-generated.event'
import { ContractSignatureFailedEvent } from '@/domain/events/contracts/contract-signature-failed.event'
import { ContractSignatureSentEvent } from '@/domain/events/contracts/contract-signature-sent.event'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import { DomainEventMediator } from '@/domain/shared'

export class SendContractToSignatureHandler {
  constructor(
    private readonly repo: IInvestmentContractRepository,
    private readonly signatureApi: ISignatureAPI,
    private readonly logger: LoggerGateway
  ) {}

  async handle(event: ContractPdfGeneratedEvent): Promise<void> {
    this.logger.info(
      `Iniciando o processamento do evento ContractPdfGeneratedEvent para o contrato com ID: ${event.payload.contract.id}`
    )

    const contract = event.payload.contract
    this.logger.info(
      `Contrato encontrado. ID: ${contract.id}. Iniciando envio para assinatura.`
    )

    try {
      const response = await this.signatureApi.send(
        contract,
        event.payload.contractFile
      )

      if (response.isLeft()) {
        this.logger.error(
          `Erro no envio para assinatura do contrato com ID: ${contract.id}. Mensagem: ${response.value.message}`
        )
        contract.markAsSignatureFailed()
        await this.repo.update(contract.id, contract)
        DomainEventMediator.dispatch(
          new ContractSignatureFailedEvent(contract.id, response.value.message)
        )
        this.logger.info(
          `Evento ContractSignatureFailedEvent despachado para o contrato com ID: ${contract.id}`
        )
        return
      }

      this.logger.info(
        `Assinatura enviada com sucesso para o contrato com ID: ${contract.id}. Request ID: ${response.value.requestId}`
      )
      contract.markSignatureSent(response.value.requestId)
      contract.markInvestorSignaturePending()
      await this.repo.update(contract.id, contract)
      this.logger.info(
        `Contrato atualizado com status de assinatura enviada para o contrato com ID: ${contract.id}`
      )

      DomainEventMediator.dispatch(
        new ContractSignatureSentEvent(contract.id, response.value.requestId)
      )
      this.logger.info(
        `Evento ContractSignatureSentEvent despachado para o contrato com ID: ${contract.id}`
      )
    } catch (error) {
      contract.markAsSignatureFailed()
      await this.repo.update(contract.id, contract)
    }
  }
}
