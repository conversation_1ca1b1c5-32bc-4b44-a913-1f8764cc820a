import type { ResubmitRejectedContractDTO } from "@/application/dtos/contracts/resubmit-rejected-contract.dto";
import type {
  ITransactionContext,
  IUnitOfWork,
} from "@/application/interfaces";
import type { IUseCase } from "@/application/interfaces/usecase";
import {
  ContractStatus,
  InvestmentContract,
} from "@/domain/entities/contracts";
import { Company, Individual } from "@/domain/entities/parties";
import type { LoggerGateway } from "@/domain/gateways/logger.gateway";
import type { IStorageGateway } from "@/domain/gateways/storage.gateway";
import type { IInvestmentContractRepository } from "@/domain/repositories";
import { type Either, left, right } from "@/domain/shared";
import { ContractType } from "@/domain/value-objects/contract-type.value-object";
import { Money } from "@/domain/value-objects/money.value-object";
import { Percentage } from "@/domain/value-objects/percentage.value-object";
import { PaymentMethod } from "@/domain/value-objects/payment-method.value-object";
import { Profile } from "@/domain/value-objects/profile.value-object";
import { CompanyType } from "@/domain/value-objects/company-type.value-object";

interface ResubmitRejectedContractResponse {
  id: string;
  status: ContractStatus;
}

interface UploadedFiles {
  personalDocumentUrl?: string;
  proofOfResidenceUrl?: string;
  cardCnpjUrl?: string;
  proofPaymentUrl?: string;
  contractUrl?: string;
}

export class ResubmitRejectedContractUseCase
  implements
    IUseCase<
      ResubmitRejectedContractDTO,
      Promise<Either<Error, ResubmitRejectedContractResponse>>
    >
{
  constructor(
    private readonly unitOfWork: IUnitOfWork,
    private readonly contractRepository: IInvestmentContractRepository,
    private readonly logger: LoggerGateway,
    private readonly storage: IStorageGateway
  ) {}

  async execute(
    data: ResubmitRejectedContractDTO
  ): Promise<Either<Error, ResubmitRejectedContractResponse>> {
    try {
      this.logger.info(`Iniciando ressubmissão do contrato ${data.contractId}`);

      const result = await this.unitOfWork.runInTransaction(
        async (
          tx: ITransactionContext
        ): Promise<Either<Error, ResubmitRejectedContractResponse>> => {
          // Passo 1: Buscar e validar contrato
          const contractResult = await this.findAndValidateContract(
            data.contractId,
            tx
          );
          if (contractResult.isLeft()) {
            return left(contractResult.value);
          }
          const contract = contractResult.value;

          // Passo 2: Fazer upload dos arquivos
          const uploadedFilesResult = await this.uploadFiles(data, contract);
          if (uploadedFilesResult.isLeft()) {
            return left(uploadedFilesResult.value);
          }
          const uploadedFiles = uploadedFilesResult.value;

          // Passo 3: Atualizar dados do investidor
          const investorUpdateResult = await this.updateInvestorData(
            data,
            contract,
            uploadedFiles
          );
          if (investorUpdateResult.isLeft()) {
            return left(investorUpdateResult.value);
          }

          // Passo 4: Atualizar dados do contrato
          const contractUpdateResult = await this.updateContractData(
            data,
            contract,
            uploadedFiles
          );
          if (contractUpdateResult.isLeft()) {
            return left(contractUpdateResult.value);
          }

          // Passo 5: Salvar alterações no repositório
          const saveResult =
            await this.contractRepository.resubmitRejectedContract(
              contract.id,
              contract
            );
          if (saveResult.isLeft()) {
            this.logger.error(
              `Erro ao salvar contrato: ${saveResult.value.message}`
            );
            return left(saveResult.value);
          }

          this.logger.info(`Contrato ${contract.id} ressubmetido com sucesso`);
          return right({
            id: contract.id,
            status: contract.getStatus(),
          });
        }
      );

      if (result.isLeft()) {
        this.logger.error(`Erro na transação: ${result.value.message}`);
        return left(result.value);
      }

      return right(result.value);
    } catch (error) {
      this.logger.error(`Erro inesperado ao ressubmeter contrato: ${error}`);
      return left(new Error(`Erro ao ressubmeter contrato: ${error}`));
    }
  }

  /**
   * Busca e valida o contrato para ressubmissão
   */
  private async findAndValidateContract(
    contractId: string,
    tx: ITransactionContext
  ): Promise<Either<Error, InvestmentContract>> {
    this.logger.info(`Buscando contrato ${contractId} para validação`);

    const contractResult = await this.contractRepository.findById(
      contractId,
      tx
    );
    if (contractResult.isLeft()) {
      this.logger.error(
        `Erro ao buscar contrato ${contractId}: ${contractResult.value.message}`
      );
      return left(contractResult.value);
    }

    const contract = contractResult.value;
    if (!contract) {
      this.logger.error(`Contrato ${contractId} não encontrado`);
      return left(new Error("Contrato não encontrado"));
    }

    this.logger.info(
      `Contrato ${contract.id} encontrado com status ${contract.getStatus()}`
    );

    const allowedStatuses = [
      ContractStatus.REJECTED_BY_AUDIT,
      ContractStatus.AWAITING_AUDIT,
    ];
    if (!allowedStatuses.includes(contract.getStatus())) {
      this.logger.error(
        `Contrato ${contract.id} não está em status permitido para ressubmissão`
      );
      return left(
        new Error(
          "Contrato não está rejeitado pela auditoria ou aguardando auditoria"
        )
      );
    }

    return right(contract);
  }

  /**
   * Faz upload dos arquivos fornecidos
   */
  private async uploadFiles(
    data: ResubmitRejectedContractDTO,
    contract: InvestmentContract
  ): Promise<Either<Error, UploadedFiles>> {
    this.logger.info(
      `Iniciando upload de arquivos para contrato ${contract.id}`
    );

    const uploadedFiles: UploadedFiles = {};
    const document = contract.getInvestor().getParty().getDocument();

    // Upload documento pessoal
    if (data.personalDocument) {
      const uploadResult = await this.uploadSingleFile(
        data.personalDocument,
        `contracts/${document}/personal-document`,
        "documento pessoal"
      );
      if (uploadResult.isLeft()) {
        return left(uploadResult.value);
      }
      uploadedFiles.personalDocumentUrl = uploadResult.value;
    }

    // Upload comprovante de residência
    if (data.proofOfResidence) {
      const uploadResult = await this.uploadSingleFile(
        data.proofOfResidence,
        `contracts/${document}/proof-of-residence`,
        "comprovante de residência"
      );
      if (uploadResult.isLeft()) {
        return left(uploadResult.value);
      }
      uploadedFiles.proofOfResidenceUrl = uploadResult.value;
    }

    // Upload cartão CNPJ (apenas para PJ)
    if (data.cardCnpj && contract.getInvestor().getParty() instanceof Company) {
      const uploadResult = await this.uploadSingleFile(
        data.cardCnpj,
        `contracts/${document}/card-cnpj`,
        "cartão CNPJ"
      );
      if (uploadResult.isLeft()) {
        return left(uploadResult.value);
      }
      uploadedFiles.cardCnpjUrl = uploadResult.value;
    }

    // Upload comprovante de pagamento
    if (data.proofOfPayment) {
      const uploadResult = await this.uploadSingleFile(
        data.proofOfPayment,
        `contracts/${document}/proof-of-payment`,
        "comprovante de pagamento"
      );
      if (uploadResult.isLeft()) {
        return left(uploadResult.value);
      }
      uploadedFiles.proofPaymentUrl = uploadResult.value;
    }

    // Upload contrato
    if (data.contract) {
      const uploadResult = await this.uploadSingleFile(
        data.contract,
        `contracts/${document}/contract`,
        "contrato"
      );
      if (uploadResult.isLeft()) {
        return left(uploadResult.value);
      }
      uploadedFiles.contractUrl = uploadResult.value;
    }

    this.logger.info(
      `Upload de arquivos concluído para contrato ${contract.id}`
    );
    return right(uploadedFiles);
  }

  /**
   * Faz upload de um arquivo específico
   */
  private async uploadSingleFile(
    file: { buffer: Buffer; mimetype: string },
    basePath: string,
    fileType: string
  ): Promise<Either<Error, string>> {
    const date = new Date().getTime();
    const extension = this.getExtensionFromMimeType(file.mimetype);
    const filePath = `${basePath}_${date}.${extension}`;

    const uploadResult = await this.storage.uploadFile(
      filePath,
      file.buffer,
      file.mimetype
    );
    if (uploadResult.isLeft()) {
      this.logger.error(
        `Erro ao enviar ${fileType}: ${uploadResult.value.message}`
      );
      return left(uploadResult.value);
    }

    this.logger.info(`${fileType} enviado com sucesso: ${filePath}`);
    return right(uploadResult.value);
  }

  /**
   * Atualiza os dados do investidor (PF ou PJ)
   */
  private async updateInvestorData(
    data: ResubmitRejectedContractDTO,
    contract: InvestmentContract,
    uploadedFiles: UploadedFiles
  ): Promise<Either<Error, void>> {
    this.logger.info(
      `Atualizando dados do investidor para contrato ${contract.id}`
    );

    if (data.company && contract.getInvestor().getParty() instanceof Company) {
      return this.updateCompanyInvestorData(
        data.company,
        contract,
        uploadedFiles
      );
    }

    if (
      data.individual &&
      contract.getInvestor().getParty() instanceof Individual
    ) {
      return this.updateIndividualInvestorData(
        data.individual,
        contract,
        uploadedFiles
      );
    }

    return right(undefined);
  }

  /**
   * Atualiza dados do investidor pessoa jurídica
   */
  private async updateCompanyInvestorData(
    companyData: any,
    contract: InvestmentContract,
    uploadedFiles: UploadedFiles
  ): Promise<Either<Error, void>> {
    this.logger.info(
      `Atualizando dados da empresa para contrato ${contract.id}`
    );

    const companyTypeResult = CompanyType.create(companyData.type);
    if (companyTypeResult.isLeft()) {
      this.logger.error(
        `Erro ao criar tipo de empresa: ${companyTypeResult.value.message}`
      );
      return left(companyTypeResult.value);
    }

    const repDTO = companyData.representative;
    const company = contract.getInvestor().getParty() as Company;
    const legalRepResult = Individual.create(
      company.getLegalRepresentative().id,
      repDTO.fullName,
      {
        cpf: repDTO.cpf,
        email: repDTO.email,
        phone: repDTO.phone,
        birthDate: new Date(repDTO.birthDate),
        motherName: repDTO.motherName,
        rg: repDTO.rg,
        address: repDTO.address,
        occupation: repDTO.occupation,
        nationality: repDTO.nationality,
        issuingAgency: repDTO.issuingAgency,
      }
    );

    if (legalRepResult.isLeft()) {
      this.logger.error(
        `Erro ao criar representante legal: ${legalRepResult.value.message}`
      );
      return left(legalRepResult.value);
    }

    const updateResult = contract.resubmitContractData(
      companyData.corporateName,
      repDTO.email,
      repDTO.phone,
      companyData.address,
      {
        cpf: repDTO.cpf,
        cnpj: companyData.cnpj,
        birthDate: new Date(repDTO.birthDate),
        motherName: repDTO.motherName,
        occupation: repDTO.occupation,
        nationality: repDTO.nationality,
        rg: repDTO.rg,
        issuingAgency: repDTO.issuingAgency,
        companyType: companyTypeResult.value,
        legalRepresentative: legalRepResult.value,
        personalDocumentUrl: uploadedFiles.personalDocumentUrl,
        proofOfResidenceUrl: uploadedFiles.proofOfResidenceUrl,
        cardCnpjUrl: uploadedFiles.cardCnpjUrl,
      }
    );

    if (updateResult.isLeft()) {
      this.logger.error(
        `Erro ao atualizar dados da empresa: ${updateResult.value.message}`
      );
      return left(updateResult.value);
    }

    this.logger.info(
      `Dados da empresa atualizados com sucesso para contrato ${contract.id}`
    );
    return right(undefined);
  }

  /**
   * Atualiza dados do investidor pessoa física
   */
  private async updateIndividualInvestorData(
    individualData: any,
    contract: InvestmentContract,
    uploadedFiles: UploadedFiles
  ): Promise<Either<Error, void>> {
    this.logger.info(
      `Atualizando dados do indivíduo para contrato ${contract.id}`
    );

    const updateResult = contract.resubmitContractData(
      individualData.fullName,
      individualData.email,
      individualData.phone,
      individualData.address,
      {
        cpf: individualData.cpf,
        birthDate: new Date(individualData.birthDate),
        motherName: individualData.motherName,
        occupation: individualData.occupation,
        nationality: individualData.nationality,
        issuingAgency: individualData.issuingAgency,
        rg: individualData.rg,
        personalDocumentUrl: uploadedFiles.personalDocumentUrl,
        proofOfResidenceUrl: uploadedFiles.proofOfResidenceUrl,
      }
    );

    if (updateResult.isLeft()) {
      this.logger.error(
        `Erro ao atualizar dados do indivíduo: ${updateResult.value.message}`
      );
      return left(updateResult.value);
    }

    this.logger.info(
      `Dados do indivíduo atualizados com sucesso para contrato ${contract.id}`
    );
    return right(undefined);
  }

  /**
   * Atualiza os dados do contrato
   */
  private async updateContractData(
    data: ResubmitRejectedContractDTO,
    contract: InvestmentContract,
    uploadedFiles: UploadedFiles
  ): Promise<Either<Error, void>> {
    if (
      !data.contractType ||
      !data.investment?.startDate ||
      !data.investment?.endDate
    ) {
      this.logger.info(`Dados do contrato não fornecidos, pulando atualização`);
      return right(undefined);
    }

    this.logger.info(`Atualizando dados do contrato ${contract.id}`);

    // Criar value objects para os dados do investimento
    const valueObjectsResult = await this.createInvestmentValueObjects(data);
    if (valueObjectsResult.isLeft()) {
      return left(valueObjectsResult.value);
    }

    const {
      contractType,
      amount,
      monthlyRate,
      paymentMethod,
      profile,
      quotaQuantity,
    } = valueObjectsResult.value;

    const updateResult = contract.updateContractDetails(
      contractType,
      new Date(data.investment.startDate),
      new Date(data.investment.endDate),
      data.investment.isDebenture,
      amount,
      monthlyRate,
      paymentMethod,
      profile,
      uploadedFiles.proofPaymentUrl,
      uploadedFiles.contractUrl,
      quotaQuantity,
      data.investment.durationInMonths
    );

    if (updateResult.isLeft()) {
      this.logger.error(
        `Erro ao atualizar dados do contrato: ${updateResult.value.message}`
      );
      return left(updateResult.value);
    }

    this.logger.info(
      `Dados do contrato atualizados com sucesso para contrato ${contract.id}`
    );
    return right(undefined);
  }

  /**
   * Cria os value objects necessários para atualização do contrato
   */
  private async createInvestmentValueObjects(
    data: ResubmitRejectedContractDTO
  ): Promise<
    Either<
      Error,
      {
        contractType: ContractType;
        amount?: Money;
        monthlyRate?: Percentage;
        paymentMethod?: PaymentMethod;
        profile?: Profile;
        quotaQuantity?: number;
      }
    >
  > {
    // Tipo de contrato
    if (!data.contractType) {
      return left(new Error("Tipo de contrato não fornecido"));
    }

    const contractTypeResult = ContractType.create(
      data.contractType.toString().toLowerCase()
    );
    if (contractTypeResult.isLeft()) {
      this.logger.error(
        `Erro ao criar tipo de contrato: ${contractTypeResult.value.message}`
      );
      return left(contractTypeResult.value);
    }

    const result: any = { contractType: contractTypeResult.value };

    // Valor do investimento
    if (data.investment?.amount) {
      const amountResult = Money.create(data.investment.amount);
      if (amountResult.isLeft()) {
        this.logger.error(
          `Erro ao criar valor do investimento: ${amountResult.value.message}`
        );
        return left(amountResult.value);
      }
      result.amount = amountResult.value;
    }

    // Taxa mensal
    if (data.investment?.monthlyRate) {
      const monthlyRateResult = Percentage.create(data.investment.monthlyRate);
      if (monthlyRateResult.isLeft()) {
        this.logger.error(
          `Erro ao criar taxa mensal: ${monthlyRateResult.value.message}`
        );
        return left(monthlyRateResult.value);
      }
      result.monthlyRate = monthlyRateResult.value;
    }

    // Método de pagamento
    if (data.investment?.paymentMethod) {
      const paymentMethodResult = PaymentMethod.create(
        data.investment.paymentMethod.toString()
      );
      if (paymentMethodResult.isLeft()) {
        this.logger.error(
          `Erro ao criar método de pagamento: ${paymentMethodResult.value.message}`
        );
        return left(paymentMethodResult.value);
      }
      result.paymentMethod = paymentMethodResult.value;
    }

    // Perfil
    if (data.investment?.profile) {
      const profileResult = Profile.create(data.investment.profile.toString());
      if (profileResult.isLeft()) {
        this.logger.error(
          `Erro ao criar perfil: ${profileResult.value.message}`
        );
        return left(profileResult.value);
      }
      result.profile = profileResult.value;
    }

    // Quantidade de cotas (apenas para SCP)
    if (
      data.contractType === ("SCP" as unknown as ContractType) &&
      data.investment?.quotaQuantity
    ) {
      result.quotaQuantity = Number(data.investment.quotaQuantity);
    }

    return right(result);
  }

  /**
   * Obtém a extensão do arquivo baseada no MIME type
   */
  private getExtensionFromMimeType(mimeType: string): string {
    switch (mimeType) {
      case "application/pdf":
        return "pdf";
      case "image/png":
        return "png";
      case "image/jpeg":
        return "jpeg";
      case "image/jpg":
        return "jpg";
      default:
        return "bin";
    }
  }
}
