export type PersonType = 'PF' | 'PJ'

export interface Address {
  street: string
  number: string
  city: string
  state: string
  zipCode: string
}

export interface BankData {
  bank: string
  agency: string
  account: string
  pixKey?: string
}

export interface InvestorRepresentative {
  name: string
  document: string
  email: string
  address: Address
  bankData: BankData
}

export interface Investor {
  id: string
  type: PersonType
  name: string
  document: string
  email: string
  address: Address
  bankData: BankData
  representative?: InvestorRepresentative
}
