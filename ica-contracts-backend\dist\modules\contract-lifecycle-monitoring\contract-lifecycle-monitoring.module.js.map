{"version": 3, "file": "contract-lifecycle-monitoring.module.js", "sourceRoot": "/", "sources": ["modules/contract-lifecycle-monitoring/contract-lifecycle-monitoring.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AAExC,8DAA0D;AAC1D,oHAA8G;AAC9G,oEAAgE;AAChE,gGAA0F;AAC1F,gGAA0F;AAC1F,wGAAiG;AACjG,sGAA+F;AAC/F,8GAAuG;AAchG,IAAM,iCAAiC,GAAvC,MAAM,iCAAiC;CAAG,CAAA;AAApC,8EAAiC;4CAAjC,iCAAiC;IAZ7C,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,CAAC;QACvB,WAAW,EAAE,CAAC,gFAAqC,CAAC;QACpD,SAAS,EAAE;YACT,8DAA4B;YAC5B,mEAA8B;YAC9B,qEAA+B;YAC/B,2EAAkC;YAClC,8DAA4B;YAC5B,oCAAgB;SACjB;KACF,CAAC;GACW,iCAAiC,CAAG", "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\r\n\r\nimport { SharedModule } from '../../shared/shared.module';\r\nimport { ContractLifecycleMonitoringController } from './controller/contract-lifecycle-monitoring.controller';\r\nimport { DashboardService } from './services/dashboard.service';\r\nimport { FinalizeContractEventService } from './services/finalize-contract-event.service';\r\nimport { FindExpiringContractsService } from './services/find-expiring-contracts.service';\r\nimport { GetContractsForRetentionService } from './services/get-contracts-for-retention.service';\r\nimport { SendContractToRetentionService } from './services/send-contract-to-retention.service';\r\nimport { UpdateContractFromRetentionService } from './services/update-contract-from-retention.service';\r\n\r\n@Module({\r\n  imports: [SharedModule],\r\n  controllers: [ContractLifecycleMonitoringController],\r\n  providers: [\r\n    FindExpiringContractsService,\r\n    SendContractToRetentionService,\r\n    GetContractsForRetentionService,\r\n    UpdateContractFromRetentionService,\r\n    FinalizeContractEventService,\r\n    DashboardService,\r\n  ],\r\n})\r\nexport class ContractLifecycleMonitoringModule {}\r\n"]}