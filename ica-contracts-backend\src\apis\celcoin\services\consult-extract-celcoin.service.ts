import {
  BadGatewayException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';

import { IConsultExtractCelcoinRequest } from '../requests/consult-extract-celcoin.request';
import { IConsultExtractCelcoinResponse } from '../responses/consult-extract-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class ConsultAccountExtractCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async consultExtract(
    input: IConsultExtractCelcoinRequest,
  ): Promise<IConsultExtractCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const searchParams = new URLSearchParams({
      ...input,
    });

    const url = `${process.env.CELCOIN_URL}/baas-walletreports/v1/wallet/movement?${searchParams}`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IConsultExtractCelcoinResponse> =
        await axios.get(url, config);
      return data;
    } catch (error) {
      if (
        ['CBE345', 'CBE151'].includes(error.response?.data?.error?.errorCode)
      ) {
        return {
          body: {
            account: input.Account,
            documentNumber: input.DocumentNumber,
            movements: [],
          },
        };
      }

      if (error.response?.data?.error?.errorCode) {
        throw new BadGatewayException(error.response?.data?.error);
      }

      throw new InternalServerErrorException(error.response.data);
    }
  }
}
