{"version": 3, "file": "transaction-pix-manual.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/transaction-pix-manual.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yDAAyC;AACzC,qDASyB;AAEzB,MAAM,QAAQ;CAwBb;AArBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yCACK;AAIhB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wCACI;AAIf;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;0CACM;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;6CACS;AAGtB,MAAa,uBAAuB;CAuBnC;AAvBD,0DAuBC;AApBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;uDACF;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACS;AAKpB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;oEACe;AAI5B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;6DACV;AAMrB;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;8BACX,QAAQ;yDAAC", "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport {\r\n  IsDateString,\r\n  IsDefined,\r\n  IsNotEmpty,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\n\r\nclass External {\r\n  @IsDefined()\r\n  @IsString()\r\n  bank: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  account: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  branch: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  document: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  accountType: string;\r\n}\r\n\r\nexport class TransactionPixManualDto {\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  amount: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  description: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  transactionPassword: string;\r\n\r\n  @IsOptional()\r\n  @IsDateString({ strict: true })\r\n  transferDate: string;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => External)\r\n  external: External;\r\n}\r\n"]}