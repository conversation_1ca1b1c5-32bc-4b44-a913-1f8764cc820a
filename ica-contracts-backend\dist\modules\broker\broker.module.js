"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrokerModule = void 0;
const common_1 = require("@nestjs/common");
const shared_module_1 = require("../../shared/shared.module");
const investor_module_1 = require("../investor/investor.module");
const owner_module_1 = require("../owner/owner.module");
const broker_controller_1 = require("./controllers/broker.controller");
const create_broker_service_1 = require("./services/create-broker.service");
const edit_broker_service_1 = require("./services/edit-broker.service");
const get_broker_advisors_service_1 = require("./services/get-broker-advisors.service");
const get_broker_investors_service_1 = require("./services/get-broker-investors.service");
const get_broker_service_1 = require("./services/get-broker.service");
const dashboard_service_1 = require("./services/dashboard.service");
const contracts_monthly_capture_service_1 = require("./services/contracts-monthly-capture.service");
const contracts_capture_service_1 = require("./services/contracts-capture.service");
const get_one_scheduled_payment_for_broker_service_1 = require("./services/get-one-scheduled-payment-for-broker.service");
const generate_payment_reports_service_1 = require("./services/generate-payment-reports.service");
const ica_report_module_1 = require("../../apis/ica-report/ica-report.module");
const list_payment_scheduled_service_1 = require("./services/list-payment-scheduled.service");
const income_payment_module_1 = require("../income-payment/income-payment.module");
const generate_scheduled_payments_reports_service_1 = require("./services/generate-scheduled-payments-reports.service");
const generate_contracts_reports_service_1 = require("./services/generate-contracts-reports.service");
const get_acquisition_per_period_service_1 = require("../acquisition/services/get-acquisition-per-period.service");
const get_broker_contracts_growth_chart_service_1 = require("./services/get-broker-contracts-growth-chart.service");
const list_investors_service_1 = require("./services/list-investors.service");
const income_report_module_1 = require("../income-report/income-report.module");
let BrokerModule = class BrokerModule {
};
exports.BrokerModule = BrokerModule;
exports.BrokerModule = BrokerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            shared_module_1.SharedModule,
            owner_module_1.OwnerModule,
            investor_module_1.InvestorModule,
            ica_report_module_1.IcaReportModule,
            income_payment_module_1.IncomePaymentModule,
            income_report_module_1.IncomeReportsModule,
        ],
        controllers: [broker_controller_1.BrokerController],
        providers: [
            create_broker_service_1.CreateBrokerService,
            get_broker_investors_service_1.GetBrokerInvestorService,
            get_broker_advisors_service_1.GetBrokerAdvisorsService,
            edit_broker_service_1.EditBrokerService,
            get_broker_service_1.GetBrokerService,
            dashboard_service_1.BrokerDashboardService,
            contracts_monthly_capture_service_1.ContractsMonthlyCaptureService,
            contracts_capture_service_1.ContractsCaptureService,
            get_one_scheduled_payment_for_broker_service_1.GetOneScheduledPaymentForBrokerService,
            generate_payment_reports_service_1.GeneratePaymentReportService,
            list_payment_scheduled_service_1.ListIncomePaymentScheduledBrokerService,
            generate_scheduled_payments_reports_service_1.GenerateScheduledPaymentsReportService,
            generate_contracts_reports_service_1.GenerateContractsReportsService,
            get_acquisition_per_period_service_1.GetAcquisitionPerPeriodService,
            get_broker_contracts_growth_chart_service_1.GetBrokerContractsGrowthChartService,
            list_investors_service_1.ListActiveInvestorsBrokerService,
        ],
        exports: [
            get_broker_investors_service_1.GetBrokerInvestorService,
            get_broker_advisors_service_1.GetBrokerAdvisorsService,
            edit_broker_service_1.EditBrokerService,
            contracts_capture_service_1.ContractsCaptureService,
            get_one_scheduled_payment_for_broker_service_1.GetOneScheduledPaymentForBrokerService,
            generate_payment_reports_service_1.GeneratePaymentReportService,
            generate_scheduled_payments_reports_service_1.GenerateScheduledPaymentsReportService,
        ],
    })
], BrokerModule);
//# sourceMappingURL=broker.module.js.map