"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePixKeyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_key_celcoin_service_1 = require("../../../apis/celcoin/services/pix-key-celcoin.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const pix_key_entity_1 = require("../../../shared/database/typeorm/entities/pix-key.entity");
const typeorm_2 = require("typeorm");
let CreatePixKeyService = class CreatePixKeyService {
    constructor(accountDb, pixKeyRepository, pix) {
        this.accountDb = accountDb;
        this.pixKeyRepository = pixKeyRepository;
        this.pix = pix;
    }
    async perform(input, id) {
        const account = await this.accountDb.findOne({
            relations: {
                business: true,
                owner: true,
            },
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
        });
        if (!account) {
            throw new common_1.NotFoundException('Conta não encontrada.');
        }
        return this.pixKeyRepository.manager.transaction(async (manager) => {
            const apiPixKey = await this.pix.createAccountPixKeys({
                account: account.number,
                key: input.key,
                keyType: input.keyType,
            });
            const newPixKey = manager.create(pix_key_entity_1.PixKeyEntity, {
                key: apiPixKey.body.key,
                accountId: account.id,
                typeKey: input.keyType,
            });
            const pix = await manager.save(newPixKey);
            return pix;
        });
    }
};
exports.CreatePixKeyService = CreatePixKeyService;
exports.CreatePixKeyService = CreatePixKeyService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(pix_key_entity_1.PixKeyEntity)),
    __param(2, (0, common_1.Inject)(pix_key_celcoin_service_1.PixKeyCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        pix_key_celcoin_service_1.PixKeyCelcoinService])
], CreatePixKeyService);
//# sourceMappingURL=create-pix-key.service.js.map