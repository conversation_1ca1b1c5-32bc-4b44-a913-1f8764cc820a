{"version": 3, "file": "get-favorite-pix-account.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/get-favorite-pix-account.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8D;AAE9D,MAAa,wBAAwB;CAKpC;AALD,4DAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;2DACS", "sourcesContent": ["import { IsDefined, IsString, IsUUID } from 'class-validator';\r\n\r\nexport class GetFavoritePixAccountDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsUUID()\r\n  accountId: string;\r\n}\r\n"]}