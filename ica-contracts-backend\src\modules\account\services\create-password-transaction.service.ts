import { NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { CreatePasswordTransactionDto } from '../dto/create-password-transaction.dto';

export class CreatePasswordTransactionService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountRepository: Repository<AccountEntity>,
  ) {}

  async perform(
    userId: string,
    data: CreatePasswordTransactionDto,
  ): Promise<void> {
    const account = await this.accountRepository.findOne({
      where: [
        { ownerId: Equal(userId) },
        { businessId: Equal(userId) },
        { id: userId },
      ],
    });

    if (!account) {
      throw new NotFoundException('Conta nao encontranda.');
    }

    const hashedPassword = await bcrypt.hash(data.password, 10);

    account.transactionPassword = hashedPassword;

    await this.accountRepository.save(account);
  }
}
