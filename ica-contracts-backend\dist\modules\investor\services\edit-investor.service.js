"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditInvestorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const address_entity_1 = require("../../../shared/database/typeorm/entities/address.entity");
const business_entity_1 = require("../../../shared/database/typeorm/entities/business.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const wallets_views_entity_1 = require("../../../shared/database/typeorm/entities/wallets-views.entity");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
let EditInvestorService = class EditInvestorService {
    constructor(ownerRoleRelationRepository, walletsViewsRepository, ownerRepository, businessRepository, addressRepository) {
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.walletsViewsRepository = walletsViewsRepository;
        this.ownerRepository = ownerRepository;
        this.businessRepository = businessRepository;
        this.addressRepository = addressRepository;
    }
    async perform({ ownerRoleRelationId, business, owner, address }, userId) {
        const investorRoleRelation = await this.ownerRoleRelationRepository.findOne({
            where: {
                id: ownerRoleRelationId,
                role: { name: roles_enum_1.RolesEnum.INVESTOR },
            },
            relations: {
                role: true,
                owner: { address: true },
                business: { address: true },
            },
        });
        if (!investorRoleRelation) {
            throw new common_1.NotFoundException('Investidor não encontrado');
        }
        await this.verifyPermissions(userId, investorRoleRelation);
        if (investorRoleRelation.owner && owner) {
            await this.uploadOwner(owner, investorRoleRelation.owner);
        }
        if (investorRoleRelation.business && business) {
            await this.uploadBusiness(business, investorRoleRelation.business);
        }
        if (address) {
            await this.uploadAddress(investorRoleRelation.owner.id, address);
        }
    }
    async uploadOwner(payload, owner) {
        const [newName, newEmail, newNickname, newBirthDay, newDocument] = await Promise.all([
            await this.validateName(owner.id, payload.name),
            await this.validateEmail(owner.id, payload.email),
            await this.validateNickname(owner.id, payload.nickName),
            await this.validateBirthday(owner.id, payload.birthDate),
            await this.validateDocument(owner.id, payload.document),
        ]);
        await this.ownerRepository.update(owner.id, {
            name: newName,
            email: newEmail,
            nickname: newNickname,
            dtBirth: newBirthDay,
            cpf: newDocument,
            motherName: owner.motherName,
            phone: payload.phone ? this.formatPhoneNumber(payload.phone) : undefined,
        });
    }
    formatPhoneNumber(phoneNumber) {
        const digitsOnly = phoneNumber.replace(/\D/g, '');
        const pattern = /^(?:55)?(\d{2})(\d{8,9})$/;
        const match = digitsOnly.match(pattern);
        if (!match) {
            throw new common_1.BadRequestException('Número de telefone inválido');
        }
        const ddd = match[1];
        const number = match[2];
        if (!digitsOnly.startsWith('55')) {
            return `55${ddd}${number}`;
        }
        return digitsOnly;
    }
    async validateField(ownerId, newValue, fieldName, errorMessage) {
        if (!newValue) {
            return undefined;
        }
        const existingOwner = await this.ownerRepository.findOne({
            where: { id: ownerId },
            select: [fieldName],
        });
        if (!existingOwner) {
            throw new common_1.NotFoundException('Proprietário não encontrado.');
        }
        if (existingOwner[fieldName] === newValue) {
            throw new common_1.UnprocessableEntityException(errorMessage.sameValue);
        }
        const valueAlreadyExists = (await this.ownerRepository.count({
            where: { [fieldName]: newValue },
        })) > 0;
        if (valueAlreadyExists) {
            throw new common_1.UnprocessableEntityException(errorMessage.duplicateValue);
        }
        return newValue;
    }
    async validateBirthday(ownerId, newBirthday) {
        if (!newBirthday) {
            return undefined;
        }
        const datePattern = /^\d{4}-\d{2}-\d{2}$/;
        if (!datePattern.test(newBirthday)) {
            throw new common_1.UnprocessableEntityException('O formato da data de aniversário é inválido. Use o formato YYYY-MM-DD.');
        }
        const parsedDate = new Date(newBirthday);
        if (Number.isNaN(parsedDate.getTime())) {
            throw new common_1.UnprocessableEntityException('A data de aniversário fornecida é inválida.');
        }
        if (parsedDate >= new Date()) {
            throw new common_1.UnprocessableEntityException('A data de aniversário deve ser anterior à data atual.');
        }
        const existingOwner = await this.ownerRepository.findOne({
            where: { id: ownerId },
            select: ['dtBirth'],
        });
        if (!existingOwner) {
            throw new common_1.NotFoundException('Usuário não encontrado.');
        }
        if (new Date(existingOwner.dtBirth).getTime() === parsedDate.getTime()) {
            throw new common_1.UnprocessableEntityException('A nova data de aniversário é igual à atual.');
        }
        return parsedDate.toISOString().split('T').at(0);
    }
    async validateName(ownerId, newName) {
        return this.validateField(ownerId, newName, 'name', {
            sameValue: 'O novo nome é igual ao nome atual.',
            duplicateValue: 'Já existe um usuário com esse nome.',
        });
    }
    async validateEmail(ownerId, newEmail) {
        return this.validateField(ownerId, newEmail, 'email', {
            sameValue: 'O novo e-mail é igual ao e-mail atual.',
            duplicateValue: 'Já existe um usuário com esse e-mail.',
        });
    }
    async validateNickname(ownerId, newNickname) {
        return this.validateField(ownerId, newNickname, 'nickname', {
            sameValue: 'O novo nickname é igual ao atual.',
            duplicateValue: 'Já existe um usuário com esse nickname.',
        });
    }
    async validateDocument(ownerId, newDocument) {
        return this.validateField(ownerId, newDocument?.replace(/\D/g, ''), 'cpf', {
            sameValue: 'O novo documento é igual ao atual.',
            duplicateValue: 'Já existe um usuário com esse documento.',
        });
    }
    async validateAddress(ownerId, addressPayload) {
        const { cep, street, neighborhood, number, city, state, complement } = addressPayload;
        if (!cep || !street || !neighborhood || !number || !city || !state) {
            throw new common_1.UnprocessableEntityException('Todos os campos obrigatórios do endereço devem ser preenchidos.');
        }
        const cepPattern = /^\d{5}-\d{3}$/;
        if (!cepPattern.test(cep)) {
            throw new common_1.UnprocessableEntityException('O formato do CEP é inválido. Use o formato 00000-000.');
        }
        const existingAddress = await this.ownerRepository.findOne({
            where: { id: ownerId },
            relations: ['address'],
        });
        if (!existingAddress) {
            throw new common_1.NotFoundException('Usuário não encontrado.');
        }
        const updatedAddress = new address_entity_1.AddressEntity();
        updatedAddress.ownerId = ownerId;
        updatedAddress.cep = cep;
        updatedAddress.street = street;
        updatedAddress.neighborhood = neighborhood;
        updatedAddress.number = number;
        updatedAddress.city = city;
        updatedAddress.state = state;
        updatedAddress.complement = complement;
        return updatedAddress;
    }
    async uploadAddress(ownerId, addressPayload) {
        const validatedAddress = await this.validateAddress(ownerId, addressPayload);
        const existingAddress = await this.ownerRepository.findOne({
            where: { id: ownerId },
            relations: { address: true },
        });
        if (existingAddress.address.length > 0) {
            await this.addressRepository.update({ ownerId }, validatedAddress);
        }
        else {
            validatedAddress.ownerId = ownerId;
            await this.addressRepository.save(validatedAddress);
        }
    }
    async uploadBusiness(payload, business) {
        const [newCompanyName, newFantasyName, newCnpj, newEmail, newType, newSize, newDtOpening,] = await Promise.all([
            this.validateBusinessField(business.id, payload.companyName, 'companyName', {
                sameValue: 'O novo nome da empresa é igual ao atual.',
                duplicateValue: 'Já existe uma empresa com esse nome.',
            }),
            this.validateBusinessField(business.id, payload.fantasyName, 'fantasyName', {
                sameValue: 'O novo nome fantasia é igual ao atual.',
                duplicateValue: 'Já existe uma empresa com esse nome fantasia.',
            }),
            this.validateBusinessField(business.id, payload.cnpj?.replace(/\D/g, ''), 'cnpj', {
                sameValue: 'O novo CNPJ é igual ao atual.',
                duplicateValue: 'Já existe uma empresa com esse CNPJ.',
            }),
            this.validateBusinessField(business.id, payload.email, 'email', {
                sameValue: 'O novo e-mail é igual ao atual.',
                duplicateValue: 'Já existe uma empresa com esse e-mail.',
            }),
            this.validateBusinessField(business.id, payload.type, 'type', {
                sameValue: 'O novo tipo é igual ao atual.',
                duplicateValue: '',
            }),
            this.validateBusinessField(business.id, payload.size, 'size', {
                sameValue: 'O novo porte é igual ao atual.',
                duplicateValue: '',
            }),
            this.validateDtOpening(business.id, payload.dtOpening),
        ]);
        await this.businessRepository.update(business.id, {
            companyName: newCompanyName,
            fantasyName: newFantasyName,
            cnpj: newCnpj,
            email: newEmail,
            type: newType,
            size: newSize,
            dtOpening: newDtOpening ? new Date(newDtOpening) : undefined,
        });
    }
    async validateBusinessField(businessId, newValue, fieldName, errorMessage) {
        if (!newValue) {
            return undefined;
        }
        const existingBusiness = await this.businessRepository.findOne({
            where: { id: businessId },
            select: [fieldName],
        });
        if (!existingBusiness) {
            throw new common_1.NotFoundException('Empresa não encontrada.');
        }
        if (existingBusiness[fieldName] === newValue) {
            throw new common_1.UnprocessableEntityException(errorMessage.sameValue);
        }
        if (errorMessage.duplicateValue) {
            const valueAlreadyExists = (await this.businessRepository.count({
                where: { [fieldName]: newValue },
            })) > 0;
            if (valueAlreadyExists) {
                throw new common_1.UnprocessableEntityException(errorMessage.duplicateValue);
            }
        }
        return newValue;
    }
    async validateDtOpening(businessId, newDtOpening) {
        if (!newDtOpening) {
            return undefined;
        }
        const datePattern = /^\d{4}-\d{2}-\d{2}$/;
        if (!datePattern.test(newDtOpening)) {
            throw new common_1.UnprocessableEntityException('O formato da data de abertura é inválido. Use o formato YYYY-MM-DD.');
        }
        const parsedDate = new Date(newDtOpening);
        if (Number.isNaN(parsedDate.getTime())) {
            throw new common_1.UnprocessableEntityException('A data de abertura fornecida é inválida.');
        }
        if (parsedDate >= new Date()) {
            throw new common_1.UnprocessableEntityException('A data de abertura deve ser anterior à data atual.');
        }
        const existingBusiness = await this.businessRepository.findOne({
            where: { id: businessId },
            select: ['dtOpening'],
        });
        if (!existingBusiness) {
            throw new common_1.NotFoundException('Empresa não encontrada.');
        }
        if (new Date(existingBusiness.dtOpening).getTime() === parsedDate.getTime()) {
            throw new common_1.UnprocessableEntityException('A nova data de abertura é igual à atual.');
        }
        return parsedDate.toISOString().split('T').at(0);
    }
    async verifyPermissions(userId, investorRoleRelation) {
        if (investorRoleRelation.ownerId === userId ||
            investorRoleRelation.businessId === userId) {
            return;
        }
        if (await this.isAdmin(userId)) {
            return;
        }
        if (await this.isBroker(userId)) {
            if (await this.hasDirectRelation(userId, investorRoleRelation.id)) {
                return;
            }
        }
        if (await this.isSuperAdmin(userId)) {
            return;
        }
        throw new common_1.ForbiddenException('Você não tem permissão para editar este investidor');
    }
    async isAdmin(userId) {
        const adminRole = await this.ownerRoleRelationRepository.findOne({
            relations: {
                role: true,
            },
            where: [
                { ownerId: userId, role: { name: roles_enum_1.RolesEnum.ADMIN } },
                { businessId: userId, role: { name: roles_enum_1.RolesEnum.ADMIN } },
            ],
        });
        return !!adminRole;
    }
    async isBroker(userId) {
        const brokerRole = await this.ownerRoleRelationRepository.findOne({
            relations: {
                role: true,
            },
            where: [
                { ownerId: userId, role: { name: roles_enum_1.RolesEnum.BROKER } },
                { businessId: userId, role: { name: roles_enum_1.RolesEnum.BROKER } },
            ],
        });
        return !!brokerRole;
    }
    async hasDirectRelation(userId, investorRoleRelationId) {
        const directRelation = await this.walletsViewsRepository.findOne({
            where: {
                upper: { ownerId: userId },
                bottom: { id: investorRoleRelationId },
            },
            relations: ['upper', 'bottom'],
        });
        return !!directRelation;
    }
    async isSuperAdmin(userId) {
        const superAdminRole = await this.ownerRoleRelationRepository.findOne({
            relations: {
                role: true,
            },
            where: { ownerId: userId, role: { name: roles_enum_1.RolesEnum.SUPERADMIN } },
        });
        return !!superAdminRole;
    }
};
exports.EditInvestorService = EditInvestorService;
exports.EditInvestorService = EditInvestorService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(wallets_views_entity_1.WalletsViewsEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(business_entity_1.BusinessEntity)),
    __param(4, (0, typeorm_1.InjectRepository)(address_entity_1.AddressEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], EditInvestorService);
//# sourceMappingURL=edit-investor.service.js.map