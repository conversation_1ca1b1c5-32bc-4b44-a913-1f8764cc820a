interface IDebitParty {
  account: string;
}

interface ICreditParty {
  bank?: string;
  key?: string;
  account?: string;
  branch?: string;
  taxId?: string;
  name?: string;
  accountType?: string;
}

export interface ITransactionPixCelcoinRequest {
  amount: number;
  clientCode: string;
  endToEndId?: string;
  initiationType: string;
  paymentType: string;
  urgency: string;
  transactionType: string;
  debitParty?: IDebitParty;
  creditParty?: ICreditParty;
  remittanceInformation?: string;
}
