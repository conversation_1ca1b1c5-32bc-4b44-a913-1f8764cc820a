import { IsDefined, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { DocumentTypeEnum } from 'src/shared/enums/document-type.enum';

export class SendDocumentsAccountDto {
  @IsOptional()
  front: Express.Multer.File;

  @IsOptional()
  back: Express.Multer.File;

  @IsOptional()
  socialContract: Express.Multer.File;

  @IsOptional()
  cardCnpj: Express.Multer.File;

  @IsDefined()
  @IsEnum(DocumentTypeEnum)
  type: DocumentTypeEnum;

  @IsOptional()
  @IsUUID()
  accountId: string;
}
