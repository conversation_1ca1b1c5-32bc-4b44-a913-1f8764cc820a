interface IBody {
  listKeys: IList<PERSON>ey[];
}

interface IListKey {
  keyType: string;
  key: string;
  account: IAccount;
  owner: IOwner;
}

interface IAccount {
  participant: string;
  branch: string;
  account: string;
  accountType: string;
  createDate: string;
}

interface IOwner {
  type: string;
  documentNumber: string;
  name: string;
}

export interface IGetAccountPixKeysCelcoinResponse {
  version: string;
  status: string;
  body: IBody;
}
