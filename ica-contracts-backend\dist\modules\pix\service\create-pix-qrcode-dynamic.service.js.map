{"version": 3, "file": "create-pix-qrcode-dynamic.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/create-pix-qrcode-dynamic.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,6CAAmD;AACnD,0GAA+F;AAC/F,6FAAoF;AACpF,mGAAyF;AACzF,qCAA4C;AAC5C,+BAA0B;AAK1B,IAAa,6BAA6B,GAA1C,MAAa,6BAA6B;IACxC,YAEU,SAAoC,EAEpC,aAA0C,EAE1C,gBAAyC;QAJzC,cAAS,GAAT,SAAS,CAA2B;QAEpC,kBAAa,GAAb,aAAa,CAA6B;QAE1C,qBAAgB,GAAhB,gBAAgB,CAAyB;IAChD,CAAC;IACJ,KAAK,CAAC,OAAO,CACX,IAA+B,EAC/B,EAAU;QAEV,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,CAAC,CAAC;QACxD,CAAC;QACD,MAAM,aAAa,GAAG,IAAA,SAAE,GAAE,CAAC;QAC3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;YAClE,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,eAAe,EAAE,aAAa;YAC9B,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,SAAS;YACxC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,SAAS;YACtC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,SAAS;YACxC,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACxB,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,oBAAoB;gBACxD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACxB,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;aACrC;SACF,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,UAAU,EAAE,aAAa;YACzB,IAAI,EAAE,SAAS;YACf,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YACrD,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC;SACtD,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEtC,OAAO;YACL,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ;SACtD,CAAC;IACJ,CAAC;CACF,CAAA;AAzDY,sEAA6B;wCAA7B,6BAA6B;IAErC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,mCAAe,CAAC,CAAA;IAEjC,WAAA,IAAA,eAAM,EAAC,oDAAuB,CAAC,CAAA;qCAHb,oBAAU;QAEN,oBAAU;QAEP,oDAAuB;GAPxC,6BAA6B,CAyDzC", "sourcesContent": ["import { Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixQRCodeCelcoinService } from 'src/apis/celcoin/services/pix-qrcode-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { PixQRCodeEntity } from 'src/shared/database/typeorm/entities/pix-qrcode.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\n\r\nimport { CreatePixQRCodeDynamicDto } from '../dto/create-pix-qrcode-dynamic.dto';\r\nimport { ICreatePixQRCodeDynamicResponse } from '../response/create-pix-qrcode-dynamic.response';\r\n\r\nexport class CreatePixQRCodeDynamicService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(PixQRCodeEntity)\r\n    private pixQRCodeRepo: Repository<PixQRCodeEntity>,\r\n    @Inject(PixQRCodeCelcoinService)\r\n    private pixQRCodeCelcoin: PixQRCodeCelcoinService,\r\n  ) {}\r\n  async perform(\r\n    data: CreatePixQRCodeDynamicDto,\r\n    id: string,\r\n  ): Promise<ICreatePixQRCodeDynamicResponse> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) {\r\n      throw new NotFoundException('Usuário não encontrado');\r\n    }\r\n    const transactionId = v4();\r\n    const apiResponse = await this.pixQRCodeCelcoin.createQRCodeDynamic({\r\n      amount: data.amount,\r\n      key: data.key,\r\n      clientRequestId: transactionId,\r\n      payerCNPJ: data.payer?.cnpj || undefined,\r\n      payerCPF: data.payer?.cpf || undefined,\r\n      payerName: data.payer?.name || undefined,\r\n      merchant: {\r\n        city: data.merchant.city,\r\n        merchantCategoryCode: data.merchant.merchantCategoryCode,\r\n        name: data.merchant.name,\r\n        postalCode: data.merchant.postalCode,\r\n      },\r\n    });\r\n\r\n    const create = this.pixQRCodeRepo.create({\r\n      accountId: account.id,\r\n      externalId: transactionId,\r\n      type: 'DYNAMIC',\r\n      emv: apiResponse.body.body.dynamicBRCodeData.emvqrcps,\r\n      transactionId: String(apiResponse.body.transactionId),\r\n    });\r\n    await this.pixQRCodeRepo.save(create);\r\n\r\n    return {\r\n      emv: apiResponse.body.body.dynamicBRCodeData.emvqrcps,\r\n    };\r\n  }\r\n}\r\n"]}