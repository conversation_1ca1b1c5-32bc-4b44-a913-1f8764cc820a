interface IAddress {
  postalCode: string;
  street: string;
  number: string;
  addressComplement: string;
  neighborhood: string;
  city: string;
  state: string;
  longitude: string;
  latitude: string;
}

interface IAccount {
  branch: string;
  account: string;
}

interface IBody {
  statusAccount: string;
  documentNumber: string;
  phoneNumber: string;
  email: string;
  motherName: string;
  fullName: string;
  socialName: string;
  birthDate: string;
  address: IAddress;
  isPoliticallyExposedPerson: boolean;
  account: IAccount;
  businessAccount: IAccount;
  createDate: string;
}

export interface IGetAccountCelcoinResponse {
  version: string;
  status: string;
  body: IBody;
}
