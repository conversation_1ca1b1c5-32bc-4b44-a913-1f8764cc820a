import type {
  ContractFiles,
  UploadedFile,
} from '@/domain/interfaces/file-upload'
import { DomainEvent } from '@/domain/shared'

export class ContractFilesUploadEvent extends DomainEvent<{
  contractId: string
  document: string
  files: ContractFiles
  companyDocumentFile?: UploadedFile
}> {
  constructor(
    contractId: string,
    document: string,
    files: ContractFiles,
    companyDocumentFile?: UploadedFile
  ) {
    super(contractId, 'ContractFilesUpload', {
      contractId,
      document,
      files,
      companyDocumentFile,
    })
  }
}
