import { AggregateRoot } from '@/domain/shared';
import type { BaseEntityProps } from '@/domain/shared/base-entity';
import type { Money } from '@/domain/value-objects';

export enum AddendumStatus {
  PENDING = 'PENDING',
  FULLY_SIGNED = 'FULLY_SIGNED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
}

interface IincomePaymentAddendum {
  proportional_days: number;
  payment_date: Date | undefined;
}

interface ContractAddendumProps extends BaseEntityProps {
  value: Money;
  startDate: Date;
  endDate: Date;
  status: AddendumStatus;
  yield_rate: number;
  incomePaymentAddendum: IincomePaymentAddendum[];
}

export class ContractAddendum extends AggregateRoot<ContractAddendumProps> {
  private constructor(props: ContractAddendumProps, id?: string) {
    super(props, id);
  }

  static create(
    value: Money,
    startDate: Date,
    endDate: Date,
    status: AddendumStatus = AddendumStatus.PENDING,
    incomePaymentAddendum: IincomePaymentAddendum[] = [],
    yield_rate: number,
    id?: string,
  ): ContractAddendum {
    if (endDate <= startDate) {
      throw new Error('End date must be after start date');
    }

    return new ContractAddendum(
      { value, startDate, endDate, status, incomePaymentAddendum, yield_rate },
      id,
    );
  }

  calculateAnnualProportionalAddendumValue(year: number): number {
    const addendaOfThisYear = this.props.incomePaymentAddendum.filter(
      (item) => item.payment_date && item.payment_date.getFullYear() === year,
    );

    const valueYield = addendaOfThisYear.reduce(
      (acc, item) =>
        acc +
        ((this.props.value.amount * (this.props.yield_rate / 100)) / 30) *
          item.proportional_days,
      0,
    );

    return parseFloat(valueYield.toFixed(2)) ?? 0;
  }

  isAddendumValidForYear = (
    addendum: ContractAddendum,
    year: number,
  ): boolean => {
    const startDate = addendum.getStartDate();
    const endDate = addendum.getEndDate();

    if (!startDate || !endDate) return false;

    return startDate.getFullYear() <= year && endDate.getFullYear() >= year;
  };

  getValue(): Money {
    return this.props.value;
  }

  getStartDate(): Date {
    return this.props.startDate;
  }

  getEndDate(): Date {
    return this.props.endDate;
  }

  getStatus(): AddendumStatus {
    return this.props.status;
  }

  markAsSigned(): void {
    this.props.status = AddendumStatus.FULLY_SIGNED;
  }

  markAsRejected(): void {
    this.props.status = AddendumStatus.REJECTED;
  }

  markAsCancelled(): void {
    this.props.status = AddendumStatus.CANCELLED;
  }

  isActive(onDate: Date = new Date()): boolean {
    return (
      this.props.status === AddendumStatus.FULLY_SIGNED &&
      onDate >= this.props.startDate &&
      onDate <= this.props.endDate
    );
  }
}
