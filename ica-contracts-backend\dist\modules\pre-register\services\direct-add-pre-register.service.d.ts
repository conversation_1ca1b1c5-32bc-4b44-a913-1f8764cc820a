import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';
import { Repository } from 'typeorm';
import { DirectAddPreRegisterDto } from '../dto/direct-add-pre-register.dto';
export declare class DirectAddPreRegisterService {
    private preRegisterDb;
    private accountDb;
    constructor(preRegisterDb: Repository<PreRegisterEntity>, accountDb: Repository<AccountEntity>);
    perform(data: DirectAddPreRegisterDto, token: string): Promise<void>;
}
