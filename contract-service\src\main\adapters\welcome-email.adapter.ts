import crypto from 'node:crypto'
import type { IEmailService } from '@/application/interfaces/email-service'
import type { HttpClient } from '@/application/interfaces/http-client'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import type { IEmailRepository } from '@/domain/repositories/email.repository'
import { type Either, left, right } from '@/domain/shared'
import { FetchHttpClient } from '@/infrastructure/http/fetch-http-client'
import { PrismaEmailRepository } from '@/infrastructure/prisma/repositories'
import env from '@/main/config/env'

interface WelcomeTemplateData {
  investorName: string
  username: string
  password: string
}

interface WelcomeEmailData {
  from_email: string
  to_email: string
  template: string
  subject: string
  template_data: WelcomeTemplateData
}

interface ResponseEmail {
  id: string
  status: string
  created_at: Date
}

/**
 * Adaptador para o serviço de envio de emails de boas-vindas
 */
export class WelcomeEmailAdapter implements IEmailService {
  private readonly apiBaseUrl: string
  private readonly apiKey: string
  private readonly httpClient: HttpClient

  private readonly emailRepository: IEmailRepository

  constructor(private readonly logger: LoggerGateway) {
    this.apiBaseUrl = env.EMAIL_API_URL
    this.apiKey = env.EMAIL_API_KEY
    this.httpClient = new FetchHttpClient()
    this.emailRepository = new PrismaEmailRepository()
  }

  /**
   * Envia um e-mail de boas-vindas para o investidor com suas credenciais
   */
  async sendWelcomeEmail(
    email: string,
    investorName: string,
    username: string,
    password: string
  ): Promise<Either<Error, string>> {
    try {
      this.logger.info(`Enviando e-mail de boas-vindas para ${email}`)

      const emailData: WelcomeEmailData = {
        from_email: 'ICA Cliente <<EMAIL>>',
        to_email: email,
        template: 'welcome-investor',
        subject: 'Acesse agora o app ICA Cliente',
        template_data: { investorName, username, password },
      }

      const emailId = crypto.randomUUID()

      // Salvar o e-mail no banco de dados com status PENDING
      const createResult = await this.emailRepository.create({
        id: emailId,
        from: emailData.from_email,
        to: emailData.to_email,
        body: JSON.stringify(emailData),
        bodyType: 'json',
        status: 'PENDING',
      })

      if (createResult.isLeft()) {
        this.logger.error(
          `Falha ao salvar e-mail no banco de dados: ${createResult.value.message}`
        )
        return left(createResult.value)
      }

      // Enviar o e-mail
      const result = await this.sendEmail(emailData)

      if (result.isLeft()) {
        this.logger.error(
          `Falha ao enviar e-mail de boas-vindas: ${result.value.message}`
        )
        return left(result.value)
      }

      // Atualizar apenas o external_id no banco de dados
      // O status será atualizado pelo webhook quando o e-mail for entregue
      const updateResult = await this.emailRepository.updateExternalId(
        emailId,
        result.value
      )

      if (updateResult.isLeft()) {
        this.logger.error(
          `Falha ao atualizar external_id do e-mail: ${updateResult.value.message}`
        )
        // Não retornamos erro aqui porque o e-mail já foi enviado com sucesso
      }

      this.logger.info(
        `E-mail de boas-vindas enviado para processamento. ID: ${result.value}`
      )
      return right(result.value)
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido'
      this.logger.error(`Erro ao enviar e-mail de boas-vindas: ${errorMessage}`)
      return left(
        new Error(`Falha ao enviar e-mail de boas-vindas: ${errorMessage}`)
      )
    }
  }

  /**
   * Envia o e-mail usando a API de e-mail
   */
  private async sendEmail(
    emailData: WelcomeEmailData
  ): Promise<Either<Error, string>> {
    try {
      const response = await this.httpClient.post<
        WelcomeEmailData,
        ResponseEmail
      >(`${this.apiBaseUrl}/api/v1/email`, emailData, {
        headers: {
          'Content-Type': 'application/json',
          'X-Api-Key': this.apiKey,
        },
      })

      this.logger.info(
        `Resposta API email: ${JSON.stringify(response, null, 2)}`
      )

      if (response.statusCode > 299) {
        return left(
          new Error(`Erro ao enviar e-mail: ${JSON.stringify(response.data)}`)
        )
      }

      return right(response.data.id)
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido'
      return left(new Error(`Falha ao enviar e-mail: ${errorMessage}`))
    }
  }
}
