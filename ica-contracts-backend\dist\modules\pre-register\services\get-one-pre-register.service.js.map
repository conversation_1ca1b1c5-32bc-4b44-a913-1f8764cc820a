{"version": 3, "file": "get-one-pre-register.service.js", "sourceRoot": "/", "sources": ["modules/pre-register/services/get-one-pre-register.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,uGAA6F;AAC7F,qCAA4C;AAKrC,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAEU,aAA4C;QAA5C,kBAAa,GAAb,aAAa,CAA+B;IACnD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAA0B;QACtC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,SAAS,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,SAAS,CAAC;gBAChC,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC;aACnB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,4BAAmB,CAC3B,4CAA4C,CAC7C,CAAC;QACJ,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;CACF,CAAA;AAtBY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCACb,oBAAU;GAHxB,wBAAwB,CAsBpC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';\r\nimport { Repository, Equal } from 'typeorm';\r\n\r\nimport { GetOnePreRegisterDto } from '../dto/get-one-pre-register.dto';\r\n\r\n@Injectable()\r\nexport class GetOnePreRegisterService {\r\n  constructor(\r\n    @InjectRepository(PreRegisterEntity)\r\n    private preRegisterDb: Repository<PreRegisterEntity>,\r\n  ) {}\r\n\r\n  async perform(data: GetOnePreRegisterDto) {\r\n    const preRegister = await this.preRegisterDb.findOne({\r\n      where: {\r\n        adviserId: Equal(data.adviserId),\r\n        id: Equal(data.id),\r\n      },\r\n    });\r\n\r\n    if (!preRegister) {\r\n      throw new BadRequestException(\r\n        'Nenhum pre registro encontrado com esse id',\r\n      );\r\n    }\r\n\r\n    return preRegister;\r\n  }\r\n}\r\n"]}