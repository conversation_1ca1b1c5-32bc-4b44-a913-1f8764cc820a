"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBrokerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const create_owner_service_1 = require("../../owner/services/create-owner.service");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const role_entity_1 = require("../../../shared/database/typeorm/entities/role.entity");
const typeorm_2 = require("typeorm");
let CreateBrokerService = class CreateBrokerService {
    constructor(ownerRepository, roleRepository, ownerRoleRelationRepository, createOwnerService, dataSource) {
        this.ownerRepository = ownerRepository;
        this.roleRepository = roleRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.createOwnerService = createOwnerService;
        this.dataSource = dataSource;
    }
    async perform(input) {
        return await this.dataSource.transaction(async (manager) => {
            const role = await this.findRoleByName('broker');
            await this.createOwnerService.perform(input);
            const owner = await this.findOwnerByDocument(input.document);
            await this.ensureUniqueOwnerRoleRelation(owner, role);
            const ownerRoleRelation = this.createOwnerRoleRelation(owner, role);
            const savedRelation = await manager.save(owner_role_relation_entity_1.OwnerRoleRelationEntity, ownerRoleRelation);
            console.log('✅ Broker relation saved successfully!');
            console.log('🆔 BROKER RELATION ID (use this for advisor vinculation):', savedRelation.id);
            console.log('👤 Owner ID:', savedRelation.ownerId);
            console.log('🏢 Business ID:', savedRelation.businessId);
            console.log('🎭 Role ID:', savedRelation.roleId);
            const verifyRelation = await manager.findOne(owner_role_relation_entity_1.OwnerRoleRelationEntity, {
                where: { id: savedRelation.id },
                relations: { role: true }
            });
            console.log('✅ Verification - relation found:', verifyRelation ? 'YES' : 'NO');
            if (verifyRelation) {
                console.log('🔍 Verification - ID:', verifyRelation.id);
                console.log('🎭 Verification - Role:', verifyRelation.role?.name);
            }
            console.log('');
            console.log('🚨 IMPORTANT: To vinculate an advisor to this broker, use the BROKER RELATION ID:');
            console.log(`   ${savedRelation.id}`);
            console.log('   NOT the owner ID or business ID!');
            console.log('');
            return this.cleanOwnerEntity(owner);
        });
    }
    async findRoleByName(roleName) {
        const role = await this.roleRepository.findOne({
            where: { name: roleName },
        });
        if (!role)
            throw new common_1.NotFoundException('Role não encontrada.');
        return role;
    }
    async findOwnerByDocument(document) {
        const owner = await this.ownerRepository.findOne({
            where: { cpf: document },
        });
        if (!owner)
            throw new common_1.NotFoundException('Proprietário não encontrado');
        return owner;
    }
    async ensureUniqueOwnerRoleRelation(owner, role) {
        const existingRelation = await this.ownerRoleRelationRepository.findOne({
            where: { owner, role },
        });
        if (existingRelation) {
            throw new common_1.ConflictException('Proprietário já possui a role de broker');
        }
    }
    createOwnerRoleRelation(owner, role) {
        const ownerRoleRelation = new owner_role_relation_entity_1.OwnerRoleRelationEntity();
        ownerRoleRelation.owner = owner;
        ownerRoleRelation.role = role;
        ownerRoleRelation.ownerId = owner.id;
        ownerRoleRelation.roleId = role.id;
        console.log('Creating broker relation:');
        console.log('Owner ID:', owner.id);
        console.log('Role ID:', role.id);
        console.log('Role name:', role.name);
        return ownerRoleRelation;
    }
    cleanOwnerEntity(owner) {
        const ownerCopy = { ...owner };
        delete ownerCopy.password;
        return ownerCopy;
    }
};
exports.CreateBrokerService = CreateBrokerService;
exports.CreateBrokerService = CreateBrokerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(role_entity_1.RoleEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(3, (0, common_1.Inject)(create_owner_service_1.CreateOwnerService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        create_owner_service_1.CreateOwnerService,
        typeorm_2.DataSource])
], CreateBrokerService);
//# sourceMappingURL=create-broker.service.js.map