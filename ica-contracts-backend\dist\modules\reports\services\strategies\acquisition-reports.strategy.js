"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AcquisitionReportStrategy = void 0;
const common_1 = require("@nestjs/common");
const acquisition_reports_strategy_abstractions_1 = require("./acquisition-reports.strategy.abstractions");
let AcquisitionReportStrategy = class AcquisitionReportStrategy extends acquisition_reports_strategy_abstractions_1.AcquisitionReportStrategyAbstraction {
    async generateReport(data) {
        return super.generateReportBase(data);
    }
};
exports.AcquisitionReportStrategy = AcquisitionReportStrategy;
exports.AcquisitionReportStrategy = AcquisitionReportStrategy = __decorate([
    (0, common_1.Injectable)()
], AcquisitionReportStrategy);
//# sourceMappingURL=acquisition-reports.strategy.js.map