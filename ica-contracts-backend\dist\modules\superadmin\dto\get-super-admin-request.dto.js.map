{"version": 3, "file": "get-super-admin-request.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-super-admin-request.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAgE;AAEhE,MAAa,aAAa;CAKzB;AALD,sCAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;IACR,IAAA,4BAAU,GAAE;;8CACG", "sourcesContent": ["import { IsDefined, IsNotEmpty, IsUUID } from 'class-validator';\r\n\r\nexport class SuperAdminDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  @IsNotEmpty()\r\n  ownerId: string;\r\n}\r\n"]}