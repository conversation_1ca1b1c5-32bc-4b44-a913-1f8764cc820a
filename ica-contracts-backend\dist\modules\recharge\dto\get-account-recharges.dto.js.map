{"version": 3, "file": "get-account-recharges.dto.js", "sourceRoot": "/", "sources": ["modules/recharge/dto/get-account-recharges.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA6E;AAE7E,MAAa,sBAAsB;CAYlC;AAZD,wDAYC;AATC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;yDACS;AAIlB;IAFC,IAAA,8BAAY,GAAE;IACd,IAAA,4BAAU,GAAE;;yDACM;AAInB;IAFC,IAAA,8BAAY,GAAE;IACd,IAAA,4BAAU,GAAE;;uDACI", "sourcesContent": ["import { IsString, IsDateString, IsOptional, IsUUID } from 'class-validator';\r\n\r\nexport class GetAccountRechargesDTO {\r\n  @IsString()\r\n  @IsUUID()\r\n  accountId: string;\r\n\r\n  @IsDateString()\r\n  @IsOptional()\r\n  startDate?: string;\r\n\r\n  @IsDateString()\r\n  @IsOptional()\r\n  endDate?: string;\r\n}\r\n"]}