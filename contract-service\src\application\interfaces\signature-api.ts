import type { InvestmentContract } from '@/domain/entities/contracts';
import type { Either } from '@/domain/shared';

/**
 * Enum to represent the signature status returned by the API.
 */
export enum SignatureStatus {
  AWAITING_SIGNATURE = 'AWAITING_SIGNATURE',
  SIGNED_BY_INVESTOR = 'SIGNED_BY_INVESTOR',
  SIGNED_BY_ALL = 'SIGNED_BY_ALL',
}

/**
 * Result of sending a contract for signature.
 */
export interface SignatureRequest {
  requestId: string;
}

/**
 * Signature API interface using Either<Error, A> for robust error handling.
 */
export interface ISignatureAPI {
  /**
   * Sends the contract for digital signature.
   * @param contract InvestmentContract aggregate
   * @param file Buffer containing the PDF file to be signed
   * @returns Either<Error, SignatureRequest>
   */
  send(
    contract: InvestmentContract,
    file: Buffer
  ): Promise<Either<Error, SignatureRequest>>;

  /**
   * Gets the current signature status from the signature provider.
   * @param requestId The request ID returned when the contract was sent
   * @returns Either<Error, SignatureStatus>
   */
  getStatus(
    contract: InvestmentContract
  ): Promise<Either<Error, SignatureStatus>>;
}
