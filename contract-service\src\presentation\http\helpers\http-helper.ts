import { HttpStatus } from './http-status'

export interface HttpResponse<T = unknown> {
  statusCode: number
  body: T
}

export const ok = <T>(data: T): HttpResponse<T> => ({
  statusCode: HttpStatus.OK,
  body: data,
})

export const created = <T>(data: T): HttpResponse<T> => ({
  statusCode: HttpStatus.CREATED,
  body: data,
})

export const badRequest = <T>(data: T): HttpResponse<T> => ({
  statusCode: HttpStatus.BAD_REQUEST,
  body: data,
})

export const unprocessable = <T>(data: T): HttpResponse<T> => ({
  statusCode: HttpStatus.UNPROCESSABLE,
  body: data,
})

export const notFound = <T>(data: T): HttpResponse<T> => ({
  statusCode: HttpStatus.NOT_FOUND,
  body: data,
})

export const serverError = (
  error: Error
): HttpResponse<{ error: string; message: string }> => ({
  statusCode: 500,
  body: {
    error: 'InternalServerError',
    message: error.message,
  },
})
