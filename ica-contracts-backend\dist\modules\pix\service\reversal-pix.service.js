"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReversalPixService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_transaction_celcoin_service_1 = require("../../../apis/celcoin/services/pix-transaction-celcoin.service");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
let ReversalPixService = class ReversalPixService {
    constructor(pixTransactionCelcoin, transactionRepo) {
        this.pixTransactionCelcoin = pixTransactionCelcoin;
        this.transactionRepo = transactionRepo;
    }
    async perform(data) {
        const transaction = await this.transactionRepo.findOne({
            where: {
                id: (0, typeorm_2.Equal)(data.id),
            },
        });
        const responseCelcoin = await this.pixTransactionCelcoin.reverse({
            id: transaction.code,
            amount: Number(transaction.value),
            endToEndId: transaction.endToEndId,
            clientCode: (0, uuid_1.v4)(),
            reason: data.reason,
        });
        return responseCelcoin;
    }
};
exports.ReversalPixService = ReversalPixService;
exports.ReversalPixService = ReversalPixService = __decorate([
    __param(0, (0, common_1.Inject)(pix_transaction_celcoin_service_1.PixTransactionCelcoinService)),
    __param(1, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __metadata("design:paramtypes", [pix_transaction_celcoin_service_1.PixTransactionCelcoinService,
        typeorm_2.Repository])
], ReversalPixService);
//# sourceMappingURL=reversal-pix.service.js.map