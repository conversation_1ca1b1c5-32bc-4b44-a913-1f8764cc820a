{"version": 3, "file": "get-one-pre-register.dto.js", "sourceRoot": "/", "sources": ["modules/pre-register/dto/get-one-pre-register.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAoD;AAEpD,MAAa,oBAAoB;CAQhC;AARD,oDAQC;AALC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;uDACS;AAIlB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;gDACE", "sourcesContent": ["import { IsDefined, IsUUID } from 'class-validator';\r\n\r\nexport class GetOnePreRegisterDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  adviserId: string;\r\n\r\n  @IsDefined()\r\n  @IsUUID()\r\n  id: string;\r\n}\r\n"]}