import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { IBillPaymentCelcoinWebhookDto } from '../dto/billpayment-celcoin-webhook.dto';
export declare class BillPaymentCelcoinWebhookService {
    private readonly transactionRepository;
    constructor(transactionRepository: Repository<TransactionEntity>);
    perform(input: IBillPaymentCelcoinWebhookDto): Promise<void>;
}
