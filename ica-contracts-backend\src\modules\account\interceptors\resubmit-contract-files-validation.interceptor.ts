import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  BadRequestException,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { Observable } from 'rxjs';
import { ResubmitContractDto } from 'src/modules/account/dto/resubmit-contract.dto';
import {
  assignFilesToBody,
  flattenValidationErrors,
} from './file-upload-validation.helper';

@Injectable()
export class ResubmitContractFilesValidationInterceptor
  implements NestInterceptor
{
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const files = request.files;
    if (files) {
      assignFilesToBody(files, request.body, [
        'contract',
        'proofOfPayment',
        'personalDocument',
        'proofOfResidence',
      ]);
    }
    const dto = plainToInstance(ResubmitContractDto, request.body);
    return validate(dto, { whitelist: true, forbidNonWhitelisted: true }).then(
      (errors) => {
        if (errors.length > 0) {
          throw new BadRequestException(flattenValidationErrors(errors));
        }
        request.body = dto;
        return next.handle();
      },
    );
  }
}
