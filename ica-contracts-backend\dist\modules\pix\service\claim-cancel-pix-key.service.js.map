{"version": 3, "file": "claim-cancel-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/claim-cancel-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,6CAAmD;AACnD,gGAAsF;AACtF,6FAAmF;AACnF,+EAAqE;AACrE,+EAAqE;AACrE,mFAAwE;AACxE,qCAAqC;AAIrC,IAAa,wBAAwB,GAArC,MAAa,wBAAwB;IACnC,YAEU,gBAA0C,EAE1C,iBAAsC;QAFtC,qBAAgB,GAAhB,gBAAgB,CAA0B;QAE1C,sBAAiB,GAAjB,iBAAiB,CAAqB;IAC7C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAqB,EAAE,OAAe;QAClD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE;gBACL;oBACE,GAAG,EAAE,KAAK,CAAC,GAAG;oBACd,MAAM,EAAE,sCAAgB,CAAC,OAAO;oBAChC,WAAW,EAAE,mCAAe,CAAC,IAAI;oBACjC,OAAO,EAAE,EAAE,OAAO,EAAE;iBACrB;gBACD;oBACE,GAAG,EAAE,KAAK,CAAC,GAAG;oBACd,MAAM,EAAE,sCAAgB,CAAC,OAAO;oBAChC,WAAW,EAAE,mCAAe,CAAC,IAAI;oBACjC,OAAO,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;iBACjC;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,kCAAkC,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAErD,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;YACvC,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,mCAAe,CAAC,iBAAiB;SAC1D,CAAC,CAAC;QAEH,MAAM,CAAC,WAAW,GAAG,mCAAe,CAAC,SAAS,CAAC;QAC/C,MAAM,CAAC,MAAM,GAAG,sCAAgB,CAAC,SAAS,CAAC;QAE3C,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,EAAE,MAAM,EAAE,sCAAsC,EAAE,CAAC;IAC5D,CAAC;CACF,CAAA;AA5CY,4DAAwB;mCAAxB,wBAAwB;IAEhC,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,eAAM,EAAC,2CAAmB,CAAC,CAAA;qCADF,oBAAU;QAET,2CAAmB;GALrC,wBAAwB,CA4CpC", "sourcesContent": ["import { Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { ClaimCelcoinService } from 'src/apis/celcoin/services/claim-celcoin.service';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { ClaimReasonEnum } from 'src/shared/enums/claim-reason.enum';\r\nimport { ClaimStatusEnum } from 'src/shared/enums/claim-status.enum';\r\nimport { PixKeyStatusEnum } from 'src/shared/enums/pix-key-status.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ClaimCancelDto } from '../dto/claim-cancel.dto';\r\n\r\nexport class ClaimCancelPixKeyService {\r\n  constructor(\r\n    @InjectRepository(PixKeyEntity)\r\n    private pixKeyRepository: Repository<PixKeyEntity>,\r\n    @Inject(ClaimCelcoinService)\r\n    private pixCelcoinService: ClaimCelcoinService,\r\n  ) {}\r\n\r\n  async execute(input: ClaimCancelDto, ownerId: string): Promise<any> {\r\n    const pixkey = await this.pixKeyRepository.findOne({\r\n      where: [\r\n        {\r\n          key: input.key,\r\n          status: PixKeyStatusEnum.PENDENT,\r\n          claimStatus: ClaimStatusEnum.OPEN,\r\n          account: { ownerId },\r\n        },\r\n        {\r\n          key: input.key,\r\n          status: PixKeyStatusEnum.PENDENT,\r\n          claimStatus: ClaimStatusEnum.OPEN,\r\n          account: { businessId: ownerId },\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (!pixkey) {\r\n      throw new NotFoundException('Chave não possui reinvindicação.');\r\n    }\r\n\r\n    const claimMetada = JSON.parse(pixkey.claimMetadata);\r\n\r\n    await this.pixCelcoinService.cancelClaim({\r\n      claimId: claimMetada.claimId,\r\n      reason: input.reason || ClaimReasonEnum.DEFAULT_OPERATION,\r\n    });\r\n\r\n    pixkey.claimStatus = ClaimStatusEnum.CANCELLED;\r\n    pixkey.status = PixKeyStatusEnum.CANCELLED;\r\n\r\n    await this.pixKeyRepository.save(pixkey);\r\n\r\n    return { status: 'Reivindicação cancelada com sucesso.' };\r\n  }\r\n}\r\n"]}