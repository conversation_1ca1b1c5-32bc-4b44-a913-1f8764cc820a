{"version": 3, "file": "report.module.js", "sourceRoot": "/", "sources": ["modules/reports/report.module.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA2D;AAC3D,wDAAkD;AAClD,8DAAwD;AAExD,mHAA4G;AAC5G,yEAAqE;AACrE,mEAA2D;AAC3D,gFAA2E;AAC3E,8DAA0D;AAC1D,qGAA+F;AAC/F,6FAAwF;AACxF,mHAA6G;AAetG,IAAM,YAAY,GAAlB,MAAM,YAAY;IACvB,YACmB,QAA+B,EAC/B,yBAAoD,EACpD,gCAAkE,EAClE,sBAA8C;QAH9C,aAAQ,GAAR,QAAQ,CAAuB;QAC/B,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,qCAAgC,GAAhC,gCAAgC,CAAkC;QAClE,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC9D,CAAC;IAEJ,YAAY;QACV,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,oCAAc,CAAC,WAAW,EAC1B,IAAI,CAAC,yBAAyB,CAC/B,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,QAAQ,CACpB,oCAAc,CAAC,gBAAgB,EAC/B,IAAI,CAAC,gCAAgC,CACtC,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,oCAAc,CAAC,IAAI,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAC3E,CAAC;CACF,CAAA;AAnBY,oCAAY;uBAAZ,YAAY;IAbxB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,EAAE,wBAAU,CAAC;QACnC,WAAW,EAAE,CAAC,sCAAiB,CAAC;QAChC,SAAS,EAAE;YACT,+CAAqB;YACrB,8BAAa;YACb,wDAAyB;YACzB,mEAA8B;YAC9B,sEAAgC;YAChC,iDAAsB;SACvB;QACD,OAAO,EAAE,CAAC,8BAAa,CAAC;KACzB,CAAC;qCAG6B,+CAAqB;QACJ,wDAAyB;QAClB,sEAAgC;QAC1C,iDAAsB;GALtD,YAAY,CAmBxB", "sourcesContent": ["import { Modu<PERSON>, type OnModuleInit } from '@nestjs/common';\r\nimport { ApisModule } from 'src/apis/apis.module';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { GetAcquisitionPerPeriodService } from '../acquisition/services/get-acquisition-per-period.service';\r\nimport { ReportsController } from './controllers/reports.controller';\r\nimport { ReportTypeEnum } from './dto/generate-report.dto';\r\nimport { ReportRegistryService } from './services/report-registry.service';\r\nimport { ReportService } from './services/report.service';\r\nimport { AcquisitionReportStrategy } from './services/strategies/acquisition-reports.strategy';\r\nimport { PaymentReportsStrategy } from './services/strategies/payment-reports.strategy';\r\nimport { ScheduledPaymentsReportsStrategy } from './services/strategies/scheduled-payments-reports.strategy';\r\n\r\n@Module({\r\n  imports: [SharedModule, ApisModule],\r\n  controllers: [ReportsController],\r\n  providers: [\r\n    ReportRegistryService,\r\n    ReportService,\r\n    AcquisitionReportStrategy,\r\n    GetAcquisitionPerPeriodService,\r\n    ScheduledPaymentsReportsStrategy,\r\n    PaymentReportsStrategy,\r\n  ],\r\n  exports: [ReportService],\r\n})\r\nexport class ReportModule implements OnModuleInit {\r\n  constructor(\r\n    private readonly registry: ReportRegistryService,\r\n    private readonly acquisitionReportStrategy: AcquisitionReportStrategy,\r\n    private readonly scheduledPaymentsReportsStrategy: ScheduledPaymentsReportsStrategy,\r\n    private readonly paymentReportsStrategy: PaymentReportsStrategy,\r\n  ) {}\r\n\r\n  onModuleInit() {\r\n    this.registry.register(\r\n      ReportTypeEnum.acquisition,\r\n      this.acquisitionReportStrategy,\r\n    );\r\n    this.registry.register(\r\n      ReportTypeEnum.scheduledPayment,\r\n      this.scheduledPaymentsReportsStrategy,\r\n    );\r\n    this.registry.register(ReportTypeEnum.paid, this.paymentReportsStrategy);\r\n  }\r\n}\r\n"]}