{"version": 3, "file": "get-favorite-pix-account.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/get-favorite-pix-account.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+C;AAC/C,6CAAmD;AACnD,6FAAoF;AACpF,uGAA6F;AAC7F,qCAA4C;AAI5C,IAAa,4BAA4B,GAAzC,MAAa,4BAA4B;IACvC,YAEU,SAAoC,EAEpC,aAA4C;QAF5C,cAAS,GAAT,SAAS,CAA2B;QAEpC,kBAAa,GAAb,aAAa,CAA+B;IACnD,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,sBAAa,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;QAE/D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YAC9C,KAAK,EAAE;gBACL,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB;SACF,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,GAAG,GAAqC,EAAE,CAAC;QAEjD,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACzB,GAAG,CAAC,IAAI,CAAC;gBACP,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ,CAAC,WAAW;oBAC1B,MAAM,EAAE,QAAQ,CAAC,aAAa;oBAC9B,QAAQ,EAAE,QAAQ,CAAC,eAAe;oBAClC,MAAM,EAAE,QAAQ,CAAC,aAAa;oBAC9B,IAAI,EAAE,QAAQ,CAAC,WAAW;iBAC3B;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACb,CAAC;CACF,CAAA;AAnDY,oEAA4B;uCAA5B,4BAA4B;IAEpC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCADjB,oBAAU;QAEN,oBAAU;GALxB,4BAA4B,CAmDxC", "sourcesContent": ["import { HttpException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { FavoritePixEntity } from 'src/shared/database/typeorm/entities/favorite-pix.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { IGetFavoritePixAccountResponse } from '../response/get-favorite-pix-account.response';\r\n\r\nexport class GetFavoritePixAccountService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(FavoritePixEntity)\r\n    private favoritePixDb: Repository<FavoritePixEntity>,\r\n  ) {}\r\n  async perform(id: string) {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new HttpException('Conta não existe', 400);\r\n\r\n    const favorites = await this.favoritePixDb.find({\r\n      where: {\r\n        accountId: account.id,\r\n      },\r\n    });\r\n\r\n    if (favorites.length <= 0) {\r\n      return [];\r\n    }\r\n\r\n    const res: IGetFavoritePixAccountResponse[] = [];\r\n\r\n    favorites.map((favorite) => {\r\n      res.push({\r\n        id: favorite.id,\r\n        name: favorite.name,\r\n        alias: favorite.alias,\r\n        account: {\r\n          bank: favorite.accountBank,\r\n          branch: favorite.accountBranch,\r\n          document: favorite.accountDocument,\r\n          number: favorite.accountNumber,\r\n          type: favorite.accountType,\r\n        },\r\n      });\r\n    });\r\n\r\n    return res;\r\n  }\r\n}\r\n"]}