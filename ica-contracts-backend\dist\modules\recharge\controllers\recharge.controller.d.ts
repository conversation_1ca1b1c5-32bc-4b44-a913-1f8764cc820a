import { CreateVirtualRechargeDto } from '../dto/create-virtual-recharge.dto';
import { ExportReportRechargesDto } from '../dto/export-report-recharges.dto';
import { GetAccountRechargesDTO } from '../dto/get-account-recharges.dto';
import { TransactionRechargeDto } from '../dto/transaction-recharge.dto';
import { CreateVirtualRechargeService } from '../services/create-virtual-recharge.service';
import { ExportReportRechargesService } from '../services/export-report-recharges.service';
import { GetAccountRecharges } from '../services/get-account-recharges.service';
import { TransactionRechargeService } from '../services/transaction-recharge.service';
export declare class RechargeController {
    private readonly transactionRechargeService;
    private readonly createVirtualRechargeService;
    private readonly getAccountRechargesService;
    private readonly exportReportService;
    constructor(transactionRechargeService: TransactionRechargeService, createVirtualRechargeService: CreateVirtualRechargeService, getAccountRechargesService: GetAccountRecharges, exportReportService: ExportReportRechargesService);
    transactionRecharge(body: TransactionRechargeDto): Promise<import("../../../apis/icainvest-credit/responses/create-transaction.response").ICreateTransactionResponse>;
    createTransaction(transactionDto: CreateVirtualRechargeDto): Promise<import("../../../apis/icainvest-credit/responses/create-transaction.response").ICreateTransactionResponse>;
    getRecharges(query: GetAccountRechargesDTO): Promise<{
        debits: import("../../../apis/icainvest-credit/responses/get-account-transactions.response").IGetAccountTransactionsResponse[];
        credits: import("../../../apis/icainvest-credit/responses/get-account-transactions.response").IGetAccountTransactionsResponse[];
        recharges: import("../../../apis/icainvest-credit/responses/get-account-transactions.response").IGetAccountTransactionsResponse[];
    }>;
    exportReport(query: ExportReportRechargesDto): Promise<string>;
}
