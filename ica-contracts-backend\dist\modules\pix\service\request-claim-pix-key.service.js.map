{"version": 3, "file": "request-claim-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/request-claim-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA6D;AAC7D,6CAAmD;AACnD,oGAAyF;AACzF,yFAAgF;AAChF,6FAAmF;AACnF,+EAAqE;AACrE,mFAAwE;AACxE,mDAA2C;AAC3C,qCAAqC;AAErC,gFAI0C;AAE1C,IAAa,yBAAyB,GAAtC,MAAa,yBAAyB;IACpC,YAEU,gBAA0C,EAE1C,eAAwC,EAExC,iBAAuC;QAJvC,qBAAgB,GAAhB,gBAAgB,CAA0B;QAE1C,oBAAe,GAAf,eAAe,CAAyB;QAExC,sBAAiB,GAAjB,iBAAiB,CAAsB;IAC9C,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,KAAiC,EACjC,MAAc;QAEd,IAAI,KAAK,CAAC,SAAS,KAAK,qCAAS,CAAC,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,sCAAU,CAAC,GAAG,EAAE,sCAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC9D,MAAM,IAAI,4BAAmB,CAC3B,GAAG,KAAK,CAAC,OAAO,kDAAkD,CACnE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,MAAM,CAAC,CAAC;QAE/D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE;gBACL,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;gBAChD,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE;aACpD;SACF,CAAC,CAAC;QAEH,IAAI,MAAM;YAAE,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;QAEpE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YAC/C,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM;aACX;YACD,SAAS,EAAE;gBACT,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAEtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;gBAC/D,OAAO,EAAE,OAAO,CAAC,MAAM;gBACvB,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACzB,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;aAC7C,CAAC;YAEF,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,6BAAY,EAAE;gBAC7C,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG;gBACtB,MAAM,EAAE,sCAAgB,CAAC,IAAI;gBAC7B,WAAW,EAAE,mCAAe,CAAC,IAAI;gBACjC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;gBAC5C,OAAO;gBACP,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE9B,OAAO,EAAE,MAAM,EAAE,mCAAmC,EAAE,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAvEY,8DAAyB;oCAAzB,yBAAyB;IAEjC,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,eAAM,EAAC,8CAAoB,CAAC,CAAA;qCAHH,oBAAU;QAEX,oBAAU;QAER,8CAAoB;GAPtC,yBAAyB,CAuErC", "sourcesContent": ["import { BadRequestException, Inject } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { ClaimStatusEnum } from 'src/shared/enums/claim-status.enum';\r\nimport { PixKeyStatusEnum } from 'src/shared/enums/pix-key-status.enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport {\r\n  ClaimType,\r\n  PixKeyType,\r\n  RequestClaimPixKeyInputDTO,\r\n} from '../dto/request-claim-pix-key.dto';\r\n\r\nexport class RequestClaimPixKeyService {\r\n  constructor(\r\n    @InjectRepository(PixKeyEntity)\r\n    private pixKeyRepository: Repository<PixKeyEntity>,\r\n    @InjectRepository(OwnerEntity)\r\n    private ownerRepository: Repository<OwnerEntity>,\r\n    @Inject(PixKeyCelcoinService)\r\n    private pixCelcoinService: PixKeyCelcoinService,\r\n  ) {}\r\n\r\n  async perform(\r\n    input: RequestClaimPixKeyInputDTO,\r\n    userId: string,\r\n  ): Promise<any> {\r\n    if (input.claimType === ClaimType.OWNERSHIP) {\r\n      if ([PixKeyType.CPF, PixKeyType.CNPJ].includes(input.typeKey)) {\r\n        throw new BadRequestException(\r\n          `${input.typeKey} não pode ser reivindicado, apenas portabilidade`,\r\n        );\r\n      }\r\n    }\r\n\r\n    logger.info('RequestClaimPixKeyService.perform() -> ', userId);\r\n\r\n    const pixkey = await this.pixKeyRepository.findOne({\r\n      where: [\r\n        { key: input.key, account: { ownerId: userId } },\r\n        { key: input.key, account: { businessId: userId } },\r\n      ],\r\n    });\r\n\r\n    if (pixkey) throw new BadRequestException('Chave já reivindicada!');\r\n\r\n    const owner = await this.ownerRepository.findOne({\r\n      where: {\r\n        id: userId,\r\n      },\r\n      relations: {\r\n        account: true,\r\n      },\r\n    });\r\n\r\n    const account = owner.account.shift();\r\n\r\n    return this.pixKeyRepository.manager.transaction(async (manager) => {\r\n      const response = await this.pixCelcoinService.requestClaimPixKey({\r\n        account: account.number,\r\n        claimType: input.claimType,\r\n        key: input.key,\r\n        keyType: input.typeKey,\r\n      });\r\n\r\n      const claimMetadata = {\r\n        claimId: response.body.id,\r\n        claimerAccount: response.body.claimerAccount,\r\n      };\r\n\r\n      const newPixKey = manager.create(PixKeyEntity, {\r\n        key: response.body.key,\r\n        status: PixKeyStatusEnum.OPEN,\r\n        claimStatus: ClaimStatusEnum.OPEN,\r\n        claimMetadata: JSON.stringify(claimMetadata),\r\n        account,\r\n        typeKey: input.typeKey,\r\n      });\r\n\r\n      await manager.save(newPixKey);\r\n\r\n      return { reason: 'Reivindicação aberta com sucesso.' };\r\n    });\r\n  }\r\n}\r\n"]}