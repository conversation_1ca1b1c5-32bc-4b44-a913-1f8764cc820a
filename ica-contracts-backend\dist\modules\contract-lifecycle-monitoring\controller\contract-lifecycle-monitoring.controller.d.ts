import { ContractEventStatus } from '../../../shared/enums/contract-events.enum';
import { CreateContractEventDto } from '../dto/create-contract-event.dto';
import { ExpiringContractDto } from '../dto/expiring-contract.dto';
import { FinalizeContractEventDto } from '../dto/finalize-contract-event.dto';
import { PaginatedResultDto } from '../dto/paginated-result.dto';
import { PaginationDto } from '../dto/pagination.dto';
import { SendToRetentionDto } from '../dto/send-to-retention.dto';
import { DashboardService } from '../services/dashboard.service';
import { FinalizeContractEventService } from '../services/finalize-contract-event.service';
import { FindExpiringContractsService } from '../services/find-expiring-contracts.service';
import { GetContractsForRetentionService } from '../services/get-contracts-for-retention.service';
import { SendContractToRetentionService } from '../services/send-contract-to-retention.service';
import { UpdateContractFromRetentionService } from '../services/update-contract-from-retention.service';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
export declare class ContractLifecycleMonitoringController {
    private readonly contractLifecycleMonitoringService;
    private readonly sendContractToRetentionService;
    private readonly getContractsForRetentionService;
    private readonly updateContractFromRetentionService;
    private readonly finalizeContractEventService;
    private readonly dashboardService;
    constructor(contractLifecycleMonitoringService: FindExpiringContractsService, sendContractToRetentionService: SendContractToRetentionService, getContractsForRetentionService: GetContractsForRetentionService, updateContractFromRetentionService: UpdateContractFromRetentionService, finalizeContractEventService: FinalizeContractEventService, dashboardService: DashboardService);
    getExpiringContracts(query: PaginationDto, request: IRequestUser): Promise<PaginatedResultDto<ExpiringContractDto>>;
    sendContractToRetention(body: SendToRetentionDto, request: IRequestUser): Promise<void>;
    getContractsForRetention(statuses: ContractEventStatus | ContractEventStatus[], request: IRequestUser, page?: number, limit?: number): Promise<PaginatedResultDto<import("../services/get-contracts-for-retention.service").IMappedContract>>;
    dashboard(request: IRequestUser): Promise<import("../dto/dashboard.dto").DashboardDto>;
    updateContractFromRetention(contractId: string, createEventDto: CreateContractEventDto, attachments: Express.Multer.File[], request: IRequestUser): Promise<{
        message: string;
        event: import("../../../shared/database/typeorm/entities/contract-event.entity").ContractEventEntity;
    }>;
    finalizeContractEvent(contractId: string, finalizeEventDto: FinalizeContractEventDto, attachments: Express.Multer.File[], request: IRequestUser): Promise<{
        message: string;
        event: import("../../../shared/database/typeorm/entities/contract-event.entity").ContractEventEntity;
    }>;
}
