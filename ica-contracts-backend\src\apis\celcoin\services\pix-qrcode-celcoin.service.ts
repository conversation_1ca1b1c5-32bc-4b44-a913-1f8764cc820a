import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';

import { CreatePixQrcodeDynamicCelcointRequest } from '../requests/create-pix-qrcode-dynamic-celcoin.request';
import { CreatePixQrcodeStaticCelcointRequest } from '../requests/create-pix-qrcode-static-celcoin.request';
import { IReadQrCodeCelcoinRequest } from '../requests/read-qrcode-celcoin.request';
import { ICreatePixQRCodeDynamicCelcoinResponse } from '../responses/create-pix-qrcode-dynamic-celcoin.response';
import { ICreatePixQRCodeStaticCelcoinResponse } from '../responses/create-pix-qrcode-static-celcoin.response';
import { IReadQrCodeCelcoinResponse } from '../responses/read-qrcode-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class PixQRCodeCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async createQRCodeDynamic(input: CreatePixQrcodeDynamicCelcointRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/pix/v1/brcode/dynamic`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<ICreatePixQRCodeDynamicCelcoinResponse> =
        await axios.post(url, input, config);

      return data;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async createQRCodeStatic(
    input: CreatePixQrcodeStaticCelcointRequest,
  ): Promise<ICreatePixQRCodeStaticCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/pix/v1/brcode/static`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<ICreatePixQRCodeStaticCelcoinResponse> =
        await axios.post(url, input, config);
      return data;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async readQRCode(
    input: IReadQrCodeCelcoinRequest,
  ): Promise<IReadQrCodeCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/pix/v1/emv`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IReadQrCodeCelcoinResponse> =
        await axios.post(url, input, config);
      return data;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }
}
