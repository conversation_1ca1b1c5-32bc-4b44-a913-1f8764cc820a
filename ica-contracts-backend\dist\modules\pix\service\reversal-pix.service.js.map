{"version": 3, "file": "reversal-pix.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/reversal-pix.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwC;AACxC,6CAAmD;AACnD,oHAAyG;AACzG,qGAA4F;AAC5F,qCAA4C;AAC5C,+BAA0B;AAI1B,IAAa,kBAAkB,GAA/B,MAAa,kBAAkB;IAC7B,YAEU,qBAAmD,EAEnD,eAA8C;QAF9C,0BAAqB,GAArB,qBAAqB,CAA8B;QAEnD,oBAAe,GAAf,eAAe,CAA+B;IACrD,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAoB;QAChC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC;aACnB;SACF,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC/D,EAAE,EAAE,WAAW,CAAC,IAAI;YACpB,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;YACjC,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,UAAU,EAAE,IAAA,SAAE,GAAE;YAChB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;CACF,CAAA;AAxBY,gDAAkB;6BAAlB,kBAAkB;IAE1B,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;qCADL,8DAA4B;QAElC,oBAAU;GAL1B,kBAAkB,CAwB9B", "sourcesContent": ["import { Inject } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\n\r\nimport { ReversalPixDto } from '../dto/reversal-pix.dto';\r\n\r\nexport class ReversalPixService {\r\n  constructor(\r\n    @Inject(PixTransactionCelcoinService)\r\n    private pixTransactionCelcoin: PixTransactionCelcoinService,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepo: Repository<TransactionEntity>,\r\n  ) {}\r\n  async perform(data: ReversalPixDto) {\r\n    const transaction = await this.transactionRepo.findOne({\r\n      where: {\r\n        id: Equal(data.id),\r\n      },\r\n    });\r\n\r\n    const responseCelcoin = await this.pixTransactionCelcoin.reverse({\r\n      id: transaction.code,\r\n      amount: Number(transaction.value),\r\n      endToEndId: transaction.endToEndId,\r\n      clientCode: v4(),\r\n      reason: data.reason,\r\n    });\r\n\r\n    return responseCelcoin;\r\n  }\r\n}\r\n"]}