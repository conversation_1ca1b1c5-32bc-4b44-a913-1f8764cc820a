{"version": 3, "file": "claim-status.enum.js", "sourceRoot": "/", "sources": ["shared/enums/claim-status.enum.ts"], "names": [], "mappings": ";;;AAAA,IAAY,eAMX;AAND,WAAY,eAAe;IACzB,gCAAa,CAAA;IACb,sCAAmB,CAAA;IACnB,0CAAuB,CAAA;IACvB,0CAAuB,CAAA;IACvB,0CAAuB,CAAA;AACzB,CAAC,EANW,eAAe,+BAAf,eAAe,QAM1B", "sourcesContent": ["export enum ClaimStatusEnum {\r\n  OPEN = 'OPEN',\r\n  WAITING = 'WAITING',\r\n  CANCELLED = 'CANCELLED',\r\n  CONFIRMED = 'CONFIRMED',\r\n  COMPLETED = 'COMPLETED',\r\n}\r\n"]}