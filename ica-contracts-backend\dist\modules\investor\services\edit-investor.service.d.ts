import { Repository } from 'typeorm';
import { AddressEntity } from '../../../shared/database/typeorm/entities/address.entity';
import { BusinessEntity } from '../../../shared/database/typeorm/entities/business.entity';
import { OwnerRoleRelationEntity } from '../../../shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from '../../../shared/database/typeorm/entities/owner.entity';
import { WalletsViewsEntity } from '../../../shared/database/typeorm/entities/wallets-views.entity';
import { EditBrokerDto } from '../../broker/dto/edit-broker.dto';
export declare class EditInvestorService {
    private readonly ownerRoleRelationRepository;
    private readonly walletsViewsRepository;
    private readonly ownerRepository;
    private readonly businessRepository;
    private readonly addressRepository;
    constructor(ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, walletsViewsRepository: Repository<WalletsViewsEntity>, ownerRepository: Repository<OwnerEntity>, businessRepository: Repository<BusinessEntity>, addressRepository: Repository<AddressEntity>);
    perform({ ownerRoleRelationId, business, owner, address }: EditBrokerDto, userId: string): Promise<void>;
    private uploadOwner;
    private formatPhoneNumber;
    private validateField;
    private validateBirthday;
    private validateName;
    private validateEmail;
    private validateNickname;
    private validateDocument;
    private validateAddress;
    private uploadAddress;
    private uploadBusiness;
    private validateBusinessField;
    private validateDtOpening;
    private verifyPermissions;
    private isAdmin;
    private isBroker;
    private hasDirectRelation;
    private isSuperAdmin;
}
