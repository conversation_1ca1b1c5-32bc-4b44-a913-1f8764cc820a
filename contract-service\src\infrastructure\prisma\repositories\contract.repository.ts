import { type Either, left, right } from "@/domain/shared";
import { PinoLoggerAdapter } from "@/main/adapters/pino-logger.adapter";
import prisma from "../client";
import { hasGetClient } from "./broker.repository";
import { ITransactionContext } from "@/application/interfaces";
import type {
  IContractRepository,
  Contract,
} from "@/domain/repositories/contract.repository";
import type { ContractStatus } from "@/domain/entities/contracts";

export class PrismaContractRepository implements IContractRepository {
  private readonly logger = new PinoLoggerAdapter();

  async findById(id: string): Promise<Either<Error, Contract | null>> {
    try {
      this.logger.info(`Procurando contrato com ID: ${id}`);

      const contract = await prisma.contract.findUnique({
        where: { id },
        select: {
          id: true,
          status: true,
          updatedAt: true,
          external_id: true,
        },
      });

      if (!contract) {
        return right(null);
      }

      if (!contract.status) {
        this.logger.error(`Contrato ${id} com status nulo`);
        return left(new Error(`Contract ${id} has a null status`));
      }

      return right({
        id: contract.id,
        status: contract.status as ContractStatus,
        updatedAt: contract.updatedAt,
        externalId: contract.external_id?.toString() ?? "",
      });
    } catch (error) {
      this.logger.error(`Erro ao encontrar contrato: ${error}`);
      return left(error as Error);
    }
  }

  async updateStatus(
    id: string,
    status: ContractStatus,
    tx?: ITransactionContext,
    externalId?: string
  ): Promise<Either<Error, Contract>> {
    try {
      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

      const contract = await client.contract.update({
        where: { id },
        data: {
          status,
          updatedAt: new Date(),
          external_id: externalId ? Number(externalId) : undefined,
        },
        select: {
          id: true,
          status: true,
          updatedAt: true,
          external_id: true,
        },
      });

      this.logger.info(`Contrato ${id} status atualizado para ${status}`);

      return right({
        id: contract.id,
        status: contract.status as ContractStatus,
        updatedAt: contract.updatedAt,
        externalId: String(contract.external_id ?? ""),
      });
    } catch (error) {
      this.logger.error(`Erro ao atualizar status do contrato: ${error}`);
      return left(error as Error);
    }
  }
}
