{"version": 3, "file": "payment-reports.strategy.js", "sourceRoot": "/", "sources": ["modules/reports/services/strategies/payment-reports.strategy.ts"], "names": [], "mappings": ";;;AAGA,iGAA2F;AAE3F,MAAa,sBAAuB,SAAQ,wEAAiC;IAE3E,KAAK,CAAC,cAAc,CAAC,IAAuB;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAE3E,MAAM,YAAY,GAAG,IAAI,CAAC,+BAA+B,CAAC,QAAQ,CAAC,CAAC;QAGpE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;CAEF;AAnBD,wDAmBC", "sourcesContent": ["import { FileEntity } from 'src/shared/database/typeorm/entities/files-entity';\r\n\r\nimport { GenerateReportDto } from '../../dto/generate-report.dto';\r\nimport { PaymentReportsStrategyAbstraction } from './payment-reports.strategy.abstraction';\r\n\r\nexport class PaymentReportsStrategy extends PaymentReportsStrategyAbstraction {\r\n\r\n  async generateReport(data: GenerateReportDto): Promise<FileEntity | null> {\r\n    const period = this.getPeriodDate(data.period);\r\n\r\n    const payments = await this.findByPeriod(period.startDate, period.endDate);\r\n\r\n    const transactions = this.transformPaymentsToReportFormat(payments);\r\n\r\n\r\n    if (transactions.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    const pdf = await this.generatePdfPayload(data, transactions);\r\n\r\n    return this.savePDF(pdf);\r\n  }\r\n\r\n}\r\n"]}