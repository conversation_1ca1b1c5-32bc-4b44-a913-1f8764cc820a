{"version": 3, "file": "delete-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/delete-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,oGAAyF;AACzF,6FAAoF;AACpF,6FAAmF;AACnF,mDAA2C;AAC3C,qCAA4C;AAKrC,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEU,SAAoC,EAEpC,QAAkC,EAElC,cAAoC;QAJpC,cAAS,GAAT,SAAS,CAA2B;QAEpC,aAAQ,GAAR,QAAQ,CAA0B;QAElC,mBAAc,GAAd,cAAc,CAAsB;IAC3C,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAqB,EAAE,EAAU;QAC7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAEnE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzC,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;aACZ;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,0BAAiB,CAAC,kBAAkB,CAAC,CAAC;QAE7D,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC;YAC5C,aAAa,EAAE,OAAO,CAAC,MAAM;YAC7B,MAAM,EAAE,MAAM,CAAC,GAAG;SACnB,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AAxCY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,eAAM,EAAC,8CAAoB,CAAC,CAAA;qCAHV,oBAAU;QAEX,oBAAU;QAEJ,8CAAoB;GAPnC,mBAAmB,CAwC/B", "sourcesContent": ["import { Inject, Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { DeletePixKeyDto } from '../dto/delete-pix-key.dto';\r\n\r\n@Injectable()\r\nexport class DeletePixKeyService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(PixKeyEntity)\r\n    private pixKeyDb: Repository<PixKeyEntity>,\r\n    @Inject(PixKeyCelcoinService)\r\n    private celcoinService: PixKeyCelcoinService,\r\n  ) {}\r\n  async perform(data: DeletePixKeyDto, id: string): Promise<void> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    logger.info('DeletePixKey.perform() -> ', account);\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    const pixKey = await this.pixKeyDb.findOne({\r\n      where: {\r\n        id: data.id,\r\n      },\r\n    });\r\n\r\n    if (!pixKey) throw new NotFoundException('Chave nao existe');\r\n\r\n    await this.celcoinService.deleteAccountPixKey({\r\n      accountNumber: account.number,\r\n      pixKey: pixKey.key,\r\n    });\r\n    await this.pixKeyDb.delete(pixKey.id);\r\n  }\r\n}\r\n"]}