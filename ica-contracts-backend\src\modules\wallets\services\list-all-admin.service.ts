import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Repository } from 'typeorm';

import { IResponseWallet } from '../responses/response-wallet';

@Injectable()
export class ListAllAdminService {
  constructor(
    @InjectRepository(OwnerRoleRelationEntity)
    private perfilDb: Repository<OwnerRoleRelationEntity>,
  ) {}
  async perform() {
    const response: IResponseWallet[] = [];
    const admins = await this.perfilDb.find({
      relations: {
        role: true,
        owner: {
          account: true,
        },
        business: {
          account: true,
        },
      },
      select: {
        owner: {
          name: true,
          cpf: true,
          email: true,
        },
        business: {
          companyName: true,
          cnpj: true,
          email: true,
        },
      },
      where: {
        role: {
          name: RolesEnum.ADMIN,
        },
      },
    });

    admins.map((admin) => {
      response.push({
        id: admin.id,
        document: admin.owner?.cpf || admin.business?.cnpj,
        email: admin.owner?.email || admin.business?.email,
        name: admin.owner?.name || admin.business?.companyName,
      });
    });

    return response;
  }
}
