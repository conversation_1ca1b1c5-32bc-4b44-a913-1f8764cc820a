export interface IChargeCreateCelcoinWebhookDto {
    entity: 'charge-create';
    createTimestamp: string;
    status: 'CREATED';
    body: {
        amount: number;
        debtor: {
            number: number;
            city: string;
            document: number;
            postalCode: number;
            name: string;
            neighborhood: string;
            state: string;
            complement: string;
            publicArea: string;
        };
        receiver: {
            city: string;
            document: number;
            postalCode: number;
            name: string;
            state: string;
            publicArea: string;
            account: number;
        };
        instructions: {
            fine: number;
            interest: number;
            discount: {
                amount: number;
                modality: 'fixed';
                limitDate: string;
            };
        };
        duedate: string;
        boleto: {
            bankNumber: number;
            bankAccount: number;
            bankEmissor: string;
            bankLine: string;
            bankAgency: number;
            transactionId: number;
            bankAssignor: string;
            status: 'PENDING';
            barCode: string;
        };
        externalId: string;
        expirationAfterPayment: number;
        transactionId: string;
        pix: {
            locationId: number;
            transactionId: number;
            key: string;
            transactionIdentification: string;
            status: 'PENDING';
            emv: string;
        };
        status: 'PENDING';
    };
}
