export interface IClient {
  full_name: string;
  cpf: string;
  birth_date: string; // Formato "dd/mm/yyyy"
  email: string;
  phone: string;
  address: string;
  postal_code: string;
  city: string;
  state: string;
  notes?: string; // Campo opcional
  profile: string;
  profitability: string; // Percentual como string
  investment_amount: string; // Valor como string (com R$)
  bank: string;
  agency: string;
  account: string;
  pix: string;
  advisor?: string;
  broker?: string;
  debenture?: boolean;
  sign_ica?: string;
  sign_investor?: string;
  details?: string; // Campo opcional
  start_date?: string; // Formato "dd/mm/yyyy"
  rent_date?: string; // Formato "dd/mm/yyyy"
  end_date?: string; // Formato "dd/mm/yyyy"
  investment_date?: string; // Formato "dd/mm/yyyy"
  type?: string;
  place_of_birth?: string;
  occupation?: string;
  payment_percentage?: number;
  contract_number?: string;
  par_value?: number;
  quota?: number;
  issuer?: string;
  document?: string;
  document_type?: string;
  testify?: {
    name: string;
    cpf: string;
    sign: string;
  }[];
  company_name?: string;
  company_document?: string;
  company_address?: string;
  company_city?: string;
  company_uf?: string;
  company_type?: string;
}
