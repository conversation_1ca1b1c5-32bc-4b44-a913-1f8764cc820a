{"version": 3, "file": "get-internal-pix-transaction.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/get-internal-pix-transaction.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,oHAAyG;AACzG,6FAAoF;AACpF,qGAA4F;AAC5F,qCAA4C;AAKrC,IAAM,gCAAgC,GAAtC,MAAM,gCAAgC;IAC3C,YAEU,SAAoC,EAEpC,qBAAoD,EAEpD,cAA4C;QAJ5C,cAAS,GAAT,SAAS,CAA2B;QAEpC,0BAAqB,GAArB,qBAAqB,CAA+B;QAEpD,mBAAc,GAAd,cAAc,CAA8B;IACnD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAmC,EAAE,EAAU;QAC3D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAEnE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,aAAa;aACvB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;QAE3E,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;QAE7D,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GACpB,MAAM,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC;YAClD,eAAe,EAAE,WAAW,CAAC,IAAI;YACjC,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,EAAE,EAAE,WAAW,CAAC,EAAE;SACnB,CAAC,CAAC;QAEL,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAA;AA5CY,4EAAgC;2CAAhC,gCAAgC;IAD5C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;qCAHlB,oBAAU;QAEE,oBAAU;QAEjB,8DAA4B;GAP3C,gCAAgC,CA4C5C", "sourcesContent": ["import { Inject, Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { GetInternalPixTransactionlDto } from '../dto/get-internal-pix-transaction.dto';\r\n\r\n@Injectable()\r\nexport class GetInternalPixTransactionService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepository: Repository<TransactionEntity>,\r\n    @Inject(PixTransactionCelcoinService)\r\n    private celcoinService: PixTransactionCelcoinService,\r\n  ) {}\r\n\r\n  async execute(data: GetInternalPixTransactionlDto, id: string): Promise<any> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    const transaction = await this.transactionRepository.findOne({\r\n      where: {\r\n        id: data.transactionId,\r\n      },\r\n    });\r\n\r\n    if (!transaction) throw new NotFoundException('Transação não encontrada.');\r\n\r\n    const tranferData = JSON.parse(transaction.transferMetadata);\r\n\r\n    const { body: result } =\r\n      await this.celcoinService.getInternalPixTransaction({\r\n        ClientRequestId: transaction.code,\r\n        EndToEndId: transaction.endToEndId,\r\n        id: tranferData.id,\r\n      });\r\n\r\n    return result;\r\n  }\r\n}\r\n"]}