import { Email } from '@/contexts/income-report/domain/entities/email';
import { EmailRepository } from '@/contexts/income-report/domain/repositories/email.respository';

import prisma from '@/infrastructure/prisma/client';

export class PrismaEmailRepository implements EmailRepository {
  async findByExternalId(external_id: string): Promise<Email | null> {
    const email = await prisma.email.findFirst({
      where: {
        external_id: external_id,
      },
    });

    if (!email) return null;
    const emailData: Email = {
      id: email.id,
      body: email.body ?? '',
      body_type: email.body_type ?? '',
      from: email.from,
      status: email.status ?? '',
      error_message: email.error_message,
      reply: email.reply ?? '',
      external_id: email.external_id ?? '',
    };
    return emailData;
  }
  async update(email: Email): Promise<void> {
    await prisma.email.update({
      where: {
        id: email.id,
      },
      data: {
        id: email.id,
        status: email.status,
        external_id: email.external_id,
        error_message: email.error_message,
        body: email.body,
        body_type: email.body_type,
        from: email.from,
        reply: email.reply,
      },
    });
  }
}
