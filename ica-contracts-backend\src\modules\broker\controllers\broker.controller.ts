import {
  Body,
  Controller,
  Get,
  HttpException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { RoleGuard } from 'src/shared/guards/role.guard';

import { EditInvestorService } from '../../investor/services/edit-investor.service';
import { CreateBrokerDto } from '../dto/create-broker.dto';
import { EditBrokerDto } from '../dto/edit-broker.dto';
import { CreateBrokerService } from '../services/create-broker.service';
import { EditBrokerService } from '../services/edit-broker.service';
import { GetBrokerAdvisorsService } from '../services/get-broker-advisors.service';
import { GetBrokerInvestorService } from '../services/get-broker-investors.service';
import { GetBrokerService } from '../services/get-broker.service';
import { BrokerDashboardService } from '../services/dashboard.service';
import { ContractsCaptureService } from '../services/contracts-capture.service';
import { ContractsMonthlyCaptureService } from '../services/contracts-monthly-capture.service';
import { GetOneScheduledPaymentForBrokerService } from '../services/get-one-scheduled-payment-for-broker.service';
import { GenerateReportDto } from '../dto/generate-report.dto';
import { GeneratePaymentReportService } from '../services/generate-payment-reports.service';
import { GenerateScheduledPaymentsReportService } from '../services/generate-scheduled-payments-reports.service';
import { ListIncomePaymentScheduledBrokerService } from '../services/list-payment-scheduled.service';
import { ListIncomePaymentScheduledBrokerDto } from '../dto/list-payment-scheduled.request.dto';
import { GenerateContractsReportsService } from '../services/generate-contracts-reports.service';
import { GetBrokerContractsGrowthChartService } from '../services/get-broker-contracts-growth-chart.service';
import { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
import { ListActiveInvestorsDto } from 'src/modules/income-report/dto/list-investors.dto';
import { ListActiveInvestorsBrokerService } from '../services/list-investors.service';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';

@Controller('broker')
@UseGuards(JwtAuthGuard, RoleGuard)
export class BrokerController {
  constructor(
    private readonly createBrokerService: CreateBrokerService,
    private readonly getBrokerAdvisorsService: GetBrokerAdvisorsService,
    private readonly getBrokerInvestorService: GetBrokerInvestorService,
    private readonly editBrokerService: EditBrokerService,
    private readonly editInvestorService: EditInvestorService,
    private readonly getBrokerService: GetBrokerService,
    private readonly brokerDashboardService: BrokerDashboardService,
    private readonly contractsCaptureService: ContractsCaptureService,
    private readonly contractsMonthlyCaptureService: ContractsMonthlyCaptureService,
    private readonly getOneScheduledPaymentForBrokerService: GetOneScheduledPaymentForBrokerService,
    private readonly generatePaymentReportService: GeneratePaymentReportService,
    private readonly listIncomePaymentScheduledBrokerService: ListIncomePaymentScheduledBrokerService,
    private readonly generateScheduledPaymentsReportService: GenerateScheduledPaymentsReportService,
    private readonly generateContractsReportsService: GenerateContractsReportsService,
    private readonly getBrokerContractsGrowthChartService: GetBrokerContractsGrowthChartService,
    private readonly listActiveInvestorsBrokerService: ListActiveInvestorsBrokerService,
  ) {}

  @Post()
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)
  async create(@Body() createBrokerDto: CreateBrokerDto) {
    const result = await this.createBrokerService.perform(createBrokerDto);

    // Buscar o ID da relação criada para retornar na resposta
    const { InjectRepository } = require('@nestjs/typeorm');
    const { OwnerRoleRelationEntity } = require('src/shared/database/typeorm/entities/owner-role-relation.entity');
    const { Equal } = require('typeorm');

    // Buscar a relação criada
    const ownerRoleRelationRepository = this.createBrokerService['ownerRoleRelationRepository'];
    const brokerRelation = await ownerRoleRelationRepository.findOne({
      where: {
        ownerId: result.id,
        role: { name: 'broker' }
      },
      relations: { role: true }
    });

    if (!brokerRelation) {
      // Tentar buscar por businessId se não encontrou por ownerId
      const brokerRelationBusiness = await ownerRoleRelationRepository.findOne({
        where: {
          businessId: result.id,
          role: { name: 'broker' }
        },
        relations: { role: true }
      });

      if (brokerRelationBusiness) {
        console.log('🎯 Broker created! Use this ID for advisor vinculation:', brokerRelationBusiness.id);
        return {
          ...result,
          brokerRelationId: brokerRelationBusiness.id,
          message: `Broker created successfully! Use brokerRelationId (${brokerRelationBusiness.id}) to vinculate advisors.`
        };
      }
    } else {
      console.log('🎯 Broker created! Use this ID for advisor vinculation:', brokerRelation.id);
      return {
        ...result,
        brokerRelationId: brokerRelation.id,
        message: `Broker created successfully! Use brokerRelationId (${brokerRelation.id}) to vinculate advisors.`
      };
    }

    return result;
  }

  @Roles(RolesEnum.BROKER)
  @Get('/advisor')
  async getBrokersAdvisors(@Request() request:IRequestUser) {
    const getAdvisorsDto = { ownerId: request.user.id };

    const result = await this.getBrokerAdvisorsService.perform(getAdvisorsDto);
    return result;
  }

  @Roles(RolesEnum.BROKER)
  @Get('/investors')
  async getBrokersInvestor(@Request() request:IRequestUser) {
    const getAdvisorsDto = { ownerId: request.user.id };

    const result = await this.getBrokerInvestorService.perform(getAdvisorsDto);
    return result;
  }

  @Roles(RolesEnum.BROKER)
  @Patch('')
  async editBroker(
    @Request() request:IRequestUser,
    @Body()
    body: EditBrokerDto,
  ) {
    await this.editBrokerService.perform(body);
  }

  @Put('investor')
  async editInvestor(
    @Request() request:IRequestUser,
    @Body()
    body: EditBrokerDto,
  ) {
    const result = await this.editInvestorService.perform(body, request.user.id);
    return result;
  }

  @Get('dashboard')
  async dashboard(@Request() request:IRequestUser) {
    const result = await this.brokerDashboardService.perform({
      ownerId: request.user.id,
    });
    return result;
  }

  @Get('income-report/investors')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER)
  async findActiveInvestors(
    @Request() request:IRequestUser,
    @Query() filters: ListActiveInvestorsDto,
  ) {
    return this.listActiveInvestorsBrokerService.perform(request.user.id, filters);
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER)
  @Get('contracts-capture/:brokerId')
  async contractsCapture(@Param('brokerId') brokerId: string) {
    const result = await this.contractsCaptureService.perform(brokerId);
    return result;
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER)
  @Get('contracts-monthly-capture/:brokerId')
  async grafics(@Param('brokerId') brokerId: string) {
    const result = await this.contractsMonthlyCaptureService.perform(brokerId);
    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Get('debug/:brokerId')
  async debugBroker(@Param('brokerId') brokerId: string) {
    try {
      // Importar os repositórios necessários para debug
      const { InjectRepository } = require('@nestjs/typeorm');
      const { OwnerRoleRelationEntity } = require('src/shared/database/typeorm/entities/owner-role-relation.entity');
      const { Equal } = require('typeorm');

      console.log('Debug - Searching for broker with ID:', brokerId);

      // Fazer consulta direta para debug
      const ownerRoleRelationRepository = this.getBrokerService['ownerRoleRelationRepository'];

      // Consulta sem filtro de role
      const anyRecord = await ownerRoleRelationRepository.findOne({
        where: { id: Equal(brokerId) },
        relations: { role: true }
      });

      console.log('Debug - Any record found:', anyRecord ? 'YES' : 'NO');
      if (anyRecord) {
        console.log('Debug - Record details:', {
          id: anyRecord.id,
          ownerId: anyRecord.ownerId,
          businessId: anyRecord.businessId,
          roleId: anyRecord.roleId,
          roleName: anyRecord.role?.name
        });
      }

      // Consulta com filtro de role
      const brokerRecord = await ownerRoleRelationRepository.findOne({
        where: {
          id: Equal(brokerId),
          role: { name: 'broker' }
        },
        relations: { role: true }
      });

      console.log('Debug - Broker record found:', brokerRecord ? 'YES' : 'NO');

      return {
        brokerId,
        anyRecord: anyRecord ? {
          id: anyRecord.id,
          ownerId: anyRecord.ownerId,
          businessId: anyRecord.businessId,
          roleId: anyRecord.roleId,
          roleName: anyRecord.role?.name
        } : null,
        brokerRecord: brokerRecord ? {
          id: brokerRecord.id,
          ownerId: brokerRecord.ownerId,
          businessId: brokerRecord.businessId,
          roleId: brokerRecord.roleId,
          roleName: brokerRecord.role?.name
        } : null
      };
    } catch (error) {
      console.error('Debug error:', error);
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get(':brokerId')
  async getOneBroker(@Param('brokerId') brokerId: string) {
    try {
      return await this.getBrokerService.perform(brokerId);
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Get('/:brokerId/income-payment-scheduled')
  @Roles(RolesEnum.BROKER)
  async listPaymentScheduledBroker(
    @Param('brokerId') brokerId: string,
    @Query() data?: ListIncomePaymentScheduledBrokerDto,
  ) {
    return this.listIncomePaymentScheduledBrokerService.perform(brokerId, data);
  }

  @Get('/:brokerId/income-payment-scheduled/:id')
  @Roles(RolesEnum.BROKER)
  async listPaymentScheduled(
    @Param('id') paymentScheduledId: string,
    @Param('brokerId') brokerId: string,
  ) {
    return this.getOneScheduledPaymentForBrokerService.perform(
      paymentScheduledId,
      brokerId,
    );
  }

  @Post('/:brokerId/payment-report')
  @Roles(RolesEnum.BROKER)
  async generatePaymentReport(
    @Param('brokerId') brokerId: string,
    @Body() data: GenerateReportDto,
  ) {
    return this.generatePaymentReportService.perform(data, brokerId);
  }

  @Post('/:brokerId/scheduled-payment-report')
  @Roles(RolesEnum.BROKER)
  async generateScheduledPaymentReport(
    @Param('brokerId') brokerId: string,
    @Body() data: GenerateReportDto,
  ) {
    return this.generateScheduledPaymentsReportService.perform(data, brokerId);
  }

  @Post('/:brokerId/contracts-report')
  @Roles(RolesEnum.BROKER)
  async generateContractsReport(
    @Param('brokerId') brokerId: string,
    @Body() data: GenerateReportDto,
  ) {
    return this.generateContractsReportsService.perform(data, brokerId);
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER)
  @Get('/:ownerRoleId/contracts-growth')
  async getContractsGrowthChart(
    @Param('ownerRoleId') ownerRoleId: string,
    @Query('period') period?: PeriodFilter,
    @Query('contractType') contractType?: ContractTypeEnum,
  ) {
    return this.getBrokerContractsGrowthChartService.perform(
      ownerRoleId,
      period,
      contractType,
    );
  }
}
