{"version": 3, "file": "add-pre-register.dto.js", "sourceRoot": "/", "sources": ["modules/pre-register/dto/add-pre-register.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAAwD;AACxD,yDAAyC;AACzC,qDAayB;AAEzB,MAAM,OAAO;CAwBZ;AArBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wCACK;AAIhB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;6CACU;AAIrB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2CACQ;AAInB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;uCACF;AAGjB,MAAM,UAAU;CA4Bf;AAzBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yCACG;AAId;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;4CACM;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yCACG;AAId;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;gDACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;gDACa;AAIrB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,8BAAY,GAAE;8BACF,IAAI;+CAAC;AAGpB,MAAa,iBAAiB;CAoD7B;AApDD,8CAoDC;AAhDC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;oDACS;AAIlB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;+CACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;6CACN;AAKX;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,uCAAW,GAAE;;mDACG;AAKjB;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,+BAAa,GAAE;;sDACI;AAIpB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qDACQ;AAInB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,8BAAY,GAAE;8BACN,IAAI;kDAAC;AAKd;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,yBAAO,GAAE;;gDACI;AAMd;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,OAAO,CAAC;8BACX,OAAO;kDAAC;AAMjB;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACX,UAAU;qDAAC;AAIvB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACU", "sourcesContent": ["import { IsCPFOrCNPJ } from 'brazilian-class-validator';\r\nimport { Type } from 'class-transformer';\r\nimport {\r\n  IsDateString,\r\n  IsDefined,\r\n  IsEmail,\r\n  IsInt,\r\n  IsNumber,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsPhoneNumber,\r\n  IsString,\r\n  IsUUID,\r\n  ValidateNested,\r\n} from 'class-validator';\r\n\r\nclass Address {\r\n  @IsDefined()\r\n  @IsString()\r\n  zipCode: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  neighborhood: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  city: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  state: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  complement: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  number: string;\r\n}\r\n\r\nclass Investment {\r\n  @IsDefined()\r\n  @IsNumber()\r\n  value: number;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  term: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  modality: string;\r\n\r\n  @IsDefined()\r\n  @IsNumber()\r\n  yield: number;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  purchaseWith: string;\r\n\r\n  @IsOptional()\r\n  @IsInt()\r\n  amountQuotes: number;\r\n\r\n  @IsDefined()\r\n  @IsDateString()\r\n  gracePeriod: Date;\r\n}\r\n\r\nexport class AddPreRegisterDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsUUID()\r\n  adviserId: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  rg: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsPhoneNumber()\r\n  phoneNumber: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  motherName: string;\r\n\r\n  @IsDefined()\r\n  @IsDateString()\r\n  dtBirth: Date;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsEmail()\r\n  email: string;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Address)\r\n  address: Address;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Investment)\r\n  investment: Investment;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  observations: string;\r\n}\r\n"]}