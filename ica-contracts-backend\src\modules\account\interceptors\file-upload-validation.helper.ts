export function getMissingFiles(
  files: Record<string, any>,
  requiredFields: string[],
): string[] {
  return requiredFields.filter((field) => !files?.[field] || !files[field][0]);
}

/**
 * Assign files to body
 * @param files - The files to assign
 * @param body - The body to assign the files to
 * @param fields - The fields to assign the files to
 * @example
 * assignFilesToBody({ contract: [{ file: 'contract.pdf' }], proofOfPayment: [{ file: 'proofOfPayment.pdf' }] }, { contract: null, proofOfPayment: null }, ['contract', 'proofOfPayment'])
 */
export function assignFilesToBody(
  files: Record<string, any>,
  body: Record<string, any>,
  fields: string[],
): void {
  fields.forEach((field) => {
    if (files[field]?.[0]) {
      body[field] = { file: files[field][0] };
    }
  });
}

/**
 * Flatten validation errors
 * @param errors - The errors to flatten
 * @param parentPath - The parent path of the errors
 * @returns The flattened errors
 * @example
 * flattenValidationErrors([{ property: 'name', constraints: { isString: 'Name must be a string' } }], 'user')
 * ['user.name: Name must be a string']
 */
export function flattenValidationErrors(
  errors: any[],
  parentPath = '',
): string[] {
  return errors.flatMap((err) => {
    const propertyPath = parentPath
      ? `${parentPath}.${err.property}`
      : err.property;

    const pathParts = propertyPath.split('.');
    const parentProperty = pathParts[pathParts.length - 2] || pathParts[0];
    const isFileField = Object.keys(fileLabels).includes(parentProperty);

    const constraints = Object.values(err.constraints ?? {}).map(
      (msg: string) => {
        // Only add label for file validation errors
        if (isFileField && msg.includes('Arquivo')) {
          return `${fileLabels[parentProperty]}: ${msg}`;
        }
        // For non-file errors, just return the message
        return msg;
      },
    );
    const children = err.children?.length
      ? flattenValidationErrors(err.children, propertyPath)
      : [];
    return [...constraints, ...children];
  });
}

export const fileLabels: Record<string, string> = {
  contract: 'Contrato',
  proofOfPayment: 'Comprovante',
  personalDocument: 'Documento de identidade',
  proofOfResidence: 'Comprovante de residência',
};
