"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuperAdminEditAdvisorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const edit_advisor_service_1 = require("../../advisor/services/edit-advisor.service");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const typeorm_2 = require("typeorm");
let SuperAdminEditAdvisorService = class SuperAdminEditAdvisorService {
    constructor(ownerRoleRelationRepository, editAdvisorService) {
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.editAdvisorService = editAdvisorService;
    }
    async perform(payload) {
        const advisorProfile = await this.ownerRoleRelationRepository.findOne({
            relations: {
                role: true,
            },
            where: {
                id: payload.ownerRoleRelationId,
                role: {
                    name: roles_enum_1.RolesEnum.ADVISOR,
                },
            },
        });
        if (!advisorProfile) {
            throw new common_1.NotFoundException('Assessor não encontrado');
        }
        if (payload.rate) {
            await this.ownerRoleRelationRepository.update(advisorProfile.id, {
                partPercent: payload.rate.toString(),
            });
        }
        return await this.editAdvisorService.perform(payload);
    }
};
exports.SuperAdminEditAdvisorService = SuperAdminEditAdvisorService;
exports.SuperAdminEditAdvisorService = SuperAdminEditAdvisorService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        edit_advisor_service_1.EditAdvisorService])
], SuperAdminEditAdvisorService);
//# sourceMappingURL=super-admin-edit-advisor.service.js.map