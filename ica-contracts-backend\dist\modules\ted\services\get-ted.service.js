"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetTedService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ted_celcoin_service_1 = require("../../../apis/celcoin/services/ted-celcoin.service");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const typeorm_2 = require("typeorm");
let GetTedService = class GetTedService {
    constructor(transactionRepo, celcoinService) {
        this.transactionRepo = transactionRepo;
        this.celcoinService = celcoinService;
    }
    async perform(data) {
        const transaction = await this.transactionRepo.findOne({
            where: {
                id: (0, typeorm_2.Equal)(data.id),
            },
        });
        const { body } = await this.celcoinService.getTed({
            id: transaction.code,
        });
        return body;
    }
};
exports.GetTedService = GetTedService;
exports.GetTedService = GetTedService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __param(1, (0, common_1.Inject)(ted_celcoin_service_1.TedCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        ted_celcoin_service_1.TedCelcoinService])
], GetTedService);
//# sourceMappingURL=get-ted.service.js.map