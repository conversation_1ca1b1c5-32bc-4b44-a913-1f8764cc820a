import type { File } from '@/domain/entities/files'
import type { IFileUploadRepository } from '@/domain/repositories/file-upload.repository'
import prisma from '../client'
import { FileMapper } from '../mappers/file.mapper'

export class PrismaFileUploadRepository implements IFileUploadRepository {
  async save(file: File): Promise<void> {
    const mapperFile = FileMapper.toPersistence(file)
    await prisma.files.create({
      data: mapperFile,
    })
  }
}
