{"version": 3, "file": "get-filter-pre-register.dto.js", "sourceRoot": "/", "sources": ["modules/pre-register/dto/get-filter-pre-register.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAAwD;AACxD,qDAQyB;AACzB,6FAAkF;AAElF,MAAa,uBAAuB;CAgCnC;AAhCD,0DAgCC;AA7BC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;0DACS;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,gDAAqB,CAAC;;uDACA;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;8BACL,IAAI;yDAAC;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;8BACP,IAAI;uDAAC;AAIb;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,uCAAW,GAAE;;yDACG;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACE", "sourcesContent": ["import { Is<PERSON><PERSON><PERSON>rCNPJ } from 'brazilian-class-validator';\r\nimport {\r\n  IsDateString,\r\n  IsDefined,\r\n  IsEnum,\r\n  IsN<PERSON>ber,\r\n  IsOptional,\r\n  IsString,\r\n  IsUUID,\r\n} from 'class-validator';\r\nimport { PreRegisterStatusEnum } from 'src/shared/enums/pre-register-status.enum';\r\n\r\nexport class GetFilterPreRegisterDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  adviserId: string;\r\n\r\n  @IsOptional()\r\n  @IsEnum(PreRegisterStatusEnum)\r\n  status: PreRegisterStatusEnum;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  valueMin: number;\r\n\r\n  @IsOptional()\r\n  @IsNumber()\r\n  valueMax: number;\r\n\r\n  @IsOptional()\r\n  @IsDateString()\r\n  dateFrom: Date;\r\n\r\n  @IsOptional()\r\n  @IsDateString()\r\n  dateTo: Date;\r\n\r\n  @IsOptional()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  name: string;\r\n}\r\n"]}