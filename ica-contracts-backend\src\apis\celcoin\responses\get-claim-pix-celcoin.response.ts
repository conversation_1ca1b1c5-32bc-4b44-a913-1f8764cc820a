export interface IGetPixClaimResponse {
  version: string;
  status: string;
  body: {
    id: string;
    claimType: string;
    key: string;
    keyType: string;
    claimerAccount: {
      participant: string;
      branch: string;
      account: string;
      accountType: string;
    };
    claimer: {
      personType: string;
      taxId: string;
      name: string;
    };
    donorParticipant: string;
    createTimestamp: string;
    completionPeriodEnd: string;
    resolutionPeriodEnd: string;
    lastModified: string;
    confirmReason: string;
    cancelReason: string;
    cancelledBy: string;
    donorAccount: {
      account: string;
      branch: string;
      taxId: string;
      name: string;
    };
  };
}
