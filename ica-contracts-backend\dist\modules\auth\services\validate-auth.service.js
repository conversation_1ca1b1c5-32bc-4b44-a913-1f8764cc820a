"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidateAuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const bcrypt = require("bcrypt");
const business_entity_1 = require("../../../shared/database/typeorm/entities/business.entity");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const typeorm_2 = require("typeorm");
let ValidateAuthService = class ValidateAuthService {
    constructor(ownerDb, businessDb, jwtService) {
        this.ownerDb = ownerDb;
        this.businessDb = businessDb;
        this.jwtService = jwtService;
    }
    async perform(data) {
        if (!data.password && !data.refreshToken) {
            throw new common_1.BadRequestException('Password ou RefreshToken deve ser fornecido');
        }
        const typeDocument = this.cpfOrCnpj(data.document);
        if (typeDocument === 'cpf') {
            const owner = await this.ownerDb.findOne({
                relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],
                where: {
                    cpf: data.document,
                },
            });
            if (owner) {
                if (owner.account && owner.account[0]?.isExternal) {
                    throw new common_1.UnauthorizedException();
                }
                const roles = owner.ownerRoleRelation.map((role) => role.role.name);
                if (data.refreshToken) {
                    const matchRefreshToken = await bcrypt.compare(data.refreshToken, owner.refreshToken);
                    if (matchRefreshToken) {
                        const token = this.generateToken({
                            cpf: owner.cpf,
                            email: owner.email,
                            id: owner.id,
                            roles,
                        });
                        const userRoleRelations = owner.ownerRoleRelation.map(relation => ({
                            roleId: relation.id,
                            roleName: relation.role.name,
                            partPercent: relation.partPercent,
                        }));
                        return {
                            accessToken: token,
                            user: {
                                id: owner.id,
                                name: owner.name,
                                email: owner.email,
                                document: owner.cpf,
                                type: 'owner',
                                roles: userRoleRelations,
                            },
                        };
                    }
                    throw new common_1.BadRequestException('Usuário ou refreshToken inválidos');
                }
                const validPassword = await bcrypt.compare(data.password, owner.password);
                if (validPassword) {
                    const token = this.generateToken({
                        cpf: owner.cpf,
                        email: owner.email,
                        id: owner.id,
                        roles,
                    });
                    const refreshToken = await this.generateRefreshToken({
                        id: owner.id,
                        document: owner.cpf,
                        name: owner.name,
                        email: owner.email,
                    });
                    const userRoleRelations = owner.ownerRoleRelation.map(relation => ({
                        roleId: relation.id,
                        roleName: relation.role.name,
                        partPercent: relation.partPercent,
                    }));
                    return {
                        accessToken: token,
                        refreshToken,
                        user: {
                            id: owner.id,
                            name: owner.name,
                            email: owner.email,
                            document: owner.cpf,
                            type: 'owner',
                            roles: userRoleRelations,
                        },
                    };
                }
                throw new common_1.BadRequestException('Usuário ou senha inválidos');
            }
            else {
                throw new common_1.BadRequestException('Usuário ou senha inválidos');
            }
        }
        if (typeDocument === 'cnpj') {
            const business = await this.businessDb.findOne({
                relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],
                where: {
                    cnpj: data.document,
                },
            });
            if (business) {
                if (business.account && business.account[0]?.isExternal) {
                    throw new common_1.UnauthorizedException();
                }
                const roles = business.ownerRoleRelation.map((role) => role.role.name);
                if (data.refreshToken) {
                    const matchRefreshToken = await bcrypt.compare(data.refreshToken, business.refreshToken);
                    if (matchRefreshToken) {
                        const token = this.generateToken({
                            cpf: business.cnpj,
                            email: business.email,
                            id: business.id,
                            roles,
                        });
                        const userRoleRelations = business.ownerRoleRelation.map(relation => ({
                            roleId: relation.id,
                            roleName: relation.role.name,
                            partPercent: relation.partPercent,
                        }));
                        return {
                            accessToken: token,
                            user: {
                                id: business.id,
                                name: business.companyName,
                                email: business.email,
                                document: business.cnpj,
                                type: 'business',
                                roles: userRoleRelations,
                            },
                        };
                    }
                    throw new common_1.BadRequestException('Usuário ou refreshToken inválidos');
                }
                const validPassword = await bcrypt.compare(data.password, business.password);
                if (validPassword) {
                    const token = this.generateToken({
                        cpf: business.cnpj,
                        email: business.email,
                        id: business.id,
                        roles,
                    });
                    const refreshToken = await this.generateRefreshToken({
                        id: business.id,
                        document: business.cnpj,
                        name: business.companyName,
                        email: business.email,
                    });
                    const userRoleRelations = business.ownerRoleRelation.map(relation => ({
                        roleId: relation.id,
                        roleName: relation.role.name,
                        partPercent: relation.partPercent,
                    }));
                    return {
                        accessToken: token,
                        refreshToken,
                        user: {
                            id: business.id,
                            name: business.companyName,
                            email: business.email,
                            document: business.cnpj,
                            type: 'business',
                            roles: userRoleRelations,
                        },
                    };
                }
                throw new common_1.BadRequestException('Usuário ou senha inválidos');
            }
            else {
                throw new common_1.BadRequestException('Usuário ou senha inválidos');
            }
        }
    }
    cpfOrCnpj(document) {
        if (document.length > 11) {
            return 'cnpj';
        }
        return 'cpf';
    }
    generateToken(data) {
        const token = this.jwtService.sign(data, {
            secret: process.env.JWT_SECRET,
            expiresIn: 3600,
        });
        return token;
    }
    async generateRefreshToken(data) {
        const payload = {
            id: data.id,
            document: data.document,
            name: data.name,
            email: data.email,
        };
        const token = this.jwtService.sign(payload, {
            secret: process.env.JWT_SECRET,
            expiresIn: '1y',
        });
        const cryptToken = await bcrypt.hash(token, 10);
        const typeDocument = this.cpfOrCnpj(data.document);
        if (typeDocument === 'cpf') {
            await this.ownerDb.update(data.id, {
                refreshToken: cryptToken,
            });
        }
        else if (typeDocument === 'cnpj') {
            await this.businessDb.update(data.id, {
                refreshToken: cryptToken,
            });
        }
        return token;
    }
    async getUserInfo(userId) {
        const owner = await this.ownerDb.findOne({
            where: { id: userId },
            relations: {
                ownerRoleRelation: {
                    role: true,
                },
            },
        });
        if (owner) {
            const userRoleRelations = owner.ownerRoleRelation.map(relation => ({
                roleId: relation.id,
                roleName: relation.role.name,
                partPercent: relation.partPercent,
            }));
            return {
                id: owner.id,
                name: owner.name,
                email: owner.email,
                document: owner.cpf,
                type: 'owner',
                roles: userRoleRelations,
            };
        }
        const business = await this.businessDb.findOne({
            where: { id: userId },
            relations: {
                ownerRoleRelation: {
                    role: true,
                },
            },
        });
        if (business) {
            const userRoleRelations = business.ownerRoleRelation.map(relation => ({
                roleId: relation.id,
                roleName: relation.role.name,
                partPercent: relation.partPercent,
            }));
            return {
                id: business.id,
                name: business.companyName,
                email: business.email,
                document: business.cnpj,
                type: 'business',
                roles: userRoleRelations,
            };
        }
        throw new common_1.BadRequestException('Usuário não encontrado');
    }
};
exports.ValidateAuthService = ValidateAuthService;
exports.ValidateAuthService = ValidateAuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(business_entity_1.BusinessEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService])
], ValidateAuthService);
//# sourceMappingURL=validate-auth.service.js.map