"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TedController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const delete_favorite_ted_contact_dto_1 = require("../dto/delete-favorite-ted-contact.dto");
const get_ted_dto_1 = require("../dto/get-ted.dto");
const save_favorite_ted_contact_dto_1 = require("../dto/save-favorite-ted-contact.dto");
const send_ted_dto_1 = require("../dto/send-ted.dto");
const delete_favorite_ted_contact_service_1 = require("../services/delete-favorite-ted-contact.service");
const get_favorite_ted_contact_service_1 = require("../services/get-favorite-ted-contact.service");
const get_recent_teds_service_1 = require("../services/get-recent-teds.service");
const get_ted_service_1 = require("../services/get-ted.service");
const save_favorite_ted_contact_service_1 = require("../services/save-favorite-ted-contact.service");
const send_ted_service_1 = require("../services/send-ted.service");
let TedController = class TedController {
    constructor(sendTedService, getTedService, getRecentTedsService, saveFavoriteTedContactService, getFavoriteTedContactService, deleteFavoriteTedContactService) {
        this.sendTedService = sendTedService;
        this.getTedService = getTedService;
        this.getRecentTedsService = getRecentTedsService;
        this.saveFavoriteTedContactService = saveFavoriteTedContactService;
        this.getFavoriteTedContactService = getFavoriteTedContactService;
        this.deleteFavoriteTedContactService = deleteFavoriteTedContactService;
    }
    async sendTed(body, request, twoFactorToken) {
        try {
            const data = await this.sendTedService.perform(body, request.user.id, twoFactorToken);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getTed(query) {
        try {
            const data = await this.getTedService.perform(query);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getRecent(request) {
        try {
            const data = await this.getRecentTedsService.perform(request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async saveFavoriteTedContact(body, request) {
        try {
            await this.saveFavoriteTedContactService.execute(body, request.user.id);
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getFavoriteTedContact(request) {
        try {
            const data = await this.getFavoriteTedContactService.execute(request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async deleteFavoriteTedContact(params, request) {
        try {
            await this.deleteFavoriteTedContactService.execute(params, request.user.id);
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
};
exports.TedController = TedController;
__decorate([
    (0, common_1.Post)('send'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Headers)('x-2fa-token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [send_ted_dto_1.SendTedDto, Object, String]),
    __metadata("design:returntype", Promise)
], TedController.prototype, "sendTed", null);
__decorate([
    (0, common_1.Get)('get'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_ted_dto_1.GetTedDto]),
    __metadata("design:returntype", Promise)
], TedController.prototype, "getTed", null);
__decorate([
    (0, common_1.Get)('recent'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TedController.prototype, "getRecent", null);
__decorate([
    (0, common_1.Post)('/favorite'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [save_favorite_ted_contact_dto_1.SaveFavoriteTedContactDto, Object]),
    __metadata("design:returntype", Promise)
], TedController.prototype, "saveFavoriteTedContact", null);
__decorate([
    (0, common_1.Get)('/favorite'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TedController.prototype, "getFavoriteTedContact", null);
__decorate([
    (0, common_1.Delete)('/favorite/:contactId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [delete_favorite_ted_contact_dto_1.DeleteFavoriteTedContactDto, Object]),
    __metadata("design:returntype", Promise)
], TedController.prototype, "deleteFavoriteTedContact", null);
exports.TedController = TedController = __decorate([
    (0, common_1.Controller)('ted'),
    __param(0, (0, common_1.Inject)(send_ted_service_1.SendTedService)),
    __param(1, (0, common_1.Inject)(get_ted_service_1.GetTedService)),
    __param(2, (0, common_1.Inject)(get_recent_teds_service_1.GetRecentTedsService)),
    __param(3, (0, common_1.Inject)(save_favorite_ted_contact_service_1.SaveFavoriteTedContactService)),
    __param(4, (0, common_1.Inject)(get_favorite_ted_contact_service_1.GetFavoriteTedContactService)),
    __param(5, (0, common_1.Inject)(delete_favorite_ted_contact_service_1.DeleteFavoriteTedContactService)),
    __metadata("design:paramtypes", [send_ted_service_1.SendTedService,
        get_ted_service_1.GetTedService,
        get_recent_teds_service_1.GetRecentTedsService,
        save_favorite_ted_contact_service_1.SaveFavoriteTedContactService,
        get_favorite_ted_contact_service_1.GetFavoriteTedContactService,
        delete_favorite_ted_contact_service_1.DeleteFavoriteTedContactService])
], TedController);
//# sourceMappingURL=ted.controller.js.map