"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddFavoritePixService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const favorite_pix_entity_1 = require("../../../shared/database/typeorm/entities/favorite-pix.entity");
const typeorm_2 = require("typeorm");
let AddFavoritePixService = class AddFavoritePixService {
    constructor(accountDb, favoritePixDb) {
        this.accountDb = accountDb;
        this.favoritePixDb = favoritePixDb;
    }
    async perform(data, id) {
        const account = await this.accountDb.findOne({
            relations: {
                business: true,
                owner: true,
            },
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
        });
        if (!account)
            throw new common_1.HttpException('Conta não existe ou não vinculada ao owner', 400);
        const create = this.favoritePixDb.create({
            alias: data.alias,
            name: data.name,
            accountBank: data.account.bank,
            accountBranch: data.account.branch,
            accountNumber: data.account.number,
            accountDocument: data.account.document,
            accountType: data.account.type,
            accountId: account.id,
        });
        await this.favoritePixDb.save(create);
    }
};
exports.AddFavoritePixService = AddFavoritePixService;
exports.AddFavoritePixService = AddFavoritePixService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(favorite_pix_entity_1.FavoritePixEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AddFavoritePixService);
//# sourceMappingURL=add-favorite-pix.service.js.map