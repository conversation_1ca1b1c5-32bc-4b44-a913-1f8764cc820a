{"version": 3, "file": "transaction-pix-manual.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/transaction-pix-manual.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAKwB;AACxB,6CAAmD;AACnD,iCAAiC;AACjC,2BAAyB;AACzB,uCAAsD;AACtD,oHAAyG;AACzG,6GAAmG;AACnG,+GAA+G;AAC/G,6HAAiH;AACjH,6FAAoF;AACpF,qGAA4F;AAC5F,yGAA8F;AAC9F,2FAAiF;AACjF,mDAA2C;AAC3C,qCAA4C;AAC5C,+BAA0B;AAC1B,2CAA+C;AAE/C,2FAA2F;AAC3F,uFAAsE;AAM/D,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAEU,SAAoC,EAEpC,qBAAoD,EAE3C,gBAAwD,EAEjE,cAA4C,EAE5C,qBAA4C,EAE5C,QAAgC,EAChC,aAA4B,EACnB,oBAA0C,EACtB,SAAoB;QAbjD,cAAS,GAAT,SAAS,CAA2B;QAEpC,0BAAqB,GAArB,qBAAqB,CAA+B;QAE3C,qBAAgB,GAAhB,gBAAgB,CAAwC;QAEjE,mBAAc,GAAd,cAAc,CAA8B;QAE5C,0BAAqB,GAArB,qBAAqB,CAAuB;QAE5C,aAAQ,GAAR,QAAQ,CAAwB;QAChC,kBAAa,GAAb,aAAa,CAAe;QACnB,yBAAoB,GAApB,oBAAoB,CAAsB;QACtB,cAAS,GAAT,SAAS,CAAW;IACxD,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,IAA6B,EAC7B,EAAU,EACV,cAAuB;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,wBAAwB,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,sBAAsB,CAAC,CAAC;QAEvE,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC;QAC3D,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAErC,IAAI,WAAW,IAAI,OAAO,IAAI,WAAW,GAAG,SAAS,EAAE,CAAC;YACtD,MAAM,IAAI,4BAAmB,CAC3B,4CAA4C,CAC7C,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,CAAC,oBAAoB,CAAC,6BAA6B,CAAC;YAC5D,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,cAAc;SACtB,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAC9C,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CACjC,CAAC;QAEF,IAAI,SAAS,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,4BAAmB,CAC3B,wIAAwI,CACzI,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAWnE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAExD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAClD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QAIrE,IAAI,WAA8B,CAAC;QAEnC,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,IAAA,kBAAO,EAAC,IAAI,CAAC,YAAY,CAAC;YAC3B,IAAA,kBAAO,EAAC,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,EACtC,CAAC;YACD,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,WAAW,CAChE,KAAK,EAAE,OAAO,EAAE,EAAE;gBAChB,MAAM,IAAI,GAAG,IAAA,SAAE,GAAE,CAAC;gBAClB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;oBACnE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC3B,cAAc,EAAE,QAAQ;oBACxB,WAAW,EAAE,WAAW;oBACxB,qBAAqB,EAAE,IAAI,CAAC,WAAW;oBACvC,eAAe,EAAE,UAAU;oBAC3B,OAAO,EAAE,MAAM;oBACf,UAAU,EAAE,IAAI;oBAChB,WAAW,EAAE;wBACX,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;wBAC9B,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;wBACtC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;wBACxB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;wBAC5B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;wBACxB,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;qBAC9B;oBACD,UAAU,EAAE;wBACV,OAAO,EAAE,OAAO,CAAC,MAAM;qBACxB;iBACF,CAAC,CAAC;gBAEH,MAAM,gBAAgB,GAAG;oBACvB,WAAW,EAAE,MAAM,CAAC,WAAW;oBAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;iBAC9B,CAAC;gBAEF,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,sCAAiB,EAAE;oBACvD,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,OAAO;oBACP,IAAI,EAAE,IAAI;oBACV,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,KAAK,EAAE,IAAI,CAAC,MAAM;oBAClB,MAAM,EAAE,+CAAqB,CAAC,OAAO;oBACrC,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO;oBAC1C,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI;oBACpC,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;oBACxC,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK;oBACzC,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI;oBACpC,IAAI,EAAE,4DAA2B,CAAC,UAAU;oBAC5C,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;iBACnD,CAAC,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACvD,OAAO,WAAW,CAAC;YACrB,CAAC,CACF,CAAC;QACJ,CAAC;QAED,OAAO;YACL,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,WAAW,EAAE;gBACX,OAAO,EAAE,WAAW,CAAC,cAAc;gBACnC,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,QAAQ,EAAE,WAAW,CAAC,eAAe;aACtC;YACD,UAAU,EAAE;gBACV,OAAO,EAAE,OAAO,CAAC,MAAM;gBACvB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;gBACzD,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI;aACtD;YACD,SAAS,EAAE,IAAA,mBAAQ,EAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;SAC/C,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAsB,EAAE,MAAc;QACrE,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY;YACf,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBAC9C,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;QAEL,eAAM,CAAC,IAAI,CACT,6DAA6D,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CACjH,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,MAAM;YAAE,OAAO;QAEjC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC;YACnE,SAAS,EAAE,OAAO,CAAC,EAAE;SACtB,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAExC,IAAI,cAAc,IAAI,YAAY,GAAG,cAAc,CAAC,MAAM;YACxD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QAErE,MAAM,2BAA2B,GAC/B,YAAY,IAAI,YAAY,CAAC,oBAAoB,CAAC;QACpD,MAAM,yBAAyB,GAAG,YAAY,IAAI,YAAY,CAAC,YAAY,CAAC;QAC5E,MAAM,yBAAyB,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;QAE1E,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,0BAA0B,GAC9B,GAAG,IAAI,EAAE;YACP,CAAC,CAAC,YAAY,IAAI,YAAY,CAAC,eAAe,GAAG,YAAY,CAAC,UAAU;YACxE,CAAC,CAAC,IAAI,CAAC;QAEX,eAAM,CAAC,IAAI,CAAC,kDAAkD,EAAE;YAC9D,2BAA2B;YAC3B,yBAAyB;YACzB,yBAAyB;YACzB,0BAA0B;SAC3B,CAAC,CAAC;QAEH,IACE,CAAC,CACC,2BAA2B;YAC3B,yBAAyB;YACzB,yBAAyB;YACzB,0BAA0B,CAC3B,EACD,CAAC;YACD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,IAA6B,EAC7B,OAAsB;QAEtB,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACrC,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,OAAO;YACP,IAAI,EAAE,IAAA,SAAE,GAAE;YACV,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;YACzC,MAAM,EAAE,+CAAqB,CAAC,SAAS;YACvC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;YACrC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;YAC7C,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YAC/B,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YACnC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACvC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YAC/B,IAAI,EAAE,4DAA2B,CAAC,UAAU;SAC7C,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA3OY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,2DAA0B,CAAC,CAAA;IAE5C,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;IAEpC,WAAA,IAAA,eAAM,EAAC,+CAAqB,CAAC,CAAA;IAE7B,WAAA,IAAA,eAAM,EAAC,kDAAsB,CAAC,CAAA;IAI9B,WAAA,IAAA,eAAM,EAAC,gCAAU,CAAC,CAAA;qCAbA,oBAAU;QAEE,oBAAU;QAEN,oBAAU;QAErB,8DAA4B;QAErB,+CAAqB;QAElC,kDAAsB;QACjB,sBAAa;QACG,8CAAoB;GAflD,2BAA2B,CA2OvC", "sourcesContent": ["/* eslint-disable import-helpers/order-imports */\r\nimport {\r\n  BadRequestException,\r\n  Inject,\r\n  Injectable,\r\n  NotFoundException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport * as moment from 'moment';\r\nimport 'moment-timezone';\r\nimport { addHours, isAfter, isToday } from 'date-fns';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { VirtualBalanceService } from 'src/apis/icainvest-credit/services/virtual-balance.service';\r\nimport { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';\r\nimport { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';\r\nimport { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\nimport { TwoFactorAuthService } from 'src/modules/two-factor-auth/two-factor-auth.service';\r\nimport { BR_HOLIDAY } from 'src/shared/providers/br-holiday.provider';\r\nimport type { BRHoliday } from 'br-holiday';\r\nimport { TransactionPixManualDto } from '../dto/transaction-pix-manual.dto';\r\nimport { ITransactionPixKeyResponse } from '../response/transaction-pix-key-response';\r\n\r\n@Injectable()\r\nexport class TransactionPixManualService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepository: Repository<TransactionEntity>,\r\n    @InjectRepository(AccountTransferLimitEntity)\r\n    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,\r\n    @Inject(PixTransactionCelcoinService)\r\n    private celcoinService: PixTransactionCelcoinService,\r\n    @Inject(VirtualBalanceService)\r\n    private virtualBalanceService: VirtualBalanceService,\r\n    @Inject(GetAccountLimitService)\r\n    private getLimit: GetAccountLimitService,\r\n    private configService: ConfigService,\r\n    private readonly twoFactorAuthService: TwoFactorAuthService,\r\n    @Inject(BR_HOLIDAY) private readonly brHoliday: BRHoliday,\r\n  ) {}\r\n\r\n  async perform(\r\n    data: TransactionPixManualDto,\r\n    id: string,\r\n    twoFactorToken?: string,\r\n  ): Promise<ITransactionPixKeyResponse> {\r\n    const startHour = this.configService.get<number>('TRANSACTION_START_HOUR');\r\n    const endHour = this.configService.get<number>('TRANSACTION_END_HOUR');\r\n\r\n    const currentDate = moment().utc().tz('America/Sao_Paulo');\r\n    const currentHour = currentDate.hour();\r\n    const currentDay = currentDate.day(); // 0 = Domingo, 6 = Sábado\r\n\r\n    if (currentHour >= endHour || currentHour < startHour) {\r\n      throw new BadRequestException(\r\n        'Transações não permitidas entre 19h e 08h.',\r\n      );\r\n    }\r\n\r\n    await this.twoFactorAuthService.verifyTwoFactorAuthentication({\r\n      userId: id,\r\n      token: twoFactorToken,\r\n    });\r\n\r\n    const isHoliday = await this.brHoliday.isHoliday(\r\n      currentDate.format('YYYY-MM-DD'),\r\n    );\r\n\r\n    if (isHoliday || currentDay === 0 || currentDay === 6) {\r\n      throw new BadRequestException(\r\n        'Por motivos de segurança, as transações via PIX e TED na plataforma ICA Invest estão disponíveis apenas em dias úteis, das 08h às 19h.',\r\n      );\r\n    }\r\n\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    // const isPasswordCorrect = await bcrypt.compare(\r\n    //   data.transactionPassword,\r\n    //   account.transactionPassword,\r\n    // );\r\n\r\n    // if (!isPasswordCorrect) {\r\n    //   throw new UnauthorizedException('Senha de transação incorreta.');\r\n    // }\r\n\r\n    const balance = await this.getLimit.execute(account.id);\r\n\r\n    if (Number(data.amount) > Number(balance.dailyLimit))\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n\r\n    // await this.isValidTransaction(account, data.amount);\r\n\r\n    let transaction: TransactionEntity;\r\n\r\n    if (\r\n      data.transferDate &&\r\n      !isToday(data.transferDate) &&\r\n      isAfter(data.transferDate, new Date())\r\n    ) {\r\n      transaction = await this.scheduleTransaction(data, account);\r\n    } else {\r\n      transaction = await this.transactionRepository.manager.transaction(\r\n        async (manager) => {\r\n          const uuid = v4();\r\n          const { body: result } = await this.celcoinService.transactionPixKey({\r\n            amount: Number(data.amount),\r\n            initiationType: 'MANUAL',\r\n            paymentType: 'IMMEDIATE',\r\n            remittanceInformation: data.description,\r\n            transactionType: 'TRANSFER',\r\n            urgency: 'HIGH',\r\n            clientCode: uuid,\r\n            creditParty: {\r\n              account: data.external.account,\r\n              accountType: data.external.accountType,\r\n              bank: data.external.bank,\r\n              branch: data.external.branch,\r\n              name: data.external.name,\r\n              taxId: data.external.document,\r\n            },\r\n            debitParty: {\r\n              account: account.number,\r\n            },\r\n          });\r\n\r\n          const transferMetadata = {\r\n            creditParty: result.creditParty,\r\n            debitParty: result.debitParty,\r\n          };\r\n\r\n          const newTransaction = manager.create(TransactionEntity, {\r\n            accountId: account.id,\r\n            account,\r\n            code: uuid,\r\n            description: data.description,\r\n            value: data.amount,\r\n            status: TransactionStatusEnum.PENDENT,\r\n            destinyAccount: result.creditParty.account,\r\n            destinyBank: result.creditParty.bank,\r\n            destinyBranch: result.creditParty.branch,\r\n            destinyDocument: result.creditParty.taxId,\r\n            destinyName: result.creditParty.name,\r\n            type: TransactionMovementTypeEnum.PIX_MANUAL,\r\n            endToEndId: result.endToEndId,\r\n            transferMetadata: JSON.stringify(transferMetadata),\r\n          });\r\n          const transaction = await manager.save(newTransaction);\r\n          return transaction;\r\n        },\r\n      );\r\n    }\r\n\r\n    return {\r\n      id: transaction.id,\r\n      status: transaction.status,\r\n      creditParty: {\r\n        account: transaction.destinyAccount,\r\n        name: transaction.destinyName,\r\n        document: transaction.destinyDocument,\r\n      },\r\n      debitParty: {\r\n        account: account.number,\r\n        name: account.owner?.name || account.business.fantasyName,\r\n        document: account.owner?.cpf || account.business.cnpj,\r\n      },\r\n      createdAt: addHours(transaction.createdAt, -3),\r\n    };\r\n  }\r\n\r\n  private async isValidTransaction(account: AccountEntity, amount: string) {\r\n    let accountLimit = await this.accountLimitRepo.findOne({\r\n      where: { accountId: account.id },\r\n    });\r\n\r\n    if (!accountLimit)\r\n      accountLimit = await this.accountLimitRepo.save({\r\n        accountId: account.id,\r\n      });\r\n\r\n    logger.info(\r\n      `TransactionPixKeyService.isValidTransaction() -> [amount: ${amount}, entity: ${JSON.stringify(accountLimit)} ]`,\r\n    );\r\n\r\n    if (!accountLimit.active) return;\r\n\r\n    const virtualBalance = await this.virtualBalanceService.checkBalance({\r\n      accountId: account.id,\r\n    });\r\n\r\n    const amountNumber = parseFloat(amount);\r\n\r\n    if (virtualBalance && amountNumber > virtualBalance.amount)\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n\r\n    const amountIsValidOnGeneralLimit =\r\n      amountNumber <= accountLimit.generalTransferLimit;\r\n    const amountIsValidOnMonthLimit = amountNumber <= accountLimit.monthlyLimit;\r\n    const amountIsValidOnDailyLimit = amountNumber <= accountLimit.dailyLimit;\r\n\r\n    const now = new Date().getHours();\r\n    const amountIsValidOnNightlLimit =\r\n      now >= 23\r\n        ? amountNumber <= accountLimit.dailyNightLimit - accountLimit.dailyLimit\r\n        : true;\r\n\r\n    logger.info('TransactionPixKeyService.isValidTransaction() ->', {\r\n      amountIsValidOnGeneralLimit,\r\n      amountIsValidOnMonthLimit,\r\n      amountIsValidOnDailyLimit,\r\n      amountIsValidOnNightlLimit,\r\n    });\r\n\r\n    if (\r\n      !(\r\n        amountIsValidOnGeneralLimit &&\r\n        amountIsValidOnMonthLimit &&\r\n        amountIsValidOnDailyLimit &&\r\n        amountIsValidOnNightlLimit\r\n      )\r\n    ) {\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n    }\r\n  }\r\n\r\n  private async scheduleTransaction(\r\n    data: TransactionPixManualDto,\r\n    account: AccountEntity,\r\n  ) {\r\n    return this.transactionRepository.save({\r\n      accountId: account.id,\r\n      account,\r\n      code: v4(),\r\n      description: data.description,\r\n      value: data.amount,\r\n      transferDate: new Date(data.transferDate),\r\n      status: TransactionStatusEnum.SCHEDULED,\r\n      destinyAccount: data.external.account,\r\n      destinyAccountType: data.external.accountType,\r\n      destinyBank: data.external.bank,\r\n      destinyBranch: data.external.branch,\r\n      destinyDocument: data.external.document,\r\n      destinyName: data.external.name,\r\n      type: TransactionMovementTypeEnum.PIX_MANUAL,\r\n    });\r\n  }\r\n}\r\n"]}