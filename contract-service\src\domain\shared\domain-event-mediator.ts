import type { DomainEvent } from './domain-event'

export class DomainEventMediator {
  private static handlers: {
    [event: string]: ((event: DomainEvent<any>) => Promise<void>)[]
  } = {}

  static register<T>(
    eventType: string,
    handler: (event: DomainEvent<T>) => Promise<void>
  ): void {
    if (!DomainEventMediator.handlers[eventType]) {
      DomainEventMediator.handlers[eventType] = []
    }
    DomainEventMediator.handlers[eventType].push(handler as any)
  }

  static async dispatch<T>(event: DomainEvent<T>): Promise<void> {
    const handlers = DomainEventMediator.handlers[event.eventName] || []
    for (const handler of handlers) {
      await handler(event)
    }
  }

  static clear() {
    this.handlers = {}
  }
}
