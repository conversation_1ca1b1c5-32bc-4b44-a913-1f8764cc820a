import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { S3 } from 'aws-sdk';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { DocumentEntity } from 'src/shared/database/typeorm/entities/document.entity';
import { Equal, Repository } from 'typeorm';

import { SendDocumentsAccountDto } from '../dto/send-documents-account.dto';

@Injectable()
export class SendDocumentsAccountService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(DocumentEntity)
    private documentDb: Repository<DocumentEntity>,
  ) {}
  async perform(data: SendDocumentsAccountDto, id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    if (!data.back || !data.front) {
      throw new BadRequestException('Documentos exigidos frente e verso');
    }

    if (account.type === 'business' && !data.socialContract) {
      throw new BadRequestException(
        'Contrato Social obrigatório para contas PJ',
      );
    }

    const filename = `${account.id}-${new Date()}`;
    const s3 = new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });

    const saveFront = await s3
      .upload({
        Bucket: process.env.S3_BUCKET,
        Key: `${filename}-FRONT.jpg`,
        Body: data.front.buffer,
        ACL: 'public-read',
      })
      .promise();

    const saveBack = await s3
      .upload({
        Bucket: process.env.S3_BUCKET,
        Key: `${filename}-BACK.jpg`,
        Body: data.back.buffer,
        ACL: 'public-read',
      })
      .promise();

    let createDoc = this.documentDb.create({
      accountId: account.id,
      back: saveBack.Key,
      front: saveFront.Key,
    });
    if (data.socialContract) {
      const saveContract = await s3
        .upload({
          Bucket: process.env.S3_BUCKET,
          Key: `${filename}-CONTRATO_SOCIAL.pdf`,
          Body: data.socialContract.buffer,
          ContentType: 'application/pdf',
          ACL: 'public-read',
        })
        .promise();

      createDoc = this.documentDb.create({
        ...createDoc,
        socialContract: saveContract.Key,
      });
    }

    if (data.cardCnpj) {
      const saveCard = await s3
        .upload({
          Bucket: process.env.S3_BUCKET,
          Key: `${filename}-CARD_CNPJ.pdf`,
          Body: data.cardCnpj.buffer,
          ContentType: 'application/pdf',
          ACL: 'public-read',
        })
        .promise();

      createDoc = this.documentDb.create({
        ...createDoc,
        cardCNPJ: saveCard.Key,
      });
    }

    await this.documentDb.save(createDoc);
  }
}
