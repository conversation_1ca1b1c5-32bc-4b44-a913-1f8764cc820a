import { ProcessInvestorSignatureUseCase } from '@/application/usecases/process-investor-signature/process-investor-signature.usecase';
import { FetchHttpClient } from '@/infrastructure/http/fetch-http-client';
import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories';
import { PinoLoggerAdapter } from '@/main/adapters/pino-logger.adapter';

import { SignatureAdapter } from '@/main/adapters/rbm/signature.adapter';

export function makeProcessInvestorSignatureUseCase() {
  const fetchHttp = new FetchHttpClient();
  const prismaInvestmentContractRepository =
    new PrismaInvestmentContractRepository();
  const signatureApi = new SignatureAdapter(fetchHttp);
  const pinoLoggerAdapter = new PinoLoggerAdapter();

  const useCase = new ProcessInvestorSignatureUseCase(
    prismaInvestmentContractRepository,
    signatureApi,
    pinoLoggerAdapter
  );

  return useCase;
}
