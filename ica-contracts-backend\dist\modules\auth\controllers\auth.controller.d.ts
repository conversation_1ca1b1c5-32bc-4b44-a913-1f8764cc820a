import { LoginDto } from '../dto/login.dto';
import { ValidateAuthService } from '../services/validate-auth.service';
export declare class AuthController {
    private readonly validateAuthService;
    constructor(validateAuthService: ValidateAuthService);
    login(body: LoginDto): Promise<import("../interfaces/auth-response.interface").IAuthResponse>;
    getMe(req: any): Promise<{
        id: string;
        name: string;
        email: string;
        document: string;
        type: "owner";
        roles: {
            roleId: string;
            roleName: string;
            partPercent: string;
        }[];
    } | {
        id: string;
        name: string;
        email: string;
        document: string;
        type: "business";
        roles: {
            roleId: string;
            roleName: string;
            partPercent: string;
        }[];
    }>;
}
