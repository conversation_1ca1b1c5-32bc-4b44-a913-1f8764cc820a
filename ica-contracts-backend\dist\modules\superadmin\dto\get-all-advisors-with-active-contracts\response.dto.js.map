{"version": 3, "file": "response.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-all-advisors-with-active-contracts/response.dto.ts"], "names": [], "mappings": ";;;AAGA,MAAa,mBAAmB;CAM/B;AAND,kDAMC;AAED,MAAa,4CAA4C;CAKxD;AALD,oGAKC", "sourcesContent": ["import { PaginationMeta } from '../../helpers/pagination-meta.dto';\r\nimport { IPaginatedResult } from '../../helpers/pagination-query';\r\n\r\nexport class AdvisorInfoResponse {\r\n  advisorId: string;\r\n  rank: number;\r\n  name: string;\r\n  avatar: string;\r\n  totalValue: number;\r\n}\r\n\r\nexport class GetAllAdvisorsWithActiveContractsResponseDto\r\n  implements IPaginatedResult<AdvisorInfoResponse>\r\n{\r\n  data: AdvisorInfoResponse[];\r\n  meta: PaginationMeta;\r\n}\r\n"]}