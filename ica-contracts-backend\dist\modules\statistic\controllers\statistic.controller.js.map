{"version": 3, "file": "statistic.controller.js", "sourceRoot": "/", "sources": ["modules/statistic/controllers/statistic.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAkE;AAElE,gFAA8D;AAC9D,iEAAwD;AACxD,0EAAgE;AAChE,kEAAyD;AAEzD,sEAAiE;AACjE,mFAA8E;AAGvE,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YACmB,sBAA8C;QAA9C,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC9D,CAAC;IAKE,AAAN,KAAK,CAAC,eAAe,CACX,SAA6B;QAErC,MAAM,gBAAgB,GACpB,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF,CAAA;AAfY,kDAAmB;AAQxB;IAHL,IAAA,YAAG,GAAE;IACL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC;IAE1C,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAY,yCAAkB;;0DAKtC;8BAdU,mBAAmB;IAD/B,IAAA,mBAAU,EAAC,YAAY,CAAC;qCAGoB,iDAAsB;GAFtD,mBAAmB,CAe/B", "sourcesContent": ["import { Controller, Put, Body, UseGuards } from '@nestjs/common';\r\nimport { StatisticEntity } from 'src/shared/database/typeorm/entities/statistic.entity';\r\nimport { Roles } from 'src/shared/decorators/roles.decorator';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { RoleGuard } from 'src/shared/guards/role.guard';\r\n\r\nimport { UpdateStatisticDto } from '../dto/update-statistic.dto';\r\nimport { UpdateStatisticService } from '../services/update-statistic.service';\r\n\r\n@Controller('statistics')\r\nexport class StatisticController {\r\n  constructor(\r\n    private readonly updateStatisticService: UpdateStatisticService,\r\n  ) {}\r\n\r\n  @Put()\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)\r\n  async updateStatistic(\r\n    @Body() updateDto: UpdateStatisticDto,\r\n  ): Promise<StatisticEntity[]> {\r\n    const updatedStatistic =\r\n      await this.updateStatisticService.perform(updateDto);\r\n    return updatedStatistic;\r\n  }\r\n}\r\n"]}