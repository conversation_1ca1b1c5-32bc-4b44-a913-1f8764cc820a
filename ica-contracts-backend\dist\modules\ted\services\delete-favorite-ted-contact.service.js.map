{"version": 3, "file": "delete-favorite-ted-contact.service.js", "sourceRoot": "/", "sources": ["modules/ted/services/delete-favorite-ted-contact.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAmD;AACnD,6CAAmD;AACnD,6FAAoF;AACpF,uGAA6F;AAC7F,qCAA4C;AAI5C,IAAa,+BAA+B,GAA5C,MAAa,+BAA+B;IAC1C,YAEU,WAAsC,EAEtC,eAA8C;QAF9C,gBAAW,GAAX,WAAW,CAA2B;QAEtC,oBAAe,GAAf,eAAe,CAA+B;IACrD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAiC,EAAE,EAAU;QACzD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,0BAAiB,CACzB,iDAAiD,CAClD,CAAC;QAEJ,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YAC5D,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,SAAS;gBAClB,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB;YACrB,MAAM,IAAI,0BAAiB,CAAC,wCAAwC,CAAC,CAAC;QAExE,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,kBAAkB,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,CAAC;CACF,CAAA;AA9BY,0EAA+B;0CAA/B,+BAA+B;IAEvC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCADf,oBAAU;QAEN,oBAAU;GAL1B,+BAA+B,CA8B3C", "sourcesContent": ["import { NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { FavoriteTedEntity } from 'src/shared/database/typeorm/entities/favorite-ted.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { DeleteFavoriteTedContactDto } from '../dto/delete-favorite-ted-contact.dto';\r\n\r\nexport class DeleteFavoriteTedContactService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountRepo: Repository<AccountEntity>,\r\n    @InjectRepository(FavoriteTedEntity)\r\n    private favoriteTedRepo: Repository<FavoriteTedEntity>,\r\n  ) {}\r\n\r\n  async execute(data: DeleteFavoriteTedContactDto, id: string): Promise<void> {\r\n    const account = await this.accountRepo.findOne({\r\n      where: [{ ownerId: Equal(id) }, { businessId: Equal(id) }],\r\n    });\r\n\r\n    if (!account)\r\n      throw new NotFoundException(\r\n        'Conta não encontrada ou não vinculada ao owner.',\r\n      );\r\n\r\n    const favoriteTedContact = await this.favoriteTedRepo.findOne({\r\n      where: {\r\n        id: data.contactId,\r\n        accountId: account.id,\r\n      },\r\n    });\r\n\r\n    if (!favoriteTedContact)\r\n      throw new NotFoundException('Contato não existe ou já foi excluido.');\r\n\r\n    await this.favoriteTedRepo.delete({ id: favoriteTedContact.id });\r\n  }\r\n}\r\n"]}