{"version": 3, "file": "direct-add-pre-register.dto.js", "sourceRoot": "/", "sources": ["modules/pre-register/dto/direct-add-pre-register.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yDAAyC;AACzC,qDASyB;AAEzB,MAAM,OAAO;CAoBZ;AAjBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wCACK;AAIhB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;6CACU;AAIrB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qCACE;AAIb;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2CACQ;AAInB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;uCACF;AAGjB,MAAa,uBAAuB;CA+BnC;AA/BD,0DA+BC;AA5BC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qDACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;mDACN;AAKX;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,+BAAa,GAAE;;4DACI;AAIpB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;2DACQ;AAInB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,8BAAY,GAAE;8BACN,IAAI;wDAAC;AAMd;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,OAAO,CAAC;8BACX,OAAO;wDAAC;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6DACU", "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport {\r\n  IsDateString,\r\n  IsDefined,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsPhoneNumber,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\n\r\nclass Address {\r\n  @IsDefined()\r\n  @IsString()\r\n  zipCode: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  neighborhood: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  city: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  complement: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  number: string;\r\n}\r\n\r\nexport class DirectAddPreRegisterDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  rg: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsPhoneNumber()\r\n  phoneNumber: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  motherName: string;\r\n\r\n  @IsDefined()\r\n  @IsDateString()\r\n  dtBirth: Date;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Address)\r\n  address: Address;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  observations: string;\r\n}\r\n"]}