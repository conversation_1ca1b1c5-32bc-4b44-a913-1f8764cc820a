{"version": 3, "file": "transaction-pix.controller.js", "sourceRoot": "/", "sources": ["modules/pix/controllers/transaction-pix.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAawB;AACxB,0EAAgE;AAGhE,8FAAwF;AACxF,kFAAyE;AACzE,8EAAyE;AACzE,8DAAyD;AACzD,4EAAsE;AACtE,kFAA4E;AAC5E,0GAAmG;AACnG,0FAAqF;AACrF,0EAAqE;AACrE,wFAAkF;AAClF,8FAAwF;AACxF,8FAAwF;AAGjF,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAEmB,wBAAkD,EAElD,2BAAwD,EAExD,2BAAwD,EAExD,gCAAkE,EAElE,0BAAsD,EAEtD,kBAAsC;QAVtC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAElD,gCAA2B,GAA3B,2BAA2B,CAA6B;QAExD,gCAA2B,GAA3B,2BAA2B,CAA6B;QAExD,qCAAgC,GAAhC,gCAAgC,CAAkC;QAElE,+BAA0B,GAA1B,0BAA0B,CAA4B;QAEtD,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAIE,AAAN,KAAK,CAAC,iBAAiB,CAErB,OAAqB,EAErB,IAA0B,EACF,cAAsB;QAE9C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CACtD,IAAI,EACJ,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,KAAK,EACL,cAAc,CACf,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,oBAAoB,CAExB,OAAqB,EAErB,IAA6B,EACL,cAAsB;QAE9C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CACzD,IAAI,EACJ,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,cAAc,CACf,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,iBAAiB,CAErB,OAAqB,EAErB,KAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CACzD,KAAK,EACL,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,yBAAyB,CAE7B,OAAqB,EAErB,KAAoC;QAEpC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAC9D,KAAK,EACL,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,mBAAmB,CACZ,OAAqB,EACxB,IAA4B;QAEpC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,WAAW,CAEf,IAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAhJY,4DAAwB;AAkB7B;IAHL,IAAA,aAAI,EAAC,KAAK,CAAC;IACX,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,EAAC,aAAa,CAAC,CAAA;;6CADjB,8CAAoB;;iEAiB3B;AAKK;IAHL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,EAAC,aAAa,CAAC,CAAA;;6CADjB,oDAAuB;;oEAgB9B;AAKK;IAHL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,cAAK,GAAE,CAAA;;6CACD,iDAAoB;;iEAc5B;AAKK;IAHL,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,iBAAQ,EAAC,mBAAU,CAAC,EAAE,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IAET,WAAA,IAAA,cAAK,GAAE,CAAA;;6CACD,gEAA6B;;yEAcrC;AAIK;IAFL,IAAA,aAAI,EAAC,UAAU,CAAC;IAChB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAO,iDAAsB;;mEAWrC;AAGK;IADL,IAAA,aAAI,EAAC,UAAU,CAAC;IAEd,WAAA,IAAA,aAAI,GAAE,CAAA;;qCACD,iCAAc;;2DAWrB;mCA/IU,wBAAwB;IADpC,IAAA,mBAAU,EAAC,iBAAiB,CAAC;IAGzB,WAAA,IAAA,eAAM,EAAC,sDAAwB,CAAC,CAAA;IAEhC,WAAA,IAAA,eAAM,EAAC,4DAA2B,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,4DAA2B,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,uEAAgC,CAAC,CAAA;IAExC,WAAA,IAAA,eAAM,EAAC,yDAA0B,CAAC,CAAA;IAElC,WAAA,IAAA,eAAM,EAAC,yCAAkB,CAAC,CAAA;qCATgB,sDAAwB;QAErB,4DAA2B;QAE3B,4DAA2B;QAEtB,uEAAgC;QAEtC,yDAA0B;QAElC,yCAAkB;GAb9C,wBAAwB,CAgJpC", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  HttpCode,\r\n  HttpException,\r\n  HttpStatus,\r\n  Inject,\r\n  Post,\r\n  UseGuards,\r\n  Request,\r\n  Query,\r\n  Get,\r\n  Headers,\r\n} from '@nestjs/common';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport { GetInternalPixTransactionlDto } from '../dto/get-internal-pix-transaction.dto';\r\nimport { TransactionStatusDto } from '../dto/get-transaction-status.dto';\r\nimport { InternalTransactionDto } from '../dto/internal-transaction.dto';\r\nimport { ReversalPixDto } from '../dto/reversal-pix.dto';\r\nimport { TransactionPixKeyDto } from '../dto/transaction-pix-key.dto';\r\nimport { TransactionPixManualDto } from '../dto/transaction-pix-manual.dto';\r\nimport { GetInternalPixTransactionService } from '../service/get-internal-pix-transaction.service';\r\nimport { InternalTransactionService } from '../service/internal-transaction.service';\r\nimport { ReversalPixService } from '../service/reversal-pix.service';\r\nimport { TransactionPixKeyService } from '../service/transaction-pix-key.service';\r\nimport { TransactionPixManualService } from '../service/transaction-pix-manual.service';\r\nimport { TransactionPixStatusService } from '../service/transaction-pix-status.service';\r\n\r\n@Controller('pix/transaction')\r\nexport class TransactionPixController {\r\n  constructor(\r\n    @Inject(TransactionPixKeyService)\r\n    private readonly transactionPixKeyService: TransactionPixKeyService,\r\n    @Inject(TransactionPixManualService)\r\n    private readonly transactionPixManualService: TransactionPixManualService,\r\n    @Inject(TransactionPixStatusService)\r\n    private readonly transactionPixStatusService: TransactionPixStatusService,\r\n    @Inject(GetInternalPixTransactionService)\r\n    private readonly getInternalPixTransactionService: GetInternalPixTransactionService,\r\n    @Inject(InternalTransactionService)\r\n    private readonly internalTransactionService: InternalTransactionService,\r\n    @Inject(ReversalPixService)\r\n    private readonly reversalPixService: ReversalPixService,\r\n  ) {}\r\n  @Post('key')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async transactionPixKey(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Body()\r\n    body: TransactionPixKeyDto,\r\n    @Headers('X-OTP-Token') twoFactorToken: string,\r\n  ) {\r\n    try {\r\n      const data = await this.transactionPixKeyService.perform(\r\n        body,\r\n        request.user.id,\r\n        false,\r\n        twoFactorToken,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('manual')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async transactionPixManual(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Body()\r\n    body: TransactionPixManualDto,\r\n    @Headers('X-OTP-Token') twoFactorToken: string,\r\n  ) {\r\n    try {\r\n      const data = await this.transactionPixManualService.perform(\r\n        body,\r\n        request.user.id,\r\n        twoFactorToken,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('status')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async transactionStatus(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Query()\r\n    query: TransactionStatusDto,\r\n  ) {\r\n    try {\r\n      const data = await this.transactionPixStatusService.execute(\r\n        query,\r\n        request.user.id,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('internal')\r\n  @HttpCode(HttpStatus.OK)\r\n  @UseGuards(JwtAuthGuard)\r\n  async getInternalPixTransaction(\r\n    @Request()\r\n    request: IRequestUser,\r\n    @Query()\r\n    query: GetInternalPixTransactionlDto,\r\n  ) {\r\n    try {\r\n      const data = await this.getInternalPixTransactionService.execute(\r\n        query,\r\n        request.user.id,\r\n      );\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('internal')\r\n  @UseGuards(JwtAuthGuard)\r\n  async internalTransaction(\r\n    @Request() request: IRequestUser,\r\n    @Body() data: InternalTransactionDto,\r\n  ) {\r\n    try {\r\n      const ownerId = request.user.id;\r\n      return this.internalTransactionService.perform(data, ownerId);\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Post('reversal')\r\n  async reversalPix(\r\n    @Body()\r\n    body: ReversalPixDto,\r\n  ) {\r\n    try {\r\n      const data = await this.reversalPixService.perform(body);\r\n      return data;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}