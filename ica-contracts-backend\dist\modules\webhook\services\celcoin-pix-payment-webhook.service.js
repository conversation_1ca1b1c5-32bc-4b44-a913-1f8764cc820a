"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CelcoinPixPaymentWebhookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const transaction_movement_type_enum_1 = require("../../../shared/enums/transaction-movement-type.enum");
const transaction_status_enum_1 = require("../../../shared/enums/transaction-status-enum");
const logger_1 = require("../../../shared/logger");
const typeorm_2 = require("typeorm");
let CelcoinPixPaymentWebhookService = class CelcoinPixPaymentWebhookService {
    constructor(accountRepository, transactionRepository) {
        this.accountRepository = accountRepository;
        this.transactionRepository = transactionRepository;
    }
    async perform(input) {
        const data = input.body;
        logger_1.logger.info('dados webhook cashin', input);
        const account = await this.accountRepository.findOne({
            where: {
                number: data.creditParty.account.toString(),
            },
        });
        if (!account) {
            return;
        }
        await this.transactionRepository.save({
            accountId: account.id,
            endToEndId: data.endToEndId,
            code: data.id,
            description: 'RECEIVE_PIX',
            status: transaction_status_enum_1.TransactionStatusEnum.DONE,
            type: transaction_movement_type_enum_1.TransactionMovementTypeEnum.PIX_CASHIN,
            value: data.amount.toString(),
            destinyAccount: data.debitParty.account.toString(),
            destinyAccountType: data.debitParty.accountType,
            destinyBank: data.debitParty.bank.toString(),
            destinyBranch: data.debitParty.branch.toString(),
            destinyDocument: data.debitParty.taxId.toString(),
            destinyName: data.debitParty.name,
            transferMetadata: JSON.stringify(data),
            transferDate: input.createTimestamp,
        });
    }
};
exports.CelcoinPixPaymentWebhookService = CelcoinPixPaymentWebhookService;
exports.CelcoinPixPaymentWebhookService = CelcoinPixPaymentWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], CelcoinPixPaymentWebhookService);
//# sourceMappingURL=celcoin-pix-payment-webhook.service.js.map