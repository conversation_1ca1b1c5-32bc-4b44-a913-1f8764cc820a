import { ClaimCelcoinService } from 'src/apis/celcoin/services/claim-celcoin.service';
import { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';
import { Repository } from 'typeorm';
import { ClaimGetDto } from '../dto/claim-get.dto';
export declare class ClaimGetPixKeyService {
    private pixKeyRepository;
    private pixCelcoinService;
    constructor(pixKeyRepository: Repository<PixKeyEntity>, pixCelcoinService: ClaimCelcoinService);
    execute(input: ClaimGetDto, ownerId: string): Promise<import("../../../apis/celcoin/responses/get-claim-pix-celcoin.response").IGetPixClaimResponse>;
}
