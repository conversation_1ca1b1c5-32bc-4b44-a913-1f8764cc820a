"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChargeCreateCelcoinWebhookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const billet_entity_1 = require("../../../shared/database/typeorm/entities/billet.entity");
const boleto_status_enum_1 = require("../../../shared/enums/boleto-status.enum");
const logger_1 = require("../../../shared/logger");
const typeorm_2 = require("typeorm");
let ChargeCreateCelcoinWebhookService = class ChargeCreateCelcoinWebhookService {
    constructor(billetEntityRepository) {
        this.billetEntityRepository = billetEntityRepository;
    }
    async perform(input) {
        logger_1.logger.info('ChargeCreateCelcoinWebhookService.perform() -> charge-create body', input);
        const { status } = input;
        if (status === 'CREATED') {
            const boleto = await this.billetEntityRepository.findOne({
                where: {
                    transactionId: input.body.transactionId,
                    status: boleto_status_enum_1.BoletoStatusEnum.PROCESSING,
                },
            });
            logger_1.logger.info('ChargeCreateCelcoinWebhookService.perform() -> charge-create: boleto', boleto);
            if (boleto) {
                await this.billetEntityRepository.update({ id: boleto.id }, {
                    status: boleto_status_enum_1.BoletoStatusEnum.ACTIVE,
                    barcode: input.body.boleto.barCode,
                    metadata: JSON.stringify(input.body.boleto),
                });
            }
        }
    }
};
exports.ChargeCreateCelcoinWebhookService = ChargeCreateCelcoinWebhookService;
exports.ChargeCreateCelcoinWebhookService = ChargeCreateCelcoinWebhookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(billet_entity_1.BilletEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ChargeCreateCelcoinWebhookService);
//# sourceMappingURL=charge-create-celcoin-webhook.service.js.map