import type { Readable } from 'node:stream'
import type { IStorageGateway } from '@/domain/gateways/storage.gateway'
import { type Either, left, right } from '@/domain/shared'
import {
  DeleteObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3'
import env from '../config/env'

export class AwsStorageGateway implements IStorageGateway {
  private s3: S3Client
  private bucketName: string

  constructor(bucketName: string, region: string) {
    this.s3 = new S3Client({
      region,
      credentials: {
        accessKeyId: env.AWS_ACCESS_KEY,
        secretAccessKey: env.AWS_SECRET_KEY,
      },
    })
    this.bucketName = bucketName
  }

  async uploadFile(
    path: string,
    content: Buffer,
    contentType?: string
  ): Promise<Either<Error, string>> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: path,
        Body: content,
        ContentType: contentType || 'application/octet-stream',
        ACL: 'public-read',
      })

      await this.s3.send(command)
      const region = await this.s3.config.region()
      return right(
        `https://${this.bucketName}.s3.${region}.amazonaws.com/${path}`
      )
    } catch (error) {
      return left(error as Error)
    }
  }

  async downloadFile(path: string): Promise<Buffer> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: path,
    })

    const response = await this.s3.send(command)

    if (!response.Body) {
      throw new Error('Arquivo não encontrado')
    }

    return this.streamToBuffer(response.Body as Readable)
  }

  async deleteFile(path: string): Promise<boolean> {
    const command = new DeleteObjectCommand({
      Bucket: this.bucketName,
      Key: path,
    })

    await this.s3.send(command)

    return true
  }

  private async streamToBuffer(stream: Readable): Promise<Buffer> {
    const chunks: Uint8Array[] = []
    for await (const chunk of stream) {
      chunks.push(chunk)
    }
    return Buffer.concat(chunks)
  }
}
