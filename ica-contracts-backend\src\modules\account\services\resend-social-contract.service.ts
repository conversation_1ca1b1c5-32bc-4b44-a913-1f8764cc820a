import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { S3 } from 'aws-sdk';
import { KYCCelcoinService } from 'src/apis/celcoin/services/kyc-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { DocumentEntity } from 'src/shared/database/typeorm/entities/document.entity';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { Repository, Equal } from 'typeorm';

import { ResendSocialContractDto } from '../dto/resend-social-contract.dto';

@Injectable()
export class ResendSocialContractService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(DocumentEntity)
    private documentDb: Repository<DocumentEntity>,
    @Inject(KYCCelcoinService)
    private apiCelcoin: KYCCelcoinService,
  ) {}
  async perform(data: ResendSocialContractDto, id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    if (
      account.type !== 'business' ||
      (!data.socialContract && !data.cardCnpj)
    ) {
      throw new BadRequestException(
        'Contrato Social ou Cartao CNPJ obrigatório para contas PJ',
      );
    }

    const actualDocs = await this.documentDb.findOne({
      where: {
        accountId: account.id,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    const filename = `${account.id}-${new Date()}`;
    const s3 = new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });

    let createDoc = this.documentDb.create({
      accountId: account.id,
      back: actualDocs.back,
      front: actualDocs.front,
      sent: true,
    });
    if (data.socialContract) {
      await this.apiCelcoin.sendDocuments({
        documentnumber: account.business.cnpj,
        filetype: 'CONTRATO_SOCIAL',
        front: data.socialContract.buffer,
        cnpj: account.business.cnpj,
      });
      const saveSocialContract = await s3
        .upload({
          Bucket: process.env.S3_BUCKET,
          Key: `${filename}-CONTRATO_SOCIAL.pdf`,
          Body: data.socialContract.buffer,
          ContentType: 'application/pdf',
          ACL: 'public-read',
        })
        .promise();
      createDoc = this.documentDb.create({
        ...createDoc,
        socialContract: saveSocialContract.Key,
      });
    }
    if (data.cardCnpj) {
      await this.apiCelcoin.sendDocuments({
        documentnumber: account.business.cnpj,
        filetype: 'CARTAO_CNPJ',
        front: data.cardCnpj.buffer,
        cnpj: account.business.cnpj,
      });

      const saveCard = await s3
        .upload({
          Bucket: process.env.S3_BUCKET,
          Key: `${filename}-CARTAO_CNPJ.pdf`,
          Body: data.cardCnpj.buffer,
          ContentType: 'application/pdf',
          ACL: 'public-read',
        })
        .promise();

      createDoc = this.documentDb.create({
        ...createDoc,
        cardCNPJ: saveCard.Key,
      });
    }

    await this.documentDb.save(createDoc);
    await this.accountDb.update(account.id, {
      status: AccountStatusEnum.PENDING,
    });
  }
}
