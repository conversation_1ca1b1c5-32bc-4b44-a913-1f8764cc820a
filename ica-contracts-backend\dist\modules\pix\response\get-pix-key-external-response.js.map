{"version": 3, "file": "get-pix-key-external-response.js", "sourceRoot": "/", "sources": ["modules/pix/response/get-pix-key-external-response.ts"], "names": [], "mappings": "", "sourcesContent": ["import { AccountTypeEnum } from 'src/shared/enums/account-type.enum';\r\nimport { KeyTypeEnum } from 'src/shared/enums/key-type.enum';\r\n\r\nexport interface IGetPixKeyExternalResponse {\r\n  keyType: KeyTypeEnum;\r\n  key: string;\r\n  endToEndId: string;\r\n  account: {\r\n    branch: string;\r\n    number: string;\r\n    type: string;\r\n    bank: string;\r\n  };\r\n  owner: {\r\n    name: string;\r\n    document: string;\r\n    type: AccountTypeEnum;\r\n  };\r\n}\r\n"]}