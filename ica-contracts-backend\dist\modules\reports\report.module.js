"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportModule = void 0;
const common_1 = require("@nestjs/common");
const apis_module_1 = require("../../apis/apis.module");
const shared_module_1 = require("../../shared/shared.module");
const get_acquisition_per_period_service_1 = require("../acquisition/services/get-acquisition-per-period.service");
const reports_controller_1 = require("./controllers/reports.controller");
const generate_report_dto_1 = require("./dto/generate-report.dto");
const report_registry_service_1 = require("./services/report-registry.service");
const report_service_1 = require("./services/report.service");
const acquisition_reports_strategy_1 = require("./services/strategies/acquisition-reports.strategy");
const payment_reports_strategy_1 = require("./services/strategies/payment-reports.strategy");
const scheduled_payments_reports_strategy_1 = require("./services/strategies/scheduled-payments-reports.strategy");
let ReportModule = class ReportModule {
    constructor(registry, acquisitionReportStrategy, scheduledPaymentsReportsStrategy, paymentReportsStrategy) {
        this.registry = registry;
        this.acquisitionReportStrategy = acquisitionReportStrategy;
        this.scheduledPaymentsReportsStrategy = scheduledPaymentsReportsStrategy;
        this.paymentReportsStrategy = paymentReportsStrategy;
    }
    onModuleInit() {
        this.registry.register(generate_report_dto_1.ReportTypeEnum.acquisition, this.acquisitionReportStrategy);
        this.registry.register(generate_report_dto_1.ReportTypeEnum.scheduledPayment, this.scheduledPaymentsReportsStrategy);
        this.registry.register(generate_report_dto_1.ReportTypeEnum.paid, this.paymentReportsStrategy);
    }
};
exports.ReportModule = ReportModule;
exports.ReportModule = ReportModule = __decorate([
    (0, common_1.Module)({
        imports: [shared_module_1.SharedModule, apis_module_1.ApisModule],
        controllers: [reports_controller_1.ReportsController],
        providers: [
            report_registry_service_1.ReportRegistryService,
            report_service_1.ReportService,
            acquisition_reports_strategy_1.AcquisitionReportStrategy,
            get_acquisition_per_period_service_1.GetAcquisitionPerPeriodService,
            scheduled_payments_reports_strategy_1.ScheduledPaymentsReportsStrategy,
            payment_reports_strategy_1.PaymentReportsStrategy,
        ],
        exports: [report_service_1.ReportService],
    }),
    __metadata("design:paramtypes", [report_registry_service_1.ReportRegistryService,
        acquisition_reports_strategy_1.AcquisitionReportStrategy,
        scheduled_payments_reports_strategy_1.ScheduledPaymentsReportsStrategy,
        payment_reports_strategy_1.PaymentReportsStrategy])
], ReportModule);
//# sourceMappingURL=report.module.js.map