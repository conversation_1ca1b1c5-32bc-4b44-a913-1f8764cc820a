import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { ClaimCancelDto } from '../dto/claim-cancel.dto';
import { ClaimConfirmDto } from '../dto/claim-confirm.dto';
import { ClaimGetDto } from '../dto/claim-get.dto';
import { CreatePixDto } from '../dto/create-pix-key.dto';
import { CreatePixQRCodeDynamicDto } from '../dto/create-pix-qrcode-dynamic.dto';
import { CreatePixQRCodeStaticDto } from '../dto/create-pix-qrcode-static.dto';
import { DeletePixKeyDto } from '../dto/delete-pix-key.dto';
import { GetPixKeyExternalDto } from '../dto/get-pix-key-external.dto';
import { GetPixParticipantsDto } from '../dto/get-pix-participants.dto';
import { ReadQRCodePixDto } from '../dto/read-qrcode-pix.dto';
import { RequestClaimPixKeyInputDTO } from '../dto/request-claim-pix-key.dto';
import { TransactionPixKeyDto } from '../dto/transaction-pix-key.dto';
import { ClaimCancelPixKeyService } from '../service/claim-cancel-pix-key.service';
import { ClaimConfirmPixKeyService } from '../service/claim-confim-pix-key.service';
import { ClaimGetPixKeyService } from '../service/claim-get-pix-key.service';
import { CreatePixKeyService } from '../service/create-pix-key.service';
import { CreatePixQRCodeDynamicService } from '../service/create-pix-qrcode-dynamic.service';
import { CreatePixQRCodeStaticService } from '../service/create-pix-qrcode-static.service';
import { DeletePixKeyService } from '../service/delete-pix-key.service';
import { GetPixKeyExternalService } from '../service/get-pix-key-external.service';
import { GetPixKeyService } from '../service/get-pix-key.service';
import { GetPixParticipantsService } from '../service/get-pix-participants.service';
import { ReadQRCodePixService } from '../service/read-qrcode-pix.service';
import { RequestClaimPixKeyService } from '../service/request-claim-pix-key.service';
import { TransactionPixKeyService } from '../service/transaction-pix-key.service';
export declare class PixController {
    private readonly createPixKeyService;
    private readonly getPixKeyService;
    private readonly getPixKeyExternalService;
    private readonly deletePixKeyService;
    private readonly getPixParticipantsService;
    private readonly createQRCodeStaticService;
    private readonly createQRCodeDynamicService;
    private readonly requestClaimPixKeyService;
    private readonly claimConfirmPixKeyService;
    private readonly readQrCodeService;
    private readonly claimCancelPixKeyService;
    private readonly claimGetPixKeyService;
    private readonly transactionPixKeyService;
    constructor(createPixKeyService: CreatePixKeyService, getPixKeyService: GetPixKeyService, getPixKeyExternalService: GetPixKeyExternalService, deletePixKeyService: DeletePixKeyService, getPixParticipantsService: GetPixParticipantsService, createQRCodeStaticService: CreatePixQRCodeStaticService, createQRCodeDynamicService: CreatePixQRCodeDynamicService, requestClaimPixKeyService: RequestClaimPixKeyService, claimConfirmPixKeyService: ClaimConfirmPixKeyService, readQrCodeService: ReadQRCodePixService, claimCancelPixKeyService: ClaimCancelPixKeyService, claimGetPixKeyService: ClaimGetPixKeyService, transactionPixKeyService: TransactionPixKeyService);
    createPixKey(body: CreatePixDto, request: IRequestUser): Promise<import("../../../shared/database/typeorm/entities/pix-key.entity").PixKeyEntity>;
    getPixKeys(request: IRequestUser): Promise<import("../response/get-pix-key-response").IGetPixKeyReponse[]>;
    getPixKeyExternal(request: IRequestUser, query: GetPixKeyExternalDto): Promise<import("../response/get-pix-key-external-response").IGetPixKeyExternalResponse>;
    deletePixKey(query: DeletePixKeyDto, request: IRequestUser): Promise<void>;
    getPixParticipants(request: IRequestUser, query: GetPixParticipantsDto): Promise<any>;
    createQRStatic(request: IRequestUser, body: CreatePixQRCodeStaticDto): Promise<import("../response/create-pix-qrcode-static.response").ICreatePixQRCodeStaticResponse>;
    createQRDynamic(request: IRequestUser, body: CreatePixQRCodeDynamicDto): Promise<import("../response/create-pix-qrcode-dynamic.response").ICreatePixQRCodeDynamicResponse>;
    requestClaim(request: IRequestUser, body: RequestClaimPixKeyInputDTO): Promise<{
        reason: string;
    }>;
    ConfirmClaim(request: IRequestUser, input: ClaimConfirmDto): Promise<{
        reason: string;
    }>;
    readQRCode(body: ReadQRCodePixDto): Promise<import("../../../apis/celcoin/responses/read-qrcode-celcoin.response").IReadQrCodeCelcoinResponse>;
    CancelClaim(request: IRequestUser, input: ClaimCancelDto): Promise<{
        reason: string;
    }>;
    getClaim(request: IRequestUser, input: ClaimGetDto): Promise<import("../../../apis/celcoin/responses/get-claim-pix-celcoin.response").IGetPixClaimResponse>;
    transactionPixKey(request: IRequestUser, body: TransactionPixKeyDto, twoFactorToken: string): Promise<import("../response/transaction-pix-key-response").ITransactionPixKeyResponse>;
}
