{"version": 3, "file": "internal-transaction.response.js", "sourceRoot": "/", "sources": ["modules/pix/response/internal-transaction.response.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IInternalTransactionResponse {\r\n  id: string;\r\n  creditParty: {\r\n    account: string;\r\n    name: string;\r\n    document: string;\r\n  };\r\n  debitParty: {\r\n    account: string;\r\n    name: string;\r\n    document: string;\r\n  };\r\n}\r\n"]}