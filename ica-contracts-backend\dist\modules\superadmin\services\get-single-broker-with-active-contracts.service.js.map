{"version": 3, "file": "get-single-broker-with-active-contracts.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/get-single-broker-with-active-contracts.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,yDAAsD;AACtD,2CAAoD;AACpD,6CAAmD;AAEnD,+FAAsF;AACtF,qCAA6E;AAI7E,kEAAmE;AACnE,+FAAsF;AACtF,qFAA2E;AAGpE,IAAM,yCAAyC,GAA/C,MAAM,yCAAyC;IACpD,YAEU,WAAuC,EAChB,YAAmB;QAD1C,gBAAW,GAAX,WAAW,CAA4B;QAChB,iBAAY,GAAZ,YAAY,CAAO;IACjD,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,QAAgB,EAChB,KAAiD;QAEjD,MAAM,QAAQ,GAAG,2BAA2B,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;QAEhF,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAEzB,QAAQ,CAAC,CAAC;QAEd,IAAI,MAAM;YAAE,OAAO,MAAM,CAAC;QAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GACzB,uCAAoB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,OAAO,GAA4B;YACvC,WAAW,EAAE,IAAA,yBAAe,EAAC,IAAI,IAAI,EAAE,CAAC;YACxC,MAAM,EAAE,yCAAkB,CAAC,MAAM;SAClC,CAAC;QAEF,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,uCAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEtE,OAAO,CAAC,aAAa,GAAG,IAAA,iBAAO,EAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;aAChC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,SAAS,CAAC,4BAA4B,EAAE,KAAK,CAAC;aAC9C,SAAS,CAAC,mBAAmB,EAAE,UAAU,CAAC;aAC1C,QAAQ,CAAC,gBAAgB,EAAE,eAAe,CAAC;aAC3C,QAAQ,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;aACjD,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC,6BAA6B,EAAE,OAAO,CAAC;aAC9C,KAAK,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,CAAC;aACzC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAErB,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,UAAU,CAAC,QAAQ,CAAC,gDAAgD,EAAE;gBACpE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,CAAC,SAAS,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE5B,MAAM,0BAA0B,GAAG,MAAM,IAAI,CAAC,WAAW;aACtD,kBAAkB,CAAC,UAAU,CAAC;aAC9B,SAAS,CAAC,4BAA4B,EAAE,KAAK,CAAC;aAC9C,SAAS,CAAC,mBAAmB,EAAE,UAAU,CAAC;aAC1C,QAAQ,CAAC,gBAAgB,EAAE,eAAe,CAAC;aAC3C,QAAQ,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;aACjD,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC;YACN,oBAAoB;YACpB,2BAA2B;YAC3B,oEAAoE;YACpE,mEAAmE;YACnE,iEAAiE;YACjE,iCAAiC;YACjC,yGAAyG;SAC1G,CAAC;aACD,KAAK,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,CAAC;aACzC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAErB,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,0BAA0B,CAAC,QAAQ,CACjC,gDAAgD,EAChD;gBACE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CACF,CAAC;QACJ,CAAC;QAED,MAAM,gCAAgC,GAAG,MAAM,0BAA0B;aACtE,OAAO,CACN,oLAAoL,CACrL;aACA,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B,MAAM,CAAC,IAAI,CAAC;aACZ,KAAK,CAAC,KAAK,CAAC;aACZ,UAAU,EAQP,CAAC;QAEP,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW;aACxC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,SAAS,CAAC,4BAA4B,EAAE,KAAK,CAAC;aAC9C,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,QAAQ,CAAC,mBAAmB,EAAE,UAAU,EAAE,2BAA2B,EAAE;YACtE,MAAM,EAAE,gCAAc,CAAC,YAAY;SACpC,CAAC;aACD,MAAM,CACL,kFAAkF,EAClF,OAAO,CACR;aACA,KAAK,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,CAAC;aACzC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAErB,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,kBAAkB,CAAC,QAAQ,CACzB,gDAAgD,EAChD;gBACE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CACF,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,kBAAkB,CAAC,SAAS,EAAE,CAAC;QAEtE,MAAM,MAAM,GAAG,gCAAgC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC/D,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,mBAAmB,EAAE,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC;YACvD,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC;SACrC,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,uCAAoB,CAAC,uBAAuB,CAC3D,MAAM,EACN,KAAK,EACL,IAAI,EACJ,KAAK,CACN,CAAC;QAEF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AA9JY,8FAAyC;oDAAzC,yCAAyC;IADrD,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,eAAM,EAAC,6BAAa,CAAC,CAAA;qCADD,oBAAU;GAHtB,yCAAyC,CA8JrD", "sourcesContent": ["import { CACHE_MANAGER } from '@nestjs/cache-manager';\r\nimport { Inject, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Cache } from 'cache-manager';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { Between, FindOperator, MoreThanOrEqual, Repository } from 'typeorm';\r\n\r\nimport { GetSingleBrokerWithActiveContractsQueryDto } from '../dto/get-single-broker-with-active-contracts/query.dto';\r\nimport { GetSinleBrokerWithActiveContractsResponseDto } from '../dto/get-single-broker-with-active-contracts/response.dto';\r\nimport { PaginatedQueryHelper } from '../helpers/pagination-query';\r\nimport { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\n\r\n@Injectable()\r\nexport class GetSingleBrokerWithActiveContractsService {\r\n  constructor(\r\n    @InjectRepository(ContractEntity)\r\n    private contractsDb: Repository<ContractEntity>,\r\n    @Inject(CACHE_MANAGER) private cacheManager: Cache,\r\n  ) {}\r\n\r\n  async perform(\r\n    brokerId: string,\r\n    query: GetSingleBrokerWithActiveContractsQueryDto,\r\n  ) {\r\n    const cacheKey = `active-broker-contracts-${brokerId}-${JSON.stringify(query)}`;\r\n\r\n    const cached =\r\n      await this.cacheManager.get<\r\n        GetSinleBrokerWithActiveContractsResponseDto[]\r\n      >(cacheKey);\r\n\r\n    if (cached) return cached;\r\n\r\n    const { page, limit, skip } =\r\n      PaginatedQueryHelper.getPaginationParams(query);\r\n\r\n    const filters: Record<string, unknown> = {\r\n      endContract: MoreThanOrEqual(new Date()),\r\n      status: ContractStatusEnum.ACTIVE,\r\n    };\r\n\r\n    if (query.dateTo || query.dateFrom) {\r\n      const { start, end } = PaginatedQueryHelper.getDateRangeParams(query);\r\n\r\n      filters.startContract = Between(start, end);\r\n    }\r\n\r\n    const totalQuery = this.contractsDb\r\n      .createQueryBuilder('contract')\r\n      .innerJoin('contract.ownerRoleRelation', 'orr')\r\n      .innerJoin('contract.investor', 'investor')\r\n      .leftJoin('investor.owner', 'investorowner')\r\n      .leftJoin('investor.business', 'investorbusiness')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select('COUNT(DISTINCT contract.id)', 'count')\r\n      .where('orr.id = :brokerId', { brokerId })\r\n      .andWhere(filters);\r\n\r\n    if (query.contractType) {\r\n      totalQuery.andWhere('signataries.investmentModality = :contractType', {\r\n        contractType: query.contractType,\r\n      });\r\n    }\r\n\r\n    const { count } = await totalQuery.getRawOne();\r\n    const total = Number(count);\r\n\r\n    const activeBrokerContractsQuery = await this.contractsDb\r\n      .createQueryBuilder('contract')\r\n      .innerJoin('contract.ownerRoleRelation', 'orr')\r\n      .innerJoin('contract.investor', 'investor')\r\n      .leftJoin('investor.owner', 'investorowner')\r\n      .leftJoin('investor.business', 'investorbusiness')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select([\r\n        'orr.id as brokerid',\r\n        'investor.id as investorid',\r\n        'COALESCE(investorowner.name, investorbusiness.companyName) as name',\r\n        'COALESCE(investorowner.avatar, investorbusiness.avatar) as avatar',\r\n        'COALESCE(investorowner.cpf , investorbusiness.cnpj) as document',\r\n        'contract.createdAt as createdat',\r\n        'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0)) as totalcontractamount',\r\n      ])\r\n      .where('orr.id = :brokerId', { brokerId })\r\n      .andWhere(filters);\r\n\r\n    if (query.contractType) {\r\n      activeBrokerContractsQuery.andWhere(\r\n        'signataries.investmentModality = :contractType',\r\n        {\r\n          contractType: query.contractType,\r\n        },\r\n      );\r\n    }\r\n\r\n    const activeBrokerContractsQueryResult = await activeBrokerContractsQuery\r\n      .groupBy(\r\n        'orr.id, investor.id, investorowner.name, investorbusiness.companyName, investorowner.avatar, investorbusiness.avatar, investorowner.cpf, investorbusiness.cnpj, contract.createdAt',\r\n      )\r\n      .orderBy('createdat', 'DESC')\r\n      .offset(skip)\r\n      .limit(limit)\r\n      .getRawMany<{\r\n        investorid: string;\r\n        brokerid: string;\r\n        name: string;\r\n        avatar: string;\r\n        document: string;\r\n        totalcontractamount: string;\r\n        createdat: Date;\r\n      }>();\r\n\r\n    const totalCapturedQuery = this.contractsDb\r\n      .createQueryBuilder('contract')\r\n      .innerJoin('contract.ownerRoleRelation', 'orr')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoin('contract.addendum', 'addendum', 'addendum.status = :status', {\r\n        status: AddendumStatus.FULLY_SIGNED,\r\n      })\r\n      .select(\r\n        'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0))',\r\n        'total',\r\n      )\r\n      .where('orr.id = :brokerId', { brokerId })\r\n      .andWhere(filters);\r\n\r\n    if (query.contractType) {\r\n      totalCapturedQuery.andWhere(\r\n        'signataries.investmentModality = :contractType',\r\n        {\r\n          contractType: query.contractType,\r\n        },\r\n      );\r\n    }\r\n\r\n    const { total: totalCaptured } = await totalCapturedQuery.getRawOne();\r\n\r\n    const result = activeBrokerContractsQueryResult.map((broker) => ({\r\n      investorId: broker.investorid,\r\n      brokerId: broker.brokerid,\r\n      name: broker.name,\r\n      avatar: broker.avatar,\r\n      document: broker.document,\r\n      createdAt: broker.createdat,\r\n      totalContractAmount: Number(broker.totalcontractamount),\r\n      totalCaptured: Number(totalCaptured),\r\n    }));\r\n\r\n    const response = PaginatedQueryHelper.createPaginatedResponse(\r\n      result,\r\n      total,\r\n      page,\r\n      limit,\r\n    );\r\n\r\n    await this.cacheManager.set(cacheKey, response, 60000);\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}