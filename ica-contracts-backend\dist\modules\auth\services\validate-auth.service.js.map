{"version": 3, "file": "validate-auth.service.js", "sourceRoot": "/", "sources": ["modules/auth/services/validate-auth.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,qCAAyC;AACzC,6CAAmD;AACnD,iCAAiC;AACjC,+FAAsF;AACtF,yFAAgF;AAChF,qCAAqC;AAa9B,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEU,OAAgC,EAEhC,UAAsC,EACtC,UAAsB;QAHtB,YAAO,GAAP,OAAO,CAAyB;QAEhC,eAAU,GAAV,UAAU,CAA4B;QACtC,eAAU,GAAV,UAAU,CAAY;IAC7B,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAc;QAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,IAAI,4BAAmB,CAC3B,6CAA6C,CAC9C,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEnD,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACvC,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,SAAS,CAAC;gBACrE,KAAK,EAAE;oBACL,GAAG,EAAE,IAAI,CAAC,QAAQ;iBACnB;aACF,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC;oBAClD,MAAM,IAAI,8BAAqB,EAAE,CAAC;gBACpC,CAAC;gBAED,MAAM,KAAK,GAAkB,KAAK,CAAC,iBAAiB,CAAC,GAAG,CACtD,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CACzB,CAAC;gBAEF,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAC5C,IAAI,CAAC,YAAY,EACjB,KAAK,CAAC,YAAY,CACnB,CAAC;oBACF,IAAI,iBAAiB,EAAE,CAAC;wBACtB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;4BAC/B,GAAG,EAAE,KAAK,CAAC,GAAG;4BACd,KAAK,EAAE,KAAK,CAAC,KAAK;4BAClB,EAAE,EAAE,KAAK,CAAC,EAAE;4BACZ,KAAK;yBACN,CAAC,CAAC;wBAGH,MAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;4BACjE,MAAM,EAAE,QAAQ,CAAC,EAAE;4BACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;4BAC5B,WAAW,EAAE,QAAQ,CAAC,WAAW;yBAClC,CAAC,CAAC,CAAC;wBAEJ,OAAO;4BACL,WAAW,EAAE,KAAK;4BAClB,IAAI,EAAE;gCACJ,EAAE,EAAE,KAAK,CAAC,EAAE;gCACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gCAChB,KAAK,EAAE,KAAK,CAAC,KAAK;gCAClB,QAAQ,EAAE,KAAK,CAAC,GAAG;gCACnB,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,iBAAiB;6BACzB;yBACF,CAAC;oBACJ,CAAC;oBACD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;gBACrE,CAAC;gBAED,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CACxC,IAAI,CAAC,QAAQ,EACb,KAAK,CAAC,QAAQ,CACf,CAAC;gBACF,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;wBAC/B,GAAG,EAAE,KAAK,CAAC,GAAG;wBACd,KAAK,EAAE,KAAK,CAAC,KAAK;wBAClB,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,KAAK;qBACN,CAAC,CAAC;oBACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC;wBACnD,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,QAAQ,EAAE,KAAK,CAAC,GAAG;wBACnB,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;qBACnB,CAAC,CAAC;oBAGH,MAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;wBACjE,MAAM,EAAE,QAAQ,CAAC,EAAE;wBACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;wBAC5B,WAAW,EAAE,QAAQ,CAAC,WAAW;qBAClC,CAAC,CAAC,CAAC;oBAEJ,OAAO;wBACL,WAAW,EAAE,KAAK;wBAClB,YAAY;wBACZ,IAAI,EAAE;4BACJ,EAAE,EAAE,KAAK,CAAC,EAAE;4BACZ,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,KAAK,EAAE,KAAK,CAAC,KAAK;4BAClB,QAAQ,EAAE,KAAK,CAAC,GAAG;4BACnB,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,iBAAiB;yBACzB;qBACF,CAAC;gBACJ,CAAC;gBAED,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC7C,SAAS,EAAE,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,SAAS,CAAC;gBACrE,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI,CAAC,QAAQ;iBACpB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC;oBACxD,MAAM,IAAI,8BAAqB,EAAE,CAAC;gBACpC,CAAC;gBAED,MAAM,KAAK,GAAkB,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CACzD,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CACzB,CAAC;gBAEF,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtB,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAC5C,IAAI,CAAC,YAAY,EACjB,QAAQ,CAAC,YAAY,CACtB,CAAC;oBACF,IAAI,iBAAiB,EAAE,CAAC;wBACtB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;4BAC/B,GAAG,EAAE,QAAQ,CAAC,IAAI;4BAClB,KAAK,EAAE,QAAQ,CAAC,KAAK;4BACrB,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,KAAK;yBACN,CAAC,CAAC;wBAGH,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;4BACpE,MAAM,EAAE,QAAQ,CAAC,EAAE;4BACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;4BAC5B,WAAW,EAAE,QAAQ,CAAC,WAAW;yBAClC,CAAC,CAAC,CAAC;wBAEJ,OAAO;4BACL,WAAW,EAAE,KAAK;4BAClB,IAAI,EAAE;gCACJ,EAAE,EAAE,QAAQ,CAAC,EAAE;gCACf,IAAI,EAAE,QAAQ,CAAC,WAAW;gCAC1B,KAAK,EAAE,QAAQ,CAAC,KAAK;gCACrB,QAAQ,EAAE,QAAQ,CAAC,IAAI;gCACvB,IAAI,EAAE,UAAU;gCAChB,KAAK,EAAE,iBAAiB;6BACzB;yBACF,CAAC;oBACJ,CAAC;oBACD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;gBACrE,CAAC;gBAED,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CACxC,IAAI,CAAC,QAAQ,EACb,QAAQ,CAAC,QAAQ,CAClB,CAAC;gBACF,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;wBAC/B,GAAG,EAAE,QAAQ,CAAC,IAAI;wBAClB,KAAK,EAAE,QAAQ,CAAC,KAAK;wBACrB,EAAE,EAAE,QAAQ,CAAC,EAAE;wBACf,KAAK;qBACN,CAAC,CAAC;oBACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC;wBACnD,EAAE,EAAE,QAAQ,CAAC,EAAE;wBACf,QAAQ,EAAE,QAAQ,CAAC,IAAI;wBACvB,IAAI,EAAE,QAAQ,CAAC,WAAW;wBAC1B,KAAK,EAAE,QAAQ,CAAC,KAAK;qBACtB,CAAC,CAAC;oBAGH,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;wBACpE,MAAM,EAAE,QAAQ,CAAC,EAAE;wBACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;wBAC5B,WAAW,EAAE,QAAQ,CAAC,WAAW;qBAClC,CAAC,CAAC,CAAC;oBAEJ,OAAO;wBACL,WAAW,EAAE,KAAK;wBAClB,YAAY;wBACZ,IAAI,EAAE;4BACJ,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,IAAI,EAAE,QAAQ,CAAC,WAAW;4BAC1B,KAAK,EAAE,QAAQ,CAAC,KAAK;4BACrB,QAAQ,EAAE,QAAQ,CAAC,IAAI;4BACvB,IAAI,EAAE,UAAU;4BAChB,KAAK,EAAE,iBAAiB;yBACzB;qBACF,CAAC;gBACJ,CAAC;gBAED,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,QAAgB;QAChC,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,IAA2B;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE;YACvC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;YAC9B,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAS;QAC1C,MAAM,OAAO,GAAG;YACd,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE;YAC1C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;YAC9B,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEhD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;gBACjC,YAAY,EAAE,UAAU;aACzB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;gBACpC,YAAY,EAAE,UAAU;aACzB,CAAC,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAc;QAE9B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YACvC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,SAAS,EAAE;gBACT,iBAAiB,EAAE;oBACjB,IAAI,EAAE,IAAI;iBACX;aACF;SACF,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,iBAAiB,GAAG,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACjE,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;gBAC5B,WAAW,EAAE,QAAQ,CAAC,WAAW;aAClC,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,QAAQ,EAAE,KAAK,CAAC,GAAG;gBACnB,IAAI,EAAE,OAAgB;gBACtB,KAAK,EAAE,iBAAiB;aACzB,CAAC;QACJ,CAAC;QAGD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,SAAS,EAAE;gBACT,iBAAiB,EAAE;oBACjB,IAAI,EAAE,IAAI;iBACX;aACF;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACpE,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;gBAC5B,WAAW,EAAE,QAAQ,CAAC,WAAW;aAClC,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,WAAW;gBAC1B,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,IAAI,EAAE,UAAmB;gBACzB,KAAK,EAAE,iBAAiB;aACzB,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;IAC1D,CAAC;CACF,CAAA;AAzTY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCADhB,oBAAU;QAEP,oBAAU;QACV,gBAAU;GANrB,mBAAmB,CAyT/B", "sourcesContent": ["import {\r\n  BadRequestException,\r\n  Injectable,\r\n  UnauthorizedException,\r\n} from '@nestjs/common';\r\nimport { JwtService } from '@nestjs/jwt';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport * as bcrypt from 'bcrypt';\r\nimport { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { LoginDto } from '../dto/login.dto';\r\nimport { IAuthResponse } from '../interfaces/auth-response.interface';\r\n\r\ninterface IGenerateTokenPayload {\r\n  id: string;\r\n  cpf: string;\r\n  email: string;\r\n  roles: Array<string>;\r\n}\r\n\r\n@Injectable()\r\nexport class ValidateAuthService {\r\n  constructor(\r\n    @InjectRepository(OwnerEntity)\r\n    private ownerDb: Repository<OwnerEntity>,\r\n    @InjectRepository(BusinessEntity)\r\n    private businessDb: Repository<BusinessEntity>,\r\n    private jwtService: JwtService,\r\n  ) {}\r\n\r\n  async perform(data: LoginDto): Promise<IAuthResponse> {\r\n    if (!data.password && !data.refreshToken) {\r\n      throw new BadRequestException(\r\n        'Password ou RefreshToken deve ser fornecido',\r\n      );\r\n    }\r\n\r\n    const typeDocument = this.cpfOrCnpj(data.document);\r\n\r\n    if (typeDocument === 'cpf') {\r\n      const owner = await this.ownerDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],\r\n        where: {\r\n          cpf: data.document,\r\n        },\r\n      });\r\n\r\n      if (owner) {\r\n        if (owner.account && owner.account[0]?.isExternal) {\r\n          throw new UnauthorizedException();\r\n        }\r\n\r\n        const roles: Array<string> = owner.ownerRoleRelation.map(\r\n          (role) => role.role.name,\r\n        );\r\n\r\n        if (data.refreshToken) {\r\n          const matchRefreshToken = await bcrypt.compare(\r\n            data.refreshToken,\r\n            owner.refreshToken,\r\n          );\r\n          if (matchRefreshToken) {\r\n            const token = this.generateToken({\r\n              cpf: owner.cpf,\r\n              email: owner.email,\r\n              id: owner.id,\r\n              roles,\r\n            });\r\n\r\n            // Buscar informações das relações de role para incluir na resposta\r\n            const userRoleRelations = owner.ownerRoleRelation.map(relation => ({\r\n              roleId: relation.id, // Este é o ID que deve ser usado para vinculações\r\n              roleName: relation.role.name,\r\n              partPercent: relation.partPercent,\r\n            }));\r\n\r\n            return {\r\n              accessToken: token,\r\n              user: {\r\n                id: owner.id,\r\n                name: owner.name,\r\n                email: owner.email,\r\n                document: owner.cpf,\r\n                type: 'owner',\r\n                roles: userRoleRelations,\r\n              },\r\n            };\r\n          }\r\n          throw new BadRequestException('Usuário ou refreshToken inválidos');\r\n        }\r\n\r\n        const validPassword = await bcrypt.compare(\r\n          data.password,\r\n          owner.password,\r\n        );\r\n        if (validPassword) {\r\n          const token = this.generateToken({\r\n            cpf: owner.cpf,\r\n            email: owner.email,\r\n            id: owner.id,\r\n            roles,\r\n          });\r\n          const refreshToken = await this.generateRefreshToken({\r\n            id: owner.id,\r\n            document: owner.cpf,\r\n            name: owner.name,\r\n            email: owner.email,\r\n          });\r\n\r\n          // Buscar informações das relações de role para incluir na resposta\r\n          const userRoleRelations = owner.ownerRoleRelation.map(relation => ({\r\n            roleId: relation.id, // Este é o ID que deve ser usado para vinculações\r\n            roleName: relation.role.name,\r\n            partPercent: relation.partPercent,\r\n          }));\r\n\r\n          return {\r\n            accessToken: token,\r\n            refreshToken,\r\n            user: {\r\n              id: owner.id,\r\n              name: owner.name,\r\n              email: owner.email,\r\n              document: owner.cpf,\r\n              type: 'owner',\r\n              roles: userRoleRelations,\r\n            },\r\n          };\r\n        }\r\n\r\n        throw new BadRequestException('Usuário ou senha inválidos');\r\n      } else {\r\n        throw new BadRequestException('Usuário ou senha inválidos');\r\n      }\r\n    }\r\n\r\n    if (typeDocument === 'cnpj') {\r\n      const business = await this.businessDb.findOne({\r\n        relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],\r\n        where: {\r\n          cnpj: data.document,\r\n        },\r\n      });\r\n\r\n      if (business) {\r\n        if (business.account && business.account[0]?.isExternal) {\r\n          throw new UnauthorizedException();\r\n        }\r\n\r\n        const roles: Array<string> = business.ownerRoleRelation.map(\r\n          (role) => role.role.name,\r\n        );\r\n\r\n        if (data.refreshToken) {\r\n          const matchRefreshToken = await bcrypt.compare(\r\n            data.refreshToken,\r\n            business.refreshToken,\r\n          );\r\n          if (matchRefreshToken) {\r\n            const token = this.generateToken({\r\n              cpf: business.cnpj,\r\n              email: business.email,\r\n              id: business.id,\r\n              roles,\r\n            });\r\n\r\n            // Buscar informações das relações de role para incluir na resposta\r\n            const userRoleRelations = business.ownerRoleRelation.map(relation => ({\r\n              roleId: relation.id, // Este é o ID que deve ser usado para vinculações\r\n              roleName: relation.role.name,\r\n              partPercent: relation.partPercent,\r\n            }));\r\n\r\n            return {\r\n              accessToken: token,\r\n              user: {\r\n                id: business.id,\r\n                name: business.companyName,\r\n                email: business.email,\r\n                document: business.cnpj,\r\n                type: 'business',\r\n                roles: userRoleRelations,\r\n              },\r\n            };\r\n          }\r\n          throw new BadRequestException('Usuário ou refreshToken inválidos');\r\n        }\r\n\r\n        const validPassword = await bcrypt.compare(\r\n          data.password,\r\n          business.password,\r\n        );\r\n        if (validPassword) {\r\n          const token = this.generateToken({\r\n            cpf: business.cnpj,\r\n            email: business.email,\r\n            id: business.id,\r\n            roles,\r\n          });\r\n          const refreshToken = await this.generateRefreshToken({\r\n            id: business.id,\r\n            document: business.cnpj,\r\n            name: business.companyName,\r\n            email: business.email,\r\n          });\r\n\r\n          // Buscar informações das relações de role para incluir na resposta\r\n          const userRoleRelations = business.ownerRoleRelation.map(relation => ({\r\n            roleId: relation.id, // Este é o ID que deve ser usado para vinculações\r\n            roleName: relation.role.name,\r\n            partPercent: relation.partPercent,\r\n          }));\r\n\r\n          return {\r\n            accessToken: token,\r\n            refreshToken,\r\n            user: {\r\n              id: business.id,\r\n              name: business.companyName,\r\n              email: business.email,\r\n              document: business.cnpj,\r\n              type: 'business',\r\n              roles: userRoleRelations,\r\n            },\r\n          };\r\n        }\r\n\r\n        throw new BadRequestException('Usuário ou senha inválidos');\r\n      } else {\r\n        throw new BadRequestException('Usuário ou senha inválidos');\r\n      }\r\n    }\r\n  }\r\n\r\n  private cpfOrCnpj(document: string): string {\r\n    if (document.length > 11) {\r\n      return 'cnpj';\r\n    }\r\n    return 'cpf';\r\n  }\r\n\r\n  private generateToken(data: IGenerateTokenPayload) {\r\n    const token = this.jwtService.sign(data, {\r\n      secret: process.env.JWT_SECRET,\r\n      expiresIn: 3600,\r\n    });\r\n    return token;\r\n  }\r\n\r\n  private async generateRefreshToken(data: any) {\r\n    const payload = {\r\n      id: data.id,\r\n      document: data.document,\r\n      name: data.name,\r\n      email: data.email,\r\n    };\r\n\r\n    const token = this.jwtService.sign(payload, {\r\n      secret: process.env.JWT_SECRET,\r\n      expiresIn: '1y',\r\n    });\r\n\r\n    const cryptToken = await bcrypt.hash(token, 10);\r\n\r\n    const typeDocument = this.cpfOrCnpj(data.document);\r\n    if (typeDocument === 'cpf') {\r\n      await this.ownerDb.update(data.id, {\r\n        refreshToken: cryptToken,\r\n      });\r\n    } else if (typeDocument === 'cnpj') {\r\n      await this.businessDb.update(data.id, {\r\n        refreshToken: cryptToken,\r\n      });\r\n    }\r\n    return token;\r\n  }\r\n\r\n  async getUserInfo(userId: string) {\r\n    // Primeiro tentar buscar como owner\r\n    const owner = await this.ownerDb.findOne({\r\n      where: { id: userId },\r\n      relations: {\r\n        ownerRoleRelation: {\r\n          role: true,\r\n        },\r\n      },\r\n    });\r\n\r\n    if (owner) {\r\n      const userRoleRelations = owner.ownerRoleRelation.map(relation => ({\r\n        roleId: relation.id, // Este é o ID que deve ser usado para vinculações\r\n        roleName: relation.role.name,\r\n        partPercent: relation.partPercent,\r\n      }));\r\n\r\n      return {\r\n        id: owner.id,\r\n        name: owner.name,\r\n        email: owner.email,\r\n        document: owner.cpf,\r\n        type: 'owner' as const,\r\n        roles: userRoleRelations,\r\n      };\r\n    }\r\n\r\n    // Se não encontrou como owner, tentar como business\r\n    const business = await this.businessDb.findOne({\r\n      where: { id: userId },\r\n      relations: {\r\n        ownerRoleRelation: {\r\n          role: true,\r\n        },\r\n      },\r\n    });\r\n\r\n    if (business) {\r\n      const userRoleRelations = business.ownerRoleRelation.map(relation => ({\r\n        roleId: relation.id, // Este é o ID que deve ser usado para vinculações\r\n        roleName: relation.role.name,\r\n        partPercent: relation.partPercent,\r\n      }));\r\n\r\n      return {\r\n        id: business.id,\r\n        name: business.companyName,\r\n        email: business.email,\r\n        document: business.cnpj,\r\n        type: 'business' as const,\r\n        roles: userRoleRelations,\r\n      };\r\n    }\r\n\r\n    throw new BadRequestException('Usuário não encontrado');\r\n  }\r\n}\r\n"]}