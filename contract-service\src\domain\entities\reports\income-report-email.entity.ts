import { Entity } from '@/domain/shared';
import type { BaseEntityProps } from '@/domain/shared/base-entity';

export enum EmailSendStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export interface TemplateData {
  calendar_year: number;
  name: string;
  download_link: string;
  telephone: string;
  email: string;
}

export interface EmailData {
  from_email?: string;
  to_email: string;
  template?: string;
  template_data: TemplateData;
}

interface IncomeReportEmailProps extends BaseEntityProps {
  emailData: EmailData;
  status: EmailSendStatus;
  sentAt?: Date;
  failedAt?: Date;
  errorMessage?: string;
  incomeReportId: string;
}

export class IncomeReportEmail extends Entity<IncomeReportEmailProps> {
  private constructor(props: IncomeReportEmailProps, id?: string) {
    super(props, id);
  }

  static createNew(
    emailData: EmailData,
    incomeReportId: string,
  ): IncomeReportEmail {
    return new IncomeReportEmail({
      emailData,
      incomeReportId,
      status: EmailSendStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  static createFromExisting(
    props: IncomeReportEmailProps,
    id?: string,
  ): IncomeReportEmail {
    return new IncomeReportEmail(
      {
        ...props,
        updatedAt: props.updatedAt ?? new Date(), // Atualiza caso esteja ausente
      },
      id,
    );
  }

  markAsSent(): void {
    this.props.status = EmailSendStatus.SUCCESS;
    this.props.sentAt = new Date();
    this.touch();
  }

  markAsFailed(error: string): void {
    this.props.status = EmailSendStatus.FAILED;
    this.props.failedAt = new Date();
    this.props.errorMessage = error;
    this.touch();
  }

  getIncomeReportId(): string {
    return this.props.incomeReportId;
  }
  getEmailBody(): EmailData {
    return this.props.emailData;
  }

  getStatus(): EmailSendStatus {
    return this.props.status;
  }

  getSentAt(): Date | undefined {
    return this.props.sentAt;
  }

  getFailedAt(): Date | undefined {
    return this.props.failedAt;
  }

  getErrorMessage(): string | undefined {
    return this.props.errorMessage;
  }
}
