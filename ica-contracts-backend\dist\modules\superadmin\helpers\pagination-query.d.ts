import { PaginationMeta } from './pagination-meta.dto';
export interface IPaginationQuery {
    page?: number;
    limit?: number;
}
export interface IDateRangeQuery {
    dateFrom?: string;
    dateTo?: string;
}
export interface IPaginatedResult<T> {
    data: T[];
    meta: PaginationMeta;
}
export declare class PaginatedQueryHelper {
    static getPaginationParams(query: IPaginationQuery): {
        page: number;
        limit: number;
        skip: number;
    };
    static getDateRangeParams(query: IDateRangeQuery): {
        start: Date;
        end: Date;
    };
    static createPaginatedResponse<T>(data: T[], total: number, page: number, limit: number): IPaginatedResult<T>;
}
