interface IBaseReponse {
  version: string;
  status: string;
}

interface IClaimerAccount {
  participant: string;
  branch: string;
  account: string;
  accountType: string;
}

interface IClaimer {
  personType: string;
  taxId: string;
  name: string;
}

export interface IBodyResponse {
  id: string;
  claimType: string;
  key: string;
  keyType: string;
  claimerAccount: IClaimerAccount;
  claimer: IClaimer;
  donorParticipant: string;
  createTimestamp: string;
  completionPeriodEnd: string;
  resolutionPeriodEnd: string;
  lastModified: string;
}

export interface IClaimConfirmCelcoinResponse extends IBaseReponse {
  body: IBodyResponse;
}
