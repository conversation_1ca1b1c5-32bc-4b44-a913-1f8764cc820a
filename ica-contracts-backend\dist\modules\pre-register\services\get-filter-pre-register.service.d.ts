import { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';
import { Repository } from 'typeorm';
import { GetFilterPreRegisterDto } from '../dto/get-filter-pre-register.dto';
export declare class GetFilterPreRegisterService {
    private preRegisterDb;
    constructor(preRegisterDb: Repository<PreRegisterEntity>);
    perform(data: GetFilterPreRegisterDto): Promise<PreRegisterEntity[]>;
}
