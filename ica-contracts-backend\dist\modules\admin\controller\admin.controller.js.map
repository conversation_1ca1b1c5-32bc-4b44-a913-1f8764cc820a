{"version": 3, "file": "admin.controller.js", "sourceRoot": "/", "sources": ["modules/admin/controller/admin.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAWwB;AACxB,gFAA8D;AAC9D,iEAAwD;AACxD,0EAAgE;AAChE,kEAAyD;AAEzD,sEAAiE;AACjE,yFAAoF;AACpF,qEAAsE;AACtE,uEAAkE;AAClE,qEAAgE;AAEhE,mHAA2G;AAC3G,iFAAuE;AAEvE,iFAAiF;AACjF,kEAA6D;AAC7D,+EAA+E;AAIxE,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,YACmB,eAAgC,EAChC,gBAAuC,EACvC,mBAAwC,EACxC,gBAAkC,EAClC,mCAAwE,EACxE,qBAAiD,EACjD,yBAAoD;QANpD,oBAAe,GAAf,eAAe,CAAiB;QAChC,qBAAgB,GAAhB,gBAAgB,CAAuB;QACvC,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,wCAAmC,GAAnC,mCAAmC,CAAqC;QACxE,0BAAqB,GAArB,qBAAqB,CAA4B;QACjD,8BAAyB,GAAzB,yBAAyB,CAA2B;IACpE,CAAC;IAKE,AAAN,KAAK,CAAC,MAAM,CACC,OAAoB;QAE/B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACjD,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;SACzB,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAIK,AAAN,KAAK,CAAC,SAAS,CACF,OAAoB;QAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;SACzB,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IAIK,AAAN,KAAK,CAAC,YAAY,CACL,OAAoB,EAE/B,IAAmB;QAEnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7E,OAAO,MAAM,CAAC;IAChB,CAAC;IAKK,AAAN,KAAK,CAAC,UAAU,CACH,OAAoB,EAE/B,IAAmB;QAEnB,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAE/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IAKK,AAAN,KAAK,CAAC,cAAc,CAElB,IAAuB;QAEvB,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9D,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,uBAAuB,CACL,WAAmB,EACxB,MAAqB,EACf,YAA+B;QAEtD,OAAO,IAAI,CAAC,mCAAmC,CAAC,OAAO,CACrD,WAAW,EACX,MAAM,EACN,YAAY,CACb,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,kBAAkB,CAAY,OAAO,EAAW,IAAsB;QAC1E,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvE,CAAC;CACF,CAAA;AAjGY,0CAAe;AAcpB;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,KAAK,CAAC;IACtB,IAAA,YAAG,EAAC,SAAS,CAAC;IAEZ,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;6CAMX;AAIK;IAFL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,YAAG,EAAC,WAAW,CAAC;IAEd,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;gDAKX;AAIK;IAFL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,YAAG,EAAC,UAAU,CAAC;IAEb,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,+BAAa;;mDAIpB;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC;IAC5C,IAAA,cAAK,EAAC,EAAE,CAAC;IAEP,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,+BAAa;;iDAKpB;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,CAAC;IAC3B,IAAA,YAAG,EAAC,iBAAiB,CAAC;IAEpB,WAAA,IAAA,aAAI,GAAE,CAAA;;;;qDAaR;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC;IAC5C,IAAA,YAAG,EAAC,gCAAgC,CAAC;IAEnC,WAAA,IAAA,cAAK,EAAC,aAAa,CAAC,CAAA;IACpB,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,cAAK,EAAC,cAAc,CAAC,CAAA;;;;8DAOvB;AAKK;IAHL,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACtB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,KAAK,CAAC;IACG,WAAA,IAAA,gBAAO,GAAE,CAAA;IAAW,WAAA,IAAA,cAAK,GAAE,CAAA;;6CAAO,qCAAgB;;yDAE3E;0BAhGU,eAAe;IAD3B,IAAA,mBAAU,EAAC,OAAO,CAAC;qCAGkB,mCAAe;QACd,yCAAqB;QAClB,2CAAmB;QACtB,qCAAgB;QACG,8EAAmC;QACjD,oDAA0B;QACtB,kDAAyB;GAR5D,eAAe,CAiG3B", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Get,\r\n  HttpException,\r\n  Param,\r\n  Patch,\r\n  Put,\r\n  Query,\r\n  Request,\r\n  UseGuards,\r\n} from '@nestjs/common';\r\nimport { Roles } from 'src/shared/decorators/roles.decorator';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { RoleGuard } from 'src/shared/guards/role.guard';\r\n\r\nimport { EditBrokerDto } from '../../broker/dto/edit-broker.dto';\r\nimport { EditInvestorService } from '../../investor/services/edit-investor.service';\r\nimport { AdminDashboardService } from '../services/dashboard.service';\r\nimport { EditAdminService } from '../services/edit-admin.service';\r\nimport { GetAdminService } from '../services/get-admin.service';\r\nimport { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';\r\nimport { GetAdminContractsGrowthChartService } from '../services/get-admin-contracts-growth-chart.service';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\nimport type { ChangePasswordDto } from '../dto/change-password.dto';\r\nimport { ChangePasswordAdminService } from '../services/change-password.service';\r\nimport { ListContractsDto } from '../dto/list-contracts.dto';\r\nimport { ListContractsAdminService } from '../services/list-contracts.service';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\n@Controller('admin')\r\nexport class AdminController {\r\n  constructor(\r\n    private readonly getAdminService: GetAdminService,\r\n    private readonly dashboardService: AdminDashboardService,\r\n    private readonly editInvestorService: EditInvestorService,\r\n    private readonly editAdminService: EditAdminService,\r\n    private readonly getAdminContractsGrowthChartService: GetAdminContractsGrowthChartService,\r\n    private readonly changePasswordService: ChangePasswordAdminService,\r\n    private readonly listContractsAdminService: ListContractsAdminService,\r\n  ) {}\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.ADMIN)\r\n  @Get('brokers')\r\n  async search(\r\n    @Request() request:IRequestUser \r\n  ) {\r\n    const results = await this.getAdminService.perform({\r\n      ownerId: request.user.id,\r\n    });\r\n    return results;\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Get('dashboard')\r\n  async dashboard(\r\n    @Request() request:IRequestUser) {\r\n    const result = await this.dashboardService.perform({\r\n      ownerId: request.user.id,\r\n    });\r\n    return result;\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Put('investor')\r\n  async editInvestor(\r\n    @Request() request:IRequestUser,\r\n    @Body()\r\n    body: EditBrokerDto,\r\n  ) {\r\n    const result = await this.editInvestorService.perform(body, request.user.id);\r\n    return result;\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)\r\n  @Patch('')\r\n  async editBroker(\r\n    @Request() request:IRequestUser,\r\n    @Body()\r\n    body: EditBrokerDto,\r\n  ) {\r\n    const userId = request.user.id;\r\n\r\n    await this.editAdminService.perform(body, userId);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.SUPERADMIN)\r\n  @Put('change-password')\r\n  async changePassword(\r\n    @Body()\r\n    body: ChangePasswordDto,\r\n  ) {\r\n    console.log('chamou controller');\r\n    try {\r\n      const result = await this.changePasswordService.perform(body);\r\n      return result;\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)\r\n  @Get('/:ownerRoleId/contracts-growth')\r\n  async getContractsGrowthChart(\r\n    @Param('ownerRoleId') ownerRoleId: string,\r\n    @Query('period') period?: PeriodFilter,\r\n    @Query('contractType') contractType?: ContractTypeEnum,\r\n  ) {\r\n    return this.getAdminContractsGrowthChartService.perform(\r\n      ownerRoleId,\r\n      period,\r\n      contractType,\r\n    );\r\n  }\r\n\r\n  @Get('/list-contracts')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.ADMIN)\r\n  async listContractsAdmin(@Request() request, @Query() data: ListContractsDto) {\r\n    return this.listContractsAdminService.perform(data, request.user.id);\r\n  }\r\n}\r\n"]}