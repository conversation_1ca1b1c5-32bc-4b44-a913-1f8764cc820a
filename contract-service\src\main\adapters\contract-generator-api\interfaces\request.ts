export interface Address {
  street: string
  state: string
  city: string
  number: string
  postalCode: string
  neighborhood: string
}

export interface BankAccount {
  bank: string
  agency: string
  account: string
}

// Dados para pessoa física
export interface Individual {
  fullName: string
  cpf: string
  rg: string
  issuingAgency: string
  nationality: string
  occupation: string
  birthDate: string
  email: string
  phone: string
  motherName: string
  address: Address
}

// Dados para pessoa jurídica
export interface Company {
  cnpj: string
  corporateName: string
  type: string
  address: Address
  representative: Individual
}

// Investimentos
export interface InvestmentMutuo {
  amount: number
  monthlyRate: number
  durationInMonths: number
  paymentMethod: string
  endDate: string
  profile: string
  isDebenture: boolean
  startDate: string
}

export interface InvestmentSCP {
  quota?: number
  durationInMonths: number
  monthlyRate: number
}

// Tipos literais para discriminação
export type PersonType = 'PF' | 'PJ'
export type ContractType = 'MUTUO' | 'SCP'

// Tipo condicional para definir o investimento com base no tipo de contrato
type Investment<T extends ContractType> = T extends 'MUTUO'
  ? InvestmentMutuo
  : InvestmentSCP

// Interface base comum a todos os contratos
interface BaseContract {
  brokerId: string
  contractNumber: string
  personType: PersonType
  contractType: ContractType
  bankAccount: BankAccount
}

// Contratos para pessoa física
export interface PFContractMutuo extends BaseContract {
  personType: 'PF'
  contractType: 'MUTUO'
  individual: Individual
  investment: InvestmentMutuo
}

export interface PFContractSCP extends BaseContract {
  personType: 'PF'
  contractType: 'SCP'
  individual: Individual
  investment: InvestmentSCP
}

// Contratos para pessoa jurídica
export interface PJContractMutuo extends BaseContract {
  personType: 'PJ'
  contractType: 'MUTUO'
  company: Company
  investment: InvestmentMutuo
}

export interface PJContractSCP extends BaseContract {
  personType: 'PJ'
  contractType: 'SCP'
  company: Company
  investment: InvestmentSCP
}

// União discriminada para representar qualquer payload aceito pela API
export type APIContractPayload =
  | PFContractMutuo
  | PFContractSCP
  | PJContractMutuo
  | PJContractSCP
