"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionPixStatusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const date_fns_1 = require("date-fns");
const pix_transaction_celcoin_service_1 = require("../../../apis/celcoin/services/pix-transaction-celcoin.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const typeorm_2 = require("typeorm");
let TransactionPixStatusService = class TransactionPixStatusService {
    constructor(accountDb, transactionRepository, celcoinService) {
        this.accountDb = accountDb;
        this.transactionRepository = transactionRepository;
        this.celcoinService = celcoinService;
    }
    async execute(data, id) {
        const account = await this.accountDb.findOne({
            relations: {
                business: true,
                owner: true,
            },
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
        });
        if (!account)
            throw new common_1.NotFoundException('Conta não encontrada.');
        const transaction = await this.transactionRepository.findOne({
            where: {
                id: data.id,
            },
        });
        const { body: result } = await this.celcoinService.getTransactionPixStatus({
            clientCode: transaction.code,
        });
        return {
            id: transaction.id,
            amount: transaction.value,
            clientCode: transaction.code,
            endToEndId: transaction.endToEndId,
            initiationType: transaction.type,
            paymentType: transaction.type,
            creditParty: {
                bank: transaction.destinyBank,
                key: transaction.destinyKey,
                account: transaction.destinyAccount,
                branch: transaction.destinyBranch,
                taxId: transaction.destinyDocument,
                name: transaction.destinyName,
                accountType: transaction.destinyAccountType,
            },
            debitParty: {
                account: account.number,
                branch: account.branch,
                taxId: account.owner?.cpf || account.business.cnpj,
                name: account.owner?.name || account.business.companyName,
                accountType: 'TRAN',
            },
            createdAt: (0, date_fns_1.addHours)(transaction.createdAt, -3),
            result,
        };
    }
};
exports.TransactionPixStatusService = TransactionPixStatusService;
exports.TransactionPixStatusService = TransactionPixStatusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __param(2, (0, common_1.Inject)(pix_transaction_celcoin_service_1.PixTransactionCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        pix_transaction_celcoin_service_1.PixTransactionCelcoinService])
], TransactionPixStatusService);
//# sourceMappingURL=transaction-pix-status.service.js.map