# Estratégias de Fallback para Determinação de Broker

Este documento descreve as estratégias implementadas para encontrar um broker quando um advisor não possui associação direta na tabela `wallets-views`.

## Problema Original

Quando um advisor tenta criar um contrato mas não está associado a nenhum broker na tabela `wallets-views`, o sistema retornava o erro:
```
"Broker não encontrado para o advisor."
```

## Solução Implementada

O sistema agora implementa **3 estratégias de fallback** executadas em ordem de prioridade:

### 1. **Estratégia Principal: Associação Direta**
- Busca na tabela `wallets-views` por uma relação advisor-broker
- Esta é a estratégia preferencial e mais segura
- Mantém a integridade das relações hierárquicas

### 2. **Estratégia de Fallback 1: Broker Especificado no Payload**
- Permite especificar um `brokerId` no payload da requisição
- O sistema valida se o broker existe e está ativo
- Útil para casos específicos ou migrações

### 3. **Estratégia de Fallback 2: Broker Padrão do Sistema**
- Busca o primeiro broker ativo disponível no sistema
- Ordenado por data de criação (mais antigo = mais estabelecido)
- Último recurso quando nenhuma outra estratégia funciona

## Como Usar

### Payload com Broker Específico

```json
{
  "role": "advisor",
  "personType": "PF",
  "contractType": "MUTUO",
  "brokerId": "051c4f93-1a9b-4fcd-9464-878ef8a5dbbc",
  "advisors": [],
  "investment": {
    "amount": 10000,
    "monthlyRate": 1.2,
    "durationInMonths": 12,
    "paymentMethod": "pix",
    "endDate": "2026-07-09",
    "profile": "conservative",
    "isDebenture": true
  },
  "bankAccount": {
    "bank": "Ica Bank Teste",
    "agency": "001",
    "account": "********-1",
    "pix": "<EMAIL>",
    "accountType": "CORRENTE"
  },
  "individual": {
    "fullName": "Matheus Gabriel",
    "cpf": "***********",
    // ... outros campos
  }
}
```

### Payload sem Broker (Usa Fallback)

```json
{
  "role": "advisor",
  "personType": "PF",
  "contractType": "MUTUO",
  // brokerId omitido - sistema usará fallback
  "advisors": [],
  // ... resto do payload
}
```

## Logs de Debug

O sistema agora produz logs detalhados para facilitar o debug:

```
[LOG] Broker associado encontrado: 051c4f93-1a9b-4fcd-9464-878ef8a5dbbc para advisor c75e9b2c-9048-48ed-ba90-4dfe70fa8ce1
[LOG] Usando broker do payload: 051c4f93-1a9b-4fcd-9464-878ef8a5dbbc para advisor c75e9b2c-9048-48ed-ba90-4dfe70fa8ce1
[LOG] Usando broker padrão: 051c4f93-1a9b-4fcd-9464-878ef8a5dbbc para advisor c75e9b2c-9048-48ed-ba90-4dfe70fa8ce1
[LOG] Advisor c75e9b2c-9048-48ed-ba90-4dfe70fa8ce1 definido no contexto como único advisor. Broker associado: 051c4f93-1a9b-4fcd-9464-878ef8a5dbbc
```

## Casos de Uso

### 1. **Operação Normal**
- Advisor tem associação direta → Usa broker associado
- Mais seguro e mantém integridade dos dados

### 2. **Migração de Dados**
- Advisor sem associação + `brokerId` no payload → Usa broker especificado
- Útil para migrar contratos antigos ou casos especiais

### 3. **Situação de Emergência**
- Advisor sem associação + sem `brokerId` → Usa broker padrão
- Garante que o sistema continue funcionando

### 4. **Ambiente de Desenvolvimento/Teste**
- Facilita testes sem necessidade de configurar todas as associações
- Permite criação rápida de contratos para testes

## Benefícios

1. **Robustez**: Sistema não falha mais por falta de associação
2. **Flexibilidade**: Permite especificar broker quando necessário
3. **Compatibilidade**: Mantém funcionamento para casos já configurados
4. **Debug**: Logs detalhados facilitam investigação de problemas
5. **Migração**: Facilita migração de dados antigos

## Considerações de Segurança

- O `brokerId` especificado no payload é sempre validado
- Apenas brokers ativos e válidos são aceitos
- Logs registram qual estratégia foi utilizada para auditoria
- A estratégia de associação direta sempre tem prioridade

## Recomendações

1. **Para Produção**: Configure sempre as associações diretas na tabela `wallets-views`
2. **Para Casos Especiais**: Use o `brokerId` no payload apenas quando necessário
3. **Para Monitoramento**: Monitore os logs para identificar uso excessivo de fallbacks
4. **Para Manutenção**: Revise periodicamente advisors sem associação direta
