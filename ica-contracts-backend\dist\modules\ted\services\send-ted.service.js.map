{"version": 3, "file": "send-ted.service.js", "sourceRoot": "/", "sources": ["modules/ted/services/send-ted.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAgF;AAChF,6CAAmD;AACnD,uCAAqC;AACrC,4FAAkF;AAClF,+GAA+G;AAC/G,2FAA2F;AAC3F,6HAAiH;AACjH,6FAAoF;AACpF,qGAA4F;AAC5F,yGAA8F;AAC9F,qCAA4C;AAC5C,+BAA0B;AAK1B,IAAa,cAAc,GAA3B,MAAa,cAAc;IACzB,YAEU,WAAsC,EAEtC,eAA8C,EAErC,gBAAwD,EAEjE,cAAiC,EAEjC,QAAgC,EACvB,oBAA0C;QATnD,gBAAW,GAAX,WAAW,CAA2B;QAEtC,oBAAe,GAAf,eAAe,CAA+B;QAErC,qBAAgB,GAAhB,gBAAgB,CAAwC;QAEjE,mBAAc,GAAd,cAAc,CAAmB;QAEjC,aAAQ,GAAR,QAAQ,CAAwB;QACvB,yBAAoB,GAApB,oBAAoB,CAAsB;IAC1D,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,IAAgB,EAChB,EAAU,EACV,cAAuB;QAEvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,0BAAiB,CACzB,iDAAiD,CAClD,CAAC;QAEJ,MAAM,IAAI,CAAC,oBAAoB,CAAC,6BAA6B,CAAC;YAC5D,MAAM,EAAE,EAAE;YACV,KAAK,EAAE,cAAc;SACtB,CAAC,CAAC;QAUH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAExD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAClD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QAIrE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAChE,KAAK,EAAE,OAAO,EAAE,EAAE;YAChB,MAAM,IAAI,GAAG,IAAA,SAAE,GAAE,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACzD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC3B,UAAU,EAAE,IAAI;gBAChB,cAAc,EAAE,IAAI,CAAC,QAAQ;gBAC7B,WAAW,EAAE;oBACX,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;oBACrC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;oBACvC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;oBACpC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;oBAC7B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;oBACxB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI;oBAChC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;iBACjC;gBACD,UAAU,EAAE;oBACV,OAAO,EAAE,OAAO,CAAC,MAAM;iBACxB;gBACD,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG;gBACvB,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,EAAE,EAAE,MAAM,CAAC,EAAE;aACd,CAAC;YAEF,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,sCAAiB,EAAE;gBACvD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,OAAO;gBACP,IAAI,EAAE,MAAM,CAAC,EAAE;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,KAAK,EAAE,IAAI,CAAC,MAAM;gBAClB,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO;gBAC1C,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI;gBACpC,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;gBACxC,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK;gBACzC,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI;gBACpC,IAAI,EAAE,4DAA2B,CAAC,WAAW;gBAC7C,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;aACnD,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACvD,OAAO,WAAW,CAAC;QACrB,CAAC,CACF,CAAC;QAEF,OAAO;YACL,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,QAAQ,EAAE;gBACR,OAAO,EAAE,WAAW,CAAC,cAAc;gBACnC,MAAM,EAAE,WAAW,CAAC,aAAa;gBACjC,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,QAAQ,EAAE,WAAW,CAAC,eAAe;gBACrC,IAAI,EAAE,WAAW,CAAC,WAAW;aAC9B;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAsB,EAAE,MAAc;QACrE,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY;YACf,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBAC9C,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;QAEL,IAAI,CAAC,YAAY,CAAC,MAAM;YAAE,OAAO;QAEjC,IAAI,IAAA,oBAAS,EAAC,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QAErE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAEpC,MAAM,2BAA2B,GAC/B,YAAY,IAAI,YAAY,CAAC,oBAAoB,CAAC;QACpD,MAAM,yBAAyB,GAAG,YAAY,IAAI,YAAY,CAAC,YAAY,CAAC;QAC5E,MAAM,yBAAyB,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;QAE1E,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,0BAA0B,GAC9B,GAAG,IAAI,EAAE;YACP,CAAC,CAAC,YAAY,IAAI,YAAY,CAAC,eAAe,GAAG,YAAY,CAAC,UAAU;YACxE,CAAC,CAAC,IAAI,CAAC;QAEX,IACE,CAAC,CACC,2BAA2B;YAC3B,yBAAyB;YACzB,yBAAyB;YACzB,0BAA0B,CAC3B,EACD,CAAC;YACD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;QAED,YAAY,CAAC,YAAY,IAAI,YAAY,CAAC;QAC1C,YAAY,CAAC,UAAU,IAAI,YAAY,CAAC;QACxC,IAAI,GAAG,IAAI,EAAE;YAAE,YAAY,CAAC,eAAe,IAAI,YAAY,CAAC;QAE5D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;CACF,CAAA;AA3JY,wCAAc;yBAAd,cAAc;IAEtB,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,2DAA0B,CAAC,CAAA;IAE5C,WAAA,IAAA,eAAM,EAAC,uCAAiB,CAAC,CAAA;IAEzB,WAAA,IAAA,eAAM,EAAC,kDAAsB,CAAC,CAAA;qCAPV,oBAAU;QAEN,oBAAU;QAEA,oBAAU;QAErB,uCAAiB;QAEvB,kDAAsB;QACD,8CAAoB;GAZlD,cAAc,CA2J1B", "sourcesContent": ["import { BadRequestException, Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { isWeekend } from 'date-fns';\r\nimport { TedCelcoinService } from 'src/apis/celcoin/services/ted-celcoin.service';\r\nimport { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';\r\nimport { TwoFactorAuthService } from 'src/modules/two-factor-auth/two-factor-auth.service';\r\nimport { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\n\r\nimport { SendTedDto } from '../dto/send-ted.dto';\r\nimport { ISendTedResponse } from '../response/send-ted.response';\r\n\r\nexport class SendTedService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountRepo: Repository<AccountEntity>,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepo: Repository<TransactionEntity>,\r\n    @InjectRepository(AccountTransferLimitEntity)\r\n    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,\r\n    @Inject(TedCelcoinService)\r\n    private celcoinService: TedCelcoinService,\r\n    @Inject(GetAccountLimitService)\r\n    private getLimit: GetAccountLimitService,\r\n    private readonly twoFactorAuthService: TwoFactorAuthService,\r\n  ) {}\r\n\r\n  async perform(\r\n    data: SendTedDto,\r\n    id: string,\r\n    twoFactorToken?: string,\r\n  ): Promise<ISendTedResponse> {\r\n    const account = await this.accountRepo.findOne({\r\n      where: [{ ownerId: Equal(id) }, { businessId: Equal(id) }],\r\n    });\r\n\r\n    if (!account)\r\n      throw new NotFoundException(\r\n        'Conta não encontrada ou não vinculada ao owner.',\r\n      );\r\n\r\n    await this.twoFactorAuthService.verifyTwoFactorAuthentication({\r\n      userId: id,\r\n      token: twoFactorToken,\r\n    });\r\n    // const isPasswordCorrect = await bcrypt.compare(\r\n    //   data.transactionPassword,\r\n    //   account.transactionPassword,\r\n    // );\r\n\r\n    // if (!isPasswordCorrect) {\r\n    //   throw new UnauthorizedException('Senha de transação incorreta.');\r\n    // }\r\n\r\n    const balance = await this.getLimit.execute(account.id);\r\n\r\n    if (Number(data.amount) > Number(balance.dailyLimit))\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n\r\n    // await this.isValidTransaction(account, data.amount);\r\n\r\n    const transaction = await this.transactionRepo.manager.transaction(\r\n      async (manager) => {\r\n        const uuid = v4();\r\n        const { body: result } = await this.celcoinService.sendTed({\r\n          amount: Number(data.amount),\r\n          clientCode: uuid,\r\n          clientFinality: data.finality,\r\n          creditParty: {\r\n            account: data.external.account.number,\r\n            accountType: data.external.account.type,\r\n            branch: data.external.account.branch,\r\n            taxId: data.external.document,\r\n            name: data.external.name,\r\n            bank: data.external.account.bank,\r\n            personType: data.external.person,\r\n          },\r\n          debitParty: {\r\n            account: account.number,\r\n          },\r\n          description: data.description,\r\n        });\r\n\r\n        const transferMetadata = {\r\n          creditParty: result.creditParty,\r\n          debitParty: result.debitParty,\r\n          finality: data.finality,\r\n          id: result.id,\r\n        };\r\n\r\n        const newTransaction = manager.create(TransactionEntity, {\r\n          accountId: account.id,\r\n          account,\r\n          code: result.id,\r\n          description: data.description,\r\n          value: data.amount,\r\n          destinyAccount: result.creditParty.account,\r\n          destinyBank: result.creditParty.bank,\r\n          destinyBranch: result.creditParty.branch,\r\n          destinyDocument: result.creditParty.taxId,\r\n          destinyName: result.creditParty.name,\r\n          type: TransactionMovementTypeEnum.TED_CASHOUT,\r\n          transferMetadata: JSON.stringify(transferMetadata),\r\n        });\r\n\r\n        const transaction = await manager.save(newTransaction);\r\n        return transaction;\r\n      },\r\n    );\r\n\r\n    return {\r\n      id: transaction.id,\r\n      external: {\r\n        account: transaction.destinyAccount,\r\n        branch: transaction.destinyBranch,\r\n        name: transaction.destinyName,\r\n        document: transaction.destinyDocument,\r\n        bank: transaction.destinyBank,\r\n      },\r\n    };\r\n  }\r\n\r\n  private async isValidTransaction(account: AccountEntity, amount: string) {\r\n    let accountLimit = await this.accountLimitRepo.findOne({\r\n      where: { accountId: account.id },\r\n    });\r\n\r\n    if (!accountLimit)\r\n      accountLimit = await this.accountLimitRepo.save({\r\n        accountId: account.id,\r\n      });\r\n\r\n    if (!accountLimit.active) return;\r\n\r\n    if (isWeekend(new Date()))\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n\r\n    const amountNumber = Number(amount);\r\n\r\n    const amountIsValidOnGeneralLimit =\r\n      amountNumber <= accountLimit.generalTransferLimit;\r\n    const amountIsValidOnMonthLimit = amountNumber <= accountLimit.monthlyLimit;\r\n    const amountIsValidOnDailyLimit = amountNumber <= accountLimit.dailyLimit;\r\n\r\n    const now = new Date().getHours();\r\n    const amountIsValidOnNightlLimit =\r\n      now >= 18\r\n        ? amountNumber <= accountLimit.dailyNightLimit - accountLimit.dailyLimit\r\n        : true;\r\n\r\n    if (\r\n      !(\r\n        amountIsValidOnGeneralLimit &&\r\n        amountIsValidOnMonthLimit &&\r\n        amountIsValidOnDailyLimit &&\r\n        amountIsValidOnNightlLimit\r\n      )\r\n    ) {\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n    }\r\n\r\n    accountLimit.monthlyLimit -= amountNumber;\r\n    accountLimit.dailyLimit -= amountNumber;\r\n    if (now >= 18) accountLimit.dailyNightLimit -= amountNumber;\r\n\r\n    this.accountLimitRepo.save(accountLimit);\r\n  }\r\n}\r\n"]}