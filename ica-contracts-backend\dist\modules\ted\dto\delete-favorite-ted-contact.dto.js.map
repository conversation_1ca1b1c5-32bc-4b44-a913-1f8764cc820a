{"version": 3, "file": "delete-favorite-ted-contact.dto.js", "sourceRoot": "/", "sources": ["modules/ted/dto/delete-favorite-ted-contact.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAqD;AAErD,MAAa,2BAA2B;CAIvC;AAJD,kEAIC;AADC;IAFC,IAAA,wBAAM,GAAE;IACR,IAAA,4BAAU,GAAE;;8DACK", "sourcesContent": ["import { IsNotEmpty, IsUUID } from 'class-validator';\r\n\r\nexport class DeleteFavoriteTedContactDto {\r\n  @IsUUID()\r\n  @IsNotEmpty()\r\n  contactId: string;\r\n}\r\n"]}