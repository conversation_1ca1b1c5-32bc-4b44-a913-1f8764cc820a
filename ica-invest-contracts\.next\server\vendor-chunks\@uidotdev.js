"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uidotdev";
exports.ids = ["vendor-chunks/@uidotdev"];
exports.modules = {

/***/ "(ssr)/./node_modules/@uidotdev/usehooks/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@uidotdev/usehooks/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBattery: () => (/* binding */ useBattery),\n/* harmony export */   useClickAway: () => (/* binding */ useClickAway),\n/* harmony export */   useCopyToClipboard: () => (/* binding */ useCopyToClipboard),\n/* harmony export */   useCounter: () => (/* binding */ useCounter),\n/* harmony export */   useDebounce: () => (/* binding */ useDebounce),\n/* harmony export */   useDefault: () => (/* binding */ useDefault),\n/* harmony export */   useDocumentTitle: () => (/* binding */ useDocumentTitle),\n/* harmony export */   useFavicon: () => (/* binding */ useFavicon),\n/* harmony export */   useGeolocation: () => (/* binding */ useGeolocation),\n/* harmony export */   useHistoryState: () => (/* binding */ useHistoryState),\n/* harmony export */   useHover: () => (/* binding */ useHover),\n/* harmony export */   useIdle: () => (/* binding */ useIdle),\n/* harmony export */   useIntersectionObserver: () => (/* binding */ useIntersectionObserver),\n/* harmony export */   useIsClient: () => (/* binding */ useIsClient),\n/* harmony export */   useIsFirstRender: () => (/* binding */ useIsFirstRender),\n/* harmony export */   useList: () => (/* binding */ useList),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useLockBodyScroll: () => (/* binding */ useLockBodyScroll),\n/* harmony export */   useLongPress: () => (/* binding */ useLongPress),\n/* harmony export */   useMap: () => (/* binding */ useMap),\n/* harmony export */   useMeasure: () => (/* binding */ useMeasure),\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery),\n/* harmony export */   useMouse: () => (/* binding */ useMouse),\n/* harmony export */   useNetworkState: () => (/* binding */ useNetworkState),\n/* harmony export */   useObjectState: () => (/* binding */ useObjectState),\n/* harmony export */   useOrientation: () => (/* binding */ useOrientation),\n/* harmony export */   usePreferredLanguage: () => (/* binding */ usePreferredLanguage),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useQueue: () => (/* binding */ useQueue),\n/* harmony export */   useRenderCount: () => (/* binding */ useRenderCount),\n/* harmony export */   useRenderInfo: () => (/* binding */ useRenderInfo),\n/* harmony export */   useScript: () => (/* binding */ useScript),\n/* harmony export */   useSessionStorage: () => (/* binding */ useSessionStorage),\n/* harmony export */   useSet: () => (/* binding */ useSet),\n/* harmony export */   useThrottle: () => (/* binding */ useThrottle),\n/* harmony export */   useToggle: () => (/* binding */ useToggle),\n/* harmony export */   useVisibilityChange: () => (/* binding */ useVisibilityChange),\n/* harmony export */   useWindowScroll: () => (/* binding */ useWindowScroll),\n/* harmony export */   useWindowSize: () => (/* binding */ useWindowSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nfunction isShallowEqual(object1, object2) {\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (let key of keys1) {\n    if (object1[key] !== object2[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction isTouchEvent({ nativeEvent }) {\n  return window.TouchEvent\n    ? nativeEvent instanceof TouchEvent\n    : \"touches\" in nativeEvent;\n}\n\nfunction isMouseEvent(event) {\n  return event.nativeEvent instanceof MouseEvent;\n}\n\nfunction throttle(cb, ms) {\n  let lastTime = 0;\n  return () => {\n    const now = Date.now();\n    if (now - lastTime >= ms) {\n      cb();\n      lastTime = now;\n    }\n  };\n}\n\nfunction isPlainObject(value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n\nfunction dispatchStorageEvent(key, newValue) {\n  window.dispatchEvent(new StorageEvent(\"storage\", { key, newValue }));\n}\n\nfunction useBattery() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    supported: true,\n    loading: true,\n    level: null,\n    charging: null,\n    chargingTime: null,\n    dischargingTime: null,\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!navigator.getBattery) {\n      setState((s) => ({\n        ...s,\n        supported: false,\n        loading: false,\n      }));\n      return;\n    }\n\n    let battery = null;\n\n    const handleChange = () => {\n      setState({\n        supported: true,\n        loading: false,\n        level: battery.level,\n        charging: battery.charging,\n        chargingTime: battery.chargingTime,\n        dischargingTime: battery.dischargingTime,\n      });\n    };\n\n    navigator.getBattery().then((b) => {\n      battery = b;\n      handleChange();\n\n      b.addEventListener(\"levelchange\", handleChange);\n      b.addEventListener(\"chargingchange\", handleChange);\n      b.addEventListener(\"chargingtimechange\", handleChange);\n      b.addEventListener(\"dischargingtimechange\", handleChange);\n    });\n\n    return () => {\n      if (battery) {\n        battery.removeEventListener(\"levelchange\", handleChange);\n        battery.removeEventListener(\"chargingchange\", handleChange);\n        battery.removeEventListener(\"chargingtimechange\", handleChange);\n        battery.removeEventListener(\"dischargingtimechange\", handleChange);\n      }\n    };\n  }, []);\n\n  return state;\n}\n\nfunction useClickAway(cb) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const refCb = react__WEBPACK_IMPORTED_MODULE_0__.useRef(cb);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    refCb.current = cb;\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handler = (e) => {\n      const element = ref.current;\n      if (element && !element.contains(e.target)) {\n        refCb.current(e);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handler);\n    document.addEventListener(\"touchstart\", handler);\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handler);\n      document.removeEventListener(\"touchstart\", handler);\n    };\n  }, []);\n\n  return ref;\n}\n\nfunction oldSchoolCopy(text) {\n  const tempTextArea = document.createElement(\"textarea\");\n  tempTextArea.value = text;\n  document.body.appendChild(tempTextArea);\n  tempTextArea.select();\n  document.execCommand(\"copy\");\n  document.body.removeChild(tempTextArea);\n}\n\nfunction useCopyToClipboard() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n\n  const copyToClipboard = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((value) => {\n    const handleCopy = async () => {\n      try {\n        if (navigator?.clipboard?.writeText) {\n          await navigator.clipboard.writeText(value);\n          setState(value);\n        } else {\n          throw new Error(\"writeText not supported\");\n        }\n      } catch (e) {\n        oldSchoolCopy(value);\n        setState(value);\n      }\n    };\n\n    handleCopy();\n  }, []);\n\n  return [state, copyToClipboard];\n}\n\nfunction useCounter(startingValue = 0, options = {}) {\n  const { min, max } = options;\n\n  if (typeof min === \"number\" && startingValue < min) {\n    throw new Error(\n      `Your starting value of ${startingValue} is less than your min of ${min}.`\n    );\n  }\n\n  if (typeof max === \"number\" && startingValue > max) {\n    throw new Error(\n      `Your starting value of ${startingValue} is greater than your max of ${max}.`\n    );\n  }\n\n  const [count, setCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(startingValue);\n\n  const increment = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setCount((c) => {\n      const nextCount = c + 1;\n\n      if (typeof max === \"number\" && nextCount > max) {\n        return c;\n      }\n\n      return nextCount;\n    });\n  }, [max]);\n\n  const decrement = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setCount((c) => {\n      const nextCount = c - 1;\n\n      if (typeof min === \"number\" && nextCount < min) {\n        return c;\n      }\n\n      return nextCount;\n    });\n  }, [min]);\n\n  const set = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextCount) => {\n      setCount((c) => {\n        if (typeof max === \"number\" && nextCount > max) {\n          return c;\n        }\n\n        if (typeof min === \"number\" && nextCount < min) {\n          return c;\n        }\n\n        return nextCount;\n      });\n    },\n    [max, min]\n  );\n\n  const reset = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setCount(startingValue);\n  }, [startingValue]);\n\n  return [\n    count,\n    {\n      increment,\n      decrement,\n      set,\n      reset,\n    },\n  ];\n}\n\nfunction useDebounce(value, delay) {\n  const [debouncedValue, setDebouncedValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(value);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n\nfunction useDefault(initialValue, defaultValue) {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(initialValue);\n\n  if (typeof state === \"undefined\" || state === null) {\n    return [defaultValue, setState];\n  }\n\n  return [state, setState];\n}\n\nfunction useDocumentTitle(title) {\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    document.title = title;\n  }, [title]);\n}\n\nfunction useFavicon(url) {\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let link = document.querySelector(`link[rel~=\"icon\"]`);\n\n    if (!link) {\n      link = document.createElement(\"link\");\n      link.type = \"image/x-icon\";\n      link.rel = \"icon\";\n      link.href = url;\n      document.head.appendChild(link);\n    } else {\n      link.href = url;\n    }\n  }, [url]);\n}\n\nfunction useGeolocation(options = {}) {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    loading: true,\n    accuracy: null,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    latitude: null,\n    longitude: null,\n    speed: null,\n    timestamp: null,\n    error: null,\n  });\n\n  const optionsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(options);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const onEvent = ({ coords, timestamp }) => {\n      setState({\n        loading: false,\n        timestamp,\n        latitude: coords.latitude,\n        longitude: coords.longitude,\n        altitude: coords.altitude,\n        accuracy: coords.accuracy,\n        altitudeAccuracy: coords.altitudeAccuracy,\n        heading: coords.heading,\n        speed: coords.speed,\n      });\n    };\n\n    const onEventError = (error) => {\n      setState((s) => ({\n        ...s,\n        loading: false,\n        error,\n      }));\n    };\n\n    navigator.geolocation.getCurrentPosition(\n      onEvent,\n      onEventError,\n      optionsRef.current\n    );\n\n    const watchId = navigator.geolocation.watchPosition(\n      onEvent,\n      onEventError,\n      optionsRef.current\n    );\n\n    return () => {\n      navigator.geolocation.clearWatch(watchId);\n    };\n  }, []);\n\n  return state;\n}\n\nconst initialUseHistoryStateState = {\n  past: [],\n  present: null,\n  future: [],\n};\n\nconst useHistoryStateReducer = (state, action) => {\n  const { past, present, future } = state;\n\n  if (action.type === \"UNDO\") {\n    return {\n      past: past.slice(0, past.length - 1),\n      present: past[past.length - 1],\n      future: [present, ...future],\n    };\n  } else if (action.type === \"REDO\") {\n    return {\n      past: [...past, present],\n      present: future[0],\n      future: future.slice(1),\n    };\n  } else if (action.type === \"SET\") {\n    const { newPresent } = action;\n\n    if (action.newPresent === present) {\n      return state;\n    }\n\n    return {\n      past: [...past, present],\n      present: newPresent,\n      future: [],\n    };\n  } else if (action.type === \"CLEAR\") {\n    return {\n      ...initialUseHistoryStateState,\n      present: action.initialPresent,\n    };\n  } else {\n    throw new Error(\"Unsupported action type\");\n  }\n};\n\nfunction useHistoryState(initialPresent = {}) {\n  const initialPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(initialPresent);\n\n  const [state, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(useHistoryStateReducer, {\n    ...initialUseHistoryStateState,\n    present: initialPresentRef.current,\n  });\n\n  const canUndo = state.past.length !== 0;\n  const canRedo = state.future.length !== 0;\n\n  const undo = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (canUndo) {\n      dispatch({ type: \"UNDO\" });\n    }\n  }, [canUndo]);\n\n  const redo = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (canRedo) {\n      dispatch({ type: \"REDO\" });\n    }\n  }, [canRedo]);\n\n  const set = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (newPresent) => dispatch({ type: \"SET\", newPresent }),\n    []\n  );\n\n  const clear = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    () =>\n      dispatch({ type: \"CLEAR\", initialPresent: initialPresentRef.current }),\n    []\n  );\n\n  return { state: state.present, set, undo, redo, clear, canUndo, canRedo };\n}\n\nfunction useHover() {\n  const [hovering, setHovering] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const previousNode = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  const handleMouseEnter = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setHovering(true);\n  }, []);\n\n  const handleMouseLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setHovering(false);\n  }, []);\n\n  const customRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (node) => {\n      if (previousNode.current?.nodeType === Node.ELEMENT_NODE) {\n        previousNode.current.removeEventListener(\n          \"mouseenter\",\n          handleMouseEnter\n        );\n        previousNode.current.removeEventListener(\n          \"mouseleave\",\n          handleMouseLeave\n        );\n      }\n\n      if (node?.nodeType === Node.ELEMENT_NODE) {\n        node.addEventListener(\"mouseenter\", handleMouseEnter);\n        node.addEventListener(\"mouseleave\", handleMouseLeave);\n      }\n\n      previousNode.current = node;\n    },\n    [handleMouseEnter, handleMouseLeave]\n  );\n\n  return [customRef, hovering];\n}\n\nfunction useIdle(ms = 1000 * 60) {\n  const [idle, setIdle] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let timeoutId;\n\n    const handleTimeout = () => {\n      setIdle(true);\n    };\n\n    const handleEvent = throttle((e) => {\n      setIdle(false);\n\n      window.clearTimeout(timeoutId);\n      timeoutId = window.setTimeout(handleTimeout, ms);\n    }, 500);\n\n    const handleVisibilityChange = () => {\n      if (!document.hidden) {\n        handleEvent();\n      }\n    };\n\n    timeoutId = window.setTimeout(handleTimeout, ms);\n\n    window.addEventListener(\"mousemove\", handleEvent);\n    window.addEventListener(\"mousedown\", handleEvent);\n    window.addEventListener(\"resize\", handleEvent);\n    window.addEventListener(\"keydown\", handleEvent);\n    window.addEventListener(\"touchstart\", handleEvent);\n    window.addEventListener(\"wheel\", handleEvent);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n\n    return () => {\n      window.removeEventListener(\"mousemove\", handleEvent);\n      window.removeEventListener(\"mousedown\", handleEvent);\n      window.removeEventListener(\"resize\", handleEvent);\n      window.removeEventListener(\"keydown\", handleEvent);\n      window.removeEventListener(\"touchstart\", handleEvent);\n      window.removeEventListener(\"wheel\", handleEvent);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      window.clearTimeout(timeoutId);\n    };\n  }, [ms]);\n\n  return idle;\n}\n\nfunction useIntersectionObserver(options = {}) {\n  const { threshold = 1, root = null, rootMargin = \"0px\" } = options;\n  const [entry, setEntry] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n\n  const previousObserver = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  const customRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (node) => {\n      if (previousObserver.current) {\n        previousObserver.current.disconnect();\n        previousObserver.current = null;\n      }\n\n      if (node?.nodeType === Node.ELEMENT_NODE) {\n        const observer = new IntersectionObserver(\n          ([entry]) => {\n            setEntry(entry);\n          },\n          { threshold, root, rootMargin }\n        );\n\n        observer.observe(node);\n        previousObserver.current = observer;\n      }\n    },\n    [threshold, root, rootMargin]\n  );\n\n  return [customRef, entry];\n}\n\nfunction useIsClient() {\n  const [isClient, setIsClient] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  return isClient;\n}\n\nfunction useIsFirstRender() {\n  const renderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n\n  if (renderRef.current === true) {\n    renderRef.current = false;\n    return true;\n  }\n\n  return renderRef.current;\n}\n\nfunction useList(defaultList = []) {\n  const [list, setList] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultList);\n\n  const set = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((l) => {\n    setList(l);\n  }, []);\n\n  const push = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((element) => {\n    setList((l) => [...l, element]);\n  }, []);\n\n  const removeAt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((index) => {\n    setList((l) => [...l.slice(0, index), ...l.slice(index + 1)]);\n  }, []);\n\n  const insertAt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((index, element) => {\n    setList((l) => [...l.slice(0, index), element, ...l.slice(index)]);\n  }, []);\n\n  const updateAt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((index, element) => {\n    setList((l) => l.map((e, i) => (i === index ? element : e)));\n  }, []);\n\n  const clear = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => setList([]), []);\n\n  return [list, { set, push, removeAt, insertAt, updateAt, clear }];\n}\n\nconst setLocalStorageItem = (key, value) => {\n  const stringifiedValue = JSON.stringify(value);\n  window.localStorage.setItem(key, stringifiedValue);\n  dispatchStorageEvent(key, stringifiedValue);\n};\n\nconst removeLocalStorageItem = (key) => {\n  window.localStorage.removeItem(key);\n  dispatchStorageEvent(key, null);\n};\n\nconst getLocalStorageItem = (key) => {\n  return window.localStorage.getItem(key);\n};\n\nconst useLocalStorageSubscribe = (callback) => {\n  window.addEventListener(\"storage\", callback);\n  return () => window.removeEventListener(\"storage\", callback);\n};\n\nconst getLocalStorageServerSnapshot = () => {\n  throw Error(\"useLocalStorage is a client-only hook\");\n};\n\nfunction useLocalStorage(key, initialValue) {\n  const getSnapshot = () => getLocalStorageItem(key);\n\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useLocalStorageSubscribe,\n    getSnapshot,\n    getLocalStorageServerSnapshot\n  );\n\n  const setState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (v) => {\n      try {\n        const nextState = typeof v === \"function\" ? v(JSON.parse(store)) : v;\n\n        if (nextState === undefined || nextState === null) {\n          removeLocalStorageItem(key);\n        } else {\n          setLocalStorageItem(key, nextState);\n        }\n      } catch (e) {\n        console.warn(e);\n      }\n    },\n    [key, store]\n  );\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (\n      getLocalStorageItem(key) === null &&\n      typeof initialValue !== \"undefined\"\n    ) {\n      setLocalStorageItem(key, initialValue);\n    }\n  }, [key, initialValue]);\n\n  return [store ? JSON.parse(store) : initialValue, setState];\n}\n\nfunction useLockBodyScroll() {\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const originalStyle = window.getComputedStyle(document.body).overflow;\n    document.body.style.overflow = \"hidden\";\n    return () => {\n      document.body.style.overflow = originalStyle;\n    };\n  }, []);\n}\n\nfunction useLongPress(callback, options = {}) {\n  const { threshold = 400, onStart, onFinish, onCancel } = options;\n  const isLongPressActive = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const isPressed = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const timerId = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (typeof callback !== \"function\") {\n      return {};\n    }\n\n    const start = (event) => {\n      if (!isMouseEvent(event) && !isTouchEvent(event)) return;\n\n      if (onStart) {\n        onStart(event);\n      }\n\n      isPressed.current = true;\n      timerId.current = setTimeout(() => {\n        callback(event);\n        isLongPressActive.current = true;\n      }, threshold);\n    };\n\n    const cancel = (event) => {\n      if (!isMouseEvent(event) && !isTouchEvent(event)) return;\n\n      if (isLongPressActive.current) {\n        if (onFinish) {\n          onFinish(event);\n        }\n      } else if (isPressed.current) {\n        if (onCancel) {\n          onCancel(event);\n        }\n      }\n\n      isLongPressActive.current = false;\n      isPressed.current = false;\n\n      if (timerId.current) {\n        window.clearTimeout(timerId.current);\n      }\n    };\n\n    const mouseHandlers = {\n      onMouseDown: start,\n      onMouseUp: cancel,\n      onMouseLeave: cancel,\n    };\n\n    const touchHandlers = {\n      onTouchStart: start,\n      onTouchEnd: cancel,\n    };\n\n    return {\n      ...mouseHandlers,\n      ...touchHandlers,\n    };\n  }, [callback, threshold, onCancel, onFinish, onStart]);\n}\n\nfunction useMap(initialState) {\n  const mapRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new Map(initialState));\n  const [, reRender] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((x) => x + 1, 0);\n\n  mapRef.current.set = (...args) => {\n    Map.prototype.set.apply(mapRef.current, args);\n    reRender();\n    return mapRef.current;\n  };\n\n  mapRef.current.clear = (...args) => {\n    Map.prototype.clear.apply(mapRef.current, args);\n    reRender();\n  };\n\n  mapRef.current.delete = (...args) => {\n    const res = Map.prototype.delete.apply(mapRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  return mapRef.current;\n}\n\nfunction useMeasure() {\n  const [dimensions, setDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    width: null,\n    height: null,\n  });\n\n  const previousObserver = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  const customRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node) => {\n    if (previousObserver.current) {\n      previousObserver.current.disconnect();\n      previousObserver.current = null;\n    }\n\n    if (node?.nodeType === Node.ELEMENT_NODE) {\n      const observer = new ResizeObserver(([entry]) => {\n        if (entry && entry.borderBoxSize) {\n          const { inlineSize: width, blockSize: height } =\n            entry.borderBoxSize[0];\n\n          setDimensions({ width, height });\n        }\n      });\n\n      observer.observe(node);\n      previousObserver.current = observer;\n    }\n  }, []);\n\n  return [customRef, dimensions];\n}\n\nfunction useMediaQuery(query) {\n  const subscribe = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (callback) => {\n      const matchMedia = window.matchMedia(query);\n\n      matchMedia.addEventListener(\"change\", callback);\n      return () => {\n        matchMedia.removeEventListener(\"change\", callback);\n      };\n    },\n    [query]\n  );\n\n  const getSnapshot = () => {\n    return window.matchMedia(query).matches;\n  };\n\n  const getServerSnapshot = () => {\n    throw Error(\"useMediaQuery is a client-only hook\");\n  };\n\n  return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n}\n\nfunction useMouse() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    x: 0,\n    y: 0,\n    elementX: 0,\n    elementY: 0,\n    elementPositionX: 0,\n    elementPositionY: 0,\n  });\n\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleMouseMove = (event) => {\n      let newState = {\n        x: event.pageX,\n        y: event.pageY,\n      };\n\n      if (ref.current?.nodeType === Node.ELEMENT_NODE) {\n        const { left, top } = ref.current.getBoundingClientRect();\n        const elementPositionX = left + window.scrollX;\n        const elementPositionY = top + window.scrollY;\n        const elementX = event.pageX - elementPositionX;\n        const elementY = event.pageY - elementPositionY;\n\n        newState.elementX = elementX;\n        newState.elementY = elementY;\n        newState.elementPositionX = elementPositionX;\n        newState.elementPositionY = elementPositionY;\n      }\n\n      setState((s) => {\n        return {\n          ...s,\n          ...newState,\n        };\n      });\n    };\n\n    document.addEventListener(\"mousemove\", handleMouseMove);\n\n    return () => {\n      document.removeEventListener(\"mousemove\", handleMouseMove);\n    };\n  }, []);\n\n  return [state, ref];\n}\n\nconst getConnection = () => {\n  return (\n    navigator?.connection ||\n    navigator?.mozConnection ||\n    navigator?.webkitConnection\n  );\n};\n\nconst useNetworkStateSubscribe = (callback) => {\n  window.addEventListener(\"online\", callback, { passive: true });\n  window.addEventListener(\"offline\", callback, { passive: true });\n\n  const connection = getConnection();\n\n  if (connection) {\n    connection.addEventListener(\"change\", callback, { passive: true });\n  }\n\n  return () => {\n    window.removeEventListener(\"online\", callback);\n    window.removeEventListener(\"offline\", callback);\n\n    if (connection) {\n      connection.removeEventListener(\"change\", callback);\n    }\n  };\n};\n\nconst getNetworkStateServerSnapshot = () => {\n  throw Error(\"useNetworkState is a client-only hook\");\n};\n\nfunction useNetworkState() {\n  const cache = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n\n  const getSnapshot = () => {\n    const online = navigator.onLine;\n    const connection = getConnection();\n\n    const nextState = {\n      online,\n      downlink: connection?.downlink,\n      downlinkMax: connection?.downlinkMax,\n      effectiveType: connection?.effectiveType,\n      rtt: connection?.rtt,\n      saveData: connection?.saveData,\n      type: connection?.type,\n    };\n\n    if (isShallowEqual(cache.current, nextState)) {\n      return cache.current;\n    } else {\n      cache.current = nextState;\n      return nextState;\n    }\n  };\n\n  return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useNetworkStateSubscribe,\n    getSnapshot,\n    getNetworkStateServerSnapshot\n  );\n}\n\nfunction useObjectState(initialValue) {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(initialValue);\n\n  const handleUpdate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((arg) => {\n    if (typeof arg === \"function\") {\n      setState((s) => {\n        const newState = arg(s);\n\n        if (isPlainObject(newState)) {\n          return {\n            ...s,\n            ...newState,\n          };\n        }\n      });\n    }\n\n    if (isPlainObject(arg)) {\n      setState((s) => ({\n        ...s,\n        ...arg,\n      }));\n    }\n  }, []);\n\n  return [state, handleUpdate];\n}\n\nfunction useOrientation() {\n  const [orientation, setOrientation] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    angle: 0,\n    type: \"landscape-primary\",\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleChange = () => {\n      const { angle, type } = window.screen.orientation;\n      setOrientation({\n        angle,\n        type,\n      });\n    };\n\n    const handle_orientationchange = () => {\n      setOrientation({\n        type: \"UNKNOWN\",\n        angle: window.orientation,\n      });\n    };\n\n    if (window.screen?.orientation) {\n      handleChange();\n      window.screen.orientation.addEventListener(\"change\", handleChange);\n    } else {\n      handle_orientationchange();\n      window.addEventListener(\"orientationchange\", handle_orientationchange);\n    }\n\n    return () => {\n      if (window.screen?.orientation) {\n        window.screen.orientation.removeEventListener(\"change\", handleChange);\n      } else {\n        window.removeEventListener(\n          \"orientationchange\",\n          handle_orientationchange\n        );\n      }\n    };\n  }, []);\n\n  return orientation;\n}\n\nconst usePreferredLanguageSubscribe = (cb) => {\n  window.addEventListener(\"languagechange\", cb);\n  return () => window.removeEventListener(\"languagechange\", cb);\n};\n\nconst getPreferredLanguageSnapshot = () => {\n  return navigator.language;\n};\n\nconst getPreferredLanguageServerSnapshot = () => {\n  throw Error(\"usePreferredLanguage is a client-only hook\");\n};\n\nfunction usePreferredLanguage() {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    usePreferredLanguageSubscribe,\n    getPreferredLanguageSnapshot,\n    getPreferredLanguageServerSnapshot\n  );\n}\n\nfunction usePrevious(value) {\n  const [current, setCurrent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(value);\n  const [previous, setPrevious] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n\n  if (value !== current) {\n    setPrevious(current);\n    setCurrent(value);\n  }\n\n  return previous;\n}\n\nfunction useQueue(initialValue = []) {\n  const [queue, setQueue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(initialValue);\n\n  const add = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((element) => {\n    setQueue((q) => [...q, element]);\n  }, []);\n\n  const remove = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    let removedElement;\n\n    setQueue(([first, ...q]) => {\n      removedElement = first;\n      return q;\n    });\n\n    return removedElement;\n  }, []);\n\n  const clear = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setQueue([]);\n  }, []);\n\n  return {\n    add,\n    remove,\n    clear,\n    first: queue[0],\n    last: queue[queue.length - 1],\n    size: queue.length,\n    queue,\n  };\n}\n\nfunction useRenderCount() {\n  const count = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n\n  count.current++;\n\n  return count.current;\n}\n\nfunction useRenderInfo(name = \"Unknown\") {\n  const count = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const lastRender = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const now = Date.now();\n\n  count.current++;\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    lastRender.current = Date.now();\n  });\n\n  const sinceLastRender = lastRender.current ? now - lastRender.current : 0;\n\n  if (true) {\n    const info = {\n      name,\n      renders: count.current,\n      sinceLastRender,\n      timestamp: now,\n    };\n\n    console.log(info);\n\n    return info;\n  }\n}\n\nfunction useScript(src, options = {}) {\n  const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"loading\");\n  const optionsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(options);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let script = document.querySelector(`script[src=\"${src}\"]`);\n\n    const domStatus = script?.getAttribute(\"data-status\");\n    if (domStatus) {\n      setStatus(domStatus);\n      return;\n    }\n\n    if (script === null) {\n      script = document.createElement(\"script\");\n      script.src = src;\n      script.async = true;\n      script.setAttribute(\"data-status\", \"loading\");\n      document.body.appendChild(script);\n\n      const handleScriptLoad = () => {\n        script.setAttribute(\"data-status\", \"ready\");\n        setStatus(\"ready\");\n        removeEventListeners();\n      };\n\n      const handleScriptError = () => {\n        script.setAttribute(\"data-status\", \"error\");\n        setStatus(\"error\");\n        removeEventListeners();\n      };\n\n      const removeEventListeners = () => {\n        script.removeEventListener(\"load\", handleScriptLoad);\n        script.removeEventListener(\"error\", handleScriptError);\n      };\n\n      script.addEventListener(\"load\", handleScriptLoad);\n      script.addEventListener(\"error\", handleScriptError);\n\n      const removeOnUnmount = optionsRef.current.removeOnUnmount;\n\n      return () => {\n        if (removeOnUnmount === true) {\n          script.remove();\n          removeEventListeners();\n        }\n      };\n    } else {\n      setStatus(\"unknown\");\n    }\n  }, [src]);\n\n  return status;\n}\n\nconst setSessionStorageItem = (key, value) => {\n  const stringifiedValue = JSON.stringify(value);\n  window.sessionStorage.setItem(key, stringifiedValue);\n  dispatchStorageEvent(key, stringifiedValue);\n};\n\nconst removeSessionStorageItem = (key) => {\n  window.sessionStorage.removeItem(key);\n  dispatchStorageEvent(key, null);\n};\n\nconst getSessionStorageItem = (key) => {\n  return window.sessionStorage.getItem(key);\n};\n\nconst useSessionStorageSubscribe = (callback) => {\n  window.addEventListener(\"storage\", callback);\n  return () => window.removeEventListener(\"storage\", callback);\n};\n\nconst getSessionStorageServerSnapshot = () => {\n  throw Error(\"useSessionStorage is a client-only hook\");\n};\n\nfunction useSessionStorage(key, initialValue) {\n  const getSnapshot = () => getSessionStorageItem(key);\n\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useSessionStorageSubscribe,\n    getSnapshot,\n    getSessionStorageServerSnapshot\n  );\n\n  const setState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (v) => {\n      try {\n        const nextState = typeof v === \"function\" ? v(JSON.parse(store)) : v;\n\n        if (nextState === undefined || nextState === null) {\n          removeSessionStorageItem(key);\n        } else {\n          setSessionStorageItem(key, nextState);\n        }\n      } catch (e) {\n        console.warn(e);\n      }\n    },\n    [key, store]\n  );\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (\n      getSessionStorageItem(key) === null &&\n      typeof initialValue !== \"undefined\"\n    ) {\n      setSessionStorageItem(key, initialValue);\n    }\n  }, [key, initialValue]);\n\n  return [store ? JSON.parse(store) : initialValue, setState];\n}\n\nfunction useSet(values) {\n  const setRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new Set(values));\n  const [, reRender] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((x) => x + 1, 0);\n\n  setRef.current.add = (...args) => {\n    const res = Set.prototype.add.apply(setRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  setRef.current.clear = (...args) => {\n    Set.prototype.clear.apply(setRef.current, args);\n    reRender();\n  };\n\n  setRef.current.delete = (...args) => {\n    const res = Set.prototype.delete.apply(setRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  return setRef.current;\n}\n\nfunction useThrottle(value, interval = 500) {\n  const [throttledValue, setThrottledValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(value);\n  const lastUpdated = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const now = Date.now();\n\n    if (lastUpdated.current && now >= lastUpdated.current + interval) {\n      lastUpdated.current = now;\n      setThrottledValue(value);\n    } else {\n      const id = window.setTimeout(() => {\n        lastUpdated.current = now;\n        setThrottledValue(value);\n      }, interval);\n\n      return () => window.clearTimeout(id);\n    }\n  }, [value, interval]);\n\n  return throttledValue;\n}\n\nfunction useToggle(initialValue) {\n  const [on, setOn] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => {\n    if (typeof initialValue === \"boolean\") {\n      return initialValue;\n    }\n\n    return Boolean(initialValue);\n  });\n\n  const handleToggle = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((value) => {\n    if (typeof value === \"boolean\") {\n      return setOn(value);\n    }\n\n    return setOn((v) => !v);\n  }, []);\n\n  return [on, handleToggle];\n}\n\nconst useVisibilityChangeSubscribe = (callback) => {\n  document.addEventListener(\"visibilitychange\", callback);\n\n  return () => {\n    document.removeEventListener(\"visibilitychange\", callback);\n  };\n};\n\nconst getVisibilityChangeSnapshot = () => {\n  return document.visibilityState;\n};\n\nconst getVisibilityChangeServerSnapshot = () => {\n  throw Error(\"useVisibilityChange is a client-only hook\");\n};\n\nfunction useVisibilityChange() {\n  const visibilityState = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useVisibilityChangeSubscribe,\n    getVisibilityChangeSnapshot,\n    getVisibilityChangeServerSnapshot\n  );\n\n  return visibilityState === \"visible\";\n}\n\nfunction useWindowScroll() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    x: null,\n    y: null,\n  });\n\n  const scrollTo = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args) => {\n    if (typeof args[0] === \"object\") {\n      window.scrollTo(args[0]);\n    } else if (typeof args[0] === \"number\" && typeof args[1] === \"number\") {\n      window.scrollTo(args[0], args[1]);\n    } else {\n      throw new Error(\n        `Invalid arguments passed to scrollTo. See here for more info. https://developer.mozilla.org/en-US/docs/Web/API/Window/scrollTo`\n      );\n    }\n  }, []);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleScroll = () => {\n      setState({ x: window.scrollX, y: window.scrollY });\n    };\n\n    handleScroll();\n    window.addEventListener(\"scroll\", handleScroll);\n\n    return () => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n\n  return [state, scrollTo];\n}\n\nfunction useWindowSize() {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    width: null,\n    height: null,\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleResize = () => {\n      setSize({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      });\n    };\n\n    handleResize();\n    window.addEventListener(\"resize\", handleResize);\n\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n\n  return size;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uidotdev/usehooks/index.js\n");

/***/ })

};
;