{"version": 3, "file": "create-virtual-recharge.dto.js", "sourceRoot": "/", "sources": ["modules/recharge/dto/create-virtual-recharge.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAOyB;AAEzB,IAAY,YAIX;AAJD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,+BAAe,CAAA;IACf,qCAAqB,CAAA;AACvB,CAAC,EAJW,YAAY,4BAAZ,YAAY,QAIvB;AAED,MAAa,wBAAwB;CAcpC;AAdD,4DAcC;AAXC;IAFC,IAAA,wBAAM,GAAE;IACR,IAAA,0BAAQ,GAAE;;2DACO;AAGlB;IADC,IAAA,0BAAQ,GAAE;;wDACI;AAGf;IADC,IAAA,wBAAM,EAAC,YAAY,CAAC;;sDACF;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;sDACD", "sourcesContent": ["import {\r\n  IsString,\r\n  <PERSON><PERSON><PERSON>ber,\r\n  Is<PERSON>ptional,\r\n  IsEnum,\r\n  IsUUID,\r\n  IsDateString,\r\n} from 'class-validator';\r\n\r\nexport enum RechargeType {\r\n  CREDIT = 'CREDIT',\r\n  DEBIT = 'DEBIT',\r\n  RECHARGE = 'RECHARGE',\r\n}\r\n\r\nexport class CreateVirtualRechargeDto {\r\n  @IsUUID()\r\n  @IsString()\r\n  accountId: string;\r\n\r\n  @IsNumber()\r\n  amount: number;\r\n\r\n  @IsEnum(RechargeType)\r\n  type: RechargeType;\r\n\r\n  @IsOptional()\r\n  @IsDateString()\r\n  date?: string;\r\n}\r\n"]}