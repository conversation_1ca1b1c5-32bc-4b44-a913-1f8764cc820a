import { RequestWebhookController } from '@/presentation/http/controllers/webhook-email.controller';
import { makeWebhookEmailUseCase } from '../usecases/webhook-email.factory';
import { WebhookEmailValidator } from '@/main/validators/zod/webhook-email.validator';

export function makeWebhookEmailController() {
  const useCase = makeWebhookEmailUseCase();
  const validator = new WebhookEmailValidator();
  const controller = new RequestWebhookController(useCase, validator);

  return controller;
}
