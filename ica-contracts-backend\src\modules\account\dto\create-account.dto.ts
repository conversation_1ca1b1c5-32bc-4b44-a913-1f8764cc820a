import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsDefined,
  IsEmail,
  IsEnum,
  IsObject,
  IsOptional,
  IsPhoneNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { RolesEnum } from 'src/shared/enums/roles.enum';

class Address {
  @IsDefined()
  @IsString()
  cep: string;

  @IsDefined()
  @IsString()
  city: string;

  @IsDefined()
  @IsString()
  state: string;

  @IsDefined()
  @IsString()
  neighborhood: string;

  @IsDefined()
  @IsString()
  street: string;

  @IsOptional()
  @IsString()
  complement?: string;

  @IsDefined()
  @IsString()
  number: string;
}

export class CreateAccountDto {
  @IsDefined()
  @IsDateString()
  birthDate: Date;

  @IsDefined()
  @IsString()
  @IsEnum(RolesEnum)
  role: RolesEnum;

  @IsDefined()
  @IsString()
  socialName: string;

  @IsDefined()
  @IsBoolean()
  isTaxable: boolean;

  @IsDefined()
  @IsString()
  fullName: string;

  @IsDefined()
  @IsString()
  cpf: string;

  @IsDefined()
  @IsString()
  @IsEmail()
  email: string;

  @IsDefined()
  @IsString()
  @IsPhoneNumber()
  phoneNumber: string;

  @IsDefined()
  @IsString()
  motherName: string;

  @IsDefined()
  @IsBoolean()
  pep: boolean;

  @IsOptional()
  @IsString()
  password?: string;

  @IsDefined()
  @IsObject()
  @ValidateNested()
  @Type(() => Address)
  address: Address;
}
