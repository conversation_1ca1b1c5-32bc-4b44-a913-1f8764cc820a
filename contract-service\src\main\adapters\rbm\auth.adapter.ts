import type { HttpClient } from '@/application/interfaces/http-client';
import { type Either, left, right } from '@/domain/shared/either';
import env from '@/main/config/env';
import type {
  IAuthRbm,
  IAuthRbmResponse,
  IAuthResult,
} from './interfaces/auth';

export interface AuthIntegrationRequest {
  email?: string;
  senha: string;
  login?: string;
  hashIntegracao: string;
}

export class AuthRbm implements IAuthRbm {
  private readonly apiUrl: string;
  private readonly auth: AuthIntegrationRequest;

  constructor() {
    this.apiUrl = `${env.API_ASSINATURA_URL}/v2/auth`;

    if (env.NODE_ENV === 'production') {
      this.auth = {
        senha: env.ASSINATURA_SENHA,
        login: env.ASSINATURA_LOGIN,
        hashIntegracao: env.ASSINATURA_HASH_INTEGRACAO,
      };
    } else {
      this.auth = {
        email: env.ASSINATURA_EMAIL,
        senha: env.ASSINATURA_SENHA,
        hashIntegracao: env.ASSINATURA_HASH_INTEGRACAO,
      };
    }
  }

  async authenticateIntegration(
    httpClient: HttpClient
  ): Promise<Either<Error, IAuthResult>> {
    const response = await httpClient.post<
      AuthIntegrationRequest,
      IAuthRbmResponse
    >(this.apiUrl, this.auth, {
      headers: { 'Content-Type': 'application/json' },
    });

    const authResponse = response.data;

    if (authResponse.erro) {
      return left(
        new Error(authResponse.message || 'Erro ao autenticar integração.')
      );
    }

    if (!authResponse.jwt) {
      return left(new Error('JWT não encontrado na resposta.'));
    }

    return right({
      token: authResponse.jwt.token,
      refreshToken: authResponse.jwt.refreshToken,
    });
  }
}
