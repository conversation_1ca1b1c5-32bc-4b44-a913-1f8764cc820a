{"version": 3, "file": "create-broker.service.js", "sourceRoot": "/", "sources": ["modules/broker/services/create-broker.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAKwB;AACxB,6CAAmD;AACnD,oFAAqF;AACrF,qHAA0G;AAC1G,yFAAgF;AAChF,uFAA8E;AAC9E,qCAAiD;AAK1C,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEmB,eAAwC,EAExC,cAAsC,EAEtC,2BAAgE,EAEhE,kBAAsC,EACtC,UAAsB;QAPtB,oBAAe,GAAf,eAAe,CAAyB;QAExC,mBAAc,GAAd,cAAc,CAAwB;QAEtC,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAsB;QAClC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACzD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACtD,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAGpE,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,oDAAuB,EAAE,iBAAiB,CAAC,CAAC;YAIrF,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;SAC1B,CAAC,CAAC;QACH,IAAI,CAAC,IAAI;YAAE,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAChD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YAC/C,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;SACzB,CAAC,CAAC;QACH,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,0BAAiB,CAAC,6BAA6B,CAAC,CAAC;QACvE,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,6BAA6B,CACzC,KAAkB,EAClB,IAAgB;QAEhB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACtE,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;SACvB,CAAC,CAAC;QACH,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,0BAAiB,CAAC,yCAAyC,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAEO,uBAAuB,CAC7B,KAAkB,EAClB,IAAgB;QAEhB,MAAM,iBAAiB,GAAG,IAAI,oDAAuB,EAAE,CAAC;QACxD,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC;QAChC,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC;QAC9B,iBAAiB,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC;QACrC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QAGnC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAErC,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,gBAAgB,CAAC,KAAkB;QACzC,MAAM,SAAS,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QAC/B,OAAO,SAAS,CAAC,QAAQ,CAAC;QAC1B,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AAlFY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,wBAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,eAAM,EAAC,yCAAkB,CAAC,CAAA;qCALO,oBAAU;QAEX,oBAAU;QAEG,oBAAU;QAEnB,yCAAkB;QAC1B,oBAAU;GAV9B,mBAAmB,CAkF/B", "sourcesContent": ["import {\r\n  ConflictException,\r\n  Inject,\r\n  Injectable,\r\n  NotFoundException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { CreateOwnerService } from 'src/modules/owner/services/create-owner.service';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';\r\nimport { RoleEntity } from 'src/shared/database/typeorm/entities/role.entity';\r\nimport { Repository, DataSource } from 'typeorm';\r\n\r\nimport { CreateBrokerDto } from '../dto/create-broker.dto';\r\n\r\n@Injectable()\r\nexport class CreateBrokerService {\r\n  constructor(\r\n    @InjectRepository(OwnerEntity)\r\n    private readonly ownerRepository: Repository<OwnerEntity>,\r\n    @InjectRepository(RoleEntity)\r\n    private readonly roleRepository: Repository<RoleEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @Inject(CreateOwnerService)\r\n    private readonly createOwnerService: CreateOwnerService,\r\n    private readonly dataSource: DataSource,\r\n  ) {}\r\n\r\n  async perform(input: CreateBrokerDto): Promise<OwnerEntity> {\r\n    return await this.dataSource.transaction(async (manager) => {\r\n      const role = await this.findRoleByName('broker');\r\n      await this.createOwnerService.perform(input);\r\n      const owner = await this.findOwnerByDocument(input.document);\r\n      await this.ensureUniqueOwnerRoleRelation(owner, role);\r\n      const ownerRoleRelation = this.createOwnerRoleRelation(owner, role);\r\n\r\n      // Usar o manager da transação para salvar\r\n      const savedRelation = await manager.save(OwnerRoleRelationEntity, ownerRoleRelation);\r\n\r\n\r\n\r\n      return this.cleanOwnerEntity(owner);\r\n    });\r\n  }\r\n\r\n  private async findRoleByName(roleName: string): Promise<RoleEntity> {\r\n    const role = await this.roleRepository.findOne({\r\n      where: { name: roleName },\r\n    });\r\n    if (!role) throw new NotFoundException('Role não encontrada.');\r\n    return role;\r\n  }\r\n\r\n  private async findOwnerByDocument(document: string): Promise<OwnerEntity> {\r\n    const owner = await this.ownerRepository.findOne({\r\n      where: { cpf: document },\r\n    });\r\n    if (!owner) throw new NotFoundException('Proprietário não encontrado');\r\n    return owner;\r\n  }\r\n\r\n  private async ensureUniqueOwnerRoleRelation(\r\n    owner: OwnerEntity,\r\n    role: RoleEntity,\r\n  ): Promise<void> {\r\n    const existingRelation = await this.ownerRoleRelationRepository.findOne({\r\n      where: { owner, role },\r\n    });\r\n    if (existingRelation) {\r\n      throw new ConflictException('Proprietário já possui a role de broker');\r\n    }\r\n  }\r\n\r\n  private createOwnerRoleRelation(\r\n    owner: OwnerEntity,\r\n    role: RoleEntity,\r\n  ): OwnerRoleRelationEntity {\r\n    const ownerRoleRelation = new OwnerRoleRelationEntity();\r\n    ownerRoleRelation.owner = owner;\r\n    ownerRoleRelation.role = role;\r\n    ownerRoleRelation.ownerId = owner.id;\r\n    ownerRoleRelation.roleId = role.id;\r\n\r\n    // Log para debug\r\n    console.log('Creating broker relation:');\r\n    console.log('Owner ID:', owner.id);\r\n    console.log('Role ID:', role.id);\r\n    console.log('Role name:', role.name);\r\n\r\n    return ownerRoleRelation;\r\n  }\r\n\r\n  private cleanOwnerEntity(owner: OwnerEntity): OwnerEntity {\r\n    const ownerCopy = { ...owner };\r\n    delete ownerCopy.password;\r\n    return ownerCopy;\r\n  }\r\n}\r\n"]}