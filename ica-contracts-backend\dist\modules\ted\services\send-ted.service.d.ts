import { TedCelcoinService } from 'src/apis/celcoin/services/ted-celcoin.service';
import { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';
import { TwoFactorAuthService } from 'src/modules/two-factor-auth/two-factor-auth.service';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { SendTedDto } from '../dto/send-ted.dto';
import { ISendTedResponse } from '../response/send-ted.response';
export declare class SendTedService {
    private accountRepo;
    private transactionRepo;
    private readonly accountLimitRepo;
    private celcoinService;
    private getLimit;
    private readonly twoFactorAuthService;
    constructor(accountRepo: Repository<AccountEntity>, transactionRepo: Repository<TransactionEntity>, accountLimitRepo: Repository<AccountTransferLimitEntity>, celcoinService: TedCelcoinService, getLimit: GetAccountLimitService, twoFactorAuthService: TwoFactorAuthService);
    perform(data: SendTedDto, id: string, twoFactorToken?: string): Promise<ISendTedResponse>;
    private isValidTransaction;
}
