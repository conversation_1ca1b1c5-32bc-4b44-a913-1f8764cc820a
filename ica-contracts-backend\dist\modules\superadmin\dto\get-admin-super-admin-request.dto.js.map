{"version": 3, "file": "get-admin-super-admin-request.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-admin-super-admin-request.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAgE;AAEhE,MAAa,qBAAqB;CAKjC;AALD,sDAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;IACR,IAAA,4BAAU,GAAE;;sDACG", "sourcesContent": ["import { IsDefined, IsNotEmpty, IsUUID } from 'class-validator';\r\n\r\nexport class GetAdminSuperAdminDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  @IsNotEmpty()\r\n  ownerId: string;\r\n}\r\n"]}