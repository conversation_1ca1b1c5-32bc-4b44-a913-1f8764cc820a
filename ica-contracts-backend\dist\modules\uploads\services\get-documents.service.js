"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetDocumentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const uploads_entity_1 = require("../../../shared/database/typeorm/entities/uploads.entity");
const typeorm_2 = require("typeorm");
let GetDocumentsService = class GetDocumentsService {
    constructor(accountDb, uploadsDb) {
        this.accountDb = accountDb;
        this.uploadsDb = uploadsDb;
    }
    async perform(data) {
        const account = await this.accountDb.findOne({
            where: [
                { id: (0, typeorm_2.Equal)(data.id) },
                { ownerId: (0, typeorm_2.Equal)(data.id) },
                { businessId: (0, typeorm_2.Equal)(data.id) },
            ],
        });
        if (!account)
            throw new common_1.BadRequestException('Nenhuma conta encontrada');
        const upload = await this.uploadsDb.find({
            select: {
                id: true,
                type: true,
                url: true,
            },
            where: {
                businessId: account.businessId,
                ownerId: account.ownerId,
            },
        });
        return upload;
    }
};
exports.GetDocumentsService = GetDocumentsService;
exports.GetDocumentsService = GetDocumentsService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(uploads_entity_1.UploadsEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], GetDocumentsService);
//# sourceMappingURL=get-documents.service.js.map