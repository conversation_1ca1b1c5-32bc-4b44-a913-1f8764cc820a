import { Individual } from '@/domain/entities/parties'
import { type Either, left, right } from '@/domain/shared'
import { Cpf, Email } from '@/domain/value-objects'
import { Address } from '@/domain/value-objects/address.value-object'
import { Phone } from '@/domain/value-objects/phone.value-object'
import type { Prisma } from '@prisma/client'

type OwnerWithAddress = Prisma.ownerGetPayload<{
  include: {
    address: true
  }
}>

export class IndividualMapper {
  static toDomain(raw: OwnerWithAddress): Either<Error, Individual> {
    const cpf = Cpf.create(raw.cpf)
    const email = Email.create(raw.email)
    const phone = Phone.create(raw.phone)

    const rawAddress = raw.address.at(0)

    let address = Address.create('', '', '', '', '', '', '')

    if (rawAddress) {
      address = Address.create(
        rawAddress.street,
        rawAddress.number,
        rawAddress.city,
        rawAddress.state,
        rawAddress.cep,
        rawAddress.neighborhood,
        rawAddress.complement ?? undefined
      )
    }

    if (cpf.isLeft()) return left(cpf.value)
    if (email.isLeft()) return left(email.value)
    if (phone.isLeft()) return left(phone.value)
    if (address.isLeft()) return left(address.value)

    const individual = Individual.create(raw.id, raw.name, {
      cpf: cpf.value.value,
      email: email.value.value,
      phone: phone.value.value,
      birthDate: raw.dt_birth,
      address: address.value,
      motherName: raw.mother_name,
      rg: raw.rg,
      occupation: raw.occupation,
      nationality: raw.nationality,
      issuingAgency: raw.issuingAgency,
    })

    if (individual.isLeft()) {
      return left(individual.value)
    }

    return right(individual.value)
  }
}
