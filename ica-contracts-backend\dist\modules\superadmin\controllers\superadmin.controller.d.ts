import { GetAdvisorInvestorsService } from 'src/modules/advisor/services/get-advisor-investors.service';
import { EditBrokerDto } from 'src/modules/broker/dto/edit-broker.dto';
import { GetBrokerAdvisorsService } from 'src/modules/broker/services/get-broker-advisors.service';
import { GetBrokerInvestorService } from 'src/modules/broker/services/get-broker-investors.service';
import { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { IEditInvestorSuperAdminDto } from 'src/modules/investor/dto/edit-investor-dto';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { GetAllAdvisorsWithActiveContractsQueryDto } from '../dto/get-all-advisors-with-active-contracts/query.dto';
import { GetAllAdvisorsWithActiveContractsResponseDto } from '../dto/get-all-advisors-with-active-contracts/response.dto';
import { GetAllBrokersWithActiveContractsQueryDto } from '../dto/get-all-brokers-with-active-contracts/query.dto';
import { GetAllBrokersWithActiveContractsResponseDto } from '../dto/get-all-brokers-with-active-contracts/response.dto';
import { GetSingleAdvisorsWithActiveContractsQueryDto } from '../dto/get-single-advisor-with-active-contracts/query.dto';
import { GetSingleAdvisorWithActiveContractsResponseDto } from '../dto/get-single-advisor-with-active-contracts/response.dto';
import { GetSingleBrokerWithActiveContractsQueryDto } from '../dto/get-single-broker-with-active-contracts/query.dto';
import { GetSinleBrokerWithActiveContractsResponseDto } from '../dto/get-single-broker-with-active-contracts/response.dto';
import { GetAdminSuperAdminService } from '../services/find-admin-super-admin.service';
import { SuperAdminService } from '../services/find-super-admin.service';
import { GetAllAdvisorsWithActiveContractsService } from '../services/get-all-advisors-with-active-contracts.service';
import { GetAllBrokersWithActiveContractsService } from '../services/get-all-brokers-with-active-contracts.service';
import { GetContractsGrowthChartService } from '../services/get-contracts-growth-chart.service';
import { GetDashboardDataService } from '../services/get-dashboard-data.service';
import { GetSingleAdvisorWithActiveContractsService } from '../services/get-single-advisor-with-active-contracts.service';
import { GetSingleBrokerWithActiveContractsService } from '../services/get-single-broker-with-active-contracts.service';
import { SuperAdminEditAdvisorService } from '../services/super-admin-edit-advisor.service';
import { SuperAdminEditBrokerService } from '../services/super-admin-edit-broker.service';
import { SuperAdminEditInvestorService } from '../services/super-admin-edit-investor.service';
export declare class SuperAdminController {
    private readonly superAdminService;
    private readonly getAdminSuperAdminService;
    private readonly getBrokerAdvisorsService;
    private readonly getBrokerInvestorService;
    private readonly getAdvisorInvestorsService;
    private readonly getAllBrokersWithActiveContractsService;
    private readonly getSingleBrokerWithActiveContractsService;
    private readonly getAllAdvisorsWithActiveContractsService;
    private readonly getSingleAdvisorWithActiveContractsService;
    private readonly getDashboardDataService;
    private readonly superAdminEditBrokerService;
    private readonly superAdminEditAdvisorService;
    private readonly getContractsGrowthChartService;
    private readonly superAdminEditInvestorService;
    constructor(superAdminService: SuperAdminService, getAdminSuperAdminService: GetAdminSuperAdminService, getBrokerAdvisorsService: GetBrokerAdvisorsService, getBrokerInvestorService: GetBrokerInvestorService, getAdvisorInvestorsService: GetAdvisorInvestorsService, getAllBrokersWithActiveContractsService: GetAllBrokersWithActiveContractsService, getSingleBrokerWithActiveContractsService: GetSingleBrokerWithActiveContractsService, getAllAdvisorsWithActiveContractsService: GetAllAdvisorsWithActiveContractsService, getSingleAdvisorWithActiveContractsService: GetSingleAdvisorWithActiveContractsService, getDashboardDataService: GetDashboardDataService, superAdminEditBrokerService: SuperAdminEditBrokerService, superAdminEditAdvisorService: SuperAdminEditAdvisorService, getContractsGrowthChartService: GetContractsGrowthChartService, superAdminEditInvestorService: SuperAdminEditInvestorService);
    getDashboardData(): Promise<{
        distributedIncome: number;
        scpWithdraws: number;
        p2pWithdraws: number;
        numberBrokers: number;
        numberAdvisors: number;
        p2pContractNumber: number;
        scpContractNumber: number;
        p2pContractAmount: number;
        scpContractAmount: number;
        activeQuotes: number;
        shareholder: number;
        activeInvestorsNumber: number;
    }>;
    editBroker(request: IRequestUser, body: EditBrokerDto): Promise<void>;
    editAdvisor(request: IRequestUser, body: EditBrokerDto): Promise<void>;
    editInvestorSuperAdmin(req: any, body: IEditInvestorSuperAdminDto): Promise<void>;
    getAllBrokersWithActiveContracts(query: GetAllBrokersWithActiveContractsQueryDto): Promise<GetAllBrokersWithActiveContractsResponseDto>;
    getSingleBrokerWithActiveContracts(id: string, query: GetSingleBrokerWithActiveContractsQueryDto): Promise<GetSinleBrokerWithActiveContractsResponseDto[] | import("../helpers/pagination-query").IPaginatedResult<{
        investorId: string;
        brokerId: string;
        name: string;
        avatar: string;
        document: string;
        createdAt: Date;
        totalContractAmount: number;
        totalCaptured: number;
    }>>;
    getAllAdvisorsWithActiveContracts(query: GetAllAdvisorsWithActiveContractsQueryDto): Promise<GetAllAdvisorsWithActiveContractsResponseDto>;
    getSingleAdvisorWithActiveContracts(id: string, query: GetSingleAdvisorsWithActiveContractsQueryDto): Promise<GetSingleAdvisorWithActiveContractsResponseDto[] | import("../helpers/pagination-query").IPaginatedResult<{
        investorId: string;
        name: string;
        avatar: string;
        document: string;
        createdAt: Date;
        totalContractAmount: number;
        totalCaptured: number;
    }>>;
    search(request: IRequestUser): Promise<{
        superadmin: {
            superAdminId: string;
            ownerId: string;
            admin: any[];
        };
    }>;
    getAdminSuperAdmin(ownerId: string): Promise<{
        admin: {
            adminId: string;
            ownerId: string;
            broker: any[];
        };
    }>;
    getAdvisorsByBroker(ownerId: string): Promise<{
        ownerId: string;
        name: string;
        cpf: string;
        phone: string;
        email: string;
        createdAt: Date;
    }[]>;
    getInvestorsByAdvisor(ownerId: string): Promise<{
        ownerId: string;
        name: string;
        cpf: string;
        phone: string;
        email: string;
        createdAt: Date;
    }[]>;
    getInvestorsByBroker(ownerId: string): Promise<{
        ownerId: string;
        name: string;
        cpf: string;
        phone: string;
        email: string;
        createdAt: Date;
    }[]>;
    getContractsGrowthChart(period?: PeriodFilter, contractType?: ContractTypeEnum): Promise<{
        period: PeriodFilter;
        data: any[];
    }>;
}
