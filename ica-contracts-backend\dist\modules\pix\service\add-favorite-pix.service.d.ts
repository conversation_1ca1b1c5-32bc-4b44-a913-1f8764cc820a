import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { FavoritePixEntity } from 'src/shared/database/typeorm/entities/favorite-pix.entity';
import { Repository } from 'typeorm';
import { AddFavoritePixDto } from '../dto/add-favorite-pix.dto';
export declare class AddFavoritePixService {
    private accountDb;
    private favoritePixDb;
    constructor(accountDb: Repository<AccountEntity>, favoritePixDb: Repository<FavoritePixEntity>);
    perform(data: AddFavoritePixDto, id: string): Promise<void>;
}
