{"version": 3, "file": "super-admin-edit-broker.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/super-admin-edit-broker.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AAEnD,mFAAoF;AACpF,qHAA0G;AAC1G,iEAAwD;AACxD,qCAAqC;AAG9B,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAEmB,2BAAgE,EAChE,iBAAoC;QADpC,gCAA2B,GAA3B,2BAA2B,CAAqC;QAChE,sBAAiB,GAAjB,iBAAiB,CAAmB;IACpD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAAsB;QAClC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACnE,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO,CAAC,mBAAmB;gBAC/B,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,MAAM;iBACvB;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAA;QACtD,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,EAAE;gBAC9D,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;aACrC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;CACF,CAAA;AAhCY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCACI,oBAAU;QACpB,uCAAiB;GAJ5C,2BAA2B,CAgCvC", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { EditBrokerDto } from 'src/modules/broker/dto/edit-broker.dto';\r\nimport { EditBrokerService } from 'src/modules/broker/services/edit-broker.service';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\n@Injectable()\r\nexport class SuperAdminEditBrokerService {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    private readonly editBrokerService: EditBrokerService,\r\n  ) {}\r\n\r\n  async perform(payload: EditBrokerDto) {\r\n    const brokerProfile = await this.ownerRoleRelationRepository.findOne({\r\n      relations: {\r\n        role: true,\r\n      },\r\n      where: {\r\n        id: payload.ownerRoleRelationId,\r\n        role: {\r\n          name: RolesEnum.BROKER,\r\n        },\r\n      },\r\n    });\r\n\r\n    if (!brokerProfile) {\r\n      throw new NotFoundException('Broker não encontrado')\r\n    }\r\n\r\n    if (payload.rate) {\r\n      await this.ownerRoleRelationRepository.update(brokerProfile.id, {\r\n        partPercent: payload.rate.toString(),\r\n      });\r\n    }\r\n\r\n    return await this.editBrokerService.perform(payload);\r\n  }\r\n}\r\n"]}