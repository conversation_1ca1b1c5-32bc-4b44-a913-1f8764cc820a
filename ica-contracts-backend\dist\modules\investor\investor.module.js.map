{"version": 3, "file": "investor.module.js", "sourceRoot": "/", "sources": ["modules/investor/investor.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA2E;AAC3E,wGAAiG;AACjG,8DAAwD;AAExD,wDAAoD;AACpD,2EAAuE;AACvE,2EAAuE;AACvE,gFAA2E;AAC3E,gFAA2E;AAC3E,oEAAwE;AACxE,4EAAuE;AACvE,oFAA8E;AAC9E,4EAAuE;AACvE,kFAAyE;AACzE,kFAAqE;AAiB9D,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,SAAS,CAAC,QAA4B;QACpC,QAAQ;aACL,KAAK,CAAC,yDAA0B,CAAC;aACjC,SAAS,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,sBAAa,CAAC,GAAG,EAAE,CAAC,CAAC;IAC1E,CAAC;CACF,CAAA;AANY,wCAAc;yBAAd,cAAc;IAf1B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,EAAE,0BAAW,CAAC;QACpC,SAAS,EAAE;YACT,+CAAqB;YACrB,kDAAsB;YACtB,+CAAqB;YACrB,2CAAmB;YACnB,6CAAkB;YAClB,4CAAwB;YACxB,2CAAmB;YACnB,yCAAc;SACf;QACD,WAAW,EAAE,CAAC,wCAAkB,EAAE,wCAAkB,CAAC;QACrD,OAAO,EAAE,CAAC,2CAAmB,CAAC;KAC/B,CAAC;GACW,cAAc,CAM1B", "sourcesContent": ["import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';\r\nimport { ValidateInvestorMiddleware } from 'src/shared/middlewares/validate-investor.middleware';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { OwnerModule } from '../owner/owner.module';\r\nimport { InvestorController } from './controllers/investor.controller';\r\nimport { MovementController } from './controllers/movement.controller';\r\nimport { CreateInvestorService } from './services/create-investor.service';\r\nimport { CreateMovementService } from './services/create-movement.service';\r\nimport { InvestorDashboardService } from './services/dashboard.service';\r\nimport { EditInvestorService } from './services/edit-investor.service';\r\nimport { GetInvestorInfoService } from './services/get-investor-info.service';\r\nimport { GetMovementsService } from './services/get-moviments.service';\r\nimport { GetMovementService } from './services/get-one-moviment.service';\r\nimport { UpdateInvestor } from './services/update-investors.service';\r\n\r\n@Module({\r\n  imports: [SharedModule, OwnerModule],\r\n  providers: [\r\n    CreateInvestorService,\r\n    GetInvestorInfoService,\r\n    CreateMovementService,\r\n    GetMovementsService,\r\n    GetMovementService,\r\n    InvestorDashboardService,\r\n    EditInvestorService,\r\n    UpdateInvestor,\r\n  ],\r\n  controllers: [InvestorController, MovementController],\r\n  exports: [EditInvestorService],\r\n})\r\nexport class InvestorModule {\r\n  configure(consumer: MiddlewareConsumer) {\r\n    consumer\r\n      .apply(ValidateInvestorMiddleware)\r\n      .forRoutes({ path: 'investor/movements', method: RequestMethod.GET });\r\n  }\r\n}\r\n"]}