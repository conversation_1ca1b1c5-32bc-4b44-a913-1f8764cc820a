"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionRechargeService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_transaction_celcoin_service_1 = require("../../../apis/celcoin/services/pix-transaction-celcoin.service");
const create_virtual_transaction_service_1 = require("../../../apis/icainvest-credit/services/create-virtual-transaction.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const recharge_entity_1 = require("../../../shared/database/typeorm/entities/recharge.entity");
const transaction_status_enum_1 = require("../../../shared/enums/transaction-status-enum");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
let TransactionRechargeService = class TransactionRechargeService {
    constructor(accountDb, rechargeDb, transactionPixApi, createTransactionsService) {
        this.accountDb = accountDb;
        this.rechargeDb = rechargeDb;
        this.transactionPixApi = transactionPixApi;
        this.createTransactionsService = createTransactionsService;
    }
    async perform(data) {
        const transferAccount = await this.accountDb.findOne({
            where: {
                id: data.id,
            },
        });
        if (!transferAccount)
            throw new common_1.BadRequestException('Conta não encontrada para recarga.');
        const uuid = (0, uuid_1.v4)();
        const transfer = await this.transactionPixApi.internalTransfer({
            amount: Number(data.amount),
            creditParty: {
                account: transferAccount.number,
            },
            clientRequestId: uuid,
            debitParty: {
                account: '*************',
            },
            description: 'RECARGA',
        });
        const create = this.rechargeDb.create({
            accountId: transferAccount.id,
            value: data.amount,
            status: transaction_status_enum_1.TransactionStatusEnum.PENDENT,
            externalId: transfer.body.id,
        });
        await this.rechargeDb.save(create);
        const externalResponse = await this.createTransactionsService.createTransaction({
            accountId: transferAccount.id,
            amount: Number(data.amount),
            type: 'RECHARGE',
        });
        return externalResponse;
    }
};
exports.TransactionRechargeService = TransactionRechargeService;
exports.TransactionRechargeService = TransactionRechargeService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(recharge_entity_1.RechargeEntity)),
    __param(2, (0, common_1.Inject)(pix_transaction_celcoin_service_1.PixTransactionCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        pix_transaction_celcoin_service_1.PixTransactionCelcoinService,
        create_virtual_transaction_service_1.CreateTransactionsService])
], TransactionRechargeService);
//# sourceMappingURL=transaction-recharge.service.js.map