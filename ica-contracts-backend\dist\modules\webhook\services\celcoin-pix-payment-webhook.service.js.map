{"version": 3, "file": "celcoin-pix-payment-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/celcoin-pix-payment-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,6FAAoF;AACpF,qGAA4F;AAC5F,yGAA8F;AAC9F,2FAAiF;AACjF,mDAA2C;AAC3C,qCAAqC;AAK9B,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IAC1C,YAEU,iBAA4C,EAE5C,qBAAoD;QAFpD,sBAAiB,GAAjB,iBAAiB,CAA2B;QAE5C,0BAAqB,GAArB,qBAAqB,CAA+B;IAC3D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAmC;QAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAE3C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE;aAC5C;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACpC,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,EAAE;YACb,WAAW,EAAE,aAAa;YAC1B,MAAM,EAAE,+CAAqB,CAAC,IAAI;YAClC,IAAI,EAAE,4DAA2B,CAAC,UAAU;YAC5C,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YAC7B,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE;YAClD,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW;YAC/C,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC5C,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE;YAChD,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE;YACjD,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YACjC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACtC,YAAY,EAAE,KAAK,CAAC,eAAe;SACpC,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAxCY,0EAA+B;0CAA/B,+BAA+B;IAD3C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;qCADT,oBAAU;QAEN,oBAAU;GALhC,+BAA+B,CAwC3C", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';\r\nimport { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ICelcoinPixPaymentWebhookDto } from '../dto/celcoin-pix-payment-webhook.dto';\r\n\r\n@Injectable()\r\nexport class CelcoinPixPaymentWebhookService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountRepository: Repository<AccountEntity>,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepository: Repository<TransactionEntity>,\r\n  ) {}\r\n\r\n  async perform(input: ICelcoinPixPaymentWebhookDto) {\r\n    const data = input.body;\r\n    logger.info('dados webhook cashin', input);\r\n\r\n    const account = await this.accountRepository.findOne({\r\n      where: {\r\n        number: data.creditParty.account.toString(),\r\n      },\r\n    });\r\n\r\n    if (!account) {\r\n      return;\r\n    }\r\n\r\n    await this.transactionRepository.save({\r\n      accountId: account.id,\r\n      endToEndId: data.endToEndId,\r\n      code: data.id,\r\n      description: 'RECEIVE_PIX',\r\n      status: TransactionStatusEnum.DONE,\r\n      type: TransactionMovementTypeEnum.PIX_CASHIN,\r\n      value: data.amount.toString(),\r\n      destinyAccount: data.debitParty.account.toString(),\r\n      destinyAccountType: data.debitParty.accountType,\r\n      destinyBank: data.debitParty.bank.toString(),\r\n      destinyBranch: data.debitParty.branch.toString(),\r\n      destinyDocument: data.debitParty.taxId.toString(),\r\n      destinyName: data.debitParty.name,\r\n      transferMetadata: JSON.stringify(data),\r\n      transferDate: input.createTimestamp,\r\n    });\r\n  }\r\n}\r\n"]}