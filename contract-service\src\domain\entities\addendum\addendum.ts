import { AggregateRoot } from '@/domain/shared';
import type { BaseEntityProps } from '@/domain/shared/base-entity';
import type { Money } from '@/domain/value-objects';

export enum AddendumStatus {
  PENDING = 'PENDING',
  FULLY_SIGNED = 'FULLY_SIGNED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
}

interface ContractAddendumProps extends BaseEntityProps {
  yieldRateValue: Money;
  value: Money;
  startDate: Date;
  endDate: Date;
  status: AddendumStatus;
}

export class ContractAddendum extends AggregateRoot<ContractAddendumProps> {
  private constructor(props: ContractAddendumProps, id?: string) {
    super(props, id);
  }

  static create(
    yieldRateValue: Money,
    value: Money,
    startDate: Date,
    endDate: Date,
    status: AddendumStatus = AddendumStatus.PENDING,
    id?: string,
  ): ContractAddendum {
    if (endDate <= startDate) {
      throw new Error('End date must be after start date');
    }

    return new ContractAddendum(
      { value, yieldRateValue, startDate, endDate, status },
      id,
    );
  }

  getValue(): Money {
    return this.props.value;
  }

  getStartDate(): Date {
    return this.props.startDate;
  }

  getEndDate(): Date {
    return this.props.endDate;
  }

  getStatus(): AddendumStatus {
    return this.props.status;
  }

  markAsSigned(): void {
    this.props.status = AddendumStatus.FULLY_SIGNED;
  }

  markAsRejected(): void {
    this.props.status = AddendumStatus.REJECTED;
  }

  markAsCancelled(): void {
    this.props.status = AddendumStatus.CANCELLED;
  }

  isActive(onDate: Date = new Date()): boolean {
    return (
      this.props.status === AddendumStatus.FULLY_SIGNED &&
      onDate >= this.props.startDate &&
      onDate <= this.props.endDate
    );
  }
}
