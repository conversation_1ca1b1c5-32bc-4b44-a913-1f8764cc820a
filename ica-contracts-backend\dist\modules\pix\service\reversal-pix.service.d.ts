import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { ReversalPixDto } from '../dto/reversal-pix.dto';
export declare class ReversalPixService {
    private pixTransactionCelcoin;
    private transactionRepo;
    constructor(pixTransactionCelcoin: PixTransactionCelcoinService, transactionRepo: Repository<TransactionEntity>);
    perform(data: ReversalPixDto): Promise<import("../../../apis/celcoin/responses/reverse-pix-celcoin.response").IReversePixCelcoinResponse>;
}
