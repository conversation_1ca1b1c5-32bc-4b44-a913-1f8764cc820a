{"version": 3, "file": "get-favorite-ted-contact.service.js", "sourceRoot": "/", "sources": ["modules/ted/services/get-favorite-ted-contact.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAmD;AACnD,6CAAmD;AACnD,6FAAoF;AACpF,uGAA6F;AAC7F,qCAA4C;AAE5C,IAAa,4BAA4B,GAAzC,MAAa,4BAA4B;IACvC,YAEU,WAAsC,EAEtC,eAA8C;QAF9C,gBAAW,GAAX,WAAW,CAA2B;QAEtC,oBAAe,GAAf,eAAe,CAA+B;IACrD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,0BAAiB,CACzB,iDAAiD,CAClD,CAAC;QAEJ,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1D,KAAK,EAAE;gBACL,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB;YACD,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;QAEH,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF,CAAA;AA3BY,oEAA4B;uCAA5B,4BAA4B;IAEpC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCADf,oBAAU;QAEN,oBAAU;GAL1B,4BAA4B,CA2BxC", "sourcesContent": ["import { NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { FavoriteTedEntity } from 'src/shared/database/typeorm/entities/favorite-ted.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nexport class GetFavoriteTedContactService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountRepo: Repository<AccountEntity>,\r\n    @InjectRepository(FavoriteTedEntity)\r\n    private favoriteTedRepo: Repository<FavoriteTedEntity>,\r\n  ) {}\r\n\r\n  async execute(id: string): Promise<FavoriteTedEntity[]> {\r\n    const account = await this.accountRepo.findOne({\r\n      where: [{ ownerId: Equal(id) }, { businessId: Equal(id) }],\r\n    });\r\n\r\n    if (!account)\r\n      throw new NotFoundException(\r\n        'Conta não encontrada ou não vinculada ao owner.',\r\n      );\r\n\r\n    const favoriteTedContacts = await this.favoriteTedRepo.find({\r\n      where: {\r\n        accountId: account.id,\r\n      },\r\n      take: 10,\r\n    });\r\n\r\n    return favoriteTedContacts;\r\n  }\r\n}\r\n"]}