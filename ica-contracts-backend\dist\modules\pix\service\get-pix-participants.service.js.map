{"version": 3, "file": "get-pix-participants.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/get-pix-participants.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAKwB;AACxB,oHAAyG;AACzG,mDAA2C;AAKpC,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,YAEmB,cAA4C;QAA5C,mBAAc,GAAd,cAAc,CAA8B;IAC5D,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAA2B;QACvC,IAAI,CAAC;YACH,MAAM,eAAe,GACnB,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAErD,IAAI,CAAC,eAAe;gBAClB,MAAM,IAAI,0BAAiB,CACzB,6CAA6C,IAAI,GAAG,CACrD,CAAC;YAEJ,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,IAAI,4BAAmB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;CACF,CAAA;AArBY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;qCACJ,8DAA4B;GAHpD,yBAAyB,CAqBrC", "sourcesContent": ["import {\r\n  BadRequestException,\r\n  Inject,\r\n  Injectable,\r\n  NotFoundException,\r\n} from '@nestjs/common';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { logger } from 'src/shared/logger';\r\n\r\nimport { GetPixParticipantsDto } from '../dto/get-pix-participants.dto';\r\n\r\n@Injectable()\r\nexport class GetPixParticipantsService {\r\n  constructor(\r\n    @Inject(PixTransactionCelcoinService)\r\n    private readonly celcoinService: PixTransactionCelcoinService,\r\n  ) {}\r\n  async execute(data: GetPixParticipantsDto): Promise<any> {\r\n    try {\r\n      const pixParticipants =\r\n        await this.celcoinService.getPixParticipants(data);\r\n\r\n      if (!pixParticipants)\r\n        throw new NotFoundException(\r\n          `Nenhum participante encontrado. [payload: ${data}]`,\r\n        );\r\n\r\n      return pixParticipants;\r\n    } catch (error) {\r\n      logger.error('GetPixParticipants.execute() -> ', error);\r\n      throw new BadRequestException(error);\r\n    }\r\n  }\r\n}\r\n"]}