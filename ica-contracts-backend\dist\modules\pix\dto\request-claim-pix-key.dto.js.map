{"version": 3, "file": "request-claim-pix-key.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/request-claim-pix-key.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA+D;AAE/D,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,yBAAW,CAAA;IACX,2BAAa,CAAA;IACb,6BAAe,CAAA;IACf,6BAAe,CAAA;AACjB,CAAC,EALW,UAAU,0BAAV,UAAU,QAKrB;AAED,IAAY,SAGX;AAHD,WAAY,SAAS;IACnB,wCAA2B,CAAA;IAC3B,oCAAuB,CAAA;AACzB,CAAC,EAHW,SAAS,yBAAT,SAAS,QAGpB;AAED,MAAa,0BAA0B;CAYtC;AAZD,gEAYC;AATC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACC;AAIZ;IAFC,IAAA,wBAAM,EAAC,UAAU,CAAC;IAClB,IAAA,4BAAU,GAAE;;2DACO;AAIpB;IAFC,IAAA,wBAAM,EAAC,SAAS,CAAC;IACjB,IAAA,4BAAU,GAAE;;6DACQ", "sourcesContent": ["import { IsEnum, IsNotEmpty, IsString } from 'class-validator';\r\n\r\nexport enum PixKeyType {\r\n  CPF = 'CPF',\r\n  CNPJ = 'CNPJ',\r\n  EMAIL = 'EMAIL',\r\n  PHONE = 'PHONE',\r\n}\r\n\r\nexport enum ClaimType {\r\n  PORTABILITY = 'PORTABILITY',\r\n  OWNERSHIP = 'OWNERSHIP',\r\n}\r\n\r\nexport class RequestClaimPixKeyInputDTO {\r\n  @IsNotEmpty()\r\n  @IsString()\r\n  key: string;\r\n\r\n  @IsEnum(PixKeyType)\r\n  @IsNotEmpty()\r\n  typeKey: PixKeyType;\r\n\r\n  @IsEnum(ClaimType)\r\n  @IsNotEmpty()\r\n  claimType: ClaimType;\r\n}\r\n"]}