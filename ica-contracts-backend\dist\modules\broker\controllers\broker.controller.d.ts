import { EditInvestorService } from '../../investor/services/edit-investor.service';
import { CreateBrokerDto } from '../dto/create-broker.dto';
import { EditBrokerDto } from '../dto/edit-broker.dto';
import { CreateBrokerService } from '../services/create-broker.service';
import { EditBrokerService } from '../services/edit-broker.service';
import { GetBrokerAdvisorsService } from '../services/get-broker-advisors.service';
import { GetBrokerInvestorService } from '../services/get-broker-investors.service';
import { GetBrokerService } from '../services/get-broker.service';
import { BrokerDashboardService } from '../services/dashboard.service';
import { ContractsCaptureService } from '../services/contracts-capture.service';
import { ContractsMonthlyCaptureService } from '../services/contracts-monthly-capture.service';
import { GetOneScheduledPaymentForBrokerService } from '../services/get-one-scheduled-payment-for-broker.service';
import { GenerateReportDto } from '../dto/generate-report.dto';
import { GeneratePaymentReportService } from '../services/generate-payment-reports.service';
import { GenerateScheduledPaymentsReportService } from '../services/generate-scheduled-payments-reports.service';
import { ListIncomePaymentScheduledBrokerService } from '../services/list-payment-scheduled.service';
import { ListIncomePaymentScheduledBrokerDto } from '../dto/list-payment-scheduled.request.dto';
import { GenerateContractsReportsService } from '../services/generate-contracts-reports.service';
import { GetBrokerContractsGrowthChartService } from '../services/get-broker-contracts-growth-chart.service';
import { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
import { ListActiveInvestorsDto } from 'src/modules/income-report/dto/list-investors.dto';
import { ListActiveInvestorsBrokerService } from '../services/list-investors.service';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
export declare class BrokerController {
    private readonly createBrokerService;
    private readonly getBrokerAdvisorsService;
    private readonly getBrokerInvestorService;
    private readonly editBrokerService;
    private readonly editInvestorService;
    private readonly getBrokerService;
    private readonly brokerDashboardService;
    private readonly contractsCaptureService;
    private readonly contractsMonthlyCaptureService;
    private readonly getOneScheduledPaymentForBrokerService;
    private readonly generatePaymentReportService;
    private readonly listIncomePaymentScheduledBrokerService;
    private readonly generateScheduledPaymentsReportService;
    private readonly generateContractsReportsService;
    private readonly getBrokerContractsGrowthChartService;
    private readonly listActiveInvestorsBrokerService;
    constructor(createBrokerService: CreateBrokerService, getBrokerAdvisorsService: GetBrokerAdvisorsService, getBrokerInvestorService: GetBrokerInvestorService, editBrokerService: EditBrokerService, editInvestorService: EditInvestorService, getBrokerService: GetBrokerService, brokerDashboardService: BrokerDashboardService, contractsCaptureService: ContractsCaptureService, contractsMonthlyCaptureService: ContractsMonthlyCaptureService, getOneScheduledPaymentForBrokerService: GetOneScheduledPaymentForBrokerService, generatePaymentReportService: GeneratePaymentReportService, listIncomePaymentScheduledBrokerService: ListIncomePaymentScheduledBrokerService, generateScheduledPaymentsReportService: GenerateScheduledPaymentsReportService, generateContractsReportsService: GenerateContractsReportsService, getBrokerContractsGrowthChartService: GetBrokerContractsGrowthChartService, listActiveInvestorsBrokerService: ListActiveInvestorsBrokerService);
    create(createBrokerDto: CreateBrokerDto): Promise<import("../../../shared/database/typeorm/entities/owner.entity").OwnerEntity>;
    getBrokersAdvisors(request: IRequestUser): Promise<{
        ownerId: string;
        name: string;
        cpf: string;
        phone: string;
        email: string;
        createdAt: Date;
    }[]>;
    getBrokersInvestor(request: IRequestUser): Promise<{
        ownerId: string;
        name: string;
        cpf: string;
        phone: string;
        email: string;
        createdAt: Date;
    }[]>;
    editBroker(request: IRequestUser, body: EditBrokerDto): Promise<void>;
    editInvestor(request: IRequestUser, body: EditBrokerDto): Promise<void>;
    dashboard(request: IRequestUser): Promise<{
        distributedIncome: number;
        scpWithdraws: number;
        p2pWithdraws: number;
        p2pContractNumber: number;
        scpContractNumber: number;
        p2pContractAmount: number;
        scpContractAmount: number;
        activeQuotes: number;
        shareholder: number;
        activeInvestorsNumber: number;
    }>;
    findActiveInvestors(request: IRequestUser, filters: ListActiveInvestorsDto): Promise<{
        id: string;
        name: string;
        document: string;
        email: string;
        status: string;
    }[]>;
    contractsCapture(brokerId: string): Promise<{
        contracts: {
            today: number;
            thisWeek: number;
            thisMonth: number;
        };
        investments: {
            today: number;
            thisWeek: number;
            thisMonth: number;
        };
    }>;
    grafics(brokerId: string): Promise<{
        month: string;
        totalInvestment: number;
        totalContracts: number;
    }[]>;
    getOneBroker(brokerId: string): Promise<{
        id: string;
        document: string;
        name: string;
        email: string;
        phoneNumber: string;
        state: string;
        city: string;
        cep: string;
        street: string;
        neighborhood: string;
        addressNumber: string;
        complement: string;
        dtBirth: Date;
        motherName: string;
    }>;
    listPaymentScheduledBroker(brokerId: string, data?: ListIncomePaymentScheduledBrokerDto): Promise<import("../../income-payment/services/list-income-payment-scheduled/list-income-payment-scheduled.service").IncomePaymentScheduledResponse[]>;
    listPaymentScheduled(paymentScheduledId: string, brokerId: string): Promise<import("../../income-payment/services/get-one-scheduled-payment/get-one-scheduled-payment.abstraction.service").IncomePaymentDetails>;
    generatePaymentReport(brokerId: string, data: GenerateReportDto): Promise<import("../../../shared/database/typeorm/entities/files-entity").FileEntity>;
    generateScheduledPaymentReport(brokerId: string, data: GenerateReportDto): Promise<import("../../../shared/database/typeorm/entities/files-entity").FileEntity>;
    generateContractsReport(brokerId: string, data: GenerateReportDto): Promise<import("../../../shared/database/typeorm/entities/files-entity").FileEntity>;
    getContractsGrowthChart(ownerRoleId: string, period?: PeriodFilter, contractType?: ContractTypeEnum): Promise<{
        period: PeriodFilter;
        data: any[];
    }>;
}
