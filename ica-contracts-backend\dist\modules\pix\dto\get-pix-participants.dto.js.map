{"version": 3, "file": "get-pix-participants.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/get-pix-participants.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAuD;AAEvD,MAAa,qBAAqB;CAQjC;AARD,sDAQC;AALC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACG", "sourcesContent": ["import { IsOptional, IsString } from 'class-validator';\r\n\r\nexport class GetPixParticipantsDto {\r\n  @IsOptional()\r\n  @IsString()\r\n  ISPB?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  name?: string;\r\n}\r\n"]}