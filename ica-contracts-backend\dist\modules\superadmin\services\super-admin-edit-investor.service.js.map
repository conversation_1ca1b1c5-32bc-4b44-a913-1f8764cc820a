{"version": 3, "file": "super-admin-edit-investor.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/super-admin-edit-investor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,+GAA+G;AAC/G,uGAA6F;AAC7F,qHAA0G;AAE1G,yFAA0F;AAC1F,6FAAoF;AAG7E,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IACxC,YAEE,2BAAiF,EAEjF,qBAAqE,EAErE,iBAA6D,EAC5C,6BAA4D,EAC5D,mBAAwC;QANxC,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,0BAAqB,GAArB,qBAAqB,CAA+B;QAEpD,sBAAiB,GAAjB,iBAAiB,CAA2B;QAC5C,kCAA6B,GAA7B,6BAA6B,CAA+B;QAC5D,wBAAmB,GAAnB,mBAAmB,CAAqB;QAW3D,eAAU,GAAG,KAAK,EAAE,OAAmC,EAAE,EAAE;YACzD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;gBACjE,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,mBAAmB,EAAE;gBAC1C,SAAS,EAAE;oBACT,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;oBACvC,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;oBAC1C,IAAI,EAAE,IAAI;iBACX;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,0BAAiB,CAAC,gCAAgC,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE;gBAC9C,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI;gBACvB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,aAAa;gBAClC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM;aAC5B,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,uCAAuC;aACjD,CAAC;QACJ,CAAC,CAAC;IAtCC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAAmC,EAAE,MAAc;QAC/D,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;CA+BF,CAAA;AAjDY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCAHc,oBAAU;QAEhB,oBAAU;QAEd,oBAAU;QACE,gEAA6B;QACvC,2CAAmB;GAThD,6BAA6B,CAiDzC", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { GetContractsByInvestorService } from 'src/modules/contract/services/get-contract-by-investor.service';\r\nimport { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { IEditInvestorSuperAdminDto } from 'src/modules/investor/dto/edit-investor-dto';\r\nimport { EditInvestorService } from 'src/modules/investor/services/edit-investor.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\n\r\n@Injectable()\r\nexport class SuperAdminEditInvestorService {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @InjectRepository(PreRegisterEntity)\r\n    private readonly preRegisterRepository: Repository<PreRegisterEntity>,\r\n    @InjectRepository(AccountEntity)\r\n    private readonly accountRepository: Repository<AccountEntity>,\r\n    private readonly getContractsByInvestorService: GetContractsByInvestorService,\r\n    private readonly editInvestorService: EditInvestorService,\r\n  ) {}\r\n\r\n  async perform(payload: IEditInvestorSuperAdminDto, userId: string) {\r\n    if (payload.bank) {\r\n      await this.updateBank(payload);\r\n    }\r\n\r\n    return this.editInvestorService.perform(payload, userId);\r\n  }\r\n\r\n  updateBank = async (payload: IEditInvestorSuperAdminDto) => {\r\n    const userProfile = await this.ownerRoleRelationRepository.findOne({\r\n      where: { id: payload.ownerRoleRelationId },\r\n      relations: {\r\n        owner: { address: true, account: true },\r\n        business: { address: true, account: true },\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (!userProfile) {\r\n      throw new NotFoundException(`Usuario nāo encontrado.`);\r\n    }\r\n\r\n    const account = userProfile.owner.account[0];\r\n    if (!account) {\r\n      throw new NotFoundException(`Conta bancária não encontrada.`);\r\n    }\r\n\r\n    await this.accountRepository.update(account.id, {\r\n      bank: payload.bank.bank,\r\n      number: payload.bank.accountNumber,\r\n      branch: payload.bank.branch,\r\n    });\r\n\r\n    return {\r\n      message: 'Conta bancária atualizada com sucesso',\r\n    };\r\n  };\r\n}\r\n"]}