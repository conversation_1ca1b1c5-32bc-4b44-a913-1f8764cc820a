interface TemplateData {
  calendar_year: number;
  download_link: string;
  email: string;
  name: string;
  telephone: string;
}

export interface EmailPayload {
  to_email: string;
  template_data: TemplateData;
}

export interface IncomeReportEmailPayload {
  from: string;
  body: EmailPayload;
  error_message?: string | null;
  id: string;
  status: string;
  retry?: string;
  external_id?: string;
}

export enum EmailSendStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  SENDING = 'SENDING',
}
