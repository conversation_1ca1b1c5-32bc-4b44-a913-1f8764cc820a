{"version": 3, "file": "reports.controller.js", "sourceRoot": "/", "sources": ["modules/reports/controllers/reports.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,0EAAgE;AAEhE,oEAA+D;AAC/D,+DAA2D;AAKpD,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAGvD,AAAN,KAAK,CAAC,cAAc,CAMlB,WAA8B,EACX,MAAc,EACtB,OAAqB;QAEhC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,4BAAmB,CAAC,4BAA4B,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,EACrE,WAAW,EACX,MAAM,EACN,OAAO,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;QAEF,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC3C,CAAC;CACF,CAAA;AA1BY,8CAAiB;AAItB;IADL,IAAA,aAAI,GAAE;IAEJ,WAAA,IAAA,cAAK,EACJ,IAAI,uBAAc,CAAC;QACjB,YAAY,EAAE,uCAAiB;KAChC,CAAC,CACH,CAAA;IAEA,WAAA,IAAA,gBAAO,EAAC,QAAQ,CAAC,CAAA;IACjB,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCAFG,uCAAiB;;uDAe/B;4BAzBU,iBAAiB;IAF7B,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,mBAAU,EAAC,SAAS,CAAC;qCAEwB,8BAAa;GAD9C,iBAAiB,CA0B7B", "sourcesContent": ["import {\r\n  Controller,\r\n  Post,\r\n  Query,\r\n  UseGuards,\r\n  ValidationPipe,\r\n  Headers,\r\n  BadRequestException,\r\n  Request,\r\n} from '@nestjs/common';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\n\r\nimport { GenerateReportDto } from '../dto/generate-report.dto';\r\nimport { ReportService } from '../services/report.service';\r\nimport type { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\n@UseGuards(JwtAuthGuard)\r\n@Controller('reports')\r\nexport class ReportsController {\r\n  constructor(private readonly reportService: ReportService) {}\r\n\r\n  @Post()\r\n  async generateReport(\r\n    @Query(\r\n      new ValidationPipe({\r\n        expectedType: GenerateReportDto,\r\n      }),\r\n    )\r\n    queryParams: GenerateReportDto,\r\n    @Headers('roleId') roleId: string,\r\n    @Request() request: IRequestUser\r\n  ) {\r\n    if (!roleId) {\r\n      throw new BadRequestException('Id do perfil é obrigatório');\r\n    }\r\n\r\n    const report = await this.reportService.generateReport(queryParams.type,\r\n      queryParams,\r\n      roleId,\r\n      request.user.id\r\n    );\r\n\r\n    return { url: report ? report.url : '' };\r\n  }\r\n}\r\n"]}