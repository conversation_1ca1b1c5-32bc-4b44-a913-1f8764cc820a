interface IBarCodeInfo {
    digitable: string;
}
interface ITag {
    key: string;
    value: string;
}
interface IAuthenticationAPI {
    bloco1: string;
    bloco2: string;
    blocoCompleto: string;
}
interface IReceipt {
    receiptformatted: string;
}
interface IBasePayload {
    entity: 'billpayment';
    createTimestamp: string;
    status: 'CONFIRMED' | 'ERROR';
    body: {
        amount: number;
        id: string;
        clientRequestId: number;
        account: number;
        barCodeInfo: IBarCodeInfo;
        transactionIdAuthorize: number;
    };
}
interface IConfirmedBody extends IBasePayload {
    status: 'CONFIRMED';
    body: {
        transactionId: number;
        status: 'CONFIRMED';
        createDate: string;
        tags: ITag[];
        authentication: number;
        authenticationAPI: IAuthenticationAPI;
        convenant: string;
        isExpired: boolean;
        receipt: IReceipt;
        settleDate: string;
    } & IBasePayload['body'];
}
interface IErrorBody extends IBasePayload {
    status: 'ERROR';
    error: string;
}
export type IBillPaymentCelcoinWebhookDto = IConfirmedBody | IErrorBody;
export {};
