import type { ContractRepository } from '../../domain/repositories/contract-repository';
import type { InvestorRepository } from '../../domain/repositories/investor-repository';
import { ContractIncomeCalculator } from '../../domain/services/contract-income-calculator';

export interface IncomeReportDetailsDTO {
  year: number;
  status: string;
  broker: string;
  advisor: {
    name: string;
  }[];
  investor: {
    name: string;
    email: string;
    document: string;
    address: {
      street: string;
      number: string;
      city: string;
      state: string;
      zipCode: string;
    };
    bankData: {
      bank: string;
      agency: string;
      account: string;
    };
    representative?: {
      name: string;
      document: string;
    };
  };
  contracts: {
    contractId: string;
    baseRate: number;
    totalYield: number;
    addendTotalAmount: number;
    poofPayment: string;
    contractUrl: string;
    payments: {
      month: number;
      date: string;
      grossAmount: number;
      netAmount: number;
    }[];
  }[];
  totalAnnualYield: number;
  incomeReportUrl: string;
}

export class GetIncomeReportDetailsUseCase {
  constructor(
    private readonly contractRepo: ContractRepository,
    private readonly investorRepo: InvestorRepository,
  ) {}

  async execute(
    investorId: string,
    year: number,
  ): Promise<IncomeReportDetailsDTO | null> {
    const investor = await this.investorRepo.findById(investorId);
    if (!investor) return null;

    const contracts = await this.contractRepo.findByInvestorId(investorId);
    const incomeReport = await this.contractRepo.findByIncomeReportId(
      investorId,
      year,
    );

    const email = await this.contractRepo.findIncomeReportEmail(
      investorId,
      year,
    );

    let totalAnnualYield = 0;

    const contractReports = contracts.map((contract) => {
      const calculator = new ContractIncomeCalculator(contract);
      const { totalYield, payments, addendTotalAmount, baseValue } =
        calculator.calculateForYear(year);

      totalAnnualYield += totalYield;

      return {
        contractId: contract.id,
        baseRate: contract.baseRate,
        totalYield,
        baseValue: baseValue,
        addendTotalAmount,
        payments: payments.map((p) => ({
          month: p.month,
          date: p.date.toISOString(),
          grossAmount: p.grossAmount,
          netAmount: p.netAmount,
          addendsAmount: p.addendsAmount,
        })),
        poofPayment: contract.proofPaymentUrl,
        contractUrl: contract.contractUrl,
      };
    });

    return {
      year,
      status: email?.status ? email.status : 'PENDING',
      investor: {
        name: investor.name,
        email: investor.email,
        document: investor.document,
        address: investor.address,
        bankData: contracts[0].investor.bankData,
        representative: investor.representative,
      },
      broker: contracts.at(0)?.broker.name ?? '',
      advisor: contracts.flatMap((contract) =>
        contract.advisors.map((advisor) => ({ name: advisor.name })),
      ),
      contracts: contractReports,
      totalAnnualYield: totalAnnualYield,
      incomeReportUrl: incomeReport?.inportReportUrl ?? '',
    };
  }
}
