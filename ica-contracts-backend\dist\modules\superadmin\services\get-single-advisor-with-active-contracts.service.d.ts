import { Cache } from 'cache-manager';
import { Repository } from 'typeorm';
import { GetSingleAdvisorsWithActiveContractsQueryDto } from '../dto/get-single-advisor-with-active-contracts/query.dto';
import { GetSingleAdvisorWithActiveContractsResponseDto } from '../dto/get-single-advisor-with-active-contracts/response.dto';
import { ContractAdvisorEntity } from 'src/shared/database/typeorm/entities/contract-advisor.entity';
export declare class GetSingleAdvisorWithActiveContractsService {
    private contractsAdvisorRepository;
    private cacheManager;
    constructor(contractsAdvisorRepository: Repository<ContractAdvisorEntity>, cacheManager: Cache);
    perform(advisorId: string, query: GetSingleAdvisorsWithActiveContractsQueryDto): Promise<GetSingleAdvisorWithActiveContractsResponseDto[] | import("../helpers/pagination-query").IPaginatedResult<{
        investorId: string;
        name: string;
        avatar: string;
        document: string;
        createdAt: Date;
        totalContractAmount: number;
        totalCaptured: number;
    }>>;
}
