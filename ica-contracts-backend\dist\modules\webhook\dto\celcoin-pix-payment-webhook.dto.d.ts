export interface ICelcoinPixPaymentWebhookDto {
    entity: 'pix-payment-in';
    createTimestamp: string;
    status: 'CONFIRMED';
    body: {
        amount: number;
        debitParty: {
            bank: number;
            taxId: number;
            name: string;
            branch: number;
            account: number;
            accountType: string;
        };
        id: string;
        endToEndId: string;
        creditParty: {
            bank: number;
            taxId: number;
            branch: number;
            account: number;
            accountType: string;
            key: number;
        };
        initiationType: string;
        transactionType: 'RECEIVEPIX';
        urgency: string;
        paymentType: string;
        transactionIdentification: string;
    };
}
