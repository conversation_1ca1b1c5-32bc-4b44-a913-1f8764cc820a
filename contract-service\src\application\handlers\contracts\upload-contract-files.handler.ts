import type { ContractFilesUploadEvent } from '@/domain/events/contracts/contract-files-upload.event'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import type { IStorageGateway } from '@/domain/gateways/storage.gateway'
import type { IInvestmentContractRepository } from '@/domain/repositories'

export class UploadContractFilesHandler {
  constructor(
    private readonly storage: IStorageGateway,
    private readonly repo: IInvestmentContractRepository,
    private readonly logger: LoggerGateway
  ) {}

  private getExtensionFromMimeType(mimeType: string): string {
    switch (mimeType) {
      case 'application/pdf':
        return 'pdf'
      case 'image/png':
        return 'png'
      case 'image/jpeg':
        return 'jpeg'
      case 'image/jpg':
        return 'jpg'
      default:
        return 'bin'
    }
  }

  async handle(event: ContractFilesUploadEvent): Promise<void> {
    try {
      const { contractId, document, files, companyDocumentFile } = event.payload

      const contractResult = await this.repo.findById(contractId)

      if (contractResult.isLeft()) {
        this.logger.error(
          `Erro ao buscar contrato ${contractId}: ${contractResult.value}`
        )
        throw contractResult.value
      }

      if (!contractResult.value) {
        this.logger.error(`Contrato ${contractId} não encontrado`)
        throw new Error(`Contrato ${contractId} não encontrado`)
      }

      const contract = contractResult.value
      const date = new Date().getTime()
      // Upload dos arquivos
      const [proofOfPayment, personalDocument, contractFile, proofOfResidence] =
        await Promise.all([
          this.storage.uploadFile(
            `contracts/${document}/proof-of-payment_${date}.${this.getExtensionFromMimeType(
              files.proofOfPayment.mimetype
            )}`,
            files.proofOfPayment.buffer,
            files.proofOfPayment.mimetype
          ),
          this.storage.uploadFile(
            `contracts/${document}/personal-document_${date}.${this.getExtensionFromMimeType(
              files.personalDocument.mimetype
            )}`,
            files.personalDocument.buffer,
            files.personalDocument.mimetype
          ),
          this.storage.uploadFile(
            `contracts/${document}/contract_${date}.${this.getExtensionFromMimeType(
              files.contract.mimetype
            )}`,
            files.contract.buffer,
            files.contract.mimetype
          ),
          this.storage.uploadFile(
            `contracts/${document}/proof-of-residence${date}.${this.getExtensionFromMimeType(
              files.proofOfResidence.mimetype
            )}`,
            files.proofOfResidence.buffer,
            files.proofOfResidence.mimetype
          ),
        ])

      if (proofOfPayment.isLeft()) {
        this.logger.error(
          `Erro ao enviar comprovante de pagamento ${contractId}: ${proofOfPayment.value}`
        )
        throw proofOfPayment.value
      }

      if (personalDocument.isLeft()) {
        this.logger.error(
          `Erro ao enviar documento pessoal ${contractId}: ${personalDocument.value}`
        )
        throw personalDocument.value
      }

      if (contractFile.isLeft()) {
        this.logger.error(
          `Erro ao enviar contrato ${contractId}: ${contractFile.value}`
        )
        throw contractFile.value
      }

      if (proofOfResidence.isLeft()) {
        this.logger.error(
          `Erro ao enviar comprovante de residencia ${contractId}: ${contractFile.value}`
        )
        throw contractFile.value
      }

      let companyDocument: string | undefined = undefined

      if (companyDocumentFile) {
        const extension = this.getExtensionFromMimeType(
          files.proofOfResidence.mimetype
        )
        const companyDocumentResult = await this.storage.uploadFile(
          `contracts/${document}/company_document${date}.${extension}`,
          files.proofOfResidence.buffer,
          files.proofOfResidence.mimetype
        )

        if (companyDocumentResult.isRight()) {
          companyDocument = companyDocumentResult.value
        }
      }

      contract.addFiles({
        proofPaymentUrl: proofOfPayment.value,
        contractUrl: contractFile.value,
        proofResidenceUrl: proofOfResidence.value,
        personalDocument: personalDocument.value,
        companyDocument,
      })

      await this.repo.update(contract.id, contract)

      this.logger.info(
        `Arquivos do contrato ${contractId} enviados com sucesso`
      )
    } catch (error) {
      this.logger.error(
        `Erro ao enviar arquivos do contrato ${event.payload.contractId}: ${error}`
      )
      throw error
    }
  }
}
