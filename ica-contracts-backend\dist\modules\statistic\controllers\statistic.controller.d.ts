import { StatisticEntity } from 'src/shared/database/typeorm/entities/statistic.entity';
import { UpdateStatisticDto } from '../dto/update-statistic.dto';
import { UpdateStatisticService } from '../services/update-statistic.service';
export declare class StatisticController {
    private readonly updateStatisticService;
    constructor(updateStatisticService: UpdateStatisticService);
    updateStatistic(updateDto: UpdateStatisticDto): Promise<StatisticEntity[]>;
}
