import { type Either, left, right } from '@/domain/shared/either';
import { ValidationError } from '@/presentation/http/validation/validator';
import type { IValidator } from '@/presentation/http/validation/validator';
import { z } from 'zod';
import { DeleteContractDTO } from '@/application/dtos/contracts/deleted-contract.dto';

const DeleteContractSchema = z.object({
  contractId: z
    .string({ required_error: 'ID do contrato é obrigatório' })
    .uuid({ message: 'ID do contrato deve ser um UUID válido' }),
  role: z
    .string({ required_error: 'Role é obrigatório' })
    .uuid({ message: 'Role deve ser um UUID válido' }),
  reason: z.string({ required_error: 'Motivo é obrigatório' }),
  userId: z
    .string({ required_error: 'ID do usuário é obrigatório' })
    .uuid({ message: 'ID do usuário deve ser um UUID válido' }),
});

export class DeleteContractValidator implements IValidator<DeleteContractDTO> {
  validate(data: unknown): Either<ValidationError, DeleteContractDTO> {
    try {
      const result = DeleteContractSchema.parse(data);
      return right(result as DeleteContractDTO);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return left(new ValidationError(error.errors));
      }
      return left(new ValidationError([{ message: 'Invalid data' }]));
    }
  }
}
