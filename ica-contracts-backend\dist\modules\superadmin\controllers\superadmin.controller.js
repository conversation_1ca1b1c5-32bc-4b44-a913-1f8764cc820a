"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuperAdminController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const get_advisor_investors_service_1 = require("../../advisor/services/get-advisor-investors.service");
const edit_broker_dto_1 = require("../../broker/dto/edit-broker.dto");
const get_broker_advisors_service_1 = require("../../broker/services/get-broker-advisors.service");
const get_broker_investors_service_1 = require("../../broker/services/get-broker-investors.service");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const contract_type_enum_1 = require("../../../shared/enums/contract-type.enum");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const query_dto_1 = require("../dto/get-all-advisors-with-active-contracts/query.dto");
const response_dto_1 = require("../dto/get-all-advisors-with-active-contracts/response.dto");
const query_dto_2 = require("../dto/get-all-brokers-with-active-contracts/query.dto");
const response_dto_2 = require("../dto/get-all-brokers-with-active-contracts/response.dto");
const query_dto_3 = require("../dto/get-single-advisor-with-active-contracts/query.dto");
const response_dto_3 = require("../dto/get-single-advisor-with-active-contracts/response.dto");
const query_dto_4 = require("../dto/get-single-broker-with-active-contracts/query.dto");
const response_dto_4 = require("../dto/get-single-broker-with-active-contracts/response.dto");
const find_admin_super_admin_service_1 = require("../services/find-admin-super-admin.service");
const find_super_admin_service_1 = require("../services/find-super-admin.service");
const get_all_advisors_with_active_contracts_service_1 = require("../services/get-all-advisors-with-active-contracts.service");
const get_all_brokers_with_active_contracts_service_1 = require("../services/get-all-brokers-with-active-contracts.service");
const get_contracts_growth_chart_service_1 = require("../services/get-contracts-growth-chart.service");
const get_dashboard_data_service_1 = require("../services/get-dashboard-data.service");
const get_single_advisor_with_active_contracts_service_1 = require("../services/get-single-advisor-with-active-contracts.service");
const get_single_broker_with_active_contracts_service_1 = require("../services/get-single-broker-with-active-contracts.service");
const super_admin_edit_advisor_service_1 = require("../services/super-admin-edit-advisor.service");
const super_admin_edit_broker_service_1 = require("../services/super-admin-edit-broker.service");
const super_admin_edit_investor_service_1 = require("../services/super-admin-edit-investor.service");
let SuperAdminController = class SuperAdminController {
    constructor(superAdminService, getAdminSuperAdminService, getBrokerAdvisorsService, getBrokerInvestorService, getAdvisorInvestorsService, getAllBrokersWithActiveContractsService, getSingleBrokerWithActiveContractsService, getAllAdvisorsWithActiveContractsService, getSingleAdvisorWithActiveContractsService, getDashboardDataService, superAdminEditBrokerService, superAdminEditAdvisorService, getContractsGrowthChartService, superAdminEditInvestorService) {
        this.superAdminService = superAdminService;
        this.getAdminSuperAdminService = getAdminSuperAdminService;
        this.getBrokerAdvisorsService = getBrokerAdvisorsService;
        this.getBrokerInvestorService = getBrokerInvestorService;
        this.getAdvisorInvestorsService = getAdvisorInvestorsService;
        this.getAllBrokersWithActiveContractsService = getAllBrokersWithActiveContractsService;
        this.getSingleBrokerWithActiveContractsService = getSingleBrokerWithActiveContractsService;
        this.getAllAdvisorsWithActiveContractsService = getAllAdvisorsWithActiveContractsService;
        this.getSingleAdvisorWithActiveContractsService = getSingleAdvisorWithActiveContractsService;
        this.getDashboardDataService = getDashboardDataService;
        this.superAdminEditBrokerService = superAdminEditBrokerService;
        this.superAdminEditAdvisorService = superAdminEditAdvisorService;
        this.getContractsGrowthChartService = getContractsGrowthChartService;
        this.superAdminEditInvestorService = superAdminEditInvestorService;
    }
    async getDashboardData() {
        return this.getDashboardDataService.perform();
    }
    async editBroker(request, body) {
        await this.superAdminEditBrokerService.perform(body);
    }
    async editAdvisor(request, body) {
        await this.superAdminEditAdvisorService.perform(body);
    }
    async editInvestorSuperAdmin(req, body) {
        return this.superAdminEditInvestorService.perform(body, req.user.id);
    }
    async getAllBrokersWithActiveContracts(query) {
        return this.getAllBrokersWithActiveContractsService.perform(query);
    }
    async getSingleBrokerWithActiveContracts(id, query) {
        return this.getSingleBrokerWithActiveContractsService.perform(id, query);
    }
    async getAllAdvisorsWithActiveContracts(query) {
        return this.getAllAdvisorsWithActiveContractsService.perform(query);
    }
    async getSingleAdvisorWithActiveContracts(id, query) {
        return this.getSingleAdvisorWithActiveContractsService.perform(id, query);
    }
    async search(request) {
        return this.superAdminService.perform({ ownerId: request.user.id });
    }
    async getAdminSuperAdmin(ownerId) {
        const getAdminSuperAdminDto = { ownerId };
        return this.getAdminSuperAdminService.perform(getAdminSuperAdminDto);
    }
    async getAdvisorsByBroker(ownerId) {
        const getAdvisorsDto = { ownerId };
        return this.getBrokerAdvisorsService.perform(getAdvisorsDto);
    }
    async getInvestorsByAdvisor(ownerId) {
        const getInvestorsByBroker = { ownerId };
        return this.getAdvisorInvestorsService.perform(getInvestorsByBroker);
    }
    async getInvestorsByBroker(ownerId) {
        const getInvestorsByBroker = { ownerId };
        return this.getBrokerInvestorService.perform(getInvestorsByBroker);
    }
    async getContractsGrowthChart(period, contractType) {
        return this.getContractsGrowthChartService.perform(period, contractType);
    }
};
exports.SuperAdminController = SuperAdminController;
__decorate([
    (0, common_1.Get)('/super-admin/dashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getDashboardData", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN),
    (0, common_1.Patch)('/super-admin/broker'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, edit_broker_dto_1.EditBrokerDto]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "editBroker", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN),
    (0, common_1.Patch)('/super-admin/advisor'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, edit_broker_dto_1.EditBrokerDto]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "editAdvisor", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN),
    (0, common_1.Put)('/superadmin/investor'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "editInvestorSuperAdmin", null);
__decorate([
    (0, common_1.Get)('/super-admin/active-brokers'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all brokers with active contracts',
        type: response_dto_2.GetAllBrokersWithActiveContractsResponseDto,
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_dto_2.GetAllBrokersWithActiveContractsQueryDto]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getAllBrokersWithActiveContracts", null);
__decorate([
    (0, common_1.Get)('/super-admin/active-brokers/:id'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get single broker with active contracts',
        type: response_dto_4.GetSinleBrokerWithActiveContractsResponseDto,
        isArray: true,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, query_dto_4.GetSingleBrokerWithActiveContractsQueryDto]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getSingleBrokerWithActiveContracts", null);
__decorate([
    (0, common_1.Get)('/super-admin/active-advisors'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all advisors with active contracts',
        type: response_dto_1.GetAllAdvisorsWithActiveContractsResponseDto,
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_dto_1.GetAllAdvisorsWithActiveContractsQueryDto]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getAllAdvisorsWithActiveContracts", null);
__decorate([
    (0, common_1.Get)('/super-admin/active-advisors/:id'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get single advisor with its broker active contract total value sum',
        type: response_dto_3.GetSingleAdvisorWithActiveContractsResponseDto,
        isArray: true,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, query_dto_3.GetSingleAdvisorsWithActiveContractsQueryDto]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getSingleAdvisorWithActiveContracts", null);
__decorate([
    (0, common_1.Get)('/super-admin/admins'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('/super-admin/brokers/:ownerId'),
    __param(0, (0, common_1.Param)('ownerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getAdminSuperAdmin", null);
__decorate([
    (0, common_1.Get)('/super-admin/advisors/:ownerId'),
    __param(0, (0, common_1.Param)('ownerId', new common_1.ParseUUIDPipe())),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getAdvisorsByBroker", null);
__decorate([
    (0, common_1.Get)('/super-admin/investors/advisor/:ownerId'),
    __param(0, (0, common_1.Param)('ownerId', new common_1.ParseUUIDPipe())),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getInvestorsByAdvisor", null);
__decorate([
    (0, common_1.Get)('/super-admin/investors/:ownerId'),
    __param(0, (0, common_1.Param)('brokerId', new common_1.ParseUUIDPipe())),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getInvestorsByBroker", null);
__decorate([
    (0, common_1.Get)('/superadmin/contracts-growth'),
    __param(0, (0, common_1.Query)('period')),
    __param(1, (0, common_1.Query)('contractType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SuperAdminController.prototype, "getContractsGrowthChart", null);
exports.SuperAdminController = SuperAdminController = __decorate([
    (0, common_1.Controller)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN),
    __metadata("design:paramtypes", [find_super_admin_service_1.SuperAdminService,
        find_admin_super_admin_service_1.GetAdminSuperAdminService,
        get_broker_advisors_service_1.GetBrokerAdvisorsService,
        get_broker_investors_service_1.GetBrokerInvestorService,
        get_advisor_investors_service_1.GetAdvisorInvestorsService,
        get_all_brokers_with_active_contracts_service_1.GetAllBrokersWithActiveContractsService,
        get_single_broker_with_active_contracts_service_1.GetSingleBrokerWithActiveContractsService,
        get_all_advisors_with_active_contracts_service_1.GetAllAdvisorsWithActiveContractsService,
        get_single_advisor_with_active_contracts_service_1.GetSingleAdvisorWithActiveContractsService,
        get_dashboard_data_service_1.GetDashboardDataService,
        super_admin_edit_broker_service_1.SuperAdminEditBrokerService,
        super_admin_edit_advisor_service_1.SuperAdminEditAdvisorService,
        get_contracts_growth_chart_service_1.GetContractsGrowthChartService,
        super_admin_edit_investor_service_1.SuperAdminEditInvestorService])
], SuperAdminController);
//# sourceMappingURL=superadmin.controller.js.map