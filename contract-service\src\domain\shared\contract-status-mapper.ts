import { ContractStatus } from '../entities/contracts';

export class ContractStatusMapper {
  static fromString(status: string): ContractStatus {
    switch (status.toUpperCase()) {
      case 'DRAFT':
        return ContractStatus.DRAFT;
      case 'GENERATED':
        return ContractStatus.GENERATED;
      case 'SIGNATURE_SENT':
        return ContractStatus.SIGNATURE_SENT;
      case 'AWAITING_INVESTOR_SIGNATURE':
        return ContractStatus.AWAITING_INVESTOR_SIGNATURE;
      case 'AWAITING_DEPOSIT':
        return ContractStatus.AWAITING_DEPOSIT;
      case 'AWAITING_AUDIT':
        return ContractStatus.AWAITING_AUDIT;
      case 'ACTIVE':
        return ContractStatus.ACTIVE;
      case 'SIGNATURE_FAILED':
        return ContractStatus.SIGNATURE_FAILED;
      case 'EXPIRED':
        return ContractStatus.EXPIRED;
      case 'REJECTED':
        return ContractStatus.REJECTED;
      case 'REJECTED_BY_AUDIT':
        return ContractStatus.REJECTED_BY_AUDIT;
      default:
        return ContractStatus.DRAFT;
    }
  }
}
