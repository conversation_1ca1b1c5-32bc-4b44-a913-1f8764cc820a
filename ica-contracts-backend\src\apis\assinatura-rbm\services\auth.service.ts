import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import { IAuthResponse } from '../responses/auth.response';

class AuthIntegrationDto {
  email?: string;
  senha: string;
  login?: string;
  hashIntegracao: string;
}

@Injectable()
export class AuthIntegrationService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly authDto: AuthIntegrationDto;
  private readonly logger = new Logger(AuthIntegrationService.name);

  constructor(private readonly configService: ConfigService) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/v2/auth`;
    this.authDto = {
      email: this.configService.get<string>('ASSINATURA_EMAIL'),
      senha: this.configService.get<string>('ASSINATURA_SENHA'),
      login: this.configService.get<string>('ASSINATURA_LOGIN'),
      hashIntegracao: this.configService.get<string>(
        'ASSINATURA_HASH_INTEGRACAO',
      ),
    };

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async authenticateIntegration(): Promise<IAuthResponse> {
    try {
      const response = await this.axiosInstance.post<IAuthResponse>(
        '',
        this.authDto,
      );

      return response.data;
    } catch (error) {
      console.log(error);

      this.logger.error('Erro ao autenticar integração', error);

      if (error.response) {
        throw new InternalServerErrorException(
          `Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'Nenhuma resposta recebida da API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Erro na requisição: ${error.message}`,
        );
      }
    }
  }
}
