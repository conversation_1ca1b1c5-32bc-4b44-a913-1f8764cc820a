{"version": 3, "file": "process-scheduled-pix.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/process-scheduled-pix.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAA4C;AAC5C,+CAAwC;AACxC,6CAAmD;AACnD,uCAAgD;AAChD,qGAA4F;AAC5F,2FAAiF;AACjF,qCAAuE;AAGhE,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEU,qBAAoD;QAApD,0BAAqB,GAArB,qBAAqB,CAA+B;IAE3D,CAAC;IAGE,AAAN,KAAK,CAAC,UAAU;QACd,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QAEzB,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC;YACtE,KAAK,EAAE;gBACL,MAAM,EAAE,+CAAqB,CAAC,SAAS;gBACvC,YAAY,EACV,IAAA,yBAAe,EAAC,IAAA,qBAAU,EAAC,KAAK,CAAC,CAAC;oBAClC,IAAA,yBAAe,EAAC,IAAA,mBAAQ,EAAC,KAAK,CAAC,CAAC;aACnC;SACF,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3C,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACpC,KAAK,EAAE;gBACL,MAAM,EAAE,+CAAqB,CAAC,SAAS;gBACvC,YAAY,EACV,IAAA,yBAAe,EAAC,IAAA,qBAAU,EAAC,KAAK,CAAC,CAAC;oBAClC,IAAA,yBAAe,EAAC,IAAA,mBAAQ,EAAC,KAAK,CAAC,CAAC;aACnC;YACD,IAAI,EAAE,iBAAiB,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG;SACnD,CAAC,CAAC;IAOL,CAAC;CACF,CAAA;AAtCY,kDAAmB;AAQxB;IADL,IAAA,eAAI,EAAC,kBAAkB,CAAC;;;;qDA8BxB;8BArCU,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;qCACL,oBAAU;GAHhC,mBAAmB,CAsC/B", "sourcesContent": ["// import { InjectQueue } from '@nestjs/bull';\r\nimport { Injectable } from '@nestjs/common';\r\nimport { Cron } from '@nestjs/schedule';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { endOfDay, startOfDay } from 'date-fns';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';\r\nimport { LessThanOrEqual, MoreThanOrEqual, Repository } from 'typeorm';\r\n\r\n@Injectable()\r\nexport class ProcessScheduledPix {\r\n  constructor(\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepository: Repository<TransactionEntity>,\r\n    // @InjectQueue('transactions') private readonly transactionQueue: Queue,\r\n  ) {}\r\n\r\n  @Cron('0 */15 7-8 * * *')\r\n  async handleCron() {\r\n    const today = new Date();\r\n\r\n    const transactionsCount = await this.transactionRepository.findAndCount({\r\n      where: {\r\n        status: TransactionStatusEnum.SCHEDULED,\r\n        transferDate:\r\n          MoreThanOrEqual(startOfDay(today)) &&\r\n          LessThanOrEqual(endOfDay(today)),\r\n      },\r\n    });\r\n\r\n    const limit = transactionsCount.length / 4;\r\n\r\n    await this.transactionRepository.find({\r\n      where: {\r\n        status: TransactionStatusEnum.SCHEDULED,\r\n        transferDate:\r\n          MoreThanOrEqual(startOfDay(today)) &&\r\n          LessThanOrEqual(endOfDay(today)),\r\n      },\r\n      take: transactionsCount.length > 100 ? limit : 100,\r\n    });\r\n\r\n    // await Promise.all(\r\n    //   transactions.map(async transaction =>\r\n    //     this.transactionQueue.add({ transactionId: transaction.id }),\r\n    //   ),\r\n    // );\r\n  }\r\n}\r\n"]}