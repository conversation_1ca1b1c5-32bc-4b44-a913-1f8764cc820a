import SelectCustom from "@/components/SelectCustom"
import { useState } from "react";
import BrokerRegisterPf from "./registerForms/BrokerRegisterPf";
import BrokerRegisterPj from "./registerForms/BrokerRegisterPj";
import SelectSearch from "@/components/SelectSearch";
import { getUserProfile } from "@/functions/getUserData";
import {  useQuery } from "@tanstack/react-query";
import api from "@/core/api";
import { cnpjMask } from "@/utils/masks";



export default function BrokerCreate() {
    const [typeAccount, setTypeAccount] = useState('pf')
    const [adminId, setAdminId] = useState("");
    const userProfile = getUserProfile();


    const { data: admins = [] } = useQuery({
        queryKey: ["admins"],
        queryFn: async () => {
            const response = await api.get("/wallets/list-admin");
            return response.data.map((item: any) => ({
                ...item,
                document: cnpjMask(item?.document || ""),
                type: "admin",
            }));
        },
        enabled: userProfile.name === "superadmin",
    });


    return (
        <div>
            <div className="m-3">
                <div className=' mb-5 flex items-center gap-4'>
                    <div className="mb-5">
                        <p className="text-white mb-1">Tipo de conta</p>
                        <SelectCustom
                            value={typeAccount}
                            onChange={({ target }) => setTypeAccount(target.value)}
                        >
                            <option value={'pf'}>Pessoa Física</option>
                            <option value={'pj'}>Pessoa Jurídica</option>
                        </SelectCustom>
                    </div>
                    <div className="md:w-3/4 mb-5">
                                <SelectSearch
                                    label="Vincular ao gestor de carteiras"
                                    items={admins}
                                    value={adminId}
                                    setValue={setAdminId}
                                />
                            </div>
                </div>
                
            </div>

            {typeAccount === 'pj' ? <BrokerRegisterPj typeCreate="broker" adminId={adminId} /> : <BrokerRegisterPf typeCreate="broker" adminId={adminId} />}
        </div>
    );

}