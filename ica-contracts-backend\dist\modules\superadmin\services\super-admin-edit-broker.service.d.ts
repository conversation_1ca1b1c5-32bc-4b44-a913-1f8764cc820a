import { EditBrokerDto } from 'src/modules/broker/dto/edit-broker.dto';
import { EditBrokerService } from 'src/modules/broker/services/edit-broker.service';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';
export declare class SuperAdminEditBrokerService {
    private readonly ownerRoleRelationRepository;
    private readonly editBrokerService;
    constructor(ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, editBrokerService: EditBrokerService);
    perform(payload: EditBrokerDto): Promise<void>;
}
