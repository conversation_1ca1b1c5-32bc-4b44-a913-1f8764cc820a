{"version": 3, "file": "read-qrcode-pix.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/read-qrcode-pix.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwC;AACxC,0GAA+F;AAI/F,IAAa,oBAAoB,GAAjC,MAAa,oBAAoB;IAC/B,YAEU,gBAAyC;QAAzC,qBAAgB,GAAhB,gBAAgB,CAAyB;IAChD,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAsB;QAClC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAC7D,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;CACF,CAAA;AAZY,oDAAoB;+BAApB,oBAAoB;IAE5B,WAAA,IAAA,eAAM,EAAC,oDAAuB,CAAC,CAAA;qCACN,oDAAuB;GAHxC,oBAAoB,CAYhC", "sourcesContent": ["import { Inject } from '@nestjs/common';\r\nimport { PixQRCodeCelcoinService } from 'src/apis/celcoin/services/pix-qrcode-celcoin.service';\r\n\r\nimport { ReadQRCodePixDto } from '../dto/read-qrcode-pix.dto';\r\n\r\nexport class ReadQRCodePixService {\r\n  constructor(\r\n    @Inject(PixQRCodeCelcoinService)\r\n    private pixQRCodeCelcoin: PixQRCodeCelcoinService,\r\n  ) {}\r\n  async perform(data: ReadQRCodePixDto) {\r\n    const responseCelcoin = await this.pixQRCodeCelcoin.readQRCode({\r\n      emv: data.emv,\r\n    });\r\n\r\n    return responseCelcoin;\r\n  }\r\n}\r\n"]}