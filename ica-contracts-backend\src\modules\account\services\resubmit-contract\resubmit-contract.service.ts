import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';
import { Repository } from 'typeorm';

import { ResubmitRejectedContractRequest } from '../../../../apis/ica-contract-service/request/resubmit-rejected-contract.request';
import { ResubmitRejectedContractApiService } from '../../../../apis/ica-contract-service/services/resubmit-rejected-contract.service';
import { ResubmitContractDto } from '../../dto/resubmit-contract.dto';

const allowedStatuses = [
  ContractStatusEnum.REJECTED_BY_AUDIT,
  ContractStatusEnum.AWAITING_AUDIT,
];
@Injectable()
export class ResubmitContractService {
  private readonly logger = new Logger(ResubmitContractService.name);

  constructor(
    private readonly resubmitRejectedContractApiService: ResubmitRejectedContractApiService,
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
  ) {}

  public async perform(data: ResubmitContractDto) {
    this.logger.log(
      `Iniciando reenvio do contrato rejeitado com ID: ${data.contractId}`,
    );

    try {
      const existContract = await this.contractRepository.findOne({
        where: {
          id: data.contractId,
        },
      });
      

      if (!existContract) {
        this.logger.error(`Contrato não encontrato`);

        throw new BadRequestException(
          'Já existe um contrato ativo para este documento',
        );
      }

      if (
        !allowedStatuses.includes(existContract.status as ContractStatusEnum)
      ) {
        this.logger.error(
          `Contrato não está com status de rejeitado ou aguardando auditoria`,
        );

        throw new BadRequestException(
          'O contrato não está com status de rejeitado',
        );
      }
        
      const requestData = this.mapToResubmitRequest(data);
      
      const result = await this.resubmitRejectedContractApiService.perform(
        data.contractId,
        requestData,
      );

      this.logger.log(`Contrato ${data.contractId} reenviado com sucesso.`);
      return result;
    } catch (error) {
      this.logger.error(
        `Erro ao reenviar contrato ${data.contractId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private mapToResubmitRequest(
    data: ResubmitContractDto,
  ): ResubmitRejectedContractRequest {
    const request: ResubmitRejectedContractRequest = {
      ...data,
      ...(data.proofOfPayment &&
        data.proofOfPayment.file && {
          proofOfPayment: {
            mimetype: data.proofOfPayment.file.mimetype,
            buffer: data.proofOfPayment.file.buffer,
          },
        }),
      ...(data.personalDocument &&
        data.personalDocument.file && {
          personalDocument: {
            mimetype: data.personalDocument.file.mimetype,
            buffer: data.personalDocument.file.buffer,
          },
        }),
      ...(data.contract &&
        data.contract.file && {
          contract: {
            mimetype: data.contract.file.mimetype,
            buffer: data.contract.file.buffer,
          },
        }),
      ...(data.proofOfResidence &&
        data.proofOfResidence.file && {
          proofOfResidence: {
            mimetype: data.proofOfResidence.file.mimetype,
            buffer: data.proofOfResidence.file.buffer,
          },
        }),
      ...(data.cardCnpj &&
        data.cardCnpj.file && {
          cardCnpj: {
            mimetype: data.cardCnpj.file.mimetype,
            buffer: data.cardCnpj.file.buffer,
          },
        }),
    };
    
    return request;
  }
}
