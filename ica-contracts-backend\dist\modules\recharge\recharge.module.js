"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RechargeModule = void 0;
const common_1 = require("@nestjs/common");
const apis_module_1 = require("../../apis/apis.module");
const shared_module_1 = require("../../shared/shared.module");
const recharge_controller_1 = require("./controllers/recharge.controller");
const create_virtual_recharge_service_1 = require("./services/create-virtual-recharge.service");
const export_report_recharges_service_1 = require("./services/export-report-recharges.service");
const get_account_recharges_service_1 = require("./services/get-account-recharges.service");
const transaction_recharge_service_1 = require("./services/transaction-recharge.service");
let RechargeModule = class RechargeModule {
};
exports.RechargeModule = RechargeModule;
exports.RechargeModule = RechargeModule = __decorate([
    (0, common_1.Module)({
        controllers: [recharge_controller_1.RechargeController],
        imports: [shared_module_1.SharedModule, apis_module_1.ApisModule],
        providers: [
            transaction_recharge_service_1.TransactionRechargeService,
            create_virtual_recharge_service_1.CreateVirtualRechargeService,
            get_account_recharges_service_1.GetAccountRecharges,
            export_report_recharges_service_1.ExportReportRechargesService,
        ],
    })
], RechargeModule);
//# sourceMappingURL=recharge.module.js.map