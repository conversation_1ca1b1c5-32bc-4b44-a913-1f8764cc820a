{"version": 3, "file": "uploads.module.js", "sourceRoot": "/", "sources": ["modules/uploads/uploads.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,8DAAwD;AAExD,yEAAqE;AACrE,kFAA4E;AAC5E,4EAAuE;AAOhE,IAAM,aAAa,GAAnB,MAAM,aAAa;CAAG,CAAA;AAAhB,sCAAa;wBAAb,aAAa;IALzB,IAAA,eAAM,EAAC;QACN,WAAW,EAAE,CAAC,sCAAiB,CAAC;QAChC,OAAO,EAAE,CAAC,4BAAY,CAAC;QACvB,SAAS,EAAE,CAAC,gDAAqB,EAAE,2CAAmB,CAAC;KACxD,CAAC;GACW,aAAa,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { UploadsController } from './controllers/uploads.controller';\r\nimport { AddNewDocumentService } from './services/add-new-document.service';\r\nimport { GetDocumentsService } from './services/get-documents.service';\r\n\r\n@Module({\r\n  controllers: [UploadsController],\r\n  imports: [SharedModule],\r\n  providers: [AddNewDocumentService, GetDocumentsService],\r\n})\r\nexport class UploadsModule {}\r\n"]}