import { DeleteContractValidator } from '@/main/validators/zod/delete-contract.validator';
import { DeleteContractController } from '@/presentation/http/controllers/contracts/delete-contract.controller';
import { makeDeleteContractUseCase } from '../../usecases/delete-contract.factory';

export const makeDeleteContractController = () => {
  const useCase = makeDeleteContractUseCase();
  const validation = new DeleteContractValidator();
  return new DeleteContractController(validation, useCase);
};
