"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClaimCancelPixKeyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const claim_celcoin_service_1 = require("../../../apis/celcoin/services/claim-celcoin.service");
const pix_key_entity_1 = require("../../../shared/database/typeorm/entities/pix-key.entity");
const claim_reason_enum_1 = require("../../../shared/enums/claim-reason.enum");
const claim_status_enum_1 = require("../../../shared/enums/claim-status.enum");
const pix_key_status_enum_1 = require("../../../shared/enums/pix-key-status.enum");
const typeorm_2 = require("typeorm");
let ClaimCancelPixKeyService = class ClaimCancelPixKeyService {
    constructor(pixKeyRepository, pixCelcoinService) {
        this.pixKeyRepository = pixKeyRepository;
        this.pixCelcoinService = pixCelcoinService;
    }
    async execute(input, ownerId) {
        const pixkey = await this.pixKeyRepository.findOne({
            where: [
                {
                    key: input.key,
                    status: pix_key_status_enum_1.PixKeyStatusEnum.PENDENT,
                    claimStatus: claim_status_enum_1.ClaimStatusEnum.OPEN,
                    account: { ownerId },
                },
                {
                    key: input.key,
                    status: pix_key_status_enum_1.PixKeyStatusEnum.PENDENT,
                    claimStatus: claim_status_enum_1.ClaimStatusEnum.OPEN,
                    account: { businessId: ownerId },
                },
            ],
        });
        if (!pixkey) {
            throw new common_1.NotFoundException('Chave não possui reinvindicação.');
        }
        const claimMetada = JSON.parse(pixkey.claimMetadata);
        await this.pixCelcoinService.cancelClaim({
            claimId: claimMetada.claimId,
            reason: input.reason || claim_reason_enum_1.ClaimReasonEnum.DEFAULT_OPERATION,
        });
        pixkey.claimStatus = claim_status_enum_1.ClaimStatusEnum.CANCELLED;
        pixkey.status = pix_key_status_enum_1.PixKeyStatusEnum.CANCELLED;
        await this.pixKeyRepository.save(pixkey);
        return { status: 'Reivindicação cancelada com sucesso.' };
    }
};
exports.ClaimCancelPixKeyService = ClaimCancelPixKeyService;
exports.ClaimCancelPixKeyService = ClaimCancelPixKeyService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(pix_key_entity_1.PixKeyEntity)),
    __param(1, (0, common_1.Inject)(claim_celcoin_service_1.ClaimCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        claim_celcoin_service_1.ClaimCelcoinService])
], ClaimCancelPixKeyService);
//# sourceMappingURL=claim-cancel-pix-key.service.js.map