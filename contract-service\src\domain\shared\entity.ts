import type { BaseEntityProps } from './base-entity'

export abstract class Entity<T extends BaseEntityProps> {
  public readonly id: string
  protected readonly props: T

  constructor(props: T, id?: string) {
    this.id = id ?? crypto.randomUUID()

    const now = new Date()

    this.props = {
      ...props,
      createdAt: props.createdAt ?? now,
      updatedAt: props.updatedAt ?? now,
    }
  }

  equals(entity?: Entity<T>): boolean {
    if (!entity) return false
    return this.id === entity.id
  }

  get createdAt(): Date {
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    return this.props.createdAt!
  }

  get updatedAt(): Date {
    // biome-ignore lint/style/noNonNullAssertion: <explanation>
    return this.props.updatedAt!
  }

  protected touch(): void {
    this.props.updatedAt = new Date()
  }
}
