{"version": 3, "file": "transaction-pix-key.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/transaction-pix-key.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yDAAyC;AACzC,qDAQyB;AAEzB,MAAM,QAAQ;CA4Bb;AAzBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qCACC;AAIZ;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yCACK;AAIhB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wCACI;AAIf;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;0CACM;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;6CACS;AAGtB,MAAa,oBAAoB;CA0BhC;AA1BD,oDA0BC;AAvBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sDACM;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;oDACF;AAIf;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;iEACgB;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACS;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;0DACV;AAMrB;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;8BACX,QAAQ;sDAAC", "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport {\r\n  IsDateString,\r\n  IsDefined,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\n\r\nclass External {\r\n  @IsDefined()\r\n  @IsString()\r\n  key: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  bank: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  account: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  branch: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  document: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  accountType: string;\r\n}\r\n\r\nexport class TransactionPixKeyDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  endToEnd: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  amount: string;\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  transactionPassword?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  description: string;\r\n\r\n  @IsOptional()\r\n  @IsDateString({ strict: true })\r\n  transferDate: string;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => External)\r\n  external: External;\r\n}\r\n"]}