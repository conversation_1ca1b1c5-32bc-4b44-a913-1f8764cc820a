{"version": 3, "file": "export-report-recharges.dto.js", "sourceRoot": "/", "sources": ["modules/recharge/dto/export-report-recharges.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAgF;AAEhF,MAAa,wBAAwB;CAYpC;AAZD,4DAYC;AATC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sDACE;AAIb;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;8BACT,IAAI;sDAAC;AAIX;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;8BACX,IAAI;oDAAC", "sourcesContent": ["import { IsDateString, IsDefined, IsOptional, IsString } from 'class-validator';\r\n\r\nexport class ExportReportRechargesDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  type: string;\r\n\r\n  @IsOptional()\r\n  @IsDateString()\r\n  from: Date;\r\n\r\n  @IsOptional()\r\n  @IsDateString()\r\n  to: Date;\r\n}\r\n"]}