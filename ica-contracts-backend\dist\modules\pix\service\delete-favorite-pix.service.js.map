{"version": 3, "file": "delete-favorite-pix.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/delete-favorite-pix.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+C;AAC/C,6CAAmD;AACnD,6FAAoF;AACpF,uGAA6F;AAC7F,qCAA4C;AAI5C,IAAa,wBAAwB,GAArC,MAAa,wBAAwB;IACnC,YAEU,SAAoC,EAEpC,aAA4C;QAF5C,cAAS,GAAT,SAAS,CAA2B;QAEpC,kBAAa,GAAb,aAAa,CAA+B;IACnD,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAA0B,EAAE,EAAU;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,sBAAa,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;QAEnE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,EAAE,EAAE,IAAI,CAAC,EAAE;aACZ;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,sBAAa,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3C,CAAC;CACF,CAAA;AAnCY,4DAAwB;mCAAxB,wBAAwB;IAEhC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCADjB,oBAAU;QAEN,oBAAU;GALxB,wBAAwB,CAmCpC", "sourcesContent": ["import { HttpException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { FavoritePixEntity } from 'src/shared/database/typeorm/entities/favorite-pix.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { DeleteFavoritePixDto } from '../dto/delete-favorite-pix.dto';\r\n\r\nexport class DeleteFavoritePixService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(FavoritePixEntity)\r\n    private favoritePixDb: Repository<FavoritePixEntity>,\r\n  ) {}\r\n  async perform(data: DeleteFavoritePixDto, id: string) {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new HttpException('Conta não encontrado', 400);\r\n\r\n    const favoritePix = await this.favoritePixDb.findOne({\r\n      where: {\r\n        accountId: account.id,\r\n        id: data.id,\r\n      },\r\n    });\r\n\r\n    if (!favoritePix) {\r\n      throw new HttpException('Favorito não encontradom para essa conta', 400);\r\n    }\r\n\r\n    await this.favoritePixDb.delete(data.id);\r\n  }\r\n}\r\n"]}