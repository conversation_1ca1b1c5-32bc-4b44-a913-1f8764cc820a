{"version": 3, "file": "direct-add-pre-register.service.js", "sourceRoot": "/", "sources": ["modules/pre-register/services/direct-add-pre-register.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,+CAAsC;AACtC,6FAAoF;AACpF,uGAA6F;AAC7F,6FAAkF;AAClF,qCAA4C;AAoBrC,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAEU,aAA4C,EAE5C,SAAoC;QAFpC,kBAAa,GAAb,aAAa,CAA+B;QAE5C,cAAS,GAAT,SAAS,CAA2B;IAC3C,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAA6B,EAAE,KAAa;QACxD,MAAM,OAAO,GAAG,IAAA,qBAAM,EAAC,KAAK,CAAa,CAAC;QAC1C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAA,eAAK,EAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;gBAChD,MAAM,EAAE,gDAAqB,CAAC,MAAM;aACrC;SACF,CAAC,CAAC;QAEH,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,IAAI,4BAAmB,CAC3B,qDAAqD,CACtD,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,KAAK,EAAE;gBACL;oBACE,KAAK,EAAE;wBACL,GAAG,EAAE,IAAA,eAAK,EAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;qBAC5C;iBACF;gBACD;oBACE,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAA,eAAK,EAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;qBAC7C;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;YAC1C,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAClC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;YAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,QAAQ,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE;YACzC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE;YACnC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,MAAM,EAAE,gDAAqB,CAAC,MAAM;YACpC,YAAY,EAAE,OAAO,CAAC,sBAAsB;YAC5C,WAAW,EAAE,OAAO,CAAC,qBAAqB;YAC1C,YAAY,EAAE,OAAO,CAAC,sBAAsB;SAC7C,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AAnEY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCADT,oBAAU;QAEd,oBAAU;GALpB,2BAA2B,CAmEvC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { decode } from 'jsonwebtoken';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';\r\nimport { PreRegisterStatusEnum } from 'src/shared/enums/pre-register-status.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { DirectAddPreRegisterDto } from '../dto/direct-add-pre-register.dto';\r\n\r\ninterface IDecoded {\r\n  adviserId: string;\r\n  investorEmail: string;\r\n  investorDocument: string;\r\n  investmentValue: number;\r\n  investmentTerm: string;\r\n  investmentModality: string;\r\n  investmentYield: number;\r\n  investmentPurchaseWith: string;\r\n  investmentAmountQuotes: number;\r\n  investmentGracePeriod: Date;\r\n  iat: number;\r\n  exp: number;\r\n}\r\n\r\n@Injectable()\r\nexport class DirectAddPreRegisterService {\r\n  constructor(\r\n    @InjectRepository(PreRegisterEntity)\r\n    private preRegisterDb: Repository<PreRegisterEntity>,\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n  ) {}\r\n  async perform(data: DirectAddPreRegisterDto, token: string) {\r\n    const decoded = decode(token) as IDecoded;\r\n    const preRegister = await this.preRegisterDb.findOne({\r\n      where: {\r\n        document: Equal(decoded.investorDocument.trim()),\r\n        status: PreRegisterStatusEnum.ACTIVE,\r\n      },\r\n    });\r\n\r\n    if (preRegister) {\r\n      throw new BadRequestException(\r\n        'Ja existe um pre registro ativo para este documento',\r\n      );\r\n    }\r\n\r\n    const account = await this.accountDb.findOne({\r\n      where: [\r\n        {\r\n          owner: {\r\n            cpf: Equal(decoded.investorDocument.trim()),\r\n          },\r\n        },\r\n        {\r\n          business: {\r\n            cnpj: Equal(decoded.investorDocument.trim()),\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (account) {\r\n      throw new BadRequestException('Ja existe uma conta com este documento');\r\n    }\r\n\r\n    const create = this.preRegisterDb.create({\r\n      adviserId: decoded.adviserId,\r\n      addressComplement: data.address.complement,\r\n      addressNumber: data.address.number,\r\n      neighborhood: data.address.neighborhood,\r\n      zipCode: data.address.zipCode,\r\n      city: data.address.city,\r\n      document: decoded.investorDocument.trim(),\r\n      dtBirth: data.dtBirth,\r\n      email: decoded.investorEmail.trim(),\r\n      investmentModality: decoded.investmentModality,\r\n      investmentTerm: decoded.investmentTerm,\r\n      investmentValue: decoded.investmentValue,\r\n      investmentYield: decoded.investmentYield,\r\n      name: data.name,\r\n      phoneNumber: data.phoneNumber,\r\n      rg: data.rg,\r\n      observations: data.observations,\r\n      status: PreRegisterStatusEnum.ACTIVE,\r\n      amountQuotes: decoded.investmentAmountQuotes,\r\n      gracePeriod: decoded.investmentGracePeriod,\r\n      purchaseWith: decoded.investmentPurchaseWith,\r\n    });\r\n\r\n    await this.preRegisterDb.save(create);\r\n  }\r\n}\r\n"]}