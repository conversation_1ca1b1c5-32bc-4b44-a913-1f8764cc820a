import { KYCCelcoinService } from 'src/apis/celcoin/services/kyc-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { DocumentEntity } from 'src/shared/database/typeorm/entities/document.entity';
import { Repository } from 'typeorm';
import { CreateAccountCelcoinWebhookDto } from '../dto/create-acount-celcoin-webhook.dto';
export declare class CreateAccountCelcoinWebhookService {
    private accountDb;
    private documentDb;
    private apiCelcoin;
    constructor(accountDb: Repository<AccountEntity>, documentDb: Repository<DocumentEntity>, apiCelcoin: KYCCelcoinService);
    perform(data: CreateAccountCelcoinWebhookDto): Promise<void>;
}
