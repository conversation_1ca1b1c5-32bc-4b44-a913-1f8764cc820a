import { Repository } from 'typeorm';
import { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from '../../../shared/database/typeorm/entities/owner-role-relation.entity';
import { DashboardDto } from '../dto/dashboard.dto';
export declare class DashboardService {
    private readonly ownerRoleRelationRepository;
    private readonly contractRepository;
    constructor(ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, contractRepository: Repository<ContractEntity>);
    perform(userId: string): Promise<DashboardDto>;
    private verifyAdminPermission;
    private getExpiringContractsCount;
    private getContractsCountByLastEventStatus;
    private calculateDateThreshold;
}
