import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { FavoritePixEntity } from 'src/shared/database/typeorm/entities/favorite-pix.entity';
import { Repository } from 'typeorm';
import { DeleteFavoritePixDto } from '../dto/delete-favorite-pix.dto';
export declare class DeleteFavoritePixService {
    private accountDb;
    private favoritePixDb;
    constructor(accountDb: Repository<AccountEntity>, favoritePixDb: Repository<FavoritePixEntity>);
    perform(data: DeleteFavoritePixDto, id: string): Promise<void>;
}
