import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { VIRTUAL_TRANS_URL } from 'src/shared/helpers/constants';
import { logger } from 'src/shared/logger';

import { IManualRemoveResponse } from '../responses/manual-remove-response';

@Injectable()
export class ManualRemoveService {
  async getStatement(accountId: string): Promise<IManualRemoveResponse[]> {
    try {
      const url = new URL(`${VIRTUAL_TRANS_URL}/manual-remove/${accountId}`);

      const response = await axios.get<IManualRemoveResponse[]>(
        url.toString(),
        {
          headers: {
            'X-Api-Key': 'c9e0d900-edd3-45f7-9e69-abd96edbf724',
          },
        },
      );

      return response.data;
    } catch (error) {
      logger.info(`ManualRemoveService.getStatement() -> ${error}`);
      return [] as IManualRemoveResponse[];
    }
  }
}
