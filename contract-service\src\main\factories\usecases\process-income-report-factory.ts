import { RequestIncomeReportUseCase } from '@/contexts/income-report/application/usecases/request-income-report.usercase';
import { PrismaContractRepository } from '@/contexts/income-report/infrastructure/prisma/repositories/contract.repository';
import { PrismaEmailIncomeReportRepository } from '@/contexts/income-report/infrastructure/prisma/repositories/email-income-report.repository';
import { PrismaIncomeReportRepository } from '@/contexts/income-report/infrastructure/prisma/repositories/income-report.repository';
import { PrismaInvestorRepository } from '@/contexts/income-report/infrastructure/prisma/repositories/investor.repository';
import { FetchHttpClient } from '@/infrastructure/http/fetch-http-client';
import { PrismaFileUploadRepository } from '@/infrastructure/prisma/repositories/file-upload.repository';
import { AwsStorageGateway } from '@/main/adapters/aws-storage.adapter';
import { EmailGateway } from '@/main/adapters/email-api-gateway.adapter';
import { ReportApiGateway } from '@/main/adapters/report-api-gateway';
import env from '@/main/config/env';

export function makeProcessIncomeReportUseCase() {
  const prismaContractRepository = new PrismaContractRepository();
  const prismaInvestorRepository = new PrismaInvestorRepository();
  const prismaIncomeReportRepository = new PrismaIncomeReportRepository();

  const awsS3StorageGateway = new AwsStorageGateway(
    env.AWS_BUCKET_NAME,
    env.AWS_REGION,
  );

  const fileUploadRepository = new PrismaFileUploadRepository();
  const fetchHttpClient = new FetchHttpClient();

  const reportGateway = new ReportApiGateway(fetchHttpClient);
  const emailGateway = new EmailGateway(fetchHttpClient);

  const serviceEmail = new PrismaEmailIncomeReportRepository();

  const useCase = new RequestIncomeReportUseCase(
    prismaContractRepository,
    prismaInvestorRepository,
    prismaIncomeReportRepository,
    reportGateway,
    awsS3StorageGateway,
    fileUploadRepository,
    serviceEmail,
    emailGateway,
  );

  return useCase;
}
