import { Contract } from '@/contexts/income-report/domain/entities/contract';
import { IncomeReportEmailPayload } from '@/contexts/income-report/domain/entities/email-income-report';
import { IncomeReport } from '@/contexts/income-report/domain/entities/income-report';
import type { BankData } from '@/contexts/income-report/domain/entities/investor';
import type { ContractRepository } from '@/contexts/income-report/domain/repositories/contract-repository';
import prisma from '@/infrastructure/prisma/client';

export class PrismaContractRepository implements ContractRepository {
  async findByInvestorId(investorId: string): Promise<Contract[]> {
    const contractsList: Contract[] = [];

    const contracts = await prisma.contract.findMany({
      where: {
        investor_id: investorId,
      },
      include: {
        addendum: { where: { status: 'FULLY_SIGNED' } },
        pre_register: true,
        owner_role_relation_contract_owner_role_relationToowner_role_relation: {
          include: {
            owner: true,
            business: true,
          },
        },
        contract_advisor: {
          include: {
            owner_role_relation: {
              include: {
                owner: {
                  select: { name: true },
                },
                business: true,
              },
            },
          },
        },
        owner_role_relation_contract_investor_idToowner_role_relation: {
          include: {
            owner: { include: { address: true } },
            business: { include: { address: true } },
          },
        },
      },
    });

    for (const contract of contracts) {
      const broker =
        contract.owner_role_relation_contract_owner_role_relationToowner_role_relation;

      const investmentData = contract.pre_register[0];

      const bankData: BankData = {
        bank: investmentData.bank ?? '',
        agency: investmentData.agency ?? '',
        account: investmentData.account ?? '',
        pixKey: investmentData.pix ?? '',
      };

      const investor =
        contract.owner_role_relation_contract_investor_idToowner_role_relation;

      if (!investor) {
        continue;
      }

      if (investor.owner) {
        const investorAddress = investor.owner?.address[0];

        const contractData: Contract = new Contract(
          contract.id,
          {
            id: investor.id,
            document: investor.owner.cpf,
            address: {
              street: investorAddress.street,
              number: investorAddress.number,
              city: investorAddress.city,
              state: investorAddress.state,
              zipCode: investorAddress.cep,
            },
            type: 'PF',
            name: investor.owner.name,
            email: investor.owner.email,
            bankData,
          },
          {
            name: broker?.owner?.name ?? broker?.business?.fantasy_name ?? '',
            role: 'BROKER',
            rate: broker?.part_percent?.toNumber() ?? 0,
          },
          [],
          investmentData.investment_yield.toNumber(),
          contract.start_contract,
          contract.addendum.map((addend) => ({
            amount: addend.addendum_value.toNumber(),
            endDate: addend.expires_in,
            id: addend.id_addendum.toString(),
            startDate: addend.application_date,
          })),
          investmentData.investment_value.toNumber(),
          contract.proof_payment ?? '',
          contract.contract_pdf ?? '',
          contract.end_contract,
        );

        contractsList.push(contractData);
      } else {
        const business = investor.business;
        if (!business) {
          continue;
        }

        if (!business.owner_id) {
          continue;
        }

        const representative = await prisma.owner.findFirst({
          where: { id: business.owner_id },
          include: { address: true },
        });

        if (!representative) {
          continue;
        }

        const companyAddress = business.address[0];
        const representativeAddress = representative.address[0];

        const contractData: Contract = new Contract(
          contract.id,
          {
            id: investor.id,
            document: business.cnpj,
            address: {
              street: companyAddress.street,
              number: companyAddress.number,
              city: companyAddress.city,
              state: companyAddress.state,
              zipCode: companyAddress.cep,
            },
            representative: {
              name: representative.name,
              document: representative.cpf,
              email: representative.email,
              address: {
                street: representativeAddress.street,
                number: representativeAddress.number,
                city: representativeAddress.city,
                state: representativeAddress.state,
                zipCode: representativeAddress.cep,
              },
              bankData,
            },
            type: 'PJ',
            name: business.fantasy_name,
            email: business.email,
            bankData,
          },
          {
            name: broker?.owner?.name ?? broker?.business?.fantasy_name ?? '',
            role: 'BROKER',
            rate: broker?.part_percent?.toNumber() ?? 0,
          },
          [],
          investmentData.investment_yield.toNumber(),
          contract.start_contract,
          contract.addendum.map((addend) => ({
            amount: addend.addendum_value.toNumber(),
            endDate: addend.expires_in,
            id: addend.id_addendum.toString(),
            startDate: addend.application_date,
          })),
          investmentData.investment_value.toNumber(),
          contract.proof_payment ?? '',
          contract.contract_pdf ?? '',
          contract.end_contract,
        );

        contractsList.push(contractData);
      }
    }

    return contractsList;
  }
  async findByIncomeReportId(
    investorId: string,
    year: number,
  ): Promise<IncomeReport | null> {
    const response = await prisma.income_report.findFirst({
      where: {
        investor_id: investorId,
        reference_year: year.toString(),
      },
      include: {
        files: true,
      },
    });
    const url = response?.files?.url;
    return response
      ? {
          id: response.id,
          investor_id: response.investor_id?.toString() ?? '',
          status: response.status,
          reference_year: response.reference_year,
          inportReportUrl: url ?? '',
        }
      : null;
  }
  async findIncomeReportEmail(
    investorId: string,
    year: number,
  ): Promise<IncomeReportEmailPayload | null> {
    const response = await prisma.income_report.findFirst({
      where: {
        investor_id: investorId,
        reference_year: year.toString(),
      },
      include: {
        income_report_email: {
          orderBy: {
            email: {
              created_at: 'desc',
            },
          },
          take: 1,
          include: {
            email: true,
          },
        },
      },
    });
    if (!response?.income_report_email) return null;
    const email = response.income_report_email[0]?.email;

    if (!email) return null;

    return response
      ? {
          id: email.id,
          body: email.body ? JSON.parse(email.body) : null,
          error_message: email.error_message,
          status: email.status ?? '',
          from: email.from,
        }
      : null;
  }
}
