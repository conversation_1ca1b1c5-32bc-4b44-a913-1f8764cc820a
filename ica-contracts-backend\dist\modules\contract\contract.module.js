"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const apis_module_1 = require("../../apis/apis.module");
const contract_deletion_entity_1 = require("../../shared/database/typeorm/entities/contract-deletion.entity");
const contract_entity_1 = require("../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../shared/database/typeorm/entities/owner-role-relation.entity");
const validate_adviser_owner_middleware_1 = require("../../shared/middlewares/validate-adviser-owner.middleware");
const shared_module_1 = require("../../shared/shared.module");
const notification_module_1 = require("../notifications/notification.module");
const contract_controller_1 = require("./controller/contract.controller");
const add_signataries_service_1 = require("./services/add-signataries.service");
const create_contract_additive_manual_service_1 = require("./services/create-contract-additive-manual.service");
const create_contract_additive_service_1 = require("./services/create-contract-additive.service");
const check_duplicate_contract_handler_1 = require("./services/create-contract-manual/concrete-handlers/check-duplicate-contract.handler");
const send_contract_handler_1 = require("./services/create-contract-manual/concrete-handlers/send-contract.handler");
const validate_advisors_handler_1 = require("./services/create-contract-manual/concrete-handlers/validate-advisors.handler");
const create_contract_manual_service_1 = require("./services/create-contract-manual/create-contract-manual.service");
const create_contract_service_1 = require("./services/create-contract.service");
const delete_contract_service_1 = require("./services/delete-contract.service");
const editt_new_contract_service_1 = require("./services/editt-new-contract.service");
const get_contract_by_investor_service_1 = require("./services/get-contract-by-investor.service");
const get_contract_detail_service_1 = require("./services/get-contract-detail.service");
const get_contracts_service_1 = require("./services/get-contracts.service");
const get_contrat_addendums_by_id_service_1 = require("./services/get-contrat-addendums-by-id.service");
const get_one_contracts_service_1 = require("./services/get-one-contracts.service");
const list_contracts_superadmin_service_1 = require("./services/list-contracts-superadmin.service");
const renew_contract_service_1 = require("./services/renew-contract.service");
const send_notification_service_1 = require("./services/send-notification.service");
const upload_proof_payment_addendum_service_1 = require("./services/upload-proof-payment-addendum.service");
const upload_proof_payment_service_1 = require("./services/upload-proof-payment.service");
let ContractModule = class ContractModule {
    configure(consumer) {
        consumer
            .apply(validate_adviser_owner_middleware_1.ValidateAdviserOwnerMiddleware)
            .forRoutes({ path: 'contract', method: common_1.RequestMethod.GET }, { path: 'contract/add-signatories', method: common_1.RequestMethod.POST }, { path: 'contract/add-signatories', method: common_1.RequestMethod.POST });
    }
};
exports.ContractModule = ContractModule;
exports.ContractModule = ContractModule = __decorate([
    (0, common_1.Module)({
        imports: [
            shared_module_1.SharedModule,
            apis_module_1.ApisModule,
            notification_module_1.NotificationModule,
            typeorm_1.TypeOrmModule.forFeature([
                contract_entity_1.ContractEntity,
                owner_role_relation_entity_1.OwnerRoleRelationEntity,
                contract_deletion_entity_1.ContractDeletionEntity,
            ]),
        ],
        controllers: [contract_controller_1.ContractController],
        providers: [
            create_contract_service_1.CreateContractService,
            get_contracts_service_1.GetContractsService,
            get_one_contracts_service_1.GetOneContractService,
            add_signataries_service_1.AddSignatoriesService,
            create_contract_manual_service_1.CreateContractManualService,
            renew_contract_service_1.RenewContractService,
            get_contract_by_investor_service_1.GetContractsByInvestorService,
            upload_proof_payment_service_1.UploadProofPaymentService,
            send_notification_service_1.SendEmailNotificationContract,
            create_contract_additive_service_1.CreateContractAdditiveService,
            create_contract_additive_manual_service_1.CreateContractAdditiveManualService,
            check_duplicate_contract_handler_1.CheckDuplicateContractHandler,
            send_contract_handler_1.SendContractHandler,
            validate_advisors_handler_1.ValidateAdvisorHandler,
            list_contracts_superadmin_service_1.ListContractsSuperadminService,
            get_contrat_addendums_by_id_service_1.GetContratAddendumsByIdService,
            upload_proof_payment_addendum_service_1.UploadProofPaymentAddendumService,
            get_contract_detail_service_1.GetContractDetailService,
            delete_contract_service_1.DeleteContractService,
            editt_new_contract_service_1.EditNewContractService,
        ],
        exports: [get_contract_by_investor_service_1.GetContractsByInvestorService],
    })
], ContractModule);
//# sourceMappingURL=contract.module.js.map