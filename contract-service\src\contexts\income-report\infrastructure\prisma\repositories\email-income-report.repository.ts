import { IncomeReportEmailPayload } from '@/contexts/income-report/domain/entities/email-income-report';
import { EmailIncomeReportRepository } from '@/contexts/income-report/domain/repositories/email-income-report.respository';
import { Either, left, right } from '@/domain/shared';

import prisma from '@/infrastructure/prisma/client';

export class PrismaEmailIncomeReportRepository
  implements EmailIncomeReportRepository
{
  async save(
    incomeEmail: IncomeReportEmailPayload,
    incomeReportId: string,
  ): Promise<Either<Error, undefined>> {
    try {
      await prisma.email.create({
        data: {
          from: '<EMAIL>',
          body: JSON.stringify(incomeEmail.body),
          body_type: 'json',
          error_message: incomeEmail.error_message,
          id: incomeEmail.id,
          status: incomeEmail.status,
          reply: incomeEmail.body.to_email,
          income_report_email: {
            create: {
              income_report_id: incomeReportId,
            },
          },
        },
      });

      return right(undefined);
    } catch (error) {
      return left(error as Error);
    }
  }
  async update(incomeEmail: IncomeReportEmailPayload): Promise<void> {
    await prisma.email.update({
      where: {
        id: incomeEmail.id,
      },
      data: {
        id: incomeEmail.id,
        status: incomeEmail.status,
        external_id: incomeEmail.external_id,
      },
    });
  }
}
