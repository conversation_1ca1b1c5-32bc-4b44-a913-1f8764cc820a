"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPixKeyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_key_celcoin_service_1 = require("../../../apis/celcoin/services/pix-key-celcoin.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const account_status_enum_1 = require("../../../shared/enums/account-status.enum");
const typeorm_2 = require("typeorm");
let GetPixKeyService = class GetPixKeyService {
    constructor(accountDb, celcoinService) {
        this.accountDb = accountDb;
        this.celcoinService = celcoinService;
    }
    async perform(id) {
        const account = await this.accountDb.findOne({
            relations: {
                business: true,
                owner: true,
                pixKey: true,
            },
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
        });
        if (!account ||
            !account.number ||
            account.status !== account_status_enum_1.AccountStatusEnum.ACTIVE)
            throw new common_1.NotFoundException('Conta não encontrada ou nāo aprovada.');
        const { body: result } = await this.celcoinService.getAccountPixKeys({
            accountNumber: account.number,
        });
        const output = [];
        for (const pixKey of result.listKeys) {
            const { key } = pixKey;
            const matchingPixKey = account.pixKey.find((accountPixKey) => accountPixKey.key === key);
            if (matchingPixKey) {
                pixKey.id = matchingPixKey.id;
                output.push(pixKey);
            }
        }
        return output.map((pixKey) => ({
            id: pixKey.id,
            type: pixKey.keyType,
            key: pixKey.key,
        }));
    }
};
exports.GetPixKeyService = GetPixKeyService;
exports.GetPixKeyService = GetPixKeyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, common_1.Inject)(pix_key_celcoin_service_1.PixKeyCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        pix_key_celcoin_service_1.PixKeyCelcoinService])
], GetPixKeyService);
//# sourceMappingURL=get-pix-key.service.js.map