export class Notification {
  private errors: { field: string; message: string }[] = []

  addError(field: string, message: string): void {
    this.errors.push({ field, message })
  }

  getErrors(): { field: string; message: string }[] {
    return this.errors
  }

  hasErrors(): boolean {
    return this.errors.length > 0
  }

  messages(): string {
    return this.errors
      .map(error => `[${error.field}] ${error.message}`)
      .join('; ')
  }
}
