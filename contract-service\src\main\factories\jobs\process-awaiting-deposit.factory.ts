import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories';
import { QueueNames } from '@/main/config/queue-names';
import { createQueueAdapter } from '../bullmq';
import { AwaitingDepositPayment } from '@/infrastructure/jobs/node-cron/awaiting-deposit-payment.job';

export function makeProcessAwaitingDepositJob() {
  const investorRepository = new PrismaInvestmentContractRepository();

  const queueGateway = createQueueAdapter(QueueNames.PROCESS_AWAITING_DEPOSIT);

  const awaitingDepositPayment = new AwaitingDepositPayment(
    investorRepository,
    queueGateway
  );

  return awaitingDepositPayment;
}
