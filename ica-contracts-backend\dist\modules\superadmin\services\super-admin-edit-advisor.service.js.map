{"version": 3, "file": "super-admin-edit-advisor.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/super-admin-edit-advisor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,sFAAuF;AAEvF,qHAA0G;AAC1G,iEAAwD;AACxD,qCAAqC;AAG9B,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IACvC,YAEmB,2BAAgE,EAChE,kBAAsC;QADtC,gCAA2B,GAA3B,2BAA2B,CAAqC;QAChE,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAAsB;QAClC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACpE,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO,CAAC,mBAAmB;gBAC/B,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,OAAO;iBACxB;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE;gBAC/D,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;aACrC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACxD,CAAC;CACF,CAAA;AA/BY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCACI,oBAAU;QACnB,yCAAkB;GAJ9C,4BAA4B,CA+BxC", "sourcesContent": ["import { Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { EditAdvisorService } from 'src/modules/advisor/services/edit-advisor.service';\r\nimport { EditBrokerDto } from 'src/modules/broker/dto/edit-broker.dto';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\n@Injectable()\r\nexport class SuperAdminEditAdvisorService {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    private readonly editAdvisorService: EditAdvisorService,\r\n  ) {}\r\n\r\n  async perform(payload: EditBrokerDto) {\r\n    const advisorProfile = await this.ownerRoleRelationRepository.findOne({\r\n      relations: {\r\n        role: true,\r\n      },\r\n      where: {\r\n        id: payload.ownerRoleRelationId,\r\n        role: {\r\n          name: RolesEnum.ADVISOR,\r\n        },\r\n      },\r\n    });\r\n\r\n    if (!advisorProfile) {\r\n      throw new NotFoundException('Assessor não encontrado');\r\n    }\r\n    if (payload.rate) {\r\n      await this.ownerRoleRelationRepository.update(advisorProfile.id, {\r\n        partPercent: payload.rate.toString(),\r\n      });\r\n    }\r\n\r\n    return await this.editAdvisorService.perform(payload);\r\n  }\r\n}\r\n"]}