import {
  type AddendumStatus,
  ContractAddendum,
} from '@/domain/entities/contracts/contract-addenduns.entity'
import { Money } from '@/domain/value-objects'
import type { Prisma } from '@prisma/client'

type AddendumPrismaType = Prisma.addendumGetPayload<{
  include: {
    income_payment_scheduled_addendum: {
      include: {
        income_payment_scheduled: {
          select: {
            scheduled_date: true
          }
        }
      }
    }
  }
}>

// Valor total dos aditivos durante

export class PrismaAddendumMapper {
  static toDomainAddendum(raw: AddendumPrismaType[]): ContractAddendum[] {
    const addendums: ContractAddendum[] = []

    for (const addendum of raw) {
      const income_payment_addendum =
        addendum.income_payment_scheduled_addendum.map(item => {
          return {
            proportional_days: item.proportional_days,
            payment_date: item.income_payment_scheduled?.scheduled_date,
          }
        })

      const valueApplicationAddendum = Money.create(
        Number(addendum.addendum_value)
      )

      if (valueApplicationAddendum.isRight()) {
        addendums.push(
          ContractAddendum.create(
            valueApplicationAddendum.value,
            addendum.application_date,
            addendum.expires_in,
            addendum.status as AddendumStatus,
            income_payment_addendum,
            Number(addendum.yield_rate)
          )
        )
      }
    }

    return addendums
  }

  static toPrismaAddendum() {}
}
