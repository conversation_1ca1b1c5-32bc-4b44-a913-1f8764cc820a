import { BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';

export class GetAddressService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountRepository: Repository<AccountEntity>,
  ) {}

  async execute(userId: string): Promise<void> {
    const account = await this.accountRepository.findOne({
      where: [
        {
          ownerId: userId,
        },
        {
          businessId: userId,
        },
      ],
      relations: {
        owner: { address: true },
        business: { address: true },
      },
    });

    if (!account) throw new NotFoundException('Conta não encontrada!');

    let address: any;

    if (account.owner) {
      address = account.owner.address;
    }

    if (account.business) {
      address = account.business.address;
    }

    if (!address)
      throw new BadRequestException(
        'Conta não possui nenhum endereço cadastrado',
      );

    return address;
  }
}
