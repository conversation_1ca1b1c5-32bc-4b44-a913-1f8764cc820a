import { AccountTypeEnum } from 'src/shared/enums/account-type.enum';
declare class Investment {
    value: number;
    term: string;
    modality: string;
    yield: number;
    purchaseWith: string;
    amountQuotes: number;
    startContract: Date;
    endContract: Date;
    gracePeriod: Date;
    debenture: boolean;
    brokerParticipationPercentage: string;
    advisorParticipationPercentage: string;
    observations: string;
}
declare class Owner {
    cpf: string;
    name: string;
}
export declare class SendPreRegisterDto {
    adviserId: string;
    email: string;
    document: string;
    accountType: AccountTypeEnum;
    owner: Owner;
    investment: Investment;
    signIca: string;
}
export {};
