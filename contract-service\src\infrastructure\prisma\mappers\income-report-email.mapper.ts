import { IncomeReportEmail } from '@/domain/entities/reports/income-report-email.entity'
import type { EmailSendStatus } from '@/domain/entities/reports/income-report-email.entity'
import { type Either, left, right } from '@/domain/shared'
import { EmailMessage } from '@/domain/value-objects'

interface EmailPersistence {
  id: string
  from: string
  reply?: string
  body?: string
  body_type?: string
  status: string
  sent_at?: Date
  failed_at?: Date
  error_message?: string
  created_at: Date
}

export class IncomeReportEmailMapper {
  static toDomain(raw: EmailPersistence): Either<Error, IncomeReportEmail> {
    if (!raw.from) return left(new Error('Email is missing sender'))

    const emailMessage = EmailMessage.create(
      raw.from,
      raw.reply,
      raw.body,
      raw.body_type
    )

    const emailEntity = IncomeReportEmail.createFromExisting(
      {
        emailMessage,
        status: raw.status as EmailSendStatus,
        sentAt: raw.sent_at,
        failedAt: raw.failed_at,
        errorMessage: raw.error_message,
        createdAt: raw.created_at,
      },
      raw.id
    )

    return right(emailEntity)
  }

  static toPersistence(entity: IncomeReportEmail): EmailPersistence {
    return {
      id: entity.id,
      from: entity.getEmailMessage().getFrom(),
      reply: entity.getEmailMessage().getReplyTo(),
      body: entity.getEmailMessage().getBody(),
      body_type: entity.getEmailMessage().getBodyType(),
      status: entity.getStatus(),
      sent_at: entity.getSentAt(),
      failed_at: entity.getFailedAt(),
      error_message: entity.getErrorMessage(),
      created_at: entity.createdAt,
    }
  }
}
