"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuperAdminService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const superadmin_entity_1 = require("../../../shared/database/typeorm/entities/superadmin.entity");
const typeorm_2 = require("typeorm");
let SuperAdminService = class SuperAdminService {
    constructor(superAdminRepository) {
        this.superAdminRepository = superAdminRepository;
    }
    async perform(input) {
        const superAdmin = await this.superAdminRepository.findOne({
            where: { ownerId: input.ownerId || null },
            relations: { admin: { owner: true } },
        });
        if (!superAdmin) {
            throw new common_1.NotFoundException(`Superadmin with ID ${input.ownerId} not found.`);
        }
        const response = {
            superadmin: {
                superAdminId: superAdmin.id,
                ownerId: superAdmin.ownerId,
                admin: [],
            },
        };
        for (const admin of superAdmin.admin) {
            const adminObj = {
                adminId: admin.id,
                ownerId: admin.ownerId,
                name: admin.owner?.name || null,
                cpf: admin.owner?.cpf || null,
                phone: admin.owner?.phone || null,
                email: admin.owner?.email || null,
                createdAt: admin.owner?.createdAt || null,
            };
            response.superadmin.admin.push(adminObj);
        }
        return response;
    }
};
exports.SuperAdminService = SuperAdminService;
exports.SuperAdminService = SuperAdminService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(superadmin_entity_1.SuperadminEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SuperAdminService);
//# sourceMappingURL=find-super-admin.service.js.map