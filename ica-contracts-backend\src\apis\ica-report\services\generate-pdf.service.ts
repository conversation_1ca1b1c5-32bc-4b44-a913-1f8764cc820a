import { Injectable, HttpException } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';

import { IGeneratePdfRequest } from '../requests/generate-pdf.request';

@Injectable()
export class GeneratePdfService {
  async generatePdf(request: IGeneratePdfRequest): Promise<string> {
    try {
      const url =
        'https://reportgen.prodbr01.icabank.org/generate_pdf?download=1';

      const { data }: AxiosResponse<string> = await axios.post(url, request, {
        headers: {
          'Content-Type': 'application/json',
        },
        responseType: 'arraybuffer',
      });

      return data;
    } catch (error) {
      console.log(error);

      throw new HttpException(
        error.response?.data?.message || 'Error generating PDF',
        error.response?.data?.statusCode || 500,
      );
    }
  }
}
