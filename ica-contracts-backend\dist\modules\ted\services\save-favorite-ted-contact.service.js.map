{"version": 3, "file": "save-favorite-ted-contact.service.js", "sourceRoot": "/", "sources": ["modules/ted/services/save-favorite-ted-contact.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwE;AACxE,6CAAmD;AACnD,6FAAoF;AACpF,uGAA6F;AAC7F,qCAA4C;AAI5C,IAAa,6BAA6B,GAA1C,MAAa,6BAA6B;IACxC,YAEU,WAAsC,EAEtC,eAA8C;QAF9C,gBAAW,GAAX,WAAW,CAA2B;QAEtC,oBAAe,GAAf,eAAe,CAA+B;IACrD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAA+B,EAAE,EAAU;QACvD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE,CAAC;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,0BAAiB,CACzB,iDAAiD,CAClD,CAAC;QAEJ,MAAM,yBAAyB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACnE,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE;SACpE,CAAC,CAAC;QAEH,IAAI,yBAAyB;YAC3B,MAAM,IAAI,4BAAmB,CAAC,oBAAoB,CAAC,CAAC;QAEtD,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAEO,KAAK,CAAC,wBAAwB,CACpC,KAAgC,EAChC,OAAsB;QAEtB,MAAM,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YACrD,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,aAAa,EAAE,KAAK,CAAC,aAAa;YAClC,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,aAAa,EAAE,KAAK,CAAC,aAAa;YAClC,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACtD,CAAC;CACF,CAAA;AA5CY,sEAA6B;wCAA7B,6BAA6B;IAErC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCADf,oBAAU;QAEN,oBAAU;GAL1B,6BAA6B,CA4CzC", "sourcesContent": ["import { BadRequestException, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { FavoriteTedEntity } from 'src/shared/database/typeorm/entities/favorite-ted.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { SaveFavoriteTedContactDto } from '../dto/save-favorite-ted-contact.dto';\r\n\r\nexport class SaveFavoriteTedContactService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountRepo: Repository<AccountEntity>,\r\n    @InjectRepository(FavoriteTedEntity)\r\n    private favoriteTedRepo: Repository<FavoriteTedEntity>,\r\n  ) {}\r\n\r\n  async execute(data: SaveFavoriteTedContactDto, id: string): Promise<void> {\r\n    const account = await this.accountRepo.findOne({\r\n      where: [{ ownerId: Equal(id) }, { businessId: Equal(id) }],\r\n    });\r\n\r\n    if (!account)\r\n      throw new NotFoundException(\r\n        'Conta não encontrada ou não vinculada ao owner.',\r\n      );\r\n\r\n    const favoriteTedContactFounded = await this.favoriteTedRepo.findOne({\r\n      where: { accountNumber: data.accountNumber, accountId: account.id },\r\n    });\r\n\r\n    if (favoriteTedContactFounded)\r\n      throw new BadRequestException('Contato existente!');\r\n\r\n    await this.createFavoriteTedContact(data, account);\r\n  }\r\n\r\n  private async createFavoriteTedContact(\r\n    input: SaveFavoriteTedContactDto,\r\n    account: AccountEntity,\r\n  ) {\r\n    const favoriteTecContact = this.favoriteTedRepo.create({\r\n      name: input.name,\r\n      accountBank: input.accountBank,\r\n      accountBranch: input.accountBranch,\r\n      accountId: account.id,\r\n      accountNumber: input.accountNumber,\r\n      alias: input.alias,\r\n      document: input.document,\r\n    });\r\n\r\n    await this.favoriteTedRepo.save(favoriteTecContact);\r\n  }\r\n}\r\n"]}