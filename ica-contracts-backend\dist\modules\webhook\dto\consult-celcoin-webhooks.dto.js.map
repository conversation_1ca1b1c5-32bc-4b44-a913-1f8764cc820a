{"version": 3, "file": "consult-celcoin-webhooks.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/consult-celcoin-webhooks.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsD;AAEtD,MAAa,wBAAwB;CAQpC;AARD,4DAQC;AALC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wDACI;AAIf;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wDACI", "sourcesContent": ["import { IsString, IsDefined } from 'class-validator';\r\n\r\nexport class ConsultCelcoinWebhookDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  active: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  entity: string;\r\n}\r\n"]}