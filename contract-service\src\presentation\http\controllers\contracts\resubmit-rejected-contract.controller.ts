import type { HttpRequest, HttpResponse } from '@/presentation/http/protocols';
import type { IController } from '@/presentation/http/protocols/controller';
import type { IValidator } from '../../validation/validator';
import type { IUseCase } from '@/application/interfaces/usecase';
import type { ResubmitRejectedContractDTO } from '@/application/dtos/contracts/resubmit-rejected-contract.dto';
import { ContractStatus } from '@/domain/entities/contracts/investment-contract.entity';
import { Either } from '@/domain/shared';
import { badRequest, ok } from '../../helpers/http-helper';

interface ResubmitRejectedContractResponse {
  id: string;
  status: ContractStatus;
}

export class ResubmitRejectedContractController implements IController {
  constructor(
    private readonly validator: IValidator<ResubmitRejectedContractDTO>,
    private readonly useCase: IUseCase<
      ResubmitRejectedContractDTO,
      Promise<Either<Error, ResubmitRejectedContractResponse>>
    >
  ) {}

  async handle(request: HttpRequest): Promise<HttpResponse> {
    if (!request.params?.id) {
      return badRequest({
        error: 'ValidationError',
        message: ['Contract ID is required'],
      });
    }

    const validationResult = this.validator.validate({
      ...request.body,
      contractId: request.params.id,
    });

    if (validationResult.isLeft()) {
      const error = validationResult.value;
      return badRequest({ error: error.name, message: error.errors });
    }

    const result = await this.useCase.execute(validationResult.value);

    if (result.isLeft()) {
      return badRequest(result.value);
    }

    return ok(result.value);
  }
}
