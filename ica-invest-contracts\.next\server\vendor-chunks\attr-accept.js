/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/attr-accept";
exports.ids = ["vendor-chunks/attr-accept"];
exports.modules = {

/***/ "(ssr)/./node_modules/attr-accept/dist/index.js":
/*!************************************************!*\
  !*** ./node_modules/attr-accept/dist/index.js ***!
  \************************************************/
/***/ ((module) => {

eval("module.exports=function(e){var r={};function t(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=r,t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:n})},t.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})},t.t=function(e,r){if(1&r&&(e=t(e)),8&r)return e;if(4&r&&\"object\"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(t.r(n),Object.defineProperty(n,\"default\",{enumerable:!0,value:e}),2&r&&\"string\"!=typeof e)for(var o in e)t.d(n,o,function(r){return e[r]}.bind(null,o));return n},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,\"a\",r),r},t.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},t.p=\"\",t(t.s=0)}([function(e,r,t){\"use strict\";r.__esModule=!0,r.default=function(e,r){if(e&&r){var t=Array.isArray(r)?r:r.split(\",\");if(0===t.length)return!0;var n=e.name||\"\",o=(e.type||\"\").toLowerCase(),u=o.replace(/\\/.*$/,\"\");return t.some((function(e){var r=e.trim().toLowerCase();return\".\"===r.charAt(0)?n.toLowerCase().endsWith(r):r.endsWith(\"/*\")?u===r.replace(/\\/.*$/,\"\"):o===r}))}return!0}}]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/attr-accept/dist/index.js\n");

/***/ })

};
;