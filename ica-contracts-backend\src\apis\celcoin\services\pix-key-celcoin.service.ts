import {
  Injectable,
  Inject,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';
import { logger } from 'src/shared/logger';

import { ICreateAccountPixKeysCelcoinRequest } from '../requests/create-account-pix-key-celcoin.request';
import { IDeleteAccountPixKeyCelcoinRequest } from '../requests/delete-account-pix-key-celcoin.request';
import { IGetAccountPixKeysCelcoinRequest } from '../requests/get-account-pix-keys-celcoin.request';
import { IRequestClaimPixKeyCelcoinRequest } from '../requests/request-claim-pix-key.request';
import { ICreateAccountPixKeysCelcoinResponse } from '../responses/create-account-pix-key-celcoin.response';
import { IGetAccountPixKeysCelcoinResponse } from '../responses/get-account-pix-keys-celcoin.response';
import { IRequestClaimPixKeyCelcoinResponse } from '../responses/request-claim-pix-key.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class PixKeyCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async getAccountPixKeys(input: IGetAccountPixKeysCelcoinRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/entry/${input.accountNumber}`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IGetAccountPixKeysCelcoinResponse> =
        await axios.get(url, config);
      return data;
    } catch (error) {
      logger.error('PixKeyCelcoinService.getAccountPixKeys() -> ', error);
      if (error.response.data.error?.errorCode === 'CBE180') {
        return {
          body: {
            listKeys: [],
          },
        };
      }
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async createAccountPixKeys(input: ICreateAccountPixKeysCelcoinRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/entry`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<ICreateAccountPixKeysCelcoinResponse> =
        await axios.post(url, input, config);

      return data;
    } catch (error) {
      logger.error('PixKeyCelcoinService.createAccountPixKeys() -> ', error);
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async deleteAccountPixKey(
    input: IDeleteAccountPixKeyCelcoinRequest,
  ): Promise<void> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/entry/${input.pixKey}`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      data: {
        account: input.accountNumber,
      },
      httpsAgent,
    };

    try {
      axios.delete(url, config);
    } catch (error) {
      logger.error(
        'PixKeyCelcoinService.deleteAccountPixKey() -> ',
        error.response.data,
      );
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async requestClaimPixKey(
    input: IRequestClaimPixKeyCelcoinRequest,
  ): Promise<IRequestClaimPixKeyCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/claim`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IRequestClaimPixKeyCelcoinResponse> =
        await axios.post(url, input, config);
      return data;
    } catch (error) {
      logger.error(
        'PixKeyCelcoinService.requestClaimPixKey() -> ',
        error.response,
      );

      throw new InternalServerErrorException(error.response.data);
    }
  }
}
