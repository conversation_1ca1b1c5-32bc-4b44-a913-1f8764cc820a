{"version": 3, "file": "edit-investor.service.js", "sourceRoot": "/", "sources": ["modules/investor/services/edit-investor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAMwB;AACxB,6CAAmD;AACnD,qCAAqC;AAErC,6FAAyF;AACzF,+FAA2F;AAC3F,qHAA+G;AAC/G,yFAAqF;AACrF,yGAAoG;AACpG,iEAA6D;AAOtD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEmB,2BAAgE,EAEhE,sBAAsD,EAEtD,eAAwC,EAExC,kBAA8C,EAE9C,iBAA4C;QAR5C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,2BAAsB,GAAtB,sBAAsB,CAAgC;QAEtD,oBAAe,GAAf,eAAe,CAAyB;QAExC,uBAAkB,GAAlB,kBAAkB,CAA4B;QAE9C,sBAAiB,GAAjB,iBAAiB,CAA2B;IAC5D,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAiB,EAChE,MAAc;QAEd,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CACzE;YACE,KAAK,EAAE;gBACL,EAAE,EAAE,mBAAmB;gBACvB,IAAI,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,QAAQ,EAAE;aACnC;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;gBACxB,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;aAC5B;SACF,CACF,CAAC;QAEF,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,MAAM,IAAI,0BAAiB,CAAC,2BAA2B,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAG3D,IAAI,oBAAoB,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC;YACxC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC;QAGD,IAAI,oBAAoB,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC;YAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACrE,CAAC;QAGD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,OAQC,EACD,KAAkB;QAElB,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,GAC9D,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC;YAC/C,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC;YACjD,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC;YACvD,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC;YACxD,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC;SACxD,CAAC,CAAC;QAEL,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YAC1C,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,QAAQ;YACf,QAAQ,EAAE,WAAW;YACrB,OAAO,EAAE,WAAW;YACpB,GAAG,EAAE,WAAW;YAChB,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SACzE,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,WAAmB;QAC3C,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,2BAA2B,CAAC;QAC5C,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,CAAC,CAAC;QAC/D,CAAC;QACD,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,GAAG,GAAG,MAAM,EAAE,CAAC;QAC7B,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,OAAe,EACf,QAA4B,EAC5B,SAAgD,EAChD,YAA2D;QAE3D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,MAAM,EAAE,CAAC,SAAS,CAAC;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,aAAa,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC1C,MAAM,IAAI,qCAA4B,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,kBAAkB,GACtB,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;YAChC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE;SACjC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEV,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,IAAI,qCAA4B,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,OAAe,EACf,WAAoB;QAEpB,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,WAAW,GAAG,qBAAqB,CAAC;QAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,qCAA4B,CACpC,wEAAwE,CACzE,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;QACzC,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,qCAA4B,CACpC,6CAA6C,CAC9C,CAAC;QACJ,CAAC;QAED,IAAI,UAAU,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;YAC7B,MAAM,IAAI,qCAA4B,CACpC,uDAAuD,CACxD,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,MAAM,EAAE,CAAC,SAAS,CAAC;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;YACvE,MAAM,IAAI,qCAA4B,CACpC,6CAA6C,CAC9C,CAAC;QACJ,CAAC;QAED,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,OAAe,EACf,OAAgB;QAEhB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;YAClD,SAAS,EAAE,oCAAoC;YAC/C,cAAc,EAAE,qCAAqC;SACtD,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,OAAe,EACf,QAAiB;QAEjB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;YACpD,SAAS,EAAE,wCAAwC;YACnD,cAAc,EAAE,uCAAuC;SACxD,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,OAAe,EACf,WAAoB;QAEpB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE;YAC1D,SAAS,EAAE,mCAAmC;YAC9C,cAAc,EAAE,yCAAyC;SAC1D,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,OAAe,EACf,WAAoB;QAEpB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE;YACzE,SAAS,EAAE,oCAAoC;YAC/C,cAAc,EAAE,0CAA0C;SAC3D,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,OAAe,EACf,cAQC;QAED,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,GAClE,cAAc,CAAC;QAEjB,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACnE,MAAM,IAAI,qCAA4B,CACpC,iEAAiE,CAClE,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,eAAe,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,qCAA4B,CACpC,uDAAuD,CACxD,CAAC;QACJ,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,SAAS,EAAE,CAAC,SAAS,CAAC;SACvB,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,8BAAa,EAAE,CAAC;QAC3C,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC;QACjC,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC;QACzB,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;QAC/B,cAAc,CAAC,YAAY,GAAG,YAAY,CAAC;QAC3C,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;QAC/B,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;QAC3B,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;QAC7B,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;QAEvC,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,OAAe,EACf,cAQC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CACjD,OAAO,EACP,cAAc,CACf,CAAC;QAEF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;YACtB,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;SAC7B,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,gBAAgB,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC;YACnC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,OAAwB,EACxB,QAAwB;QAExB,MAAM,CACJ,cAAc,EACd,cAAc,EACd,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,YAAY,EAEb,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpB,IAAI,CAAC,qBAAqB,CACxB,QAAQ,CAAC,EAAE,EACX,OAAO,CAAC,WAAW,EACnB,aAAa,EACb;gBACE,SAAS,EAAE,0CAA0C;gBACrD,cAAc,EAAE,sCAAsC;aACvD,CACF;YACD,IAAI,CAAC,qBAAqB,CACxB,QAAQ,CAAC,EAAE,EACX,OAAO,CAAC,WAAW,EACnB,aAAa,EACb;gBACE,SAAS,EAAE,wCAAwC;gBACnD,cAAc,EAAE,+CAA+C;aAChE,CACF;YACD,IAAI,CAAC,qBAAqB,CACxB,QAAQ,CAAC,EAAE,EACX,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAChC,MAAM,EACN;gBACE,SAAS,EAAE,+BAA+B;gBAC1C,cAAc,EAAE,sCAAsC;aACvD,CACF;YACD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE;gBAC9D,SAAS,EAAE,iCAAiC;gBAC5C,cAAc,EAAE,wCAAwC;aACzD,CAAC;YACF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;gBAC5D,SAAS,EAAE,+BAA+B;gBAC1C,cAAc,EAAE,EAAE;aACnB,CAAC;YACF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;gBAC5D,SAAS,EAAE,gCAAgC;gBAC3C,cAAc,EAAE,EAAE;aACnB,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC;SACvD,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE;YAChD,WAAW,EAAE,cAAc;YAC3B,WAAW,EAAE,cAAc;YAC3B,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO;YACb,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;SAC7D,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,UAAkB,EAClB,QAA4B,EAC5B,SAMU,EACV,YAA2D;QAE3D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,MAAM,EAAE,CAAC,SAAS,CAAC;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,gBAAgB,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC7C,MAAM,IAAI,qCAA4B,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,kBAAkB,GACtB,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;gBACnC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE;aACjC,CAAC,CAAC,GAAG,CAAC,CAAC;YAEV,IAAI,kBAAkB,EAAE,CAAC;gBACvB,MAAM,IAAI,qCAA4B,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,UAAkB,EAClB,YAAqB;QAErB,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,WAAW,GAAG,qBAAqB,CAAC;QAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,qCAA4B,CACpC,qEAAqE,CACtE,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1C,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,qCAA4B,CACpC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QAED,IAAI,UAAU,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;YAC7B,MAAM,IAAI,qCAA4B,CACpC,oDAAoD,CACrD,CAAC;QACJ,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;YACzB,MAAM,EAAE,CAAC,WAAW,CAAC;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,CAAC,CAAC;QACzD,CAAC;QAED,IACE,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,KAAK,UAAU,CAAC,OAAO,EAAE,EACvE,CAAC;YACD,MAAM,IAAI,qCAA4B,CACpC,0CAA0C,CAC3C,CAAC;QACJ,CAAC;QAED,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,MAAc,EACd,oBAA6C;QAG7C,IACE,oBAAoB,CAAC,OAAO,KAAK,MAAM;YACvC,oBAAoB,CAAC,UAAU,KAAK,MAAM,EAC1C,CAAC;YACD,OAAO;QACT,CAAC;QAGD,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QAGD,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAEhC,IAAI,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClE,OAAO;YACT,CAAC;QACH,CAAC;QAGD,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAGD,MAAM,IAAI,2BAAkB,CAC1B,oDAAoD,CACrD,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,OAAO,CAAC,MAAc;QAClC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC/D,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,KAAK,EAAE,EAAE;gBACpD,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,KAAK,EAAE,EAAE;aACxD;SACF,CAAC,CAAC;QACH,OAAO,CAAC,CAAC,SAAS,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,MAAc;QACnC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAChE,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,MAAM,EAAE,EAAE;gBACrD,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,MAAM,EAAE,EAAE;aACzD;SACF,CAAC,CAAC;QACH,OAAO,CAAC,CAAC,UAAU,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,MAAc,EACd,sBAA8B;QAE9B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC/D,KAAK,EAAE;gBACL,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC1B,MAAM,EAAE,EAAE,EAAE,EAAE,sBAAsB,EAAE;aACvC;YACD,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;SAC/B,CAAC,CAAC;QACH,OAAO,CAAC,CAAC,cAAc,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,MAAc;QACvC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YACpE,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,UAAU,EAAE,EAAE;SACjE,CAAC,CAAC;QACH,OAAO,CAAC,CAAC,cAAc,CAAC;IAC1B,CAAC;CACF,CAAA;AAjiBY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,yCAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,0BAAW,CAAC,CAAA;IAE7B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCAPc,oBAAU;QAEf,oBAAU;QAEjB,oBAAU;QAEP,oBAAU;QAEX,oBAAU;GAXrC,mBAAmB,CAiiB/B", "sourcesContent": ["import {\r\n  BadRequestException,\r\n  ForbiddenException,\r\n  Injectable,\r\n  NotFoundException,\r\n  UnprocessableEntityException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { AddressEntity } from '../../../shared/database/typeorm/entities/address.entity';\r\nimport { BusinessEntity } from '../../../shared/database/typeorm/entities/business.entity';\r\nimport { OwnerRoleRelationEntity } from '../../../shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { OwnerEntity } from '../../../shared/database/typeorm/entities/owner.entity';\r\nimport { WalletsViewsEntity } from '../../../shared/database/typeorm/entities/wallets-views.entity';\r\nimport { RolesEnum } from '../../../shared/enums/roles.enum';\r\nimport {\r\n  EditBrokerDto,\r\n  EditBusinessDto,\r\n} from '../../broker/dto/edit-broker.dto';\r\n\r\n@Injectable()\r\nexport class EditInvestorService {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @InjectRepository(WalletsViewsEntity)\r\n    private readonly walletsViewsRepository: Repository<WalletsViewsEntity>,\r\n    @InjectRepository(OwnerEntity)\r\n    private readonly ownerRepository: Repository<OwnerEntity>,\r\n    @InjectRepository(BusinessEntity)\r\n    private readonly businessRepository: Repository<BusinessEntity>,\r\n    @InjectRepository(AddressEntity)\r\n    private readonly addressRepository: Repository<AddressEntity>,\r\n  ) {}\r\n\r\n  async perform(\r\n    { ownerRoleRelationId, business, owner, address }: EditBrokerDto,\r\n    userId: string,\r\n  ) {\r\n    const investorRoleRelation = await this.ownerRoleRelationRepository.findOne(\r\n      {\r\n        where: {\r\n          id: ownerRoleRelationId,\r\n          role: { name: RolesEnum.INVESTOR },\r\n        },\r\n        relations: {\r\n          role: true,\r\n          owner: { address: true },\r\n          business: { address: true },\r\n        },\r\n      },\r\n    );\r\n\r\n    if (!investorRoleRelation) {\r\n      throw new NotFoundException('Investidor não encontrado');\r\n    }\r\n\r\n    await this.verifyPermissions(userId, investorRoleRelation);\r\n\r\n    // Update owner information if provided\r\n    if (investorRoleRelation.owner && owner) {\r\n      await this.uploadOwner(owner, investorRoleRelation.owner);\r\n    }\r\n\r\n    // Update business information if provided\r\n    if (investorRoleRelation.business && business) {\r\n      await this.uploadBusiness(business, investorRoleRelation.business);\r\n    }\r\n\r\n    // Update address if provided\r\n    if (address) {\r\n      await this.uploadAddress(investorRoleRelation.owner.id, address);\r\n    }\r\n  }\r\n\r\n  private async uploadOwner(\r\n    payload: {\r\n      name?: string;\r\n      document?: string;\r\n      email?: string;\r\n      phone?: string;\r\n      motherName?: string;\r\n      birthDate?: string;\r\n      nickName?: string;\r\n    },\r\n    owner: OwnerEntity,\r\n  ) {\r\n    const [newName, newEmail, newNickname, newBirthDay, newDocument] =\r\n      await Promise.all([\r\n        await this.validateName(owner.id, payload.name),\r\n        await this.validateEmail(owner.id, payload.email),\r\n        await this.validateNickname(owner.id, payload.nickName),\r\n        await this.validateBirthday(owner.id, payload.birthDate),\r\n        await this.validateDocument(owner.id, payload.document),\r\n      ]);\r\n\r\n    await this.ownerRepository.update(owner.id, {\r\n      name: newName,\r\n      email: newEmail,\r\n      nickname: newNickname,\r\n      dtBirth: newBirthDay,\r\n      cpf: newDocument,\r\n      motherName: owner.motherName,\r\n      phone: payload.phone ? this.formatPhoneNumber(payload.phone) : undefined,\r\n    });\r\n  }\r\n\r\n  private formatPhoneNumber(phoneNumber: string): string {\r\n    const digitsOnly = phoneNumber.replace(/\\D/g, '');\r\n    const pattern = /^(?:55)?(\\d{2})(\\d{8,9})$/;\r\n    const match = digitsOnly.match(pattern);\r\n    if (!match) {\r\n      throw new BadRequestException('Número de telefone inválido');\r\n    }\r\n    const ddd = match[1];\r\n    const number = match[2];\r\n    if (!digitsOnly.startsWith('55')) {\r\n      return `55${ddd}${number}`;\r\n    }\r\n    return digitsOnly;\r\n  }\r\n\r\n  private async validateField(\r\n    ownerId: string,\r\n    newValue: string | undefined,\r\n    fieldName: 'name' | 'email' | 'nickname' | 'cpf',\r\n    errorMessage: { sameValue: string; duplicateValue: string },\r\n  ): Promise<string | undefined> {\r\n    if (!newValue) {\r\n      return undefined;\r\n    }\r\n\r\n    const existingOwner = await this.ownerRepository.findOne({\r\n      where: { id: ownerId },\r\n      select: [fieldName], // Limita os campos retornados para otimizar a consulta\r\n    });\r\n\r\n    if (!existingOwner) {\r\n      throw new NotFoundException('Proprietário não encontrado.');\r\n    }\r\n\r\n    if (existingOwner[fieldName] === newValue) {\r\n      throw new UnprocessableEntityException(errorMessage.sameValue);\r\n    }\r\n\r\n    const valueAlreadyExists =\r\n      (await this.ownerRepository.count({\r\n        where: { [fieldName]: newValue },\r\n      })) > 0;\r\n\r\n    if (valueAlreadyExists) {\r\n      throw new UnprocessableEntityException(errorMessage.duplicateValue);\r\n    }\r\n\r\n    return newValue;\r\n  }\r\n\r\n  private async validateBirthday(\r\n    ownerId: string,\r\n    newBirthday?: string,\r\n  ): Promise<string | undefined> {\r\n    if (!newBirthday) {\r\n      return undefined;\r\n    }\r\n\r\n    const datePattern = /^\\d{4}-\\d{2}-\\d{2}$/;\r\n    if (!datePattern.test(newBirthday)) {\r\n      throw new UnprocessableEntityException(\r\n        'O formato da data de aniversário é inválido. Use o formato YYYY-MM-DD.',\r\n      );\r\n    }\r\n\r\n    const parsedDate = new Date(newBirthday);\r\n    if (Number.isNaN(parsedDate.getTime())) {\r\n      throw new UnprocessableEntityException(\r\n        'A data de aniversário fornecida é inválida.',\r\n      );\r\n    }\r\n\r\n    if (parsedDate >= new Date()) {\r\n      throw new UnprocessableEntityException(\r\n        'A data de aniversário deve ser anterior à data atual.',\r\n      );\r\n    }\r\n\r\n    const existingOwner = await this.ownerRepository.findOne({\r\n      where: { id: ownerId },\r\n      select: ['dtBirth'], // Limita os campos retornados para otimizar a consulta\r\n    });\r\n\r\n    if (!existingOwner) {\r\n      throw new NotFoundException('Usuário não encontrado.');\r\n    }\r\n\r\n    if (new Date(existingOwner.dtBirth).getTime() === parsedDate.getTime()) {\r\n      throw new UnprocessableEntityException(\r\n        'A nova data de aniversário é igual à atual.',\r\n      );\r\n    }\r\n\r\n    return parsedDate.toISOString().split('T').at(0);\r\n  }\r\n\r\n  private async validateName(\r\n    ownerId: string,\r\n    newName?: string,\r\n  ): Promise<string | undefined> {\r\n    return this.validateField(ownerId, newName, 'name', {\r\n      sameValue: 'O novo nome é igual ao nome atual.',\r\n      duplicateValue: 'Já existe um usuário com esse nome.',\r\n    });\r\n  }\r\n\r\n  private async validateEmail(\r\n    ownerId: string,\r\n    newEmail?: string,\r\n  ): Promise<string | undefined> {\r\n    return this.validateField(ownerId, newEmail, 'email', {\r\n      sameValue: 'O novo e-mail é igual ao e-mail atual.',\r\n      duplicateValue: 'Já existe um usuário com esse e-mail.',\r\n    });\r\n  }\r\n\r\n  private async validateNickname(\r\n    ownerId: string,\r\n    newNickname?: string,\r\n  ): Promise<string | undefined> {\r\n    return this.validateField(ownerId, newNickname, 'nickname', {\r\n      sameValue: 'O novo nickname é igual ao atual.',\r\n      duplicateValue: 'Já existe um usuário com esse nickname.',\r\n    });\r\n  }\r\n\r\n  private async validateDocument(\r\n    ownerId: string,\r\n    newDocument?: string,\r\n  ): Promise<string | undefined> {\r\n    return this.validateField(ownerId, newDocument?.replace(/\\D/g, ''), 'cpf', {\r\n      sameValue: 'O novo documento é igual ao atual.',\r\n      duplicateValue: 'Já existe um usuário com esse documento.',\r\n    });\r\n  }\r\n\r\n  private async validateAddress(\r\n    ownerId: string,\r\n    addressPayload: {\r\n      cep: string;\r\n      street: string;\r\n      neighborhood: string;\r\n      number: string;\r\n      city: string;\r\n      state: string;\r\n      complement?: string;\r\n    },\r\n  ): Promise<AddressEntity> {\r\n    const { cep, street, neighborhood, number, city, state, complement } =\r\n      addressPayload;\r\n\r\n    if (!cep || !street || !neighborhood || !number || !city || !state) {\r\n      throw new UnprocessableEntityException(\r\n        'Todos os campos obrigatórios do endereço devem ser preenchidos.',\r\n      );\r\n    }\r\n\r\n    const cepPattern = /^\\d{5}-\\d{3}$/;\r\n    if (!cepPattern.test(cep)) {\r\n      throw new UnprocessableEntityException(\r\n        'O formato do CEP é inválido. Use o formato 00000-000.',\r\n      );\r\n    }\r\n\r\n    const existingAddress = await this.ownerRepository.findOne({\r\n      where: { id: ownerId },\r\n      relations: ['address'],\r\n    });\r\n\r\n    if (!existingAddress) {\r\n      throw new NotFoundException('Usuário não encontrado.');\r\n    }\r\n\r\n    const updatedAddress = new AddressEntity();\r\n    updatedAddress.ownerId = ownerId;\r\n    updatedAddress.cep = cep;\r\n    updatedAddress.street = street;\r\n    updatedAddress.neighborhood = neighborhood;\r\n    updatedAddress.number = number;\r\n    updatedAddress.city = city;\r\n    updatedAddress.state = state;\r\n    updatedAddress.complement = complement;\r\n\r\n    return updatedAddress;\r\n  }\r\n\r\n  private async uploadAddress(\r\n    ownerId: string,\r\n    addressPayload: {\r\n      cep: string;\r\n      street: string;\r\n      neighborhood: string;\r\n      number: string;\r\n      city: string;\r\n      state: string;\r\n      complement?: string;\r\n    },\r\n  ): Promise<void> {\r\n    const validatedAddress = await this.validateAddress(\r\n      ownerId,\r\n      addressPayload,\r\n    );\r\n\r\n    const existingAddress = await this.ownerRepository.findOne({\r\n      where: { id: ownerId },\r\n      relations: { address: true },\r\n    });\r\n\r\n    if (existingAddress.address.length > 0) {\r\n      await this.addressRepository.update({ ownerId }, validatedAddress);\r\n    } else {\r\n      validatedAddress.ownerId = ownerId;\r\n      await this.addressRepository.save(validatedAddress);\r\n    }\r\n  }\r\n\r\n  private async uploadBusiness(\r\n    payload: EditBusinessDto,\r\n    business: BusinessEntity,\r\n  ) {\r\n    const [\r\n      newCompanyName,\r\n      newFantasyName,\r\n      newCnpj,\r\n      newEmail,\r\n      newType,\r\n      newSize,\r\n      newDtOpening,\r\n      // Add more fields as necessary\r\n    ] = await Promise.all([\r\n      this.validateBusinessField(\r\n        business.id,\r\n        payload.companyName,\r\n        'companyName',\r\n        {\r\n          sameValue: 'O novo nome da empresa é igual ao atual.',\r\n          duplicateValue: 'Já existe uma empresa com esse nome.',\r\n        },\r\n      ),\r\n      this.validateBusinessField(\r\n        business.id,\r\n        payload.fantasyName,\r\n        'fantasyName',\r\n        {\r\n          sameValue: 'O novo nome fantasia é igual ao atual.',\r\n          duplicateValue: 'Já existe uma empresa com esse nome fantasia.',\r\n        },\r\n      ),\r\n      this.validateBusinessField(\r\n        business.id,\r\n        payload.cnpj?.replace(/\\D/g, ''),\r\n        'cnpj',\r\n        {\r\n          sameValue: 'O novo CNPJ é igual ao atual.',\r\n          duplicateValue: 'Já existe uma empresa com esse CNPJ.',\r\n        },\r\n      ),\r\n      this.validateBusinessField(business.id, payload.email, 'email', {\r\n        sameValue: 'O novo e-mail é igual ao atual.',\r\n        duplicateValue: 'Já existe uma empresa com esse e-mail.',\r\n      }),\r\n      this.validateBusinessField(business.id, payload.type, 'type', {\r\n        sameValue: 'O novo tipo é igual ao atual.',\r\n        duplicateValue: '',\r\n      }),\r\n      this.validateBusinessField(business.id, payload.size, 'size', {\r\n        sameValue: 'O novo porte é igual ao atual.',\r\n        duplicateValue: '',\r\n      }),\r\n      this.validateDtOpening(business.id, payload.dtOpening),\r\n    ]);\r\n\r\n    await this.businessRepository.update(business.id, {\r\n      companyName: newCompanyName,\r\n      fantasyName: newFantasyName,\r\n      cnpj: newCnpj,\r\n      email: newEmail,\r\n      type: newType,\r\n      size: newSize,\r\n      dtOpening: newDtOpening ? new Date(newDtOpening) : undefined,\r\n    });\r\n  }\r\n\r\n  private async validateBusinessField(\r\n    businessId: string,\r\n    newValue: string | undefined,\r\n    fieldName:\r\n      | 'companyName'\r\n      | 'fantasyName'\r\n      | 'cnpj'\r\n      | 'email'\r\n      | 'type'\r\n      | 'size',\r\n    errorMessage: { sameValue: string; duplicateValue: string },\r\n  ): Promise<string | undefined> {\r\n    if (!newValue) {\r\n      return undefined;\r\n    }\r\n\r\n    const existingBusiness = await this.businessRepository.findOne({\r\n      where: { id: businessId },\r\n      select: [fieldName],\r\n    });\r\n\r\n    if (!existingBusiness) {\r\n      throw new NotFoundException('Empresa não encontrada.');\r\n    }\r\n\r\n    if (existingBusiness[fieldName] === newValue) {\r\n      throw new UnprocessableEntityException(errorMessage.sameValue);\r\n    }\r\n\r\n    if (errorMessage.duplicateValue) {\r\n      const valueAlreadyExists =\r\n        (await this.businessRepository.count({\r\n          where: { [fieldName]: newValue },\r\n        })) > 0;\r\n\r\n      if (valueAlreadyExists) {\r\n        throw new UnprocessableEntityException(errorMessage.duplicateValue);\r\n      }\r\n    }\r\n\r\n    return newValue;\r\n  }\r\n\r\n  private async validateDtOpening(\r\n    businessId: string,\r\n    newDtOpening?: string,\r\n  ): Promise<string | undefined> {\r\n    if (!newDtOpening) {\r\n      return undefined;\r\n    }\r\n\r\n    const datePattern = /^\\d{4}-\\d{2}-\\d{2}$/;\r\n    if (!datePattern.test(newDtOpening)) {\r\n      throw new UnprocessableEntityException(\r\n        'O formato da data de abertura é inválido. Use o formato YYYY-MM-DD.',\r\n      );\r\n    }\r\n\r\n    const parsedDate = new Date(newDtOpening);\r\n    if (Number.isNaN(parsedDate.getTime())) {\r\n      throw new UnprocessableEntityException(\r\n        'A data de abertura fornecida é inválida.',\r\n      );\r\n    }\r\n\r\n    if (parsedDate >= new Date()) {\r\n      throw new UnprocessableEntityException(\r\n        'A data de abertura deve ser anterior à data atual.',\r\n      );\r\n    }\r\n\r\n    const existingBusiness = await this.businessRepository.findOne({\r\n      where: { id: businessId },\r\n      select: ['dtOpening'],\r\n    });\r\n\r\n    if (!existingBusiness) {\r\n      throw new NotFoundException('Empresa não encontrada.');\r\n    }\r\n\r\n    if (\r\n      new Date(existingBusiness.dtOpening).getTime() === parsedDate.getTime()\r\n    ) {\r\n      throw new UnprocessableEntityException(\r\n        'A nova data de abertura é igual à atual.',\r\n      );\r\n    }\r\n\r\n    return parsedDate.toISOString().split('T').at(0);\r\n  }\r\n\r\n  private async verifyPermissions(\r\n    userId: string,\r\n    investorRoleRelation: OwnerRoleRelationEntity,\r\n  ): Promise<void> {\r\n    // Check if the user is the investor themselves\r\n    if (\r\n      investorRoleRelation.ownerId === userId ||\r\n      investorRoleRelation.businessId === userId\r\n    ) {\r\n      return; // User has permission\r\n    }\r\n\r\n    // Check if the user is an admin\r\n    if (await this.isAdmin(userId)) {\r\n      return; // User has permission\r\n    }\r\n\r\n    // Check if the user is a broker\r\n    if (await this.isBroker(userId)) {\r\n      // Check if there's a direct relation between broker and investor\r\n      if (await this.hasDirectRelation(userId, investorRoleRelation.id)) {\r\n        return; // User has permission\r\n      }\r\n    }\r\n\r\n    // Check if the user is a superadmin\r\n    if (await this.isSuperAdmin(userId)) {\r\n      return; // User has permission\r\n    }\r\n\r\n    // User does not have permission\r\n    throw new ForbiddenException(\r\n      'Você não tem permissão para editar este investidor',\r\n    );\r\n  }\r\n\r\n  private async isAdmin(userId: string): Promise<boolean> {\r\n    const adminRole = await this.ownerRoleRelationRepository.findOne({\r\n      relations: {\r\n        role: true,\r\n      },\r\n      where: [\r\n        { ownerId: userId, role: { name: RolesEnum.ADMIN } },\r\n        { businessId: userId, role: { name: RolesEnum.ADMIN } },\r\n      ],\r\n    });\r\n    return !!adminRole;\r\n  }\r\n\r\n  private async isBroker(userId: string): Promise<boolean> {\r\n    const brokerRole = await this.ownerRoleRelationRepository.findOne({\r\n      relations: {\r\n        role: true,\r\n      },\r\n      where: [\r\n        { ownerId: userId, role: { name: RolesEnum.BROKER } },\r\n        { businessId: userId, role: { name: RolesEnum.BROKER } },\r\n      ],\r\n    });\r\n    return !!brokerRole;\r\n  }\r\n\r\n  private async hasDirectRelation(\r\n    userId: string,\r\n    investorRoleRelationId: string,\r\n  ): Promise<boolean> {\r\n    const directRelation = await this.walletsViewsRepository.findOne({\r\n      where: {\r\n        upper: { ownerId: userId },\r\n        bottom: { id: investorRoleRelationId },\r\n      },\r\n      relations: ['upper', 'bottom'],\r\n    });\r\n    return !!directRelation;\r\n  }\r\n\r\n  private async isSuperAdmin(userId: string): Promise<boolean> {\r\n    const superAdminRole = await this.ownerRoleRelationRepository.findOne({\r\n      relations: {\r\n        role: true,\r\n      },\r\n      where: { ownerId: userId, role: { name: RolesEnum.SUPERADMIN } },\r\n    });\r\n    return !!superAdminRole;\r\n  }\r\n}\r\n"]}