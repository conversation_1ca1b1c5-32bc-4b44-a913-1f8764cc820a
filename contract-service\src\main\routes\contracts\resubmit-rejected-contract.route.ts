import { fastifyRouteAdapter } from "@/main/adapters";
import { makeResubmitRejectedContractController } from "@/main/factories/controllers/contracts/resubmit-rejected-contract.factory";
import multipart from "@fastify/multipart";
import type { FastifyInstance } from "fastify";

export const resubmitRejectedContractRoute = async (app: FastifyInstance) => {
  await app.register(multipart, {
    limits: {
      fileSize: 100 * 1024 * 1024, // 100MB
      files: 4, // Máximo de 4 arquivos
    },
    attachFieldsToBody: true,
  });

  app.put(
    "/contracts/:id/resubmit",
    {
      config: {
        rawBody: true,
      },
    },
    fastifyRouteAdapter(makeResubmitRejectedContractController())
  );
};
