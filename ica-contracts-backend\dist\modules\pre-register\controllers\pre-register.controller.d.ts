import { AddPreRegisterDto } from '../dto/add-pre-register.dto';
import { DirectAddPreRegisterDto } from '../dto/direct-add-pre-register.dto';
import { GetFilterPreRegisterDto } from '../dto/get-filter-pre-register.dto';
import { GetOnePreRegisterDto } from '../dto/get-one-pre-register.dto';
import { SendPreRegisterDto } from '../dto/send-pre-register.dto';
import { AddPreRegisterService } from '../services/add-pre-register.service';
import { DirectAddPreRegisterService } from '../services/direct-add-pre-register.service';
import { GetFilterPreRegisterService } from '../services/get-filter-pre-register.service';
import { GetOnePreRegisterService } from '../services/get-one-pre-register.service';
import { SendPreRegisterService } from '../services/send-pre-register.service';
import { ValidateTokenPreRegisterService } from '../services/validate-token-pre-register.service';
export declare class PreRegisterController {
    private addService;
    private getFilterService;
    private getOneService;
    private sendService;
    private directAddService;
    private validateTokenService;
    constructor(addService: AddPreRegisterService, getFilterService: GetFilterPreRegisterService, getOneService: GetOnePreRegisterService, sendService: SendPreRegisterService, directAddService: DirectAddPreRegisterService, validateTokenService: ValidateTokenPreRegisterService);
    add(body: AddPreRegisterDto, headers: any): Promise<void>;
    getOne(query: GetOnePreRegisterDto): Promise<import("../../../shared/database/typeorm/entities/pre-register.entity").PreRegisterEntity>;
    getFilter(query: GetFilterPreRegisterDto): Promise<import("../../../shared/database/typeorm/entities/pre-register.entity").PreRegisterEntity[]>;
    send(body: SendPreRegisterDto): Promise<{
        token: string;
    }>;
    directAdd(body: DirectAddPreRegisterDto, headers: any): Promise<void>;
    tokenValidate(headers: any): Promise<{
        email: string;
        document: string;
        owner: {
            name: string;
            cpf: string;
        };
        investment: {
            value: number;
            term: string;
            modality: string;
            yield: number;
            purchaseWith: string;
            amountQuotes: number;
            startContract: Date;
            endContract: Date;
            gracePeriod: Date;
            debenture: boolean;
            observations: string;
            brokerParticipationPercentage: string;
            advisorParticipationPercentage: string;
        };
        signIca: string;
    }>;
}
