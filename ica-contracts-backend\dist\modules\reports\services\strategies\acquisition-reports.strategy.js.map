{"version": 3, "file": "acquisition-reports.strategy.js", "sourceRoot": "/", "sources": ["modules/reports/services/strategies/acquisition-reports.strategy.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAO5C,2GAAmG;AAG5F,IAAM,yBAAyB,GAA/B,MAAM,yBAA0B,SAAQ,gFAAoC;IACjF,KAAK,CAAC,cAAc,CAAC,IAAuB;QAC1C,OAAO,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AAJY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;GACA,yBAAyB,CAIrC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { FileEntity } from 'src/shared/database/typeorm/entities/files-entity';\r\nimport {\r\n  GenerateReportDto,\r\n  PeriodEnum,\r\n  ReportTypeEnum,\r\n} from '../../dto/generate-report.dto';\r\nimport { AcquisitionReportStrategyAbstraction } from './acquisition-reports.strategy.abstractions';\r\n\r\n@Injectable()\r\nexport class AcquisitionReportStrategy extends AcquisitionReportStrategyAbstraction {\r\n  async generateReport(data: GenerateReportDto): Promise<FileEntity | null> {\r\n    return super.generateReportBase(data);\r\n  }\r\n}\r\n"]}