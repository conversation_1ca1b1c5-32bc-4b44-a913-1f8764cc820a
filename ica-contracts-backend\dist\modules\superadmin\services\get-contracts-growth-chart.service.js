"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetContractsGrowthChartService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const get_contracts_growth_chart_absctraction_1 = require("../../contract/helpers/get-contracts-growth-chart.absctraction");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const addendum_entity_1 = require("../../../shared/database/typeorm/entities/addendum.entity");
let GetContractsGrowthChartService = class GetContractsGrowthChartService extends get_contracts_growth_chart_absctraction_1.GetContractsGrowthChartAbstraction {
    constructor(contractRepository) {
        super();
        this.contractRepository = contractRepository;
    }
    async perform(periodFilter = 'year', contractType) {
        const { startDate, endDate, groupBy } = this.getDateRange(periodFilter);
        const data = await this.fetchChartData(startDate, endDate, groupBy, contractType);
        return this.generateChartData(data, periodFilter);
    }
    async fetchChartData(startDate, endDate, groupBy, contractType) {
        const query = this.contractRepository
            .createQueryBuilder('contract')
            .leftJoin('contract.signataries', 'signataries')
            .leftJoinAndSelect('contract.addendum', 'addendum', 'addendum.status = :addStatus', { addStatus: addendum_entity_1.AddendumStatus.FULLY_SIGNED })
            .select(`DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract) AS period`)
            .addSelect('COUNT(contract.id)', 'totalContracts')
            .addSelect('COALESCE(SUM(signataries.investment_value), 0) + COALESCE(SUM(addendum.value), 0)', 'totalValue')
            .where('CAST(contract.startContract AS DATE) BETWEEN CAST(:startDate AS DATE) AND CAST(:endDate AS DATE)', { startDate, endDate })
            .andWhere('contract.status = :status', {
            status: contract_status_enum_1.ContractStatusEnum.ACTIVE,
        })
            .andWhere('contract.endContract >= :currentDate', {
            currentDate: new Date(),
        });
        if (contractType) {
            query.andWhere('contract.type = :type', {
                type: contractType,
            });
        }
        return query
            .groupBy(`DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`)
            .orderBy(`DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`, 'ASC')
            .getRawMany();
    }
};
exports.GetContractsGrowthChartService = GetContractsGrowthChartService;
exports.GetContractsGrowthChartService = GetContractsGrowthChartService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], GetContractsGrowthChartService);
//# sourceMappingURL=get-contracts-growth-chart.service.js.map