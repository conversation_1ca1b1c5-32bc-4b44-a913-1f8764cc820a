import type { ContractStatus } from '@/domain/entities/contracts/investment-contract.entity';
import type { PaymentMethod } from '@/domain/value-objects';
import type { ContractType } from '@/domain/value-objects/contract-type.value-object';
import type { Profile } from '@/domain/value-objects/profile.value-object';
import type {
  BankAccountDTO,
  CompanyDTO,
  IndividualDTO,
} from './create-new-contract.dto';
import { ContractFiles } from '@/domain/interfaces/file-upload';

export enum PersonType {
  PF = 'PF',
  PJ = 'PJ',
}

export interface InvestmentDetailsDTO {
  amount: number;
  monthlyRate: number;
  paymentMethod: PaymentMethod;
  profile: Profile;
  startDate: string;
  endDate: string;
  durationInMonths: number;
  isDebenture: boolean;
  quotaQuantity?: number;
}

export interface ResubmitRejectedContractDTO {
  contractId: string;
  status: ContractStatus;
  personType?: PersonType;
  contractType?: ContractType;
  investment?: InvestmentDetailsDTO;
  bankAccount?: BankAccountDTO;
  individual?: IndividualDTO;
  company?: CompanyDTO;
  proofOfPayment?: ContractFiles['proofOfPayment'];
  personalDocument?: ContractFiles['personalDocument'];
  contract?: ContractFiles['contract'];
  proofOfResidence?: ContractFiles['proofOfResidence'];
  cardCnpj?: ContractFiles['cardCnpj'];
}
