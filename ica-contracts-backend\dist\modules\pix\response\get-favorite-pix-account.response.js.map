{"version": 3, "file": "get-favorite-pix-account.response.js", "sourceRoot": "/", "sources": ["modules/pix/response/get-favorite-pix-account.response.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IGetFavoritePixAccountResponse {\r\n  id: string;\r\n  alias: string;\r\n  name: string;\r\n  account: {\r\n    document: string;\r\n    number: string;\r\n    branch: string;\r\n    bank: string;\r\n    type: string;\r\n  };\r\n}\r\n"]}