import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountCelcoinService } from 'src/apis/celcoin/services/account-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { Equal, Repository } from 'typeorm';

import { ICloseAccountRequestDto } from '../dto/close-account-request.dto';

@Injectable()
export class CloseAccountService {
  constructor(
    @InjectRepository(OwnerEntity)
    private ownerDb: Repository<OwnerEntity>,
    @InjectRepository(AccountEntity)
    private readonly accountDb: Repository<AccountEntity>,
    @Inject(AccountCelcoinService)
    private apiCelcoin: AccountCelcoinService,
  ) {}

  async execute(data: ICloseAccountRequestDto) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        pixKey: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: {
        id: Equal(data.accountId),
      },
    });

    if (!account) throw new NotFoundException('Usuario não possui conta ativa');

    if (account.pixKey.length > 0)
      throw new BadRequestException('Conta possui chaves pix ativas');

    const document =
      account.type === 'physical' ? account.owner.cpf : account.business.cnpj;

    await this.apiCelcoin.closeAccount({
      account: account.number,
      document,
      reason: data.reason,
    });

    await this.accountDb.update(account.id, {
      status: AccountStatusEnum.BLOCKED,
    });
  }
}
