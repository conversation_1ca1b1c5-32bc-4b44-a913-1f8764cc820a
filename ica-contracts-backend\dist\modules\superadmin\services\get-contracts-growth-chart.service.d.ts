import { Repository } from 'typeorm';
import { GetContractsGrowthChartAbstraction, PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
export declare class GetContractsGrowthChartService extends GetContractsGrowthChartAbstraction {
    private readonly contractRepository;
    constructor(contractRepository: Repository<ContractEntity>);
    perform(periodFilter?: PeriodFilter, contractType?: ContractTypeEnum): Promise<{
        period: PeriodFilter;
        data: any[];
    }>;
    private fetchChartData;
}
