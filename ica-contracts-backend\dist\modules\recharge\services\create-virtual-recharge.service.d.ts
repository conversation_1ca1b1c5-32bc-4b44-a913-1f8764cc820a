import { CreateTransactionsService } from 'src/apis/icainvest-credit/services/create-virtual-transaction.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';
import { CreateVirtualRechargeDto } from '../dto/create-virtual-recharge.dto';
export declare class CreateVirtualRechargeService {
    private readonly createTransactionsService;
    private accountDb;
    constructor(createTransactionsService: CreateTransactionsService, accountDb: Repository<AccountEntity>);
    perform(transaction: CreateVirtualRechargeDto): Promise<import("../../../apis/icainvest-credit/responses/create-transaction.response").ICreateTransactionResponse>;
}
