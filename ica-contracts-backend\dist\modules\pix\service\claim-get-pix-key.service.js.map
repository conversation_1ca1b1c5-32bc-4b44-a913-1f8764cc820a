{"version": 3, "file": "claim-get-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/claim-get-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,6CAAmD;AACnD,gGAAsF;AACtF,6FAAmF;AACnF,qCAAqC;AAIrC,IAAa,qBAAqB,GAAlC,MAAa,qBAAqB;IAChC,YAEU,gBAA0C,EAE1C,iBAAsC;QAFtC,qBAAgB,GAAhB,gBAAgB,CAA0B;QAE1C,sBAAiB,GAAjB,iBAAiB,CAAqB;IAC7C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAkB,EAAE,OAAe;QAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE;gBACL;oBACE,GAAG,EAAE,KAAK,CAAC,GAAG;oBACd,OAAO,EAAE,EAAE,OAAO,EAAE;iBACrB;gBACD;oBACE,GAAG,EAAE,KAAK,CAAC,GAAG;oBACd,OAAO,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;iBACjC;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0CAA0C,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAErD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE5E,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAhCY,sDAAqB;gCAArB,qBAAqB;IAE7B,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,eAAM,EAAC,2CAAmB,CAAC,CAAA;qCADF,oBAAU;QAET,2CAAmB;GALrC,qBAAqB,CAgCjC", "sourcesContent": ["import { Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { ClaimCelcoinService } from 'src/apis/celcoin/services/claim-celcoin.service';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ClaimGetDto } from '../dto/claim-get.dto';\r\n\r\nexport class ClaimGetPixKeyService {\r\n  constructor(\r\n    @InjectRepository(PixKeyEntity)\r\n    private pixKeyRepository: Repository<PixKeyEntity>,\r\n    @Inject(ClaimCelcoinService)\r\n    private pixCelcoinService: ClaimCelcoinService,\r\n  ) {}\r\n\r\n  async execute(input: ClaimGetDto, ownerId: string) {\r\n    const pixkey = await this.pixKeyRepository.findOne({\r\n      where: [\r\n        {\r\n          key: input.key,\r\n          account: { ownerId },\r\n        },\r\n        {\r\n          key: input.key,\r\n          account: { businessId: ownerId },\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (!pixkey) {\r\n      throw new NotFoundException('Chave pix não encontrada para sua conta.');\r\n    }\r\n\r\n    const claimMetada = JSON.parse(pixkey.claimMetadata);\r\n\r\n    const response = await this.pixCelcoinService.getClaim(claimMetada.claimId);\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}