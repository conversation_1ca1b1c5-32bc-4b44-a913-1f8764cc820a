{"version": 3, "file": "get-documents.dto.js", "sourceRoot": "/", "sources": ["modules/uploads/dto/get-documents.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAoD;AAEpD,MAAa,eAAe;CAI3B;AAJD,0CAIC;AADC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;2CACE", "sourcesContent": ["import { IsDefined, IsUUID } from 'class-validator';\r\n\r\nexport class GetDocumentsDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  id: string;\r\n}\r\n"]}