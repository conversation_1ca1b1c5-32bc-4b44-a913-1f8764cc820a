import { StatisticEntity } from 'src/shared/database/typeorm/entities/statistic.entity';
import { Repository } from 'typeorm';
import { UpdateStatisticDto } from '../dto/update-statistic.dto';
export declare class UpdateStatisticService {
    private readonly statisticRepository;
    constructor(statisticRepository: Repository<StatisticEntity>);
    perform(updateDto: UpdateStatisticDto): Promise<StatisticEntity[]>;
}
