"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RechargeController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const create_virtual_recharge_dto_1 = require("../dto/create-virtual-recharge.dto");
const export_report_recharges_dto_1 = require("../dto/export-report-recharges.dto");
const get_account_recharges_dto_1 = require("../dto/get-account-recharges.dto");
const transaction_recharge_dto_1 = require("../dto/transaction-recharge.dto");
const create_virtual_recharge_service_1 = require("../services/create-virtual-recharge.service");
const export_report_recharges_service_1 = require("../services/export-report-recharges.service");
const get_account_recharges_service_1 = require("../services/get-account-recharges.service");
const transaction_recharge_service_1 = require("../services/transaction-recharge.service");
let RechargeController = class RechargeController {
    constructor(transactionRechargeService, createVirtualRechargeService, getAccountRechargesService, exportReportService) {
        this.transactionRechargeService = transactionRechargeService;
        this.createVirtualRechargeService = createVirtualRechargeService;
        this.getAccountRechargesService = getAccountRechargesService;
        this.exportReportService = exportReportService;
    }
    async transactionRecharge(body) {
        try {
            const response = await this.transactionRechargeService.perform(body);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status || 400);
        }
    }
    async createTransaction(transactionDto) {
        const result = await this.createVirtualRechargeService.perform(transactionDto);
        return result;
    }
    async getRecharges(query) {
        const result = await this.getAccountRechargesService.perform(query);
        return result;
    }
    async exportReport(query) {
        const result = await this.exportReportService.perform(query);
        return result;
    }
};
exports.RechargeController = RechargeController;
__decorate([
    (0, common_1.Post)('transaction'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transaction_recharge_dto_1.TransactionRechargeDto]),
    __metadata("design:returntype", Promise)
], RechargeController.prototype, "transactionRecharge", null);
__decorate([
    (0, common_1.Post)('virtual'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_virtual_recharge_dto_1.CreateVirtualRechargeDto]),
    __metadata("design:returntype", Promise)
], RechargeController.prototype, "createTransaction", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_account_recharges_dto_1.GetAccountRechargesDTO]),
    __metadata("design:returntype", Promise)
], RechargeController.prototype, "getRecharges", null);
__decorate([
    (0, common_1.Get)('export'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [export_report_recharges_dto_1.ExportReportRechargesDto]),
    __metadata("design:returntype", Promise)
], RechargeController.prototype, "exportReport", null);
exports.RechargeController = RechargeController = __decorate([
    (0, common_1.Controller)('recharge'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN),
    __param(0, (0, common_1.Inject)(transaction_recharge_service_1.TransactionRechargeService)),
    __metadata("design:paramtypes", [transaction_recharge_service_1.TransactionRechargeService,
        create_virtual_recharge_service_1.CreateVirtualRechargeService,
        get_account_recharges_service_1.GetAccountRecharges,
        export_report_recharges_service_1.ExportReportRechargesService])
], RechargeController);
//# sourceMappingURL=recharge.controller.js.map