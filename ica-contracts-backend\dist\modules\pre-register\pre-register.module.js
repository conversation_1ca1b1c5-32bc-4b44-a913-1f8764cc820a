"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreRegisterModule = void 0;
const common_1 = require("@nestjs/common");
const validate_adviser_owner_middleware_1 = require("../../shared/middlewares/validate-adviser-owner.middleware");
const validate_link_token_pre_register_middleware_1 = require("../../shared/middlewares/validate-link-token-pre-register.middleware");
const shared_module_1 = require("../../shared/shared.module");
const pre_register_controller_1 = require("./controllers/pre-register.controller");
const add_pre_register_service_1 = require("./services/add-pre-register.service");
const direct_add_pre_register_service_1 = require("./services/direct-add-pre-register.service");
const get_filter_pre_register_service_1 = require("./services/get-filter-pre-register.service");
const get_one_pre_register_service_1 = require("./services/get-one-pre-register.service");
const send_pre_register_service_1 = require("./services/send-pre-register.service");
const validate_token_pre_register_service_1 = require("./services/validate-token-pre-register.service");
let PreRegisterModule = class PreRegisterModule {
    configure(consumer) {
        consumer
            .apply(validate_adviser_owner_middleware_1.ValidateAdviserOwnerMiddleware)
            .forRoutes({ path: 'pre-register/send', method: common_1.RequestMethod.POST });
        consumer.apply(validate_link_token_pre_register_middleware_1.ValidateLinkTokenOwnerMiddleware).forRoutes({
            path: 'pre-register/token-validate',
            method: common_1.RequestMethod.GET,
        }, {
            path: 'pre-register/direct-add',
            method: common_1.RequestMethod.POST,
        });
    }
};
exports.PreRegisterModule = PreRegisterModule;
exports.PreRegisterModule = PreRegisterModule = __decorate([
    (0, common_1.Module)({
        controllers: [pre_register_controller_1.PreRegisterController],
        providers: [
            add_pre_register_service_1.AddPreRegisterService,
            get_filter_pre_register_service_1.GetFilterPreRegisterService,
            get_one_pre_register_service_1.GetOnePreRegisterService,
            send_pre_register_service_1.SendPreRegisterService,
            direct_add_pre_register_service_1.DirectAddPreRegisterService,
            validate_token_pre_register_service_1.ValidateTokenPreRegisterService,
        ],
        imports: [shared_module_1.SharedModule],
    })
], PreRegisterModule);
//# sourceMappingURL=pre-register.module.js.map