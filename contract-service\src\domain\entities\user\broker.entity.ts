import { AggregateRoot, type Either, left, right } from "@/domain/shared";
import type { BaseEntityProps } from "@/domain/shared/base-entity";
import {
  Address,
  Cnpj,
  CompanyType,
  Cpf,
  Email,
  Phone,
} from "@/domain/value-objects";
import { Company, Individual, type Party } from "../parties";

interface BrokerProps extends BaseEntityProps {
  party: Party;
}

export class Broker extends AggregateRoot<BrokerProps> {
  private constructor(props: BrokerProps, id?: string) {
    super(props, id);
  }

  /**
   * Cria um broker com uma entidade Individual já pronta
   */
  static createFromIndividual(individual: Individual, id?: string): Broker {
    return new Broker({ party: individual }, id);
  }

  /**
   * Cria um broker com uma entidade Company já pronta
   */
  static createFromCompany(company: Company, id?: string): Broker {
    return new Broker({ party: company }, id);
  }

  /**
   * Cria um broker como pessoa física a partir de primitivos
   */
  static createIndividual(
    id: string,
    name: string,
    email: string,
    phone: string,
    cpf: string,
    birthDate: Date,
    addressRaw: {
      street: string;
      number: string;
      neighborhood: string;
      city: string;
      state: string;
      postalCode: string;
      complement?: string;
    },
    motherName: string,
    rg: string,
    occupation?: string,
    nationality?: string,
    issuingAgency?: string
  ): Either<Error, Broker> {
    const individualResult = Individual.create(id, name, {
      address: addressRaw,
      birthDate,
      cpf,
      email,
      motherName,
      phone,
      rg,
      issuingAgency,
      nationality,
      occupation,
    });

    if (individualResult.isLeft()) {
      return left(individualResult.value);
    }

    return right(new Broker({ party: individualResult.value }));
  }

  /**
   * Cria um broker como pessoa jurídica a partir de primitivos
   */
  static createCompany(
    id: string,
    name: string,
    email: string,
    phone: string,
    cnpj: string,
    legalRepresentative: Individual,
    addressRaw: {
      street: string;
      number: string;
      neighborhood: string;
      city: string;
      state: string;
      postalCode: string;
      complement?: string;
    },
    companyTypeRaw: string,
    bankRaw?: {
      bank: string;
      agency: string;
      account: string;
      pix?: string;
    }
  ): Either<Error, Broker> {
    const emailResult = Email.create(email);
    const phoneResult = Phone.create(phone);
    const cnpjResult = Cnpj.create(cnpj);
    const addressResult = Address.create(
      addressRaw.street,
      addressRaw.number,
      addressRaw.city,
      addressRaw.state,
      addressRaw.postalCode,
      addressRaw.neighborhood,
      addressRaw.complement
    );
    const typeResult = CompanyType.create(companyTypeRaw);

    if (emailResult.isLeft()) return left(emailResult.value);
    if (phoneResult.isLeft()) return left(phoneResult.value);
    if (cnpjResult.isLeft()) return left(cnpjResult.value);
    if (addressResult.isLeft()) return left(addressResult.value);
    if (typeResult.isLeft()) return left(typeResult.value);

    const company = new Company(
      id,
      name,
      emailResult.value,
      phoneResult.value,
      cnpjResult.value,
      legalRepresentative,
      addressResult.value,
      typeResult.value,
      ""
    );

    return right(new Broker({ party: company }));
  }

  getCnpj(): string | undefined {
    if (this.props.party instanceof Company) {
      return this.props.party.getCnpj();
    }
  }

  getCpf(): string {
    return this.props.party.getCpf();
  }

  getName(): string {
    return this.props.party.getName();
  }

  getEmail(): string {
    return this.props.party.getEmail();
  }

  getPhone(): string {
    return this.props.party.getPhone();
  }

  isCompany(): boolean {
    return this.props.party instanceof Company;
  }

  isIndividual(): boolean {
    return this.props.party instanceof Individual;
  }

  getParty(): Party {
    return this.props.party;
  }
}
