"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetFilterPreRegisterService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pre_register_entity_1 = require("../../../shared/database/typeorm/entities/pre-register.entity");
const typeorm_2 = require("typeorm");
let GetFilterPreRegisterService = class GetFilterPreRegisterService {
    constructor(preRegisterDb) {
        this.preRegisterDb = preRegisterDb;
    }
    async perform(data) {
        const whereClause = {
            adviserId: data.adviserId,
        };
        if (data.status) {
            whereClause.status = data.status;
        }
        if (data.name) {
            whereClause.name = (0, typeorm_2.Like)(`${data.name}%`);
        }
        if (data.valueMin !== undefined && data.valueMax !== undefined) {
            whereClause.investmentValue = (0, typeorm_2.Between)(data.valueMin, data.valueMax);
        }
        if (data.dateFrom && data.dateTo) {
            whereClause.createdAt = (0, typeorm_2.Between)(data.dateFrom, data.dateTo);
        }
        if (data.document) {
            whereClause.document = data.document;
        }
        const preRegisters = await this.preRegisterDb.find({
            where: whereClause,
        });
        return preRegisters;
    }
};
exports.GetFilterPreRegisterService = GetFilterPreRegisterService;
exports.GetFilterPreRegisterService = GetFilterPreRegisterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(pre_register_entity_1.PreRegisterEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], GetFilterPreRegisterService);
//# sourceMappingURL=get-filter-pre-register.service.js.map