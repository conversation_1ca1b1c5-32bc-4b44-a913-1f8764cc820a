import {
  type ContractStatus,
  InvestmentContract,
  type InvestmentContractProps,
} from "@/domain/entities/contracts";
import { type Either, left } from "@/domain/shared";
import { BankAccount, PaymentMethod } from "@/domain/value-objects";
import { ContractType } from "@/domain/value-objects/contract-type.value-object";
import { Money } from "@/domain/value-objects/money.value-object";
import { Percentage } from "@/domain/value-objects/percentage.value-object";
import { Profile } from "@/domain/value-objects/profile.value-object";
import type { Prisma } from "@prisma/client";
import { PrismaAddendumMapper } from "./addendum.mapper";
import { InvestorMapper } from "./investor.mapper";

export type ContractIncludes = {
  include: {
    pre_register: true;
    contract_advisor: {
      include: {
        owner_role_relation: {
          include: {
            owner: {
              include: { address: true; account: true };
            };
            business: {
              include: {
                address: true;
                account: true;
                owner_business_relation: {
                  include: {
                    owner: {
                      include: { address: true };
                    };
                  };
                };
              };
            };
          };
        };
      };
    };
    owner_role_relation_contract_investor_idToowner_role_relation: {
      include: {
        pre_register: true;
        owner: {
          include: { address: true; account: true };
        };
        business: {
          include: {
            address: true;
            account: true;
            owner_business_relation: {
              include: {
                owner: {
                  include: { address: true };
                };
              };
            };
          };
        };
      };
    };
    owner_role_relation_contract_owner_role_relationToowner_role_relation: true;
    addendum: {
      include: {
        income_payment_scheduled_addendum: {
          include: {
            income_payment_scheduled: {
              select: {
                scheduled_date: true;
              };
            };
          };
        };
      };
      where: {
        status: "FULLY_SIGNED";
      };
    };
  };
};

export type ContractWithRelations = Prisma.contractGetPayload<ContractIncludes>;
export class InvestmentContractMapper {
  static toDomain(
    raw: ContractWithRelations,
    proofResidenceUrl?: string,
    companyDocumentUrl?: string,
    personalDocumentUrl?: string
  ): Either<Error, InvestmentContract> {
    // 🔢 Dados do investimento (pega o primeiro pre_register)
    const investment = raw.pre_register?.[0];

    if (!investment)
      return left(new Error("Contract missing pre_register data"));

    const amountResult = Money.create(Number(investment.investment_value));
    const yieldResult = Percentage.create(Number(investment.investment_yield));

    if (amountResult.isLeft()) return left(amountResult.value);
    if (yieldResult.isLeft()) return left(yieldResult.value);

    if (!raw.owner_role_relation_contract_investor_idToowner_role_relation) {
      return left(new Error("Contrato sem investidor"));
    }

    const paymentMethodResult = PaymentMethod.create(
      investment.purchase_with ?? "pix"
    );

    // 🧑 Investor
    const investorResult = InvestorMapper.toDomain(
      raw.owner_role_relation_contract_investor_idToowner_role_relation
    );
    if (investorResult.isLeft()) return left(investorResult.value);

    if (
      !raw.owner_role_relation_contract_owner_role_relationToowner_role_relation
    ) {
      return left(new Error("Contrato sem broker"));
    }

    // 📄 Tipo do contrato
    const contractTypeResult =
      raw.type === "scp" ? ContractType.scp() : ContractType.mutuo();

    // 🔢 Aditivos referente ao contrato
    const addendums = PrismaAddendumMapper.toDomainAddendum(raw.addendum);

    if (!investment.bank) return left(new Error("Banco não encontrado"));
    if (!investment.agency) return left(new Error("Agência não encontrada"));
    if (!investment.account) return left(new Error("Conta não encontrada"));

    // 🔢 Conta do investidor
    const bankAccount = BankAccount.create(
      investment.bank,
      investment.agency,
      investment.account,
      investment.pix ?? undefined
    );

    if (bankAccount.isLeft()) return left(bankAccount.value);

    const props: InvestmentContractProps = {
      investor: investorResult.value,
      brokerId:
        raw
          .owner_role_relation_contract_owner_role_relationToowner_role_relation
          .id,
      advisors: raw.contract_advisor.map((advisor) => ({
        id: advisor.id,
        rate: advisor.rate.toNumber(),
      })),
      amount: amountResult.value,
      type: contractTypeResult,
      profitability: yieldResult.value,
      paymentMethod: paymentMethodResult.isLeft()
        ? PaymentMethod.pix()
        : paymentMethodResult.value,
      contractNumber: raw.contract_number,
      startDate: raw.start_contract,
      endDate: raw.end_contract,
      profile: Profile.conservative(), // ⚠️ pode vir do investment.investment_modality
      signatureRequestId: raw.external_id?.toString(),
      addendums,
      createdAt: raw.createdAt,
      updatedAt: raw.updatedAt,
      contractPdf: raw.contract_pdf,
      proofPayment: raw.proof_payment,
      status: raw.status as ContractStatus,
      isDebenture: raw.is_debenture,
      proofOfResidence: proofResidenceUrl,
      companyDocument: companyDocumentUrl,
      personalDocument: personalDocumentUrl,
      quotaQuantity: investment.amount_quotes ?? undefined,
      durationInMonths: raw.duration_in_months ?? undefined,
    };

    return InvestmentContract.createFromExisting(props, raw.id);
  }
}
