interface ITransactionParty {
  account: string;
  taxId: string;
  name: string;
  branch: string;
  bank: string;
}

interface IBody {
  id: string;
  amount: number;
  clientRequestId: string;
  endToEndId: string;
  debitParty: ITransactionParty;
  creditParty: ITransactionParty;
  description?: string;
}

export interface IGetInternalPixTransactionCelcoinResponse {
  status: string;
  version: string;
  body: IBody;
}
