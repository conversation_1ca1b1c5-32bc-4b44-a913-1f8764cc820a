{"version": 3, "file": "send-ted.dto.js", "sourceRoot": "/", "sources": ["modules/ted/dto/send-ted.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAAwD;AACxD,yDAAyC;AACzC,qDAUyB;AACzB,iGAAsF;AACtF,+EAAqE;AACrE,qFAA0E;AAE1E,MAAM,OAAO;CAiBZ;AAdC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;uCACI;AAKf;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,oDAAuB,CAAC;;qCACF;AAI9B;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;uCACI;AAIf;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qCACE;AAGf,MAAM,QAAQ;CAoBb;AAfC;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,OAAO,CAAC;8BACX,OAAO;yCAAC;AAKjB;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,uCAAW,GAAE;;0CACG;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAKb;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,wCAAiB,CAAC;;wCACA;AAG5B,MAAa,UAAU;CA4BtB;AA5BD,gCA4BC;AAzBC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;6CACS;AAIlB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;0CACF;AAKf;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;uDACe;AAK5B;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,mCAAe,CAAC;;4CACE;AAM1B;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;8BACX,QAAQ;4CAAC;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACS", "sourcesContent": ["import { IsCPFOrCNPJ } from 'brazilian-class-validator';\r\nimport { Type } from 'class-transformer';\r\nimport {\r\n  IsDefined,\r\n  IsEnum,\r\n  IsNotEmpty,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  IsUUID,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { AccountTransferTypeEnum } from 'src/shared/enums/account-transfer-type.enum';\r\nimport { FinalityTedEnum } from 'src/shared/enums/finality-ted.enum';\r\nimport { PersonTypeTedEnum } from 'src/shared/enums/person-type-ted.enum';\r\n\r\nclass Account {\r\n  @IsDefined()\r\n  @IsString()\r\n  number: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsEnum(AccountTransferTypeEnum)\r\n  type: AccountTransferTypeEnum;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  branch: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  bank: string;\r\n}\r\n\r\nclass External {\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Account)\r\n  account: Account;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsEnum(PersonTypeTedEnum)\r\n  person: PersonTypeTedEnum;\r\n}\r\n\r\nexport class SendTedDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  accountId: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  amount: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  transactionPassword: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsEnum(FinalityTedEnum)\r\n  finality: FinalityTedEnum;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => External)\r\n  external: External;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  description: string;\r\n}\r\n"]}