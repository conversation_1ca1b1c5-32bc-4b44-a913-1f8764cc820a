import { PixQRCodeCelcoinService } from 'src/apis/celcoin/services/pix-qrcode-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { PixQRCodeEntity } from 'src/shared/database/typeorm/entities/pix-qrcode.entity';
import { Repository } from 'typeorm';
import { CreatePixQRCodeDynamicDto } from '../dto/create-pix-qrcode-dynamic.dto';
import { ICreatePixQRCodeDynamicResponse } from '../response/create-pix-qrcode-dynamic.response';
export declare class CreatePixQRCodeDynamicService {
    private accountDb;
    private pixQRCodeRepo;
    private pixQRCodeCelcoin;
    constructor(accountDb: Repository<AccountEntity>, pixQRCodeRepo: Repository<PixQRCodeEntity>, pixQRCodeCelcoin: PixQRCodeCelcoinService);
    perform(data: CreatePixQRCodeDynamicDto, id: string): Promise<ICreatePixQRCodeDynamicResponse>;
}
