import { ClaimCelcoinService } from 'src/apis/celcoin/services/claim-celcoin.service';
import { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';
import { Repository } from 'typeorm';
import { ClaimCancelDto } from '../dto/claim-cancel.dto';
export declare class ClaimCancelPixKeyService {
    private pixKeyRepository;
    private pixCelcoinService;
    constructor(pixKeyRepository: Repository<PixKeyEntity>, pixCelcoinService: ClaimCelcoinService);
    execute(input: ClaimCancelDto, ownerId: string): Promise<any>;
}
