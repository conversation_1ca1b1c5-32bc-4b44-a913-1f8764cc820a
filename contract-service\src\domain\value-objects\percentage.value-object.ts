import { type Either, left, right } from '@/domain/shared'

export class Percentage {
  private constructor(private readonly decimalValue: number) {}

  static create(percentValue: number): Either<Error, Percentage> {
    if (percentValue < 0 || percentValue > 100) {
      return left(new Error('Percentage must be between 0 and 100'))
    }

    const decimal = percentValue / 100
    return right(new Percentage(decimal))
  }

  get valueAsDecimal(): number {
    return this.decimalValue
  }

  get valueAsPercent(): number {
    return this.decimalValue * 100
  }

  toString(): string {
    return `${this.valueAsPercent.toFixed(2)}%`
  }
}
