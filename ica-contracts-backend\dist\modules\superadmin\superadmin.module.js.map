{"version": 3, "file": "superadmin.module.js", "sourceRoot": "/", "sources": ["modules/superadmin/superadmin.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,yDAAoD;AACpD,2CAAwC;AACxC,8DAAwD;AAExD,8DAA0D;AAC1D,2DAAuD;AACvD,iEAA6D;AAC7D,iEAA6D;AAC7D,+EAA2E;AAC3E,8FAAsF;AACtF,kFAAwE;AACxE,8HAAqH;AACrH,4HAAmH;AACnH,kIAAyH;AACzH,gIAAuH;AACvH,sFAAgF;AAChF,gGAAyF;AACzF,kGAA2F;AAC3F,sGAA+F;AAC/F,oGAA6F;AAoBtF,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;CAAG,CAAA;AAAnB,4CAAgB;2BAAhB,gBAAgB;IAlB5B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,EAAE,4BAAY,EAAE,8BAAa,EAAE,gCAAc,EAAE,gCAAc,EAAE,2BAAW,CAAC,QAAQ,EAAE,CAAC;QAC5G,SAAS,EAAE;YACT,4CAAiB;YACjB,0DAAyB;YACzB,uFAAuC;YACvC,2FAAyC;YACzC,yFAAwC;YACxC,6FAA0C;YAC1C,oDAAuB;YACvB,6DAA2B;YAC3B,+DAA4B;YAC5B,mEAA8B;YAC9B,iEAA6B;SAC9B;QACD,WAAW,EAAE,CAAC,4CAAoB,CAAC;QACnC,OAAO,EAAE,EAAE;KACZ,CAAC;GACW,gBAAgB,CAAG", "sourcesContent": ["import { CacheModule } from '@nestjs/cache-manager';\r\nimport { Module } from '@nestjs/common';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { AdvisorModule } from '../advisor/advisor.module';\r\nimport { BrokerModule } from '../broker/broker.module';\r\nimport { ContractModule } from '../contract/contract.module';\r\nimport { InvestorModule } from '../investor/investor.module';\r\nimport { SuperAdminController } from './controllers/superadmin.controller';\r\nimport { GetAdminSuperAdminService } from './services/find-admin-super-admin.service';\r\nimport { SuperAdminService } from './services/find-super-admin.service';\r\nimport { GetAllAdvisorsWithActiveContractsService } from './services/get-all-advisors-with-active-contracts.service';\r\nimport { GetAllBrokersWithActiveContractsService } from './services/get-all-brokers-with-active-contracts.service';\r\nimport { GetSingleAdvisorWithActiveContractsService } from './services/get-single-advisor-with-active-contracts.service';\r\nimport { GetSingleBrokerWithActiveContractsService } from './services/get-single-broker-with-active-contracts.service';\r\nimport { GetDashboardDataService } from './services/get-dashboard-data.service';\r\nimport { SuperAdminEditBrokerService } from './services/super-admin-edit-broker.service';\r\nimport { SuperAdminEditAdvisorService } from './services/super-admin-edit-advisor.service';\r\nimport { GetContractsGrowthChartService } from './services/get-contracts-growth-chart.service';\r\nimport { SuperAdminEditInvestorService } from './services/super-admin-edit-investor.service';\r\n\r\n@Module({\r\n  imports: [SharedModule, BrokerModule, AdvisorModule, InvestorModule, ContractModule, CacheModule.register()],\r\n  providers: [\r\n    SuperAdminService,\r\n    GetAdminSuperAdminService,\r\n    GetAllBrokersWithActiveContractsService,\r\n    GetSingleBrokerWithActiveContractsService,\r\n    GetAllAdvisorsWithActiveContractsService,\r\n    GetSingleAdvisorWithActiveContractsService,\r\n    GetDashboardDataService,\r\n    SuperAdminEditBrokerService,\r\n    SuperAdminEditAdvisorService,\r\n    GetContractsGrowthChartService,\r\n    SuperAdminEditInvestorService,\r\n  ],\r\n  controllers: [SuperAdminController],\r\n  exports: [],\r\n})\r\nexport class SuperAdminModule {}\r\n"]}