import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';
import { GetPixKeyExternalDto } from '../dto/get-pix-key-external.dto';
import { IGetPixKeyExternalResponse } from '../response/get-pix-key-external-response';
export declare class GetPixKeyExternalService {
    private accountDb;
    private celcoinService;
    private runningRequests;
    constructor(accountDb: Repository<AccountEntity>, celcoinService: PixTransactionCelcoinService);
    perform(data: GetPixKeyExternalDto, id: string): Promise<IGetPixKeyExternalResponse>;
    stopPerformingRequests(data: GetPixKeyExternalDto, accountNumber: string): void;
}
