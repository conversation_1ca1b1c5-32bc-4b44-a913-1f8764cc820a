import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';

import { AccountCelcoinService } from './services/account-celcoin.service';
import { AuthCelcoinService } from './services/auth-celcoin.service';
import { BalanceCelcoinService } from './services/balance-celcoin.service';
import { BoletoPaymentCelcoinService } from './services/boleto-payment-celcoin.service';
import { ClaimCelcoinService } from './services/claim-celcoin.service';
import { ConsultBoletoCelcoinService } from './services/consult-boleto-celcoin.service';
import { ConsultCelcoinStatusBoleto } from './services/consult-celcoin-status-boleto.service';
import { ConsultAccountExtractCelcoinService } from './services/consult-extract-celcoin.service';
import { GenerateCelcoinBoletoService } from './services/generate-celcoin-boleto.service';
import { GetRegisteredWebhooksService } from './services/get-registered-webhooks.service';
import { KYCCelcoinService } from './services/kyc-celcoin.service';
import { PixKeyCelcoinService } from './services/pix-key-celcoin.service';
import { PixQRCodeCelcoinService } from './services/pix-qrcode-celcoin.service';
import { PixTransactionCelcoinService } from './services/pix-transaction-celcoin.service';
import { RegisterWebhookService } from './services/register-webhook.service';
import { TedCelcoinService } from './services/ted-celcoin.service';
import { UpdateWebhookService } from './services/update-webhook.service';

@Module({
  imports: [CacheModule.register()],
  providers: [
    AuthCelcoinService,
    BalanceCelcoinService,
    AccountCelcoinService,
    AuthCelcoinService,
    KYCCelcoinService,
    PixKeyCelcoinService,
    PixTransactionCelcoinService,
    ConsultAccountExtractCelcoinService,
    PixQRCodeCelcoinService,
    ClaimCelcoinService,
    GenerateCelcoinBoletoService,
    TedCelcoinService,
    ConsultBoletoCelcoinService,
    BoletoPaymentCelcoinService,
    ConsultCelcoinStatusBoleto,
    RegisterWebhookService,
    GetRegisteredWebhooksService,
    UpdateWebhookService,
  ],
  exports: [
    AuthCelcoinService,
    BalanceCelcoinService,
    AccountCelcoinService,
    AuthCelcoinService,
    KYCCelcoinService,
    PixKeyCelcoinService,
    PixTransactionCelcoinService,
    ConsultAccountExtractCelcoinService,
    PixQRCodeCelcoinService,
    ClaimCelcoinService,
    GenerateCelcoinBoletoService,
    ConsultBoletoCelcoinService,
    BoletoPaymentCelcoinService,
    TedCelcoinService,
    ConsultCelcoinStatusBoleto,
    RegisterWebhookService,
    GetRegisteredWebhooksService,
    UpdateWebhookService,
  ],
})
export class CelcoinModule {}
