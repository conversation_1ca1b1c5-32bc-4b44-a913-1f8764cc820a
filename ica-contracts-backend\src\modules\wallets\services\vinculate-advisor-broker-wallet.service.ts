import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Equal, Repository } from 'typeorm';

import { VinculateAdvisorBrokerWalletDto } from '../dto/vinculate-advisor-broker-wallet.dto';

@Injectable()
export class VinculateAdvisorBrokerWalletService {
  constructor(
    @InjectRepository(WalletsViewsEntity)
    private walletDb: Repository<WalletsViewsEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private perfilDb: Repository<OwnerRoleRelationEntity>,
  ) {}

  async perform(data: VinculateAdvisorBrokerWalletDto) {
    const advisor = await this.perfilDb.findOne({
      where: {
        id: Equal(data.advisorId),
        role: {
          name: RolesEnum.ADVISOR,
        },
      },
      relations: {
        role: true,
      },
    });

    if (!advisor) throw new BadRequestException('Assessor nao encontrado');

    // Primeiro, vamos tentar encontrar o registro sem filtro de role
    const anyBrokerRecord = await this.perfilDb.findOne({
      where: {
        id: Equal(data.brokerId),
      },
      relations: {
        role: true,
      },
    });

    console.log('🔍 Debug - Searching for broker ID:', data.brokerId);
    console.log('📋 Debug - Any record found:', anyBrokerRecord ? 'YES' : 'NO');

    if (anyBrokerRecord) {
      console.log('📊 Debug - Record details:', {
        id: anyBrokerRecord.id,
        roleId: anyBrokerRecord.roleId,
        roleName: anyBrokerRecord.role?.name,
        ownerId: anyBrokerRecord.ownerId,
        businessId: anyBrokerRecord.businessId
      });
    }

    // Agora vamos tentar com o filtro de role
    const broker = await this.perfilDb.findOne({
      where: {
        id: Equal(data.brokerId),
        role: {
          name: RolesEnum.BROKER,
        },
      },
      relations: {
        role: true,
      },
    });

    console.log('🎯 Debug - Broker with role filter found:', broker ? 'YES' : 'NO');

    if (!broker) {
      // Se não encontrou com filtro, vamos verificar se o problema é o enum
      const brokerWithStringRole = await this.perfilDb.findOne({
        where: {
          id: Equal(data.brokerId),
          role: {
            name: 'broker',
          },
        },
        relations: {
          role: true,
        },
      });

      console.log('🔧 Debug - Broker with string role found:', brokerWithStringRole ? 'YES' : 'NO');

      if (brokerWithStringRole) {
        console.log('✅ Found broker with string role, using it');
        // Se encontrou com string, usar esse
      } else {
        throw new BadRequestException(`Broker não encontrado. ID: ${data.brokerId}. Record exists: ${anyBrokerRecord ? 'YES' : 'NO'}. Role: ${anyBrokerRecord?.role?.name || 'N/A'}`);
      }
    }

    const wallet = await this.walletDb.findOne({
      where: {
        bottomId: Equal(data.advisorId),
      },
    });

    if (wallet)
      throw new BadRequestException('Assessor ja está vinculado a um Broker');

    const createVinc = this.walletDb.create({
      upperId: data.brokerId,
      bottomId: data.advisorId,
    });

    await this.walletDb.save(createVinc);
  }
}
