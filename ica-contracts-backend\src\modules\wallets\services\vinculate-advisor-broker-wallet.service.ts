import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Equal, Repository } from 'typeorm';

import { VinculateAdvisorBrokerWalletDto } from '../dto/vinculate-advisor-broker-wallet.dto';

@Injectable()
export class VinculateAdvisorBrokerWalletService {
  constructor(
    @InjectRepository(WalletsViewsEntity)
    private walletDb: Repository<WalletsViewsEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private perfilDb: Repository<OwnerRoleRelationEntity>,
  ) {}

  async perform(data: VinculateAdvisorBrokerWalletDto) {
    const advisor = await this.perfilDb.findOne({
      where: {
        id: Equal(data.advisorId),
        role: {
          name: RolesEnum.ADVISOR,
        },
      },
      relations: {
        role: true,
      },
    });

    if (!advisor) throw new BadRequestException('Assessor nao encontrado');

    const broker = await this.perfilDb.findOne({
      where: {
        id: Equal(data.brokerId),
        role: {
          name: RolesEnum.BROKER,
        },
      },
      relations: {
        role: true,
      },
    });

    if (!broker) throw new BadRequestException('Broker nao encontrado');

    const wallet = await this.walletDb.findOne({
      where: {
        bottomId: Equal(data.advisorId),
      },
    });

    if (wallet)
      throw new BadRequestException('Assessor ja está vinculado a um Broker');

    const createVinc = this.walletDb.create({
      upperId: data.brokerId,
      bottomId: data.advisorId,
    });

    await this.walletDb.save(createVinc);
  }
}
