import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Equal, Repository } from 'typeorm';

import { VinculateAdvisorBrokerWalletDto } from '../dto/vinculate-advisor-broker-wallet.dto';

@Injectable()
export class VinculateAdvisorBrokerWalletService {
  constructor(
    @InjectRepository(WalletsViewsEntity)
    private walletDb: Repository<WalletsViewsEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private perfilDb: Repository<OwnerRoleRelationEntity>,
  ) {}

  async perform(data: VinculateAdvisorBrokerWalletDto) {
    const advisor = await this.perfilDb.findOne({
      where: {
        id: Equal(data.advisorId),
        role: {
          name: RolesEnum.ADVISOR,
        },
      },
      relations: {
        role: true,
      },
    });

    if (!advisor) throw new BadRequestException('Assessor nao encontrado');

    // Primeiro, vamos tentar encontrar o registro sem filtro de role
    const anyBrokerRecord = await this.perfilDb.findOne({
      where: {
        id: Equal(data.brokerId),
      },
      relations: {
        role: true,
      },
    });

    console.log('🔍 Debug - Searching for broker ID:', data.brokerId);
    console.log('📋 Debug - Any record found:', anyBrokerRecord ? 'YES' : 'NO');

    if (anyBrokerRecord) {
      console.log('📊 Debug - Record details:', {
        id: anyBrokerRecord.id,
        roleId: anyBrokerRecord.roleId,
        roleName: anyBrokerRecord.role?.name,
        ownerId: anyBrokerRecord.ownerId,
        businessId: anyBrokerRecord.businessId
      });
    }

    // Vamos verificar se o registro existe e tem role de broker
    if (!anyBrokerRecord) {
      throw new BadRequestException(`Broker não encontrado. ID: ${data.brokerId} não existe na base de dados.`);
    }

    if (anyBrokerRecord.role?.name !== 'broker') {
      throw new BadRequestException(`ID ${data.brokerId} existe mas não é um broker. Role atual: ${anyBrokerRecord.role?.name || 'N/A'}`);
    }

    // Se chegou até aqui, o registro existe e é um broker, então usar ele
    const broker = anyBrokerRecord;

    const wallet = await this.walletDb.findOne({
      where: {
        bottomId: Equal(data.advisorId),
      },
    });

    if (wallet)
      throw new BadRequestException('Assessor ja está vinculado a um Broker');

    const createVinc = this.walletDb.create({
      upperId: data.brokerId,
      bottomId: data.advisorId,
    });

    await this.walletDb.save(createVinc);
  }
}
