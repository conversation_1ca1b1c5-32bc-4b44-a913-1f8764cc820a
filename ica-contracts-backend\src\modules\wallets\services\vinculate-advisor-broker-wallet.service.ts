import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Equal, Repository } from 'typeorm';

import { VinculateAdvisorBrokerWalletDto } from '../dto/vinculate-advisor-broker-wallet.dto';

@Injectable()
export class VinculateAdvisorBrokerWalletService {
  constructor(
    @InjectRepository(WalletsViewsEntity)
    private walletDb: Repository<WalletsViewsEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private perfilDb: Repository<OwnerRoleRelationEntity>,
  ) {}

  async perform(data: VinculateAdvisorBrokerWalletDto) {
    const advisor = await this.perfilDb.findOne({
      where: {
        id: Equal(data.advisorId),
        role: {
          name: RolesEnum.ADVISOR,
        },
      },
      relations: {
        role: true,
      },
    });

    if (!advisor) throw new BadRequestException('Assessor nao encontrado');

    // Log para debug - verificar o ID que está sendo buscado
    console.log('🔍 Searching for broker with ID:', data.brokerId);

    // Primeiro, vamos verificar se existe algum registro com esse ID
    const anyRecord = await this.perfilDb.findOne({
      where: {
        id: Equal(data.brokerId),
      },
      relations: {
        role: true,
      },
    });

    console.log('📋 Any record with this ID:', anyRecord ? 'YES' : 'NO');

    if (!anyRecord) {
      // Se não encontrou nenhum registro, vamos mostrar os IDs disponíveis
      console.log('❌ ID not found. Let me show you available broker IDs:');

      const allBrokers = await this.perfilDb.find({
        where: {
          role: {
            name: RolesEnum.BROKER,
          },
        },
        relations: {
          role: true,
          owner: true,
          business: true,
        },
        select: {
          id: true,
          ownerId: true,
          businessId: true,
          owner: {
            id: true,
            name: true,
            cpf: true,
          },
          business: {
            id: true,
            companyName: true,
            cnpj: true,
          },
        },
      });

      console.log('📊 Available brokers:');
      allBrokers.forEach((broker, index) => {
        console.log(`  ${index + 1}. Broker Relation ID: ${broker.id}`);
        if (broker.owner) {
          console.log(`     - Owner ID: ${broker.ownerId} (${broker.owner.name} - ${broker.owner.cpf})`);
        }
        if (broker.business) {
          console.log(`     - Business ID: ${broker.businessId} (${broker.business.companyName} - ${broker.business.cnpj})`);
        }
        console.log('');
      });

      throw new BadRequestException(`Broker não encontrado. ID fornecido: ${data.brokerId}. Use um dos IDs de relação listados acima.`);
    }

    const broker = await this.perfilDb.findOne({
      where: {
        id: Equal(data.brokerId),
        role: {
          name: RolesEnum.BROKER,
        },
      },
      relations: {
        role: true,
      },
    });

    if (!broker) throw new BadRequestException('Broker nao encontrado');

    const wallet = await this.walletDb.findOne({
      where: {
        bottomId: Equal(data.advisorId),
      },
    });

    if (wallet)
      throw new BadRequestException('Assessor ja está vinculado a um Broker');

    const createVinc = this.walletDb.create({
      upperId: data.brokerId,
      bottomId: data.advisorId,
    });

    await this.walletDb.save(createVinc);
  }
}
