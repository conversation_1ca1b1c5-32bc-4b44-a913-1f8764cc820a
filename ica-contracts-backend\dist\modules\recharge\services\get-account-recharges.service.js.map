{"version": 3, "file": "get-account-recharges.service.js", "sourceRoot": "/", "sources": ["modules/recharge/services/get-account-recharges.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AAEnD,+HAAoH;AACpH,6FAAoF;AACpF,qCAAqC;AAK9B,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAEU,SAAoC,EACpC,6BAA4D;QAD5D,cAAS,GAAT,SAAS,CAA2B;QACpC,kCAA6B,GAA7B,6BAA6B,CAA+B;IACnE,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAA4B;QACxC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;aACf;YACD,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,SAAS;aACnB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,YAAY,GAChB,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAEvE,IAAI,oBAAuD,CAAC;QAE5D,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAEpC,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YAClC,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAE9B,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;gBAChB,MAAM,IAAI,4BAAmB,CAC3B,qDAAqD,CACtD,CAAC;YACJ,CAAC;YAED,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAClD,YAAY,EACZ,KAAK,EACL,GAAG,CACJ,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,oBAAoB,GAAG,YAAY,CAAC;QACtC,CAAC;QAED,OAAO,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;IAC5D,CAAC;IAEO,wBAAwB,CAC9B,YAA+C,EAC/C,KAAW,EACX,GAAS;QAET,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE;YACzC,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACnD,OAAO,eAAe,IAAI,KAAK,IAAI,eAAe,IAAI,GAAG,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,uBAAuB,CAC7B,YAA+C;QAE/C,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAC3C,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,OAAO,CAC9C,CAAC;QACF,MAAM,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAC5C,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,QAAQ,CAC/C,CAAC;QACF,MAAM,oBAAoB,GAAG,YAAY,CAAC,MAAM,CAC9C,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,UAAU,CACjD,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,iBAAiB;YACzB,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE,oBAAoB;SAChC,CAAC;IACJ,CAAC;CACF,CAAA;AAhFY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCACb,oBAAU;QACU,gEAA6B;GAJ3D,mBAAmB,CAgF/B", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { IGetAccountTransactionsResponse } from 'src/apis/icainvest-credit/responses/get-account-transactions.response';\r\nimport { GetAccountTransactionsService } from 'src/apis/icainvest-credit/services/get-account-transactions.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { GetAccountRechargesDTO } from '../dto/get-account-recharges.dto';\r\n\r\n@Injectable()\r\nexport class GetAccountRecharges {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    private getAccountTransactionsService: GetAccountTransactionsService,\r\n  ) {}\r\n\r\n  async perform(data: GetAccountRechargesDTO) {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        recharge: true,\r\n      },\r\n      where: {\r\n        id: data.accountId,\r\n      },\r\n    });\r\n\r\n    if (!account) {\r\n      throw new BadRequestException('Conta não encontrada para recarga.');\r\n    }\r\n\r\n    const transactions: IGetAccountTransactionsResponse[] =\r\n      await this.getAccountTransactionsService.perform({ id: account.id });\r\n\r\n    let filteredTransactions: IGetAccountTransactionsResponse[];\r\n\r\n    const { startDate, endDate } = data;\r\n\r\n    if (startDate && endDate) {\r\n      const start = new Date(startDate);\r\n      const end = new Date(endDate);\r\n\r\n      if (start > end) {\r\n        throw new BadRequestException(\r\n          'A data inicial não pode ser posterior à data final.',\r\n        );\r\n      }\r\n\r\n      filteredTransactions = this.filterTransactionsByDate(\r\n        transactions,\r\n        start,\r\n        end,\r\n      );\r\n    } else {\r\n      filteredTransactions = transactions;\r\n    }\r\n\r\n    return this.groupTransactionsByType(filteredTransactions);\r\n  }\r\n\r\n  private filterTransactionsByDate(\r\n    transactions: IGetAccountTransactionsResponse[],\r\n    start: Date,\r\n    end: Date,\r\n  ): IGetAccountTransactionsResponse[] {\r\n    return transactions.filter((transaction) => {\r\n      const transactionDate = new Date(transaction.date);\r\n      return transactionDate >= start && transactionDate <= end;\r\n    });\r\n  }\r\n\r\n  private groupTransactionsByType(\r\n    transactions: IGetAccountTransactionsResponse[],\r\n  ) {\r\n    const debitTransactions = transactions.filter(\r\n      (transaction) => transaction.type === 'DEBIT',\r\n    );\r\n    const creditTransactions = transactions.filter(\r\n      (transaction) => transaction.type === 'CREDIT',\r\n    );\r\n    const rechargeTransactions = transactions.filter(\r\n      (transaction) => transaction.type === 'RECHARGE',\r\n    );\r\n\r\n    return {\r\n      debits: debitTransactions,\r\n      credits: creditTransactions,\r\n      recharges: rechargeTransactions,\r\n    };\r\n  }\r\n}\r\n"]}