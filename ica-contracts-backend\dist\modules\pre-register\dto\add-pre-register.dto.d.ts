declare class Address {
    zipCode: string;
    neighborhood: string;
    city: string;
    state: string;
    complement: string;
    number: string;
}
declare class Investment {
    value: number;
    term: string;
    modality: string;
    yield: number;
    purchaseWith: string;
    amountQuotes: number;
    gracePeriod: Date;
}
export declare class AddPreRegisterDto {
    adviserId: string;
    name: string;
    rg: string;
    document: string;
    phoneNumber: string;
    motherName: string;
    dtBirth: Date;
    email: string;
    address: Address;
    investment: Investment;
    observations: string;
}
export {};
