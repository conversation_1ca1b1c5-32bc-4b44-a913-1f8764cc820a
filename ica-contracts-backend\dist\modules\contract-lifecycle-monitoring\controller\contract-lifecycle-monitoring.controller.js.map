{"version": 3, "file": "contract-lifecycle-monitoring.controller.js", "sourceRoot": "/", "sources": ["modules/contract-lifecycle-monitoring/controller/contract-lifecycle-monitoring.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAYwB;AACxB,+DAA4D;AAG5D,0EAAqE;AACrE,gFAA0E;AAE1E,oFAA8E;AAE9E,0DAAsD;AACtD,wEAAkE;AAClE,qEAAiE;AACjE,iGAA2F;AAC3F,iGAA2F;AAC3F,yGAAkG;AAClG,uGAAgG;AAChG,+GAAwG;AAIjG,IAAM,qCAAqC,GAA3C,MAAM,qCAAqC;IAChD,YACmB,kCAAgE,EAChE,8BAA8D,EAC9D,+BAAgE,EAChE,kCAAsE,EACtE,4BAA0D,EAC1D,gBAAkC;QALlC,uCAAkC,GAAlC,kCAAkC,CAA8B;QAChE,mCAA8B,GAA9B,8BAA8B,CAAgC;QAC9D,oCAA+B,GAA/B,+BAA+B,CAAiC;QAChE,uCAAkC,GAAlC,kCAAkC,CAAoC;QACtE,iCAA4B,GAA5B,4BAA4B,CAA8B;QAC1D,qBAAgB,GAAhB,gBAAgB,CAAkB;IAClD,CAAC;IAIE,AAAN,KAAK,CAAC,oBAAoB,CAExB,KAAoB,EACT,OAAqB;QAEhC,OAAO,IAAI,CAAC,kCAAkC,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACjF,CAAC;IAKK,AAAN,KAAK,CAAC,uBAAuB,CAE3B,IAAwB,EACb,OAAqB;QAEhC,OAAO,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC5E,CAAC;IAIK,AAAN,KAAK,CAAC,wBAAwB,CACT,QAAqD,EAC7D,OAAqB,EACjB,OAAO,CAAC,EACP,QAAQ,EAAE;QAE1B,MAAM,WAAW,GAA0B,EAAE,CAAC;QAE9C,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,WAAW,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,+BAA+B,CAAC,OAAO,CACjD,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,WAAW,EACX,MAAM,CAAC,IAAI,CAAC,EACZ,MAAM,CAAC,KAAK,CAAC,CACd,CAAC;IACJ,CAAC;IAIK,AAAN,KAAK,CAAC,SAAS,CAAY,OAAqB;QAC9C,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACxD,CAAC;IAKK,AAAN,KAAK,CAAC,2BAA2B,CACV,UAAkB,EAEvC,cAAsC,EACrB,WAAkC,EACxC,OAAqB;QAEhC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,kCAAkC,CAAC,OAAO,CACjE,UAAU,EACV,cAAc,EACd,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,WAAW,CACZ,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,uCAAuC;YAChD,KAAK;SACN,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,qBAAqB,CACJ,UAAkB,EAEvC,gBAA0C,EACzB,WAAkC,EACxC,OAAqB;QAEhC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAC3D,UAAU,EACV,gBAAgB,EAChB,OAAO,CAAC,IAAI,CAAC,EAAE,EACf,WAAW,CACZ,CAAC;QAEF,OAAO;YACL,OAAO,EAAE,2CAA2C;YACpD,KAAK;SACN,CAAC;IACJ,CAAC;CACF,CAAA;AA5GY,sFAAqC;AAY1C;IAFL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,YAAG,EAAC,UAAU,CAAC;IAEb,WAAA,IAAA,cAAK,GAAE,CAAA;IAEP,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADH,8BAAa;;iFAIrB;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,aAAI,EAAC,4BAA4B,CAAC;IAClC,IAAA,iBAAQ,EAAC,GAAG,CAAC;IAEX,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADJ,0CAAkB;;oFAIzB;AAIK;IAFL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,YAAG,EAAC,WAAW,CAAC;IAEd,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;;;;qFAkBhB;AAIK;IAFL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,YAAG,EAAC,WAAW,CAAC;IACA,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;sEAEzB;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,aAAI,EAAC,8BAA8B,CAAC;IACpC,IAAA,wBAAe,EAAC,IAAA,mCAAgB,EAAC,aAAa,CAAC,CAAC;IAE9C,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;IACnB,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,sBAAa,GAAE,CAAA;IACf,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAFM,kDAAsB;;wFAevC;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,aAAI,EAAC,sBAAsB,CAAC;IAC5B,IAAA,wBAAe,EAAC,IAAA,mCAAgB,EAAC,aAAa,CAAC,CAAC;IAE9C,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;IACnB,WAAA,IAAA,aAAI,GAAE,CAAA;IAEN,WAAA,IAAA,sBAAa,GAAE,CAAA;IACf,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CAFQ,sDAAwB;;kFAe3C;gDA3GU,qCAAqC;IADjD,IAAA,mBAAU,EAAC,+BAA+B,CAAC;qCAGa,8DAA4B;QAChC,mEAA8B;QAC7B,qEAA+B;QAC5B,2EAAkC;QACxC,8DAA4B;QACxC,oCAAgB;GAP1C,qCAAqC,CA4GjD", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Get,\r\n  HttpCode,\r\n  Param,\r\n  Post,\r\n  Query,\r\n  Request,\r\n  UploadedFiles,\r\n  UseGuards,\r\n  UseInterceptors,\r\n} from '@nestjs/common';\r\nimport { FilesInterceptor } from '@nestjs/platform-express';\r\n\r\nimport { ContractEventStatus } from '../../../shared/enums/contract-events.enum';\r\nimport { JwtAuthGuard } from '../../../shared/guards/jwt-auth.guard';\r\nimport { CreateContractEventDto } from '../dto/create-contract-event.dto';\r\nimport { ExpiringContractDto } from '../dto/expiring-contract.dto';\r\nimport { FinalizeContractEventDto } from '../dto/finalize-contract-event.dto';\r\nimport { PaginatedResultDto } from '../dto/paginated-result.dto';\r\nimport { PaginationDto } from '../dto/pagination.dto';\r\nimport { SendToRetentionDto } from '../dto/send-to-retention.dto';\r\nimport { DashboardService } from '../services/dashboard.service';\r\nimport { FinalizeContractEventService } from '../services/finalize-contract-event.service';\r\nimport { FindExpiringContractsService } from '../services/find-expiring-contracts.service';\r\nimport { GetContractsForRetentionService } from '../services/get-contracts-for-retention.service';\r\nimport { SendContractToRetentionService } from '../services/send-contract-to-retention.service';\r\nimport { UpdateContractFromRetentionService } from '../services/update-contract-from-retention.service';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\n@Controller('contract-lifecycle-monitoring')\r\nexport class ContractLifecycleMonitoringController {\r\n  constructor(\r\n    private readonly contractLifecycleMonitoringService: FindExpiringContractsService,\r\n    private readonly sendContractToRetentionService: SendContractToRetentionService,\r\n    private readonly getContractsForRetentionService: GetContractsForRetentionService,\r\n    private readonly updateContractFromRetentionService: UpdateContractFromRetentionService,\r\n    private readonly finalizeContractEventService: FinalizeContractEventService,\r\n    private readonly dashboardService: DashboardService,\r\n  ) {}\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Get('expiring')\r\n  async getExpiringContracts(\r\n    @Query()\r\n    query: PaginationDto,\r\n    @Request() request: IRequestUser,\r\n  ): Promise<PaginatedResultDto<ExpiringContractDto>> {\r\n    return this.contractLifecycleMonitoringService.perform(query, request.user.id);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Post('send-contract-to-retention')\r\n  @HttpCode(204)\r\n  async sendContractToRetention(\r\n    @Body()\r\n    body: SendToRetentionDto,\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    return this.sendContractToRetentionService.perform(body, request.user.id);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Get('retention')\r\n  async getContractsForRetention(\r\n    @Query('statuses') statuses: ContractEventStatus | ContractEventStatus[],\r\n    @Request() request: IRequestUser,\r\n    @Query('page') page = 1,\r\n    @Query('limit') limit = 10,\r\n  ) {\r\n    const statusArray: ContractEventStatus[] = [];\r\n\r\n    if (statuses) {\r\n      if (Array.isArray(statuses)) {\r\n        statusArray.push(...statuses);\r\n      } else {\r\n        statusArray.push(statuses);\r\n      }\r\n    }\r\n\r\n    return this.getContractsForRetentionService.perform(\r\n      request.user.id,\r\n      statusArray,\r\n      Number(page),\r\n      Number(limit),\r\n    );\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Get('dashboard')\r\n  async dashboard(@Request() request: IRequestUser) {\r\n    return this.dashboardService.perform(request.user.id);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Post(':contractId/retention/update')\r\n  @UseInterceptors(FilesInterceptor('attachments'))\r\n  async updateContractFromRetention(\r\n    @Param('contractId') contractId: string,\r\n    @Body()\r\n    createEventDto: CreateContractEventDto,\r\n    @UploadedFiles() attachments: Express.Multer.File[],\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    const event = await this.updateContractFromRetentionService.perform(\r\n      contractId,\r\n      createEventDto,\r\n      request.user.id,\r\n      attachments,\r\n    );\r\n\r\n    return {\r\n      message: 'Evento de contrato criado com sucesso',\r\n      event,\r\n    };\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Post(':contractId/finalize')\r\n  @UseInterceptors(FilesInterceptor('attachments'))\r\n  async finalizeContractEvent(\r\n    @Param('contractId') contractId: string,\r\n    @Body()\r\n    finalizeEventDto: FinalizeContractEventDto,\r\n    @UploadedFiles() attachments: Express.Multer.File[],\r\n    @Request() request: IRequestUser,\r\n  ) {\r\n    const event = await this.finalizeContractEventService.perform(\r\n      contractId,\r\n      finalizeEventDto,\r\n      request.user.id,\r\n      attachments,\r\n    );\r\n\r\n    return {\r\n      message: 'Evento de contrato finalizado com sucesso',\r\n      event,\r\n    };\r\n  }\r\n}\r\n"]}