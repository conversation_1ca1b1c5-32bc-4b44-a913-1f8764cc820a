import type { ITransactionContext } from '@/application/interfaces'
import type { Either } from './either'

export interface Repository<T, F = unknown> {
  save(entity: T, tx?: ITransactionContext): Promise<Either<Error, void>>
  findById(
    id: string,
    tx?: ITransactionContext
  ): Promise<Either<Error, T | null>>
  findAll(
    filters?: F,
    page?: number,
    limit?: number
  ): Promise<Either<Error, T[]>>
  delete(id: string): Promise<Either<Error, void>>
}
