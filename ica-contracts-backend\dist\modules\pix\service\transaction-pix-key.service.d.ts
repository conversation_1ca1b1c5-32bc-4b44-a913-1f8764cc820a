import 'moment-timezone';
import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { VirtualBalanceService } from 'src/apis/icainvest-credit/services/virtual-balance.service';
import { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { TwoFactorAuthService } from 'src/modules/two-factor-auth/two-factor-auth.service';
import type { BRHoliday } from 'br-holiday';
import { TransactionPixKeyDto } from '../dto/transaction-pix-key.dto';
import { ITransactionPixKeyResponse } from '../response/transaction-pix-key-response';
export declare class TransactionPixKeyService {
    private accountRepository;
    private readonly accountLimitRepo;
    private transactionRepository;
    private celcoinService;
    private virtualBalanceService;
    private getLimit;
    private configService;
    private readonly twoFactorAuthService;
    private readonly brHoliday;
    constructor(accountRepository: Repository<AccountEntity>, accountLimitRepo: Repository<AccountTransferLimitEntity>, transactionRepository: Repository<TransactionEntity>, celcoinService: PixTransactionCelcoinService, virtualBalanceService: VirtualBalanceService, getLimit: GetAccountLimitService, configService: ConfigService, twoFactorAuthService: TwoFactorAuthService, brHoliday: BRHoliday);
    perform(data: TransactionPixKeyDto, id: string, isAutomate?: boolean, twoFactorToken?: string): Promise<ITransactionPixKeyResponse>;
    private isValidTransaction;
    private scheduleTransaction;
}
