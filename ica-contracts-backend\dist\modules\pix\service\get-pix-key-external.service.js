"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPixKeyExternalService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_transaction_celcoin_service_1 = require("../../../apis/celcoin/services/pix-transaction-celcoin.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const account_type_enum_1 = require("../../../shared/enums/account-type.enum");
const key_type_enum_1 = require("../../../shared/enums/key-type.enum");
const logger_1 = require("../../../shared/logger");
const typeorm_2 = require("typeorm");
let GetPixKeyExternalService = class GetPixKeyExternalService {
    constructor(accountDb, celcoinService) {
        this.accountDb = accountDb;
        this.celcoinService = celcoinService;
        this.runningRequests = new Map();
    }
    async perform(data, id) {
        const account = await this.accountDb.findOne({
            relations: {
                business: true,
                owner: true,
            },
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
        });
        if (!account)
            throw new common_1.NotFoundException('Conta não encontrada.');
        const requestKey = `${account.number}-${data.key}`;
        if (this.runningRequests.has(requestKey)) {
            throw new common_1.HttpException({
                message: 'A requisição já está em andamento.',
                error: 'Too Many Requests',
                statusCode: 429,
            }, 429);
        }
        this.runningRequests.set(requestKey, null);
        return new Promise((resolve, reject) => {
            let isRequestPending = false;
            const intervalId = setInterval(async () => {
                if (isRequestPending) {
                    return;
                }
                isRequestPending = true;
                try {
                    const { body: result } = await this.celcoinService.getAccountExternalPixKey({
                        accountNumber: account.number,
                        key: data.key,
                    });
                    const response = {
                        endToEndId: result.endtoEndId,
                        key: result.key,
                        keyType: key_type_enum_1.KeyTypeEnum[result.keyType],
                        account: {
                            branch: result.account.branch,
                            number: result.account.account,
                            type: result.account.accountType,
                            bank: result.account.participant,
                        },
                        owner: {
                            document: result.owner.documentNumber,
                            name: result.owner.name,
                            type: account_type_enum_1.AccountTypeEnum[result.owner.type],
                        },
                    };
                    logger_1.logger.info(`Consulta feita por id ${account.id} para a chave pix ${result.key}`);
                    clearInterval(intervalId);
                    this.runningRequests.delete(requestKey);
                    resolve(response);
                }
                catch (error) {
                    clearInterval(intervalId);
                    this.runningRequests.delete(requestKey);
                    reject(new common_1.HttpException(error.response, 500));
                }
                finally {
                    isRequestPending = false;
                }
            }, 1500);
            this.runningRequests.set(requestKey, intervalId);
        });
    }
    stopPerformingRequests(data, accountNumber) {
        const requestKey = `${accountNumber}-${data.key}`;
        if (this.runningRequests.has(requestKey)) {
            const intervalId = this.runningRequests.get(requestKey);
            clearInterval(intervalId);
            this.runningRequests.delete(requestKey);
        }
    }
};
exports.GetPixKeyExternalService = GetPixKeyExternalService;
exports.GetPixKeyExternalService = GetPixKeyExternalService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, common_1.Inject)(pix_transaction_celcoin_service_1.PixTransactionCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        pix_transaction_celcoin_service_1.PixTransactionCelcoinService])
], GetPixKeyExternalService);
//# sourceMappingURL=get-pix-key-external.service.js.map