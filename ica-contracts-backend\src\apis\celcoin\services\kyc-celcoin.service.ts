import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios from 'axios';
import * as FormData from 'form-data';
import * as https from 'https';

import { IKYCCelcoinRequest } from '../requests/kyc-celcoin.request';
import { IKYCCelcoinResponse } from '../responses/kyc-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class KYCCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async sendDocuments(data: IKYCCelcoinRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const form = new FormData();

    form.append('filetype', data.filetype);
    if (
      data.filetype === 'CONTRATO_SOCIAL' ||
      data.filetype === 'CARTAO_CNPJ'
    ) {
      form.append('front', data.front, {
        filename: `${data.filetype}.pdf`,
        contentType: 'application/pdf',
      });
    } else {
      form.append('front', data.front, {
        filename: 'front.jpg',
      });
    }
    if (data.verse)
      form.append('verse', data.verse, {
        filename: 'back.jpg',
      });

    form.append('documentnumber', data.documentnumber);
    if (data.cnpj) form.append('cnpj', data.cnpj);

    return axios
      .post(
        `${process.env.CELCOIN_URL}/celcoinkyc/document/v1/fileupload`,
        form,
        {
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
          httpsAgent,
        },
      )
      .then((response: { data: IKYCCelcoinResponse }) => {
        return response.data;
      })
      .catch((error) => {
        throw new InternalServerErrorException(error.response.data);
      });
  }
}
