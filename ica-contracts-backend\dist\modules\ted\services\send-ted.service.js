"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendTedService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const date_fns_1 = require("date-fns");
const ted_celcoin_service_1 = require("../../../apis/celcoin/services/ted-celcoin.service");
const get_account_limit_service_1 = require("../../account-transfer-limit/services/get-account-limit.service");
const two_factor_auth_service_1 = require("../../two-factor-auth/two-factor-auth.service");
const account_transfer_limits_entity_1 = require("../../../shared/database/typeorm/entities/account-transfer-limits.entity");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const transaction_movement_type_enum_1 = require("../../../shared/enums/transaction-movement-type.enum");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
let SendTedService = class SendTedService {
    constructor(accountRepo, transactionRepo, accountLimitRepo, celcoinService, getLimit, twoFactorAuthService) {
        this.accountRepo = accountRepo;
        this.transactionRepo = transactionRepo;
        this.accountLimitRepo = accountLimitRepo;
        this.celcoinService = celcoinService;
        this.getLimit = getLimit;
        this.twoFactorAuthService = twoFactorAuthService;
    }
    async perform(data, id, twoFactorToken) {
        const account = await this.accountRepo.findOne({
            where: [{ ownerId: (0, typeorm_2.Equal)(id) }, { businessId: (0, typeorm_2.Equal)(id) }],
        });
        if (!account)
            throw new common_1.NotFoundException('Conta não encontrada ou não vinculada ao owner.');
        await this.twoFactorAuthService.verifyTwoFactorAuthentication({
            userId: id,
            token: twoFactorToken,
        });
        const balance = await this.getLimit.execute(account.id);
        if (Number(data.amount) > Number(balance.dailyLimit))
            throw new common_1.BadRequestException('Limite de transferencia excedido!');
        const transaction = await this.transactionRepo.manager.transaction(async (manager) => {
            const uuid = (0, uuid_1.v4)();
            const { body: result } = await this.celcoinService.sendTed({
                amount: Number(data.amount),
                clientCode: uuid,
                clientFinality: data.finality,
                creditParty: {
                    account: data.external.account.number,
                    accountType: data.external.account.type,
                    branch: data.external.account.branch,
                    taxId: data.external.document,
                    name: data.external.name,
                    bank: data.external.account.bank,
                    personType: data.external.person,
                },
                debitParty: {
                    account: account.number,
                },
                description: data.description,
            });
            const transferMetadata = {
                creditParty: result.creditParty,
                debitParty: result.debitParty,
                finality: data.finality,
                id: result.id,
            };
            const newTransaction = manager.create(transaction_entity_1.TransactionEntity, {
                accountId: account.id,
                account,
                code: result.id,
                description: data.description,
                value: data.amount,
                destinyAccount: result.creditParty.account,
                destinyBank: result.creditParty.bank,
                destinyBranch: result.creditParty.branch,
                destinyDocument: result.creditParty.taxId,
                destinyName: result.creditParty.name,
                type: transaction_movement_type_enum_1.TransactionMovementTypeEnum.TED_CASHOUT,
                transferMetadata: JSON.stringify(transferMetadata),
            });
            const transaction = await manager.save(newTransaction);
            return transaction;
        });
        return {
            id: transaction.id,
            external: {
                account: transaction.destinyAccount,
                branch: transaction.destinyBranch,
                name: transaction.destinyName,
                document: transaction.destinyDocument,
                bank: transaction.destinyBank,
            },
        };
    }
    async isValidTransaction(account, amount) {
        let accountLimit = await this.accountLimitRepo.findOne({
            where: { accountId: account.id },
        });
        if (!accountLimit)
            accountLimit = await this.accountLimitRepo.save({
                accountId: account.id,
            });
        if (!accountLimit.active)
            return;
        if ((0, date_fns_1.isWeekend)(new Date()))
            throw new common_1.BadRequestException('Limite de transferencia excedido!');
        const amountNumber = Number(amount);
        const amountIsValidOnGeneralLimit = amountNumber <= accountLimit.generalTransferLimit;
        const amountIsValidOnMonthLimit = amountNumber <= accountLimit.monthlyLimit;
        const amountIsValidOnDailyLimit = amountNumber <= accountLimit.dailyLimit;
        const now = new Date().getHours();
        const amountIsValidOnNightlLimit = now >= 18
            ? amountNumber <= accountLimit.dailyNightLimit - accountLimit.dailyLimit
            : true;
        if (!(amountIsValidOnGeneralLimit &&
            amountIsValidOnMonthLimit &&
            amountIsValidOnDailyLimit &&
            amountIsValidOnNightlLimit)) {
            throw new common_1.BadRequestException('Limite de transferencia excedido!');
        }
        accountLimit.monthlyLimit -= amountNumber;
        accountLimit.dailyLimit -= amountNumber;
        if (now >= 18)
            accountLimit.dailyNightLimit -= amountNumber;
        this.accountLimitRepo.save(accountLimit);
    }
};
exports.SendTedService = SendTedService;
exports.SendTedService = SendTedService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(account_transfer_limits_entity_1.AccountTransferLimitEntity)),
    __param(3, (0, common_1.Inject)(ted_celcoin_service_1.TedCelcoinService)),
    __param(4, (0, common_1.Inject)(get_account_limit_service_1.GetAccountLimitService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        ted_celcoin_service_1.TedCelcoinService,
        get_account_limit_service_1.GetAccountLimitService,
        two_factor_auth_service_1.TwoFactorAuthService])
], SendTedService);
//# sourceMappingURL=send-ted.service.js.map