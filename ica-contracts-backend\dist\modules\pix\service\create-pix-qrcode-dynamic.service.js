"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePixQRCodeDynamicService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_qrcode_celcoin_service_1 = require("../../../apis/celcoin/services/pix-qrcode-celcoin.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const pix_qrcode_entity_1 = require("../../../shared/database/typeorm/entities/pix-qrcode.entity");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
let CreatePixQRCodeDynamicService = class CreatePixQRCodeDynamicService {
    constructor(accountDb, pixQRCodeRepo, pixQRCodeCelcoin) {
        this.accountDb = accountDb;
        this.pixQRCodeRepo = pixQRCodeRepo;
        this.pixQRCodeCelcoin = pixQRCodeCelcoin;
    }
    async perform(data, id) {
        const account = await this.accountDb.findOne({
            relations: {
                business: true,
                owner: true,
            },
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
        });
        if (!account) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        const transactionId = (0, uuid_1.v4)();
        const apiResponse = await this.pixQRCodeCelcoin.createQRCodeDynamic({
            amount: data.amount,
            key: data.key,
            clientRequestId: transactionId,
            payerCNPJ: data.payer?.cnpj || undefined,
            payerCPF: data.payer?.cpf || undefined,
            payerName: data.payer?.name || undefined,
            merchant: {
                city: data.merchant.city,
                merchantCategoryCode: data.merchant.merchantCategoryCode,
                name: data.merchant.name,
                postalCode: data.merchant.postalCode,
            },
        });
        const create = this.pixQRCodeRepo.create({
            accountId: account.id,
            externalId: transactionId,
            type: 'DYNAMIC',
            emv: apiResponse.body.body.dynamicBRCodeData.emvqrcps,
            transactionId: String(apiResponse.body.transactionId),
        });
        await this.pixQRCodeRepo.save(create);
        return {
            emv: apiResponse.body.body.dynamicBRCodeData.emvqrcps,
        };
    }
};
exports.CreatePixQRCodeDynamicService = CreatePixQRCodeDynamicService;
exports.CreatePixQRCodeDynamicService = CreatePixQRCodeDynamicService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(pix_qrcode_entity_1.PixQRCodeEntity)),
    __param(2, (0, common_1.Inject)(pix_qrcode_celcoin_service_1.PixQRCodeCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        pix_qrcode_celcoin_service_1.PixQRCodeCelcoinService])
], CreatePixQRCodeDynamicService);
//# sourceMappingURL=create-pix-qrcode-dynamic.service.js.map