import {
  IncomeReport,
  type IncomeReportStatus,
} from '@/domain/entities/reports'
import {
  EmailSendStatus,
  type IncomeReportEmail,
} from '@/domain/entities/reports/income-report-email.entity'
import { type Either, left, right } from '@/domain/shared'
import type { Prisma } from '@prisma/client'
import { IncomeReportEmailMapper } from './income-report-email.mapper'
import {
  type ContractIncludes,
  InvestmentContractMapper,
} from './investment-contract.mapper'
import { InvestorMapper } from './investor.mapper'

export type IncomeReportWithRelations = Prisma.income_reportGetPayload<{
  include: {
    income_reports_contracts: {
      include: { contract: ContractIncludes }
    }
    income_report_email: {
      include: { email: true }
    }
    owner_role_relation: {
      include: {
        pre_register: true
        owner: { include: { account: true; address: true } }
        business: {
          include: {
            account: true
            address: true
            owner_business_relation: {
              include: { owner: { include: { address: true } } }
            }
          }
        }
      }
    }
  }
}>

export class IncomeReportMapper {
  static toDomain(raw: IncomeReportWithRelations): Either<Error, IncomeReport> {
    if (!raw.owner_role_relation) {
      return left(new Error('Investor relation missing in income report'))
    }

    const investorResult = InvestorMapper.toDomain(raw.owner_role_relation)
    if (investorResult.isLeft()) return left(investorResult.value)

    const contractYields = raw.income_reports_contracts.map(rel => {
      const contractResult = InvestmentContractMapper.toDomain(rel.contract)
      if (contractResult.isLeft()) throw contractResult.value
      return {
        contract: contractResult.value,
        amount: 0,
        valueApplicateContract: 0,
        valueInvestimentAddedum: 0,
        valueInvestimentContract: 0,
      }
    })

    const status = raw.status as IncomeReportStatus

    const emails: IncomeReportEmail[] = []

    for (const { email } of raw.income_report_email) {
      if (email) {
        let status = EmailSendStatus.PENDING

        if (raw.status === EmailSendStatus.FAILED) {
          status = EmailSendStatus.FAILED
        }

        const incomeReportEmail = IncomeReportEmailMapper.toDomain({
          id: email.id,
          from: email.from,
          status: status,
          body: email.body ?? undefined,
          body_type: email.body_type ?? undefined,
          error_message: email.error_message ?? undefined,
          reply: email.reply ?? undefined,
          sent_at: email.sent_at ?? undefined,
          failed_at: email.failed_at ?? undefined,
          created_at: email.created_at,
        })

        if (incomeReportEmail.isRight()) {
          emails.push(incomeReportEmail.value)
        }
      }
    }

    const report = IncomeReport.createFromExisting(
      {
        investor: investorResult.value,
        year: Number(raw.reference_year),
        contractYields,
        createdAt: raw.createdAt,
        updatedAt: raw.updatedAt,
        fileId: raw.file_id,
        status,
        emails,
      },
      raw.id
    )

    return right(report)
  }

  static toPersistence(
    entity: IncomeReport
  ): Prisma.income_reportUncheckedCreateInput {
    return {
      id: entity.id,
      investor_id: entity.getInvestor().id,
      reference_year: entity.getReferenceYear().toString(),
      status: entity.getStatus(),
      file_id: entity.getFileId()?.toString(),
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    }
  }
}
