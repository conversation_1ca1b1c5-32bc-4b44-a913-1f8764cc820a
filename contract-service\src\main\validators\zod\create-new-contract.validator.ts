import {
  CompanyLegalType,
  ContractType,
  InvestorProfile,
  PaymentMethod,
  PersonType,
} from "@/application/dtos/contracts/create-new-contract.dto";

import { z } from "zod";

import type { CreateNewContractDTO } from "@/application/dtos/contracts/create-new-contract.dto";
import { type Either, left, right } from "@/domain/shared/either";
import {
  type IValidator,
  ValidationError,
} from "@/presentation/http/validation/validator";

export const AddressSchema = z.object(
  {
    street: z
      .string({ required_error: "Rua é obrigatória" })
      .min(3, "A rua deve ter no mínimo 3 caracteres"),
    city: z
      .string({ required_error: "Cidade é obrigatória" })
      .min(2, "A cidade deve ter no mínimo 2 caracteres"),
    state: z
      .string({ required_error: "Estado é obrigatório" })
      .length(2, "O estado deve ter exatamente 2 caracteres"),
    neighborhood: z.string({ required_error: "Bairro é obrigatório" }),
    postalCode: z
      .string({ required_error: "CEP é obrigatório" })
      .length(8, "O CEP deve conter exatamente 8 números"),
    number: z.string({ required_error: "Número é obrigatório" }),
    complement: z.string().optional(),
  },
  { required_error: "Endereço é obrigatório" }
);

export const IndividualSchema = z.object({
  fullName: z
    .string({ required_error: "Nome completo é obrigatório" })
    .min(3, "O nome deve ter no mínimo 3 caracteres"),
  cpf: z
    .string({ required_error: "CPF é obrigatório" })
    .length(11, "CPF deve ter 11 dígitos numéricos"),
  rg: z
    .string({ required_error: "RG é obrigatório" })
    .min(3, "RG deve ter no mínimo 3 caracteres"),
  issuingAgency: z
    .string({ required_error: "Órgão emissor é obrigatório" })
    .min(2, "Órgão emissor inválido"),
  nationality: z.string({ required_error: "Nacionalidade é obrigatória" }),
  occupation: z.string({ required_error: "Ocupação é obrigatória" }),
  birthDate: z
    .string({ required_error: "Data de nascimento é obrigatória" })
    .regex(
      /^\d{4}-\d{2}-\d{2}$/,
      "Data de nascimento inválida (formato YYYY-MM-DD)"
    ),
  email: z
    .string({ required_error: "E-mail é obrigatório" })
    .email("E-mail inválido"),
  phone: z
    .string({ required_error: "Telefone é obrigatório" })
    .min(10, "Telefone deve conter no mínimo 10 dígitos"),
  motherName: z
    .string({ required_error: "Nome da mãe é obrigatório" })
    .min(3, "Nome da mãe deve conter no mínimo 3 caracteres"),
  address: AddressSchema,
});

const CompanySchema = z.object({
  corporateName: z
    .string({ required_error: "Razão Social é obrigatória" })
    .min(3, "Razão Social deve conter no mínimo 3 caracteres"),
  cnpj: z
    .string({ required_error: "CNPJ é obrigatório" })
    .length(14, "CNPJ deve conter 14 dígitos"),
  type: z.nativeEnum(CompanyLegalType, {
    errorMap: () => ({ message: "Tipo de empresa inválido" }),
  }),
  size: z.string().optional().default(""),
  address: AddressSchema,
  representative: IndividualSchema,
});

const InvestmentSchema = z.object(
  {
    amount: z
      .number({ required_error: "Valor do investimento é obrigatório" })
      .positive("Valor deve ser maior que zero"),
    monthlyRate: z
      .number({ required_error: "Taxa de remuneração é obrigatória" })
      .min(0, "Taxa mínima é 0%")
      .max(100, "Taxa máxima é 100%"),
    durationInMonths: z
      .number({ required_error: "Prazo em meses é obrigatório" })
      .int("O prazo deve ser um número inteiro")
      .positive("Prazo deve ser maior que zero"),
    paymentMethod: z.nativeEnum(PaymentMethod, {
      errorMap: () => ({
        message:
          "Método de pagamento inválido. Tipos válidos: pix, bank_transfer, boleto",
      }),
    }),
    endDate: z
      .string({ required_error: "Data de fim do contrato é obrigatória" })
      .regex(/^\d{4}-\d{2}-\d{2}$/, "Data final inválida (formato YYYY-MM-DD)"),
    profile: z.nativeEnum(InvestorProfile, {
      errorMap: () => ({
        message:
          "Perfil do investidor inválido. Tipos válidos: conservative, moderate, aggressive",
      }),
    }),
    quotaQuantity: z.number().optional(),
    isDebenture: z.boolean({ required_error: "Campo Debênture é obrigatório" }),
  },
  { required_error: "Dados de investimento são obrigatórios" }
);

const AdvisorAssignmentSchema = z.object({
  advisorId: z
    .string({ required_error: "ID do assessor é obrigatório" })
    .uuid("ID do assessor inválido"),
  rate: z
    .number({ required_error: "Taxa do assessor é obrigatória" })
    .min(0, "Taxa mínima é 0%")
    .max(100, "Taxa máxima é 100%"),
});

export const BankAccountSchema = z.object(
  {
    bank: z
      .string({ required_error: "Nome do banco é obrigatório" })
      .min(2, "Nome do banco muito curto"),
    agency: z
      .string({ required_error: "Agência é obrigatória" })
      .min(2, "Agência inválida"),
    account: z
      .string({ required_error: "Conta é obrigatória" })
      .min(3, "Conta inválida"),
    pix: z.string().optional(),
  },
  { required_error: "Dados bancários são obrigatórios" }
);

export const CreateContractSchema = z
  .object(
    {
      personType: z.nativeEnum(PersonType, {
        errorMap: () => ({
          message: "Tipo de pessoa inválido. Tipos válidos: PF e PJ",
        }),
      }),
      contractType: z.nativeEnum(ContractType, {
        errorMap: () => ({
          message: "Tipo de contrato inválido. Tipos válidos: MUTUO ou SCP",
        }),
      }),
      brokerId: z.string({ message: "Broker é obrigatório" }),
      investment: InvestmentSchema,
      advisors: z.array(AdvisorAssignmentSchema).default([]),
      bankAccount: BankAccountSchema,
      individual: IndividualSchema.optional(),
      company: CompanySchema.optional(),
    },
    { required_error: "Dados do contrato são obrigatórios" }
  )
  .refine((data) => data.personType !== PersonType.PF || !!data.individual, {
    message: "Os dados do investidor pessoa física são obrigatórios",
    path: ["individual"],
  })
  .refine((data) => data.personType !== PersonType.PJ || !!data.company, {
    message: "Os dados da empresa são obrigatórios para contratos PJ",
    path: ["company"],
  });

export class CreateContractValidator
  implements IValidator<CreateNewContractDTO>
{
  private schema = CreateContractSchema;

  validate(input: unknown): Either<ValidationError, CreateNewContractDTO> {
    const result = this.schema.safeParse(input);

    if (!result.success) {
      const messages = result.error.errors.map((issue) => issue.message);
      return left(new ValidationError(messages));
    }

    return right(result.data);
  }
}
