import { Injectable, HttpException, BadRequestException } from '@nestjs/common';
import axios, { AxiosError, AxiosResponse } from 'axios';

import { IDeleteContractResponse } from '../request/delete-contract.request';
import { IDeleteContractPayload } from '../request/delete-contract.response';

@Injectable()
export class DeleteContractApiService {
  async perform(payload: IDeleteContractPayload): Promise<{
    id: string;
  }> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/${payload.contractId}/delete`;

      const { data } = await axios.post<
        IDeleteContractPayload,
        AxiosResponse<IDeleteContractResponse>
      >(url, payload);

      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status > 399 && error.response?.status < 499)
          throw new BadRequestException(error.response.data);

        throw new HttpException(
          error.response.data.message,
          error.response.data.statusCode,
        );
      }
      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }
}
