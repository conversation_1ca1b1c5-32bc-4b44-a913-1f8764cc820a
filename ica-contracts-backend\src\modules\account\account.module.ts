import { Module } from '@nestjs/common';
import { ApisModule } from 'src/apis/apis.module';
import { IcaContractServiceModule } from 'src/apis/ica-contract-service/ica-contract-service.module';
import { ManualRemoveService } from 'src/apis/icainvest-credit/services/manual-remove.service';
import { EmailNgModule } from 'src/shared/email-ng/email-ng.module';
import { SharedModule } from 'src/shared/shared.module';

import { AccountTransferLimitModule } from '../account-transfer-limit/account-transfer-limit.module';
import { NotificationModule } from '../notifications/notification.module';
import { WalletsModule } from '../wallets/wallets.module';
import { AccountController } from './controllers/account.controller';
import { KycController } from './controllers/kyc.controller';
import { AccountStatementService } from './services/account-statement-v2.service';
import { ChangeAccountStatusService } from './services/change-account-status.service';
import { ChangePasswordService } from './services/change-password.service';
import { CloseAccountService } from './services/close-account.service';
import { ConfirmForgotPasswordService } from './services/confirm-forgot-password.service';
import { ConsultAccountExtractService } from './services/consult-account-extract.service';
import { CreateAccountPFService } from './services/create-account-pf.service';
import { CreateAccountPJService } from './services/create-account-pj.service';
import { CreateAdminAccountService } from './services/create-admin-account.service';
import { CheckExistingContractHandler } from './services/create-existing-contract/concrete-handlers/check-existing-contract.handler';
import { ContractHandler } from './services/create-existing-contract/concrete-handlers/create-contract.handler';
import { ValidateAdvisorHandler } from './services/create-existing-contract/concrete-handlers/validate-advisors.handler';
import { CreateExistingContractService } from './services/create-existing-contract/create-existing-contract.service';
import { CreatePasswordTransactionService } from './services/create-password-transaction.service';
import { CronCreateAccountService } from './services/cron-create-account.service';
import { ForgotPasswordService } from './services/forgot-password.service';
import { GetAccountService } from './services/get-account.service';
import { GetAddressService } from './services/get-address.service';
import { GetBalanceService } from './services/get-balance.service';
import { GetDocumentsService } from './services/get-document.service';
import { GetProfileService } from './services/get-profile.service';
import { GetUserInfoService } from './services/get-user-info.service';
import { GetVirtualBalanceService } from './services/get-virtual-balance.service';
import { InvestmentExtractService } from './services/investment-extract.service';
import { ListAllAccountsService } from './services/list-all-accouts.service';
import { ListOneAccountService } from './services/list-one-account.service';
import { MigrationAccountsService } from './services/migration-accounts.service';
import { ResendSocialContractService } from './services/resend-social-contract.service';
import { ResubmitContractService } from './services/resubmit-contract/resubmit-contract.service';
import { SendDocumentsAccountService } from './services/send-documents-account.service';
import { SendInvestmentExtractService } from './services/send-investment-extract.service';
import { SendManualDocumentsService } from './services/send-manual-documents.service';
import { SendProfileImageService } from './services/send-profile-image.service';
import { ServiceFeeService } from './services/service-fee.service';
import { UpdateAccountPFService } from './services/update-account-pf.service';
import { UpdateAccountPJService } from './services/update-account-pj.service';
import { UpdatePasswordTransactionService } from './services/update-password-transaction.service';

@Module({
  controllers: [AccountController, KycController],
  providers: [
    CreateAccountPFService,
    CreateAccountPJService,
    GetAccountService,
    SendDocumentsAccountService,
    CreateAccountPFService,
    CreateAccountPJService,
    ConsultAccountExtractService,
    GetBalanceService,
    UpdateAccountPFService,
    UpdateAccountPJService,
    CloseAccountService,
    ChangeAccountStatusService,
    ChangePasswordService,
    CreatePasswordTransactionService,
    CronCreateAccountService,
    SendProfileImageService,
    ServiceFeeService,
    UpdatePasswordTransactionService,
    ListAllAccountsService,
    MigrationAccountsService,
    GetAddressService,
    ForgotPasswordService,
    InvestmentExtractService,
    SendManualDocumentsService,
    SendInvestmentExtractService,
    ResendSocialContractService,
    ConfirmForgotPasswordService,
    GetDocumentsService,
    AccountStatementService,
    GetVirtualBalanceService,
    ListOneAccountService,
    ManualRemoveService,
    CreateAdminAccountService,
    CheckExistingContractHandler,
    ContractHandler,
    ValidateAdvisorHandler,
    GetProfileService,
    CreateExistingContractService,
    ResubmitContractService,
    GetUserInfoService,
  ],
  imports: [
    IcaContractServiceModule,
    SharedModule,
    ApisModule,
    AccountTransferLimitModule,
    WalletsModule,
    NotificationModule,
    EmailNgModule,
  ],
})
export class AccountModule {}
