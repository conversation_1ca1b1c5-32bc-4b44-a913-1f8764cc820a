import { Advisor } from '@/domain/entities/user'
import { type Either, left, right } from '@/domain/shared'
import type { Prisma } from '@prisma/client'
import { BusinessMapper } from './company.mapper'
import { IndividualMapper } from './individual.mapper'
import type { OwnerRoleRelationWithFullParty } from './investor.mapper'

export type AdvisorOwnerRoleRelationWithFullParty =
  Prisma.owner_role_relationGetPayload<{
    include: {
      owner: {
        include: { address: true; account: true }
      }
      business: {
        include: {
          address: true
          account: true
          owner_business_relation: {
            include: {
              owner: {
                include: { address: true }
              }
            }
          }
        }
      }
    }
  }>

export class AdvisorMapper {
  static toDomainList(
    rawList: AdvisorOwnerRoleRelationWithFullParty[],
    brokerId: string
  ): Either<Error, Advisor[]> {
    const advisors: Advisor[] = []

    for (const raw of rawList) {
      if (raw.owner) {
        const result = IndividualMapper.toDomain(raw.owner)
        if (result.isLeft()) return left(result.value)
        advisors.push(
          Advisor.createFromIndividual(
            result.value,
            brokerId,
            raw.part_percent?.toNumber() ?? 0,
            raw.id
          )
        )
        continue
      }

      if (raw.business) {
        const result = BusinessMapper.toDomain(raw.business)
        if (result.isLeft()) return left(result.value)
        advisors.push(
          Advisor.createFromCompany(
            result.value,
            brokerId,
            raw.part_percent?.toNumber() ?? 0,
            raw.id
          )
        )
        continue
      }

      return left(
        new Error(`Advisor ${raw.id} is missing both owner and business`)
      )
    }

    return right(advisors)
  }

  static toDomain(
    payload: OwnerRoleRelationWithFullParty,
    brokerId: string
  ): Either<Error, Advisor> {
    if (payload.owner) {
      const result = IndividualMapper.toDomain(payload.owner)
      if (result.isLeft()) return left(result.value)
      const advisor = Advisor.createFromIndividual(
        result.value,
        brokerId,
        payload.part_percent?.toNumber() ?? 0,
        payload.id
      )
      return right(advisor)
    }

    if (payload.business) {
      const result = BusinessMapper.toDomain(payload.business)
      if (result.isLeft()) return left(result.value)
      const advisor = Advisor.createFromCompany(
        result.value,
        brokerId,
        payload.part_percent?.toNumber() ?? 0,
        payload.id
      )
      return right(advisor)
    }

    return left(
      new Error(`Advisor ${payload.id} is missing both owner and business`)
    )
  }
}
