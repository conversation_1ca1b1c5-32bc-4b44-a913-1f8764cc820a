Index: src/components/Notifications/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import AuthContext from \"@/provider/AuthContext\";\r\nimport { BellIcon, StarIcon, XCircleIcon } from \"@heroicons/react/24/outline\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\nimport Button from \"../Button/Button\";\r\nimport Skeleton, { SkeletonItem } from \"../Skeleton/intex\";\r\nimport api from \"@/core/api\";\r\nimport { UserProfile } from \"@/models/user\";\r\nimport returnError from \"@/functions/returnError\";\r\nimport { NotificationProps } from \"../Header\";\r\nimport FilterModal from \"../FilterModal\";\r\nimport Select from \"../Select\";\r\nimport Dropzone from \"../Dropzone\";\r\nimport { getUserProfile } from \"@/functions/getUserData\";\r\nimport { X, XSquareIcon } from \"lucide-react\";\r\n\r\ninterface IProps {\r\n  open: boolean;\r\n  notifications: NotificationProps[] | undefined;\r\n  filter: string;\r\n  setFilter: (d: any) => void;\r\n  handleNotifications: () => void;\r\n}\r\n\r\ninterface NotificationProp {\r\n  notification: NotificationProps;\r\n}\r\n\r\nexport default function Notifications({\r\n  open,\r\n  notifications,\r\n  filter,\r\n  setFilter,\r\n  handleNotifications,\r\n}: IProps) {\r\n  const userProfile = getUserProfile();\r\n  const { setNotificationModal } = useContext(AuthContext);\r\n  const [notificationSelected, setNotificationSelected] =\r\n    useState<NotificationProps>();\r\n  const [loading, setLoading] = useState(false);\r\n  const [activeFilter, setActiveFilter] = useState(false);\r\n\r\n  const Notification = ({ notification }: NotificationProp) => (\r\n    <div className=\"w-full\">\r\n      <div\r\n        className=\"w-[95%] flex justify-end mt-2 mb-[-5px] ml-[-2.5px] cursor-pointer\"\r\n        onClick={() => setNotificationSelected(undefined)}\r\n      >\r\n        <X className=\"w-5 h-5\" />\r\n      </div>\r\n      <div\r\n        className=\"mt-2 p-5 bg-[#282828] rounded-lg cursor-pointer flex justify-between items-center\"\r\n        onClick={() => openNotification(notification.id, notification)}\r\n      >\r\n        <div className=\"w-[95%]\">\r\n          <div className=\"w-[50%] flex items-start flex-col\">\r\n            <p className=\"text-base font-bold flex\">{notification.title}</p>\r\n            <div className=\"w-full bg-[#FF9900] h-[1px] my-2\" />\r\n          </div>\r\n          <p className=\"text-xs w-[80%] font-extralight text-[#afafaf]\">\r\n            {notification.description}\r\n          </p>\r\n          <div className=\"flex justify-start flex-wrap w-[90%] gap-2 mt-1 items-center\">\r\n            {notification.brokerName && (\r\n              <p className=\"text-xs\">Broker: {notification.brokerName}</p>\r\n            )}\r\n            <div className=\"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full\" />\r\n            <p className=\"text-xs\">Data: {notification.date}</p>\r\n            <div className=\"ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full\" />\r\n            <p className=\"text-xs\">Horário: {notification.hour}</p>\r\n          </div>\r\n        </div>\r\n        <div className=\"w-[5%]\">\r\n          {notification.viewed === false && (\r\n            <div className=\"w-[10px] h-[10px] bg-[#FF9900] rounded-full\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const NotificationLoading = () => (\r\n    <div className=\"mt-2\">\r\n      <p className=\"text-lg font-bold mb-1\"></p>\r\n      <p className=\"text-sm w-[80%] font-extralight\"></p>\r\n      <SkeletonItem height=\"20px\" width=\"50%\" />\r\n      <SkeletonItem height=\"20px\" width=\"80%\" className=\"my-2\" />\r\n      <div className=\"flex justify-between flex-wrap w-[80%] gap-2 mt-1\">\r\n        <SkeletonItem height=\"20px\" width=\"30%\" />\r\n        <SkeletonItem height=\"20px\" width=\"30%\" />\r\n        <SkeletonItem height=\"20px\" width=\"30%\" />\r\n        <SkeletonItem height=\"20px\" width=\"50%\" />\r\n      </div>\r\n      <div className=\"w-full bg-[#FF9900] h-[1px] my-3\" />\r\n    </div>\r\n  );\r\n\r\n  const openNotification = (id: string, notification: NotificationProps) => {\r\n    api\r\n      .patch(\r\n        \"/notifications/mark-viewed\",\r\n        {},\r\n        {\r\n          params: {\r\n            notificationId: id,\r\n          },\r\n          headers: {\r\n            roleId: userProfile.roleId,\r\n          },\r\n        }\r\n      )\r\n      .then((resp) => {\r\n        setNotificationSelected(notification);\r\n      })\r\n      .catch((error) => {\r\n        returnError(error, \"Erro ao buscar dados da notificação!\");\r\n      });\r\n  };\r\n\r\n  const handleClose = () => {\r\n    if (notificationSelected) {\r\n      setNotificationSelected(undefined);\r\n    } else {\r\n      setFilter(\"\");\r\n      setNotificationModal(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as HTMLElement;\r\n      if (open && !target.closest(\".notifications-modal\")) {\r\n        handleClose();\r\n      }\r\n    };\r\n\r\n    if (open) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [open, notificationSelected]);\r\n\r\n  return (\r\n    <>\r\n      <div\r\n        className={`${\r\n          open ? \"fixed opacity-50\" : \"fixed opacity-0 pointer-events-none\"\r\n        } inset-0 bg-black z-40 transition-opacity duration-300 ease-in-out`}\r\n        onClick={handleClose}\r\n      />\r\n\r\n      <div\r\n        className={`${\r\n          open\r\n            ? \"fixed opacity-100 translate-x-0\"\r\n            : \"fixed opacity-0 translate-x-full pointer-events-none\"\r\n        } w-full md:w-5/12 2xl:w-4/12 h-full bg-[#1C1C1C] z-50 right-0 top-0 text-white p-5 overflow-auto border-t border-l border-[#FF9900] notifications-modal transition-all duration-300 ease-in-out`}\r\n      >\r\n        <div className=\"flex w-full justify-between items-center gap-4 pb-5\">\r\n          <div className=\"flex flex-1 justify-between items-center\">\r\n            <div>\r\n              <BellIcon color=\"#FF9900 \" width={40} />\r\n              <h1 className=\"font-bold text-2xl mt-1\">\r\n                {notificationSelected?.id ? \"Notificação\" : \"Notificações\"}\r\n              </h1>\r\n            </div>\r\n            {!notificationSelected && (\r\n              <FilterModal\r\n                activeModal={activeFilter}\r\n                handleSearch={handleNotifications}\r\n                setActiveModal={setActiveFilter}\r\n              >\r\n                <Select\r\n                  options={[\r\n                    {\r\n                      label: \"Todos\",\r\n                      value: \"\",\r\n                    },\r\n                    {\r\n                      label: \"Tentativa de Duplicidade\",\r\n                      value: \"DUPLICATED_DOCUMENT\",\r\n                    },\r\n                    {\r\n                      label: \"Contratos Gerados\",\r\n                      value: \"NEW_CONTRACT\",\r\n                    },\r\n                    {\r\n                      label: \"Aditivo Criado\",\r\n                      value: \"INCLUDE_ADDITIVE\",\r\n                    },\r\n                  ]}\r\n                  selected={filter}\r\n                  setSelected={setFilter}\r\n                />\r\n              </FilterModal>\r\n            )}\r\n          </div>\r\n        </div>\r\n        {!notificationSelected ? (\r\n          <div>\r\n            {loading === true ? (\r\n              <div>\r\n                <NotificationLoading />\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                {notifications?.map((notification) => (\r\n                  <Notification\r\n                    key={notification.id}\r\n                    notification={notification}\r\n                  />\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div className=\"mt-2\">\r\n            <p className=\"text-lg font-bold\">{notificationSelected.title}</p>\r\n            <p className=\"text-sm w-[80%] mb-2 font-light text-[#afafaf]\">\r\n              {notificationSelected.description}\r\n            </p>\r\n            <p className=\"text-sm mb-2\">\r\n              Broker Responsável: {notificationSelected.brokerName}\r\n            </p>\r\n            <p className=\"text-sm mb-2\">\r\n              CPF Utilizado: {notificationSelected.cpf}\r\n            </p>\r\n            {/* <p className=\"text-sm mb-2\">Valor: R$ 300.000,00</p> */}\r\n            <div className=\"flex justify-between flex-wrap w-[50%] gap-2 mt-1\">\r\n              <p className=\"text-sm font-extralight text-[#afafaf]\">\r\n                Data: {notificationSelected.date}\r\n              </p>\r\n              <p className=\"text-sm font-extralight text-[#afafaf]\">\r\n                Horário: {notificationSelected.hour}\r\n              </p>\r\n            </div>\r\n            {(notificationSelected.type === \"NEW_CONTRACT\" ||\r\n              notificationSelected.type === \"INCLUDE_ADDITIVE\") && (\r\n              <div className=\"flex flex-wrap gap-4 mt-5\">\r\n                {notificationSelected?.contractPdf && (\r\n                  <div className=\"w-56\">\r\n                    <p className=\"text-sm mb-1\">Contrato Principal</p>\r\n                    <div\r\n                      onClick={() => {\r\n                        window.open(notificationSelected.contractPdf, \"_blank\");\r\n                      }}\r\n                    >\r\n                      <Dropzone disable={true} onFileUploaded={() => {}} />\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                {notificationSelected?.proofPayment && (\r\n                  <div className=\"w-56\">\r\n                    <p className=\"text-sm mb-1\">Comprovante de Pagamento</p>\r\n                    <div\r\n                      onClick={() => {\r\n                        window.open(\r\n                          notificationSelected.proofPayment,\r\n                          \"_blank\"\r\n                        );\r\n                      }}\r\n                    >\r\n                      <Dropzone disable={true} onFileUploaded={() => {}} />\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                {notificationSelected.type === \"INCLUDE_ADDITIVE\" && (\r\n                  <div className=\"flex gap-4\">\r\n                    {notificationSelected?.addendumPdf && (\r\n                      <div className=\"w-56\">\r\n                        <p className=\"text-sm mb-1\">Contrato de Aditivo</p>\r\n                        <div\r\n                          onClick={() => {\r\n                            window.open(\r\n                              notificationSelected?.addendumPdf,\r\n                              \"_blank\"\r\n                            );\r\n                          }}\r\n                        >\r\n                          <Dropzone disable={true} onFileUploaded={() => {}} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                    {notificationSelected?.proofPaymentAddendum && (\r\n                      <div className=\"w-56\">\r\n                        <p className=\"text-sm mb-1\">\r\n                          Comprovante de Pagamento Aditivo\r\n                        </p>\r\n                        <div\r\n                          onClick={() => {\r\n                            window.open(\r\n                              notificationSelected?.proofPaymentAddendum,\r\n                              \"_blank\"\r\n                            );\r\n                          }}\r\n                        >\r\n                          <Dropzone disable={true} onFileUploaded={() => {}} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/Notifications/index.tsx b/src/components/Notifications/index.tsx
--- a/src/components/Notifications/index.tsx	(revision 2bc0ab946d1dfaf07ee4b51f553087d96a5ed19b)
+++ b/src/components/Notifications/index.tsx	(date 1750766065056)
@@ -18,7 +18,7 @@
   notifications: NotificationProps[] | undefined;
   filter: string;
   setFilter: (d: any) => void;
-  handleNotifications: () => void;
+  handleNotifications: (filter?: string) => void;
 }
 
 interface NotificationProp {
@@ -38,6 +38,8 @@
     useState<NotificationProps>();
   const [loading, setLoading] = useState(false);
   const [activeFilter, setActiveFilter] = useState(false);
+  const [isFiltering, setIsFiltering] = useState(false);
+  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);
 
   const Notification = ({ notification }: NotificationProp) => (
     <div className="w-full">
@@ -79,18 +81,33 @@
   );
 
   const NotificationLoading = () => (
-    <div className="mt-2">
-      <p className="text-lg font-bold mb-1"></p>
-      <p className="text-sm w-[80%] font-extralight"></p>
-      <SkeletonItem height="20px" width="50%" />
-      <SkeletonItem height="20px" width="80%" className="my-2" />
-      <div className="flex justify-between flex-wrap w-[80%] gap-2 mt-1">
-        <SkeletonItem height="20px" width="30%" />
-        <SkeletonItem height="20px" width="30%" />
-        <SkeletonItem height="20px" width="30%" />
-        <SkeletonItem height="20px" width="50%" />
-      </div>
-      <div className="w-full bg-[#FF9900] h-[1px] my-3" />
+    <div>
+      {[1, 2, 3].map((index) => (
+        <div key={index} className="mt-2">
+          <div className="w-[95%] flex justify-end mt-2 mb-[-5px] ml-[-2.5px]">
+            <SkeletonItem height="20px" width="20px" />
+          </div>
+          <div className="mt-2 p-5 bg-[#282828] rounded-lg flex justify-between items-center">
+            <div className="w-[95%]">
+              <div className="w-[50%] flex items-start flex-col">
+                <SkeletonItem height="20px" width="60%" />
+                <div className="w-full bg-[#FF9900] h-[1px] my-2" />
+              </div>
+              <SkeletonItem height="16px" width="80%" className="my-2" />
+              <div className="flex justify-start flex-wrap w-[90%] gap-2 mt-1 items-center">
+                <SkeletonItem height="16px" width="30%" />
+                <div className="ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full" />
+                <SkeletonItem height="16px" width="25%" />
+                <div className="ellipsis w-[5px] h-[5px] bg-[#ffffff] rounded-full" />
+                <SkeletonItem height="16px" width="25%" />
+              </div>
+            </div>
+            <div className="w-[5%]">
+              <SkeletonItem height="10px" width="10px" className="rounded-full" />
+            </div>
+          </div>
+        </div>
+      ))}
     </div>
   );
 
@@ -121,10 +138,56 @@
       setNotificationSelected(undefined);
     } else {
       setFilter("");
+      setIsFiltering(false);
       setNotificationModal(false);
     }
   };
 
+  const handleFilterChange = (newFilter: string) => {
+    setIsFiltering(true);
+    setFilter(newFilter);
+    
+    // Limpar timer anterior se existir
+    if (debounceTimer) {
+      clearTimeout(debounceTimer);
+    }
+    
+    // Criar novo timer para debounce
+    const timer = setTimeout(() => {
+      handleNotifications(newFilter);
+      setActiveFilter(false);
+    }, 300);
+    
+    setDebounceTimer(timer);
+  };
+
+  const handleFilterSearch = () => {
+    setIsFiltering(true);
+    
+    // Limpar timer anterior se existir
+    if (debounceTimer) {
+      clearTimeout(debounceTimer);
+    }
+    
+    // Criar novo timer para debounce
+    const timer = setTimeout(() => {
+      handleNotifications(filter);
+      setActiveFilter(false);
+    }, 300);
+    
+    setDebounceTimer(timer);
+  };
+
+  // Monitora mudanças nas notificações para remover o loading
+  useEffect(() => {
+    if (notifications !== undefined && isFiltering) {
+      // Pequeno delay para garantir que a UI seja atualizada
+      setTimeout(() => {
+        setIsFiltering(false);
+      }, 300);
+    }
+  }, [notifications, isFiltering]);
+
   useEffect(() => {
     const handleClickOutside = (event: MouseEvent) => {
       const target = event.target as HTMLElement;
@@ -142,6 +205,15 @@
     };
   }, [open, notificationSelected]);
 
+  // Limpar timer quando componente for desmontado
+  useEffect(() => {
+    return () => {
+      if (debounceTimer) {
+        clearTimeout(debounceTimer);
+      }
+    };
+  }, [debounceTimer]);
+
   return (
     <>
       <div
@@ -169,8 +241,9 @@
             {!notificationSelected && (
               <FilterModal
                 activeModal={activeFilter}
-                handleSearch={handleNotifications}
+                handleSearch={handleFilterSearch}
                 setActiveModal={setActiveFilter}
+                hidenButton={true}
               >
                 <Select
                   options={[
@@ -192,7 +265,7 @@
                     },
                   ]}
                   selected={filter}
-                  setSelected={setFilter}
+                  setSelected={handleFilterChange}
                 />
               </FilterModal>
             )}
@@ -200,7 +273,7 @@
         </div>
         {!notificationSelected ? (
           <div>
-            {loading === true ? (
+            {loading === true || isFiltering ? (
               <div>
                 <NotificationLoading />
               </div>
