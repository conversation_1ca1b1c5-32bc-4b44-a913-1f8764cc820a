import {
  Body,
  Controller,
  HttpException,
  Inject,
  Post,
  Request,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';

import { ResendSocialContractDto } from '../dto/resend-social-contract.dto';
import { SendDocumentsAccountDto } from '../dto/send-documents-account.dto';
import { ResendSocialContractService } from '../services/resend-social-contract.service';
import { SendDocumentsAccountService } from '../services/send-documents-account.service';
import { SendManualDocumentsService } from '../services/send-manual-documents.service';

@Controller('kyc')
export class KycController {
  constructor(
    @Inject(SendDocumentsAccountService)
    private sendDocumentsAccountService: SendDocumentsAccountService,
    @Inject(SendManualDocumentsService)
    private sendManualDocumentsService: SendManualDocumentsService,
    @Inject(ResendSocialContractService)
    private resendSocialContractService: ResendSocialContractService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'back', maxCount: 1 },
      { name: 'front', maxCount: 1 },
      { name: 'socialContract', maxCount: 1 },
      { name: 'cardCnpj', maxCount: 1 },
    ]),
  )
  async sendDocuments(
    @Request()
    request: IRequestUser,
    @Body()
    body: SendDocumentsAccountDto,
    @UploadedFiles()
    files: {
      front: Express.Multer.File[];
      back: Express.Multer.File[];
      socialContract: Express.Multer.File[];
      cardCnpj: Express.Multer.File[];
    },
  ) {
    try {
      const back = files.back ? files.back[0] : undefined;

      const socialContract = files.socialContract
        ? files.socialContract[0]
        : undefined;
      const cardCnpj = files.cardCnpj ? files.cardCnpj[0] : undefined;
      const response = await this.sendDocumentsAccountService.perform(
        {
          ...body,
          back,
          front: files.front[0],
          socialContract,
          cardCnpj,
        },
        body.accountId ? body.accountId : request.user.id,
      );
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status || 400,
      );
    }
  }

  @Post('manual')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'back', maxCount: 1 },
      { name: 'front', maxCount: 1 },
      { name: 'socialContract', maxCount: 1 },
      { name: 'cardCnpj', maxCount: 1 },
    ]),
  )
  async sendManualDocuments(
    @Request()
    request: IRequestUser,
    @Body()
    body: SendDocumentsAccountDto,
    @UploadedFiles()
    files: {
      front: Express.Multer.File[];
      back: Express.Multer.File[];
      socialContract: Express.Multer.File[];
      cardCnpj: Express.Multer.File[];
    },
  ) {
    try {
      const back = files.back ? files.back[0] : undefined;
      const socialContract = files.socialContract
        ? files.socialContract[0]
        : undefined;
      const cardCnpj = files.cardCnpj ? files.cardCnpj[0] : undefined;
      const response = await this.sendManualDocumentsService.perform(
        {
          ...body,
          back,
          front: files.front[0],
          socialContract,
          cardCnpj,
        },
        body.accountId ? body.accountId : request.user.id,
      );
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status || 400,
      );
    }
  }
  @Post('resend')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'socialContract', maxCount: 1 },
      { name: 'cardCnpj', maxCount: 1 },
    ]),
  )
  async resendSocialContract(
    @Request()
    request: IRequestUser,
    @Body()
    body: ResendSocialContractDto,
    @UploadedFiles()
    files: {
      socialContract: Express.Multer.File[];
      cardCnpj: Express.Multer.File[];
    },
  ) {
    try {
      const socialContract = files.socialContract
        ? files.socialContract[0]
        : undefined;
      const cardCnpj = files.cardCnpj ? files.cardCnpj[0] : undefined;
      const response = await this.resendSocialContractService.perform(
        {
          ...body,
          socialContract,
          cardCnpj,
        },
        body.accountId ? body.accountId : request.user.id,
      );
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status || 400,
      );
    }
  }
}
