"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestClaimPixKeyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pix_key_celcoin_service_1 = require("../../../apis/celcoin/services/pix-key-celcoin.service");
const owner_entity_1 = require("../../../shared/database/typeorm/entities/owner.entity");
const pix_key_entity_1 = require("../../../shared/database/typeorm/entities/pix-key.entity");
const claim_status_enum_1 = require("../../../shared/enums/claim-status.enum");
const pix_key_status_enum_1 = require("../../../shared/enums/pix-key-status.enum");
const logger_1 = require("../../../shared/logger");
const typeorm_2 = require("typeorm");
const request_claim_pix_key_dto_1 = require("../dto/request-claim-pix-key.dto");
let RequestClaimPixKeyService = class RequestClaimPixKeyService {
    constructor(pixKeyRepository, ownerRepository, pixCelcoinService) {
        this.pixKeyRepository = pixKeyRepository;
        this.ownerRepository = ownerRepository;
        this.pixCelcoinService = pixCelcoinService;
    }
    async perform(input, userId) {
        if (input.claimType === request_claim_pix_key_dto_1.ClaimType.OWNERSHIP) {
            if ([request_claim_pix_key_dto_1.PixKeyType.CPF, request_claim_pix_key_dto_1.PixKeyType.CNPJ].includes(input.typeKey)) {
                throw new common_1.BadRequestException(`${input.typeKey} não pode ser reivindicado, apenas portabilidade`);
            }
        }
        logger_1.logger.info('RequestClaimPixKeyService.perform() -> ', userId);
        const pixkey = await this.pixKeyRepository.findOne({
            where: [
                { key: input.key, account: { ownerId: userId } },
                { key: input.key, account: { businessId: userId } },
            ],
        });
        if (pixkey)
            throw new common_1.BadRequestException('Chave já reivindicada!');
        const owner = await this.ownerRepository.findOne({
            where: {
                id: userId,
            },
            relations: {
                account: true,
            },
        });
        const account = owner.account.shift();
        return this.pixKeyRepository.manager.transaction(async (manager) => {
            const response = await this.pixCelcoinService.requestClaimPixKey({
                account: account.number,
                claimType: input.claimType,
                key: input.key,
                keyType: input.typeKey,
            });
            const claimMetadata = {
                claimId: response.body.id,
                claimerAccount: response.body.claimerAccount,
            };
            const newPixKey = manager.create(pix_key_entity_1.PixKeyEntity, {
                key: response.body.key,
                status: pix_key_status_enum_1.PixKeyStatusEnum.OPEN,
                claimStatus: claim_status_enum_1.ClaimStatusEnum.OPEN,
                claimMetadata: JSON.stringify(claimMetadata),
                account,
                typeKey: input.typeKey,
            });
            await manager.save(newPixKey);
            return { reason: 'Reivindicação aberta com sucesso.' };
        });
    }
};
exports.RequestClaimPixKeyService = RequestClaimPixKeyService;
exports.RequestClaimPixKeyService = RequestClaimPixKeyService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(pix_key_entity_1.PixKeyEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_entity_1.OwnerEntity)),
    __param(2, (0, common_1.Inject)(pix_key_celcoin_service_1.PixKeyCelcoinService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        pix_key_celcoin_service_1.PixKeyCelcoinService])
], RequestClaimPixKeyService);
//# sourceMappingURL=request-claim-pix-key.service.js.map