import type { ContractStatus, InvestmentContract } from "../entities/contracts";
import type { IncomeReportEmail } from "../entities/reports/income-report-email.entity";
import type { Either, PaginatedResult, Repository } from "../shared";
import type { ITransactionContext } from "@/application/interfaces";

export interface ContractFilters {
  investorId?: string;
  brokerId?: string;
  status?: ContractStatus;
  type?: string;
  advisorId?: string;
}

export interface IInvestmentContractRepository
  extends Repository<InvestmentContract, ContractFilters> {
  findActiveByInvestor(investorId: string): Promise<InvestmentContract | null>;
  findByStatus(
    status: ContractStatus
  ): Promise<{ id: string; status: ContractStatus }[]>;
  isAdvisorLinkedToBroker(
    advisorId: string,
    brokerId: string
  ): Promise<boolean>;
  findActiveContractsByInvestorAndYear(
    contractId: string,
    year: number
  ): Promise<Either<Error, InvestmentContract[]>>;
  findPaginated(
    userId: string,
    page: number,
    limit: number
  ): Promise<Either<Error, PaginatedResult<InvestmentContract>>>;
  findIncomeReportByInvestorId(
    investorId: string,
    year: number
  ): Promise<IncomeReportEmail | null>;
  update(
    contractId: string,
    data: InvestmentContract
  ): Promise<Either<Error, void>>;
  resubmitRejectedContract(
    contractId: string,
    data: InvestmentContract
  ): Promise<Either<Error, void>>;
  editContract(
    contractId: string,
    data: InvestmentContract,
    tx?: ITransactionContext
  ): Promise<Either<Error, void>>;
}
