import {
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateOwnerService } from 'src/modules/owner/services/create-owner.service';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { RoleEntity } from 'src/shared/database/typeorm/entities/role.entity';
import { Repository, DataSource } from 'typeorm';

import { CreateBrokerDto } from '../dto/create-broker.dto';

@Injectable()
export class CreateBrokerService {
  constructor(
    @InjectRepository(OwnerEntity)
    private readonly ownerRepository: Repository<OwnerEntity>,
    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,
    @Inject(CreateOwnerService)
    private readonly createOwnerService: CreateOwnerService,
    private readonly dataSource: DataSource,
  ) {}

  async perform(input: CreateBrokerDto): Promise<OwnerEntity> {
    return await this.dataSource.transaction(async (manager) => {
      const role = await this.findRoleByName('broker');
      await this.createOwnerService.perform(input);
      const owner = await this.findOwnerByDocument(input.document);
      await this.ensureUniqueOwnerRoleRelation(owner, role);
      const ownerRoleRelation = this.createOwnerRoleRelation(owner, role);

      // Usar o manager da transação para salvar
      const savedRelation = await manager.save(OwnerRoleRelationEntity, ownerRoleRelation);



      return this.cleanOwnerEntity(owner);
    });
  }

  private async findRoleByName(roleName: string): Promise<RoleEntity> {
    const role = await this.roleRepository.findOne({
      where: { name: roleName },
    });
    if (!role) throw new NotFoundException('Role não encontrada.');
    return role;
  }

  private async findOwnerByDocument(document: string): Promise<OwnerEntity> {
    const owner = await this.ownerRepository.findOne({
      where: { cpf: document },
    });
    if (!owner) throw new NotFoundException('Proprietário não encontrado');
    return owner;
  }

  private async ensureUniqueOwnerRoleRelation(
    owner: OwnerEntity,
    role: RoleEntity,
  ): Promise<void> {
    const existingRelation = await this.ownerRoleRelationRepository.findOne({
      where: { owner, role },
    });
    if (existingRelation) {
      throw new ConflictException('Proprietário já possui a role de broker');
    }
  }

  private createOwnerRoleRelation(
    owner: OwnerEntity,
    role: RoleEntity,
  ): OwnerRoleRelationEntity {
    const ownerRoleRelation = new OwnerRoleRelationEntity();
    ownerRoleRelation.owner = owner;
    ownerRoleRelation.role = role;
    ownerRoleRelation.ownerId = owner.id;
    ownerRoleRelation.roleId = role.id;

    // Log para debug
    console.log('Creating broker relation:');
    console.log('Owner ID:', owner.id);
    console.log('Role ID:', role.id);
    console.log('Role name:', role.name);

    return ownerRoleRelation;
  }

  private cleanOwnerEntity(owner: OwnerEntity): OwnerEntity {
    const ownerCopy = { ...owner };
    delete ownerCopy.password;
    return ownerCopy;
  }
}
