{"name": "contract-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "export TZ=UTC && tsx watch --env-file=.env src/main/index.ts", "lint": "eslint .", "lint:fix": "eslint . --fix", "build": "tsup src --out-dir dist", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^22.13.9", "@types/node-cron": "^3.0.11", "globals": "^16.0.0", "pino-pretty": "^13.0.0", "prisma": "^6.5.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@fastify/multipart": "^9.0.3", "@prisma/client": "^6.5.0", "@types/bcrypt": "^5.0.2", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bullmq": "^5.42.0", "fastify": "^5.2.1", "ioredis": "^5.6.0", "node-cron": "^3.0.3", "pino": "^9.6.0", "tsup": "^8.4.0", "zod": "^3.24.2"}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@prisma/client", "@prisma/engines", "bcrypt", "esbuild", "msgpackr-extract", "prisma"]}, "packageManager": "pnpm@10.6.5+sha512.cdf928fca20832cd59ec53826492b7dc25dc524d4370b6b4adbf65803d32efaa6c1c88147c0ae4e8d579a6c9eec715757b50d4fa35eea179d868eada4ed043af"}