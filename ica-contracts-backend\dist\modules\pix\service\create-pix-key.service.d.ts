import { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';
import { Repository } from 'typeorm';
import { CreatePixDto } from '../dto/create-pix-key.dto';
export declare class CreatePixKeyService {
    private accountDb;
    private pixKeyRepository;
    private pix;
    constructor(accountDb: Repository<AccountEntity>, pixKeyRepository: Repository<PixKeyEntity>, pix: PixKeyCelcoinService);
    perform(input: CreatePixDto, id: string): Promise<PixKeyEntity>;
}
