import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { DeleteFavoriteTedContactDto } from '../dto/delete-favorite-ted-contact.dto';
import { GetTedDto } from '../dto/get-ted.dto';
import { SaveFavoriteTedContactDto } from '../dto/save-favorite-ted-contact.dto';
import { SendTedDto } from '../dto/send-ted.dto';
import { DeleteFavoriteTedContactService } from '../services/delete-favorite-ted-contact.service';
import { GetFavoriteTedContactService } from '../services/get-favorite-ted-contact.service';
import { GetRecentTedsService } from '../services/get-recent-teds.service';
import { GetTedService } from '../services/get-ted.service';
import { SaveFavoriteTedContactService } from '../services/save-favorite-ted-contact.service';
import { SendTedService } from '../services/send-ted.service';
export declare class TedController {
    private sendTedService;
    private getTedService;
    private getRecentTedsService;
    private readonly saveFavoriteTedContactService;
    private readonly getFavoriteTedContactService;
    private readonly deleteFavoriteTedContactService;
    constructor(sendTedService: SendTedService, getTedService: GetTedService, getRecentTedsService: GetRecentTedsService, saveFavoriteTedContactService: SaveFavoriteTedContactService, getFavoriteTedContactService: GetFavoriteTedContactService, deleteFavoriteTedContactService: DeleteFavoriteTedContactService);
    sendTed(body: SendTedDto, request: IRequestUser, twoFactorToken: string): Promise<import("../response/send-ted.response").ISendTedResponse>;
    getTed(query: GetTedDto): Promise<{
        id: string;
        amount: number;
        clientCode: string;
        debitParty: {
            account: string;
            branch: string;
            taxId: string;
            name: string;
            accountType: string;
            personType: string;
            bank: string;
        };
        creditParty: {
            bank: string;
            account: string;
            branch: string;
            taxId: string;
            name: string;
            accountType: string;
            personType: string;
        };
        description: string;
        error?: {
            errorCode: string;
            message: string;
        };
    }>;
    getRecent(request: IRequestUser): Promise<{
        transactions: {
            id: string;
            amount: number;
            clientCode: string;
            description: string;
            creditParty: {
                bank: string;
                account: string;
                branch: string;
                taxId: string;
                name: string;
                accountType: string;
                personType: string;
            };
            debitParty: {
                account: string;
                branch: string;
                taxId: string;
                name: string;
                accountType: string;
                personType: string;
                bank: string;
            };
        }[];
        transactionsCount: number;
    }>;
    saveFavoriteTedContact(body: SaveFavoriteTedContactDto, request: IRequestUser): Promise<void>;
    getFavoriteTedContact(request: IRequestUser): Promise<import("../../../shared/database/typeorm/entities/favorite-ted.entity").FavoriteTedEntity[]>;
    deleteFavoriteTedContact(params: DeleteFavoriteTedContactDto, request: IRequestUser): Promise<void>;
}
