{"version": 3, "file": "save-favorite-ted-contact.dto.js", "sourceRoot": "/", "sources": ["modules/ted/dto/save-favorite-ted-contact.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAkE;AAElE,MAAa,yBAAyB;CAwBrC;AAxBD,8DAwBC;AArBC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2DACI;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;uDACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;gEACW;AAItB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;gEACW;AAItB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;8DACS;AAIpB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wDACG", "sourcesContent": ["import { IsDefined, IsNotEmpty, IsString } from 'class-validator';\r\n\r\nexport class SaveFavoriteTedContactDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  document: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  accountBranch: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  accountNumber: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  accountBank: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  alias: string;\r\n}\r\n"]}