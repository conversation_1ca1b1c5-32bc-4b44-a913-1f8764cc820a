{"version": 3, "file": "income.module.js", "sourceRoot": "/", "sources": ["modules/income/income.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA2E;AAC3E,kGAA2F;AAC3F,8DAAwD;AAExD,uEAAmE;AACnE,4EAAuE;AACvE,4EAAuE;AACvE,0EAAqE;AACrE,wEAAmE;AACnE,4EAAuE;AAahE,IAAM,YAAY,GAAlB,MAAM,YAAY;IACvB,SAAS,CAAC,QAA4B;QACpC,QAAQ;aACL,KAAK,CAAC,mDAAuB,CAAC;aAC9B,SAAS,CACR,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,sBAAa,CAAC,IAAI,EAAE,EAC9C,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,sBAAa,CAAC,GAAG,EAAE,EAC7C,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,sBAAa,CAAC,GAAG,EAAE,EACjD,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,sBAAa,CAAC,KAAK,EAAE,EACzD,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,sBAAa,CAAC,MAAM,EAAE,CACjD,CAAC;IACN,CAAC;CACF,CAAA;AAZY,oCAAY;uBAAZ,YAAY;IAXxB,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,4BAAY,CAAC;QACvB,SAAS,EAAE;YACT,uCAAiB;YACjB,yCAAkB;YAClB,2CAAmB;YACnB,2CAAmB;YACnB,2CAAmB;SACpB;QACD,WAAW,EAAE,CAAC,oCAAgB,CAAC;KAChC,CAAC;GACW,YAAY,CAYxB", "sourcesContent": ["import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';\r\nimport { ValidateAdminMiddleware } from 'src/shared/middlewares/validate-admin.middleware';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { IncomeController } from './controllers/income.controller';\r\nimport { DeleteIncomeService } from './services/delete-income.service';\r\nimport { IncomeDetailService } from './services/income-detail.service';\r\nimport { ListIncomesService } from './services/list-incomes.service';\r\nimport { SaveIncomeService } from './services/save-income.service';\r\nimport { UpdateIncomeService } from './services/update-income.service';\r\n\r\n@Module({\r\n  imports: [SharedModule],\r\n  providers: [\r\n    SaveIncomeService,\r\n    ListIncomesService,\r\n    IncomeDetailService,\r\n    UpdateIncomeService,\r\n    DeleteIncomeService,\r\n  ],\r\n  controllers: [IncomeController],\r\n})\r\nexport class IncomeModule {\r\n  configure(consumer: MiddlewareConsumer) {\r\n    consumer\r\n      .apply(ValidateAdminMiddleware)\r\n      .forRoutes(\r\n        { path: 'income', method: RequestMethod.POST },\r\n        { path: 'income', method: RequestMethod.GET },\r\n        { path: 'income/one', method: RequestMethod.GET },\r\n        { path: 'income/:incomeId', method: RequestMethod.PATCH },\r\n        { path: 'income', method: RequestMethod.DELETE },\r\n      );\r\n  }\r\n}\r\n"]}