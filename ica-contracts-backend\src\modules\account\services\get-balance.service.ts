import { Inject, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BalanceCelcoinService } from 'src/apis/celcoin/services/balance-celcoin.service';
import { VirtualBalanceService } from 'src/apis/icainvest-credit/services/virtual-balance.service';
import { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { GetBalanceDto } from '../dto/get-balance.dto';

export class GetBalanceService {
  constructor(
    @Inject(BalanceCelcoinService)
    private balanceCelcoin: BalanceCelcoinService,
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @Inject(VirtualBalanceService)
    private virtualBalanceService: VirtualBalanceService,
    @Inject(GetAccountLimitService)
    private getLimit: GetAccountLimitService,
  ) {}

  async perform(input: GetBalanceDto) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(input.userId),
        },
        {
          businessId: Equal(input.userId),
        },
        {
          id: Equal(input.userId),
        },
      ],
    });
    if (!account) {
      throw new NotFoundException('Usuário não encontrado.');
    }

    const virtualBalanceResponse =
      await this.virtualBalanceService.checkBalance({
        accountId: account.id,
      });

    const virtualBalanceAmount = virtualBalanceResponse.amount;

    const limit = await this.getLimit.execute(account.id);

    const amount = limit.realBalance + virtualBalanceAmount;
    return {
      amount,
      virtualBalance: virtualBalanceAmount,
      realAmount: limit.realBalance,
      dailyLimit: limit.dailyLimit,
    };
  }
}
