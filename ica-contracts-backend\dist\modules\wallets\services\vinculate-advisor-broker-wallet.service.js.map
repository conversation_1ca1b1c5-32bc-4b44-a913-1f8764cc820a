{"version": 3, "file": "vinculate-advisor-broker-wallet.service.js", "sourceRoot": "/", "sources": ["modules/wallets/services/vinculate-advisor-broker-wallet.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,qHAA0G;AAC1G,yGAA+F;AAC/F,iEAAwD;AACxD,qCAA4C;AAKrC,IAAM,mCAAmC,GAAzC,MAAM,mCAAmC;IAC9C,YAEU,QAAwC,EAExC,QAA6C;QAF7C,aAAQ,GAAR,QAAQ,CAAgC;QAExC,aAAQ,GAAR,QAAQ,CAAqC;IACpD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAqC;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1C,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,OAAO;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;QAGvE,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAG/D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC5C,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;aACzB;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAErE,IAAI,CAAC,SAAS,EAAE,CAAC;YAEf,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;YAErE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC1C,KAAK,EAAE;oBACL,IAAI,EAAE;wBACJ,IAAI,EAAE,sBAAS,CAAC,MAAM;qBACvB;iBACF;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,IAAI;oBAChB,KAAK,EAAE;wBACL,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,GAAG,EAAE,IAAI;qBACV;oBACD,QAAQ,EAAE;wBACR,EAAE,EAAE,IAAI;wBACR,WAAW,EAAE,IAAI;wBACjB,IAAI,EAAE,IAAI;qBACX;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACrC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACnC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,yBAAyB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAChE,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;gBACjG,CAAC;gBACD,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACpB,OAAO,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC,UAAU,KAAK,MAAM,CAAC,QAAQ,CAAC,WAAW,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;gBACrH,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,IAAI,CAAC,QAAQ,6CAA6C,CAAC,CAAC;QACpI,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzC,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,MAAM;iBACvB;aACF;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,CAAC,CAAC;QAEpE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzC,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,SAAS,CAAC;aAChC;SACF,CAAC,CAAC;QAEH,IAAI,MAAM;YACR,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,CAAC,CAAC;QAE1E,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,QAAQ,EAAE,IAAI,CAAC,SAAS;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;CACF,CAAA;AAnHY,kFAAmC;8CAAnC,mCAAmC;IAD/C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,yCAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADxB,oBAAU;QAEV,oBAAU;GALnB,mCAAmC,CAmH/C", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { VinculateAdvisorBrokerWalletDto } from '../dto/vinculate-advisor-broker-wallet.dto';\r\n\r\n@Injectable()\r\nexport class VinculateAdvisorBrokerWalletService {\r\n  constructor(\r\n    @InjectRepository(WalletsViewsEntity)\r\n    private walletDb: Repository<WalletsViewsEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private perfilDb: Repository<OwnerRoleRelationEntity>,\r\n  ) {}\r\n\r\n  async perform(data: VinculateAdvisorBrokerWalletDto) {\r\n    const advisor = await this.perfilDb.findOne({\r\n      where: {\r\n        id: Equal(data.advisorId),\r\n        role: {\r\n          name: RolesEnum.ADVISOR,\r\n        },\r\n      },\r\n      relations: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (!advisor) throw new BadRequestException('Assessor nao encontrado');\r\n\r\n    // Log para debug - verificar o ID que está sendo buscado\r\n    console.log('🔍 Searching for broker with ID:', data.brokerId);\r\n\r\n    // Primeiro, vamos verificar se existe algum registro com esse ID\r\n    const anyRecord = await this.perfilDb.findOne({\r\n      where: {\r\n        id: Equal(data.brokerId),\r\n      },\r\n      relations: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    console.log('📋 Any record with this ID:', anyRecord ? 'YES' : 'NO');\r\n\r\n    if (!anyRecord) {\r\n      // Se não encontrou nenhum registro, vamos mostrar os IDs disponíveis\r\n      console.log('❌ ID not found. Let me show you available broker IDs:');\r\n\r\n      const allBrokers = await this.perfilDb.find({\r\n        where: {\r\n          role: {\r\n            name: RolesEnum.BROKER,\r\n          },\r\n        },\r\n        relations: {\r\n          role: true,\r\n          owner: true,\r\n          business: true,\r\n        },\r\n        select: {\r\n          id: true,\r\n          ownerId: true,\r\n          businessId: true,\r\n          owner: {\r\n            id: true,\r\n            name: true,\r\n            cpf: true,\r\n          },\r\n          business: {\r\n            id: true,\r\n            companyName: true,\r\n            cnpj: true,\r\n          },\r\n        },\r\n      });\r\n\r\n      console.log('📊 Available brokers:');\r\n      allBrokers.forEach((broker, index) => {\r\n        console.log(`  ${index + 1}. Broker Relation ID: ${broker.id}`);\r\n        if (broker.owner) {\r\n          console.log(`     - Owner ID: ${broker.ownerId} (${broker.owner.name} - ${broker.owner.cpf})`);\r\n        }\r\n        if (broker.business) {\r\n          console.log(`     - Business ID: ${broker.businessId} (${broker.business.companyName} - ${broker.business.cnpj})`);\r\n        }\r\n        console.log('');\r\n      });\r\n\r\n      throw new BadRequestException(`Broker não encontrado. ID fornecido: ${data.brokerId}. Use um dos IDs de relação listados acima.`);\r\n    }\r\n\r\n    const broker = await this.perfilDb.findOne({\r\n      where: {\r\n        id: Equal(data.brokerId),\r\n        role: {\r\n          name: RolesEnum.BROKER,\r\n        },\r\n      },\r\n      relations: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (!broker) throw new BadRequestException('Broker nao encontrado');\r\n\r\n    const wallet = await this.walletDb.findOne({\r\n      where: {\r\n        bottomId: Equal(data.advisorId),\r\n      },\r\n    });\r\n\r\n    if (wallet)\r\n      throw new BadRequestException('Assessor ja está vinculado a um Broker');\r\n\r\n    const createVinc = this.walletDb.create({\r\n      upperId: data.brokerId,\r\n      bottomId: data.advisorId,\r\n    });\r\n\r\n    await this.walletDb.save(createVinc);\r\n  }\r\n}\r\n"]}