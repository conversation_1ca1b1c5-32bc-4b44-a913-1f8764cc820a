{"version": 3, "file": "vinculate-advisor-broker-wallet.service.js", "sourceRoot": "/", "sources": ["modules/wallets/services/vinculate-advisor-broker-wallet.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,qHAA0G;AAC1G,yGAA+F;AAC/F,iEAAwD;AACxD,qCAA4C;AAKrC,IAAM,mCAAmC,GAAzC,MAAM,mCAAmC;IAC9C,YAEU,QAAwC,EAExC,QAA6C;QAF7C,aAAQ,GAAR,QAAQ,CAAgC;QAExC,aAAQ,GAAR,QAAQ,CAAqC;IACpD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAqC;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1C,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,OAAO;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;QAEvE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzC,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,MAAM;iBACvB;aACF;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,CAAC,CAAC;QAEpE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzC,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,SAAS,CAAC;aAChC;SACF,CAAC,CAAC;QAEH,IAAI,MAAM;YACR,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,CAAC,CAAC;QAE1E,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,QAAQ,EAAE,IAAI,CAAC,SAAS;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;CACF,CAAA;AArDY,kFAAmC;8CAAnC,mCAAmC;IAD/C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,yCAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADxB,oBAAU;QAEV,oBAAU;GALnB,mCAAmC,CAqD/C", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { VinculateAdvisorBrokerWalletDto } from '../dto/vinculate-advisor-broker-wallet.dto';\r\n\r\n@Injectable()\r\nexport class VinculateAdvisorBrokerWalletService {\r\n  constructor(\r\n    @InjectRepository(WalletsViewsEntity)\r\n    private walletDb: Repository<WalletsViewsEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private perfilDb: Repository<OwnerRoleRelationEntity>,\r\n  ) {}\r\n\r\n  async perform(data: VinculateAdvisorBrokerWalletDto) {\r\n    const advisor = await this.perfilDb.findOne({\r\n      where: {\r\n        id: Equal(data.advisorId),\r\n        role: {\r\n          name: RolesEnum.ADVISOR,\r\n        },\r\n      },\r\n      relations: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (!advisor) throw new BadRequestException('Assessor nao encontrado');\r\n\r\n    const broker = await this.perfilDb.findOne({\r\n      where: {\r\n        id: Equal(data.brokerId),\r\n        role: {\r\n          name: RolesEnum.BROKER,\r\n        },\r\n      },\r\n      relations: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (!broker) throw new BadRequestException('Broker nao encontrado');\r\n\r\n    const wallet = await this.walletDb.findOne({\r\n      where: {\r\n        bottomId: Equal(data.advisorId),\r\n      },\r\n    });\r\n\r\n    if (wallet)\r\n      throw new BadRequestException('Assessor ja está vinculado a um Broker');\r\n\r\n    const createVinc = this.walletDb.create({\r\n      upperId: data.brokerId,\r\n      bottomId: data.advisorId,\r\n    });\r\n\r\n    await this.walletDb.save(createVinc);\r\n  }\r\n}\r\n"]}