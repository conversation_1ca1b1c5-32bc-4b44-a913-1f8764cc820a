{"version": 3, "file": "vinculate-advisor-broker-wallet.service.js", "sourceRoot": "/", "sources": ["modules/wallets/services/vinculate-advisor-broker-wallet.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,qHAA0G;AAC1G,yGAA+F;AAC/F,iEAAwD;AACxD,qCAA4C;AAKrC,IAAM,mCAAmC,GAAzC,MAAM,mCAAmC;IAC9C,YAEU,QAAwC,EAExC,QAA6C;QAF7C,aAAQ,GAAR,QAAQ,CAAgC;QAExC,aAAQ,GAAR,QAAQ,CAAqC;IACpD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAqC;QACjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1C,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAS,CAAC,OAAO;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;QAGvE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAClD,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;aACzB;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE5E,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBACxC,EAAE,EAAE,eAAe,CAAC,EAAE;gBACtB,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,QAAQ,EAAE,eAAe,CAAC,IAAI,EAAE,IAAI;gBACpC,OAAO,EAAE,eAAe,CAAC,OAAO;gBAChC,UAAU,EAAE,eAAe,CAAC,UAAU;aACvC,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,4BAAmB,CAAC,8BAA8B,IAAI,CAAC,QAAQ,+BAA+B,CAAC,CAAC;QAC5G,CAAC;QAED,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5C,MAAM,IAAI,4BAAmB,CAAC,MAAM,IAAI,CAAC,QAAQ,4CAA4C,eAAe,CAAC,IAAI,EAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;QACtI,CAAC;QAGD,MAAM,MAAM,GAAG,eAAe,CAAC;QAE/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzC,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,SAAS,CAAC;aAChC;SACF,CAAC,CAAC;QAEH,IAAI,MAAM;YACR,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,CAAC,CAAC;QAE1E,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,QAAQ,EAAE,IAAI,CAAC,SAAS;SACzB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;CACF,CAAA;AA1EY,kFAAmC;8CAAnC,mCAAmC;IAD/C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,yCAAkB,CAAC,CAAA;IAEpC,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCADxB,oBAAU;QAEV,oBAAU;GALnB,mCAAmC,CA0E/C", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { VinculateAdvisorBrokerWalletDto } from '../dto/vinculate-advisor-broker-wallet.dto';\r\n\r\n@Injectable()\r\nexport class VinculateAdvisorBrokerWalletService {\r\n  constructor(\r\n    @InjectRepository(WalletsViewsEntity)\r\n    private walletDb: Repository<WalletsViewsEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private perfilDb: Repository<OwnerRoleRelationEntity>,\r\n  ) {}\r\n\r\n  async perform(data: VinculateAdvisorBrokerWalletDto) {\r\n    const advisor = await this.perfilDb.findOne({\r\n      where: {\r\n        id: Equal(data.advisorId),\r\n        role: {\r\n          name: RolesEnum.ADVISOR,\r\n        },\r\n      },\r\n      relations: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    if (!advisor) throw new BadRequestException('Assessor nao encontrado');\r\n\r\n    // Primeiro, vamos tentar encontrar o registro sem filtro de role\r\n    const anyBrokerRecord = await this.perfilDb.findOne({\r\n      where: {\r\n        id: Equal(data.brokerId),\r\n      },\r\n      relations: {\r\n        role: true,\r\n      },\r\n    });\r\n\r\n    console.log('🔍 Debug - Searching for broker ID:', data.brokerId);\r\n    console.log('📋 Debug - Any record found:', anyBrokerRecord ? 'YES' : 'NO');\r\n\r\n    if (anyBrokerRecord) {\r\n      console.log('📊 Debug - Record details:', {\r\n        id: anyBrokerRecord.id,\r\n        roleId: anyBrokerRecord.roleId,\r\n        roleName: anyBrokerRecord.role?.name,\r\n        ownerId: anyBrokerRecord.ownerId,\r\n        businessId: anyBrokerRecord.businessId\r\n      });\r\n    }\r\n\r\n    // Vamos verificar se o registro existe e tem role de broker\r\n    if (!anyBrokerRecord) {\r\n      throw new BadRequestException(`Broker não encontrado. ID: ${data.brokerId} não existe na base de dados.`);\r\n    }\r\n\r\n    if (anyBrokerRecord.role?.name !== 'broker') {\r\n      throw new BadRequestException(`ID ${data.brokerId} existe mas não é um broker. Role atual: ${anyBrokerRecord.role?.name || 'N/A'}`);\r\n    }\r\n\r\n    // Se chegou até aqui, o registro existe e é um broker, então usar ele\r\n    const broker = anyBrokerRecord;\r\n\r\n    const wallet = await this.walletDb.findOne({\r\n      where: {\r\n        bottomId: Equal(data.advisorId),\r\n      },\r\n    });\r\n\r\n    if (wallet)\r\n      throw new BadRequestException('Assessor ja está vinculado a um Broker');\r\n\r\n    const createVinc = this.walletDb.create({\r\n      upperId: data.brokerId,\r\n      bottomId: data.advisorId,\r\n    });\r\n\r\n    await this.walletDb.save(createVinc);\r\n  }\r\n}\r\n"]}