import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';

import { IUpdateManyAccountLimitDto } from '../dto/update-many.dto';

@Injectable()
export class UpdateManyLimitService {
  constructor(
    @InjectRepository(AccountEntity)
    private readonly accountRepo: Repository<AccountEntity>,
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
  ) {}

  async execute(input: IUpdateManyAccountLimitDto[]) {
    if (input.length === 0)
      throw new BadRequestException('Pelo menos um item deve ser passado');

    const promises = input.map(async (acc) => {
      const account = await this.accountRepo.findOne({
        where: [
          { business: { cnpj: acc.document } },
          { owner: { cpf: acc.document } },
        ],
      });

      if (!account) throw new NotFoundException('Conta não encontrada!');

      const accountLimits = await this.accountLimitRepo.findOneBy({
        accountId: account.id,
      });

      if (!accountLimits)
        Object.assign(accountLimits, {} as AccountTransferLimitEntity);

      if (acc.dailyLimit) accountLimits.dailyLimit = acc.dailyLimit;
      if (acc.dailyNightLimit)
        accountLimits.dailyNightLimit = acc.dailyNightLimit;
      if (acc.monthlyLimit) accountLimits.monthlyLimit = acc.monthlyLimit;
      if (acc.generalLimit)
        accountLimits.generalTransferLimit = acc.generalLimit;

      accountLimits.defaultValues = JSON.stringify({
        dailyLimit: acc.dailyLimit,
        dailyNightLimit: acc.dailyNightLimit,
        monthlyLimit: acc.monthlyLimit,
      });

      return this.accountLimitRepo.save(accountLimits);
    });

    await Promise.allSettled(promises);
  }
}
