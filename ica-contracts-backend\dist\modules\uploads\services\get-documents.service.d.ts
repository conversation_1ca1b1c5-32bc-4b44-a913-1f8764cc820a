import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { UploadsEntity } from 'src/shared/database/typeorm/entities/uploads.entity';
import { Repository } from 'typeorm';
import { GetDocumentsDto } from '../dto/get-documents.dto';
export declare class GetDocumentsService {
    private accountDb;
    private uploadsDb;
    constructor(accountDb: Repository<AccountEntity>, uploadsDb: Repository<UploadsEntity>);
    perform(data: GetDocumentsDto): Promise<UploadsEntity[]>;
}
