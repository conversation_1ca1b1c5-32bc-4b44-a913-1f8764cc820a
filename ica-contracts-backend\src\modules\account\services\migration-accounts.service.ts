import { BadRequestException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ClientOldEntity,
  OldStatusEnum,
} from 'src/shared/database/typeorm/entities-old/account-old.entity';
import { AddressOldEntity } from 'src/shared/database/typeorm/entities-old/address-old.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Repository } from 'typeorm';

import { CreateAccountPFService } from './create-account-pf.service';

export class MigrationAccountsService {
  constructor(
    @InjectRepository(ClientOldEntity, 'old')
    private clientOld: Repository<ClientOldEntity>,
    @InjectRepository(AddressOldEntity, 'old')
    private addressOld: Repository<AddressOldEntity>,
    @InjectRepository(OwnerEntity)
    private ownerDb: Repository<OwnerEntity>,
    @Inject(CreateAccountPFService)
    private createPF: CreateAccountPFService,
  ) {}

  // @Cron(CronExpression.EVERY_10_SECONDS)
  async perform() {
    const old = await this.clientOld.findOne({
      where: {
        migrationCompleted: false,
        status: OldStatusEnum.ACTIVE,
        type: 'pf',
      },
    });
    if (!old) {
      return;
    }
    const address = await this.addressOld.findOne({
      where: {
        clientId: old.id,
      },
    });
    const splitDate = old.birthDate.split('-');
    const formatDate = new Date(
      Number(splitDate[2]),
      Number(splitDate[1]) - 1,
      Number(splitDate[0]),
    );
    const owner = await this.createPF
      .perform({
        birthDate: formatDate,
        role: RolesEnum.INVESTOR,
        socialName: old.socialName,
        cpf: old.document,
        fullName: old.name,
        isTaxable: true,
        email: old.email,
        pep: false,
        motherName: old.motherName,
        phoneNumber: old.phoneNumber,
        address: {
          cep: address.zip_code,
          city: address.city,
          neighborhood: address.neighborhood,
          number: address.number,
          state: address.state,
          street: address.adress_line,
          complement: address.complement,
        },
      })
      .catch(async (error) => {
        await this.clientOld.update(old.id, {
          migrationCompleted: true,
          errorMigration: JSON.stringify(error.response || error),
        });
        throw new BadRequestException(error);
      });
    await this.ownerDb.update(owner.id, {
      startContract: old.start_contract,
      endContract: old.end_contract,
      totalInvested: old.total_invested,
      yield: old.profitability,
    });
    await this.clientOld.update(old.id, {
      migrationCompleted: true,
    });
  }
}
