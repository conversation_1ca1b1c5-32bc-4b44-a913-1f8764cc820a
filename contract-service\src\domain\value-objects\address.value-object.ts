import { type Either, right } from '../shared'

export class Address {
  private constructor(
    public readonly street: string,
    public readonly number: string,
    public readonly city: string,
    public readonly state: string,
    public readonly postalCode: string,
    public readonly neighborhood: string,
    public readonly complement?: string
  ) {}

  static create(
    street: string,
    number: string,
    city: string,
    state: string,
    postalCode: string,
    neighborhood: string,
    complement?: string
  ): Either<Error, Address> {
    // if (!street || !number || !city || !state || !postalCode) {
    //   return left(new Error('Missing required address fields'))
    // }

    // if (!postalCode.match(/^\d{8}$/)) {
    //   return left(new Error('Invalid postal code'))
    // }

    return right(
      new Address(
        street,
        number,
        city,
        state,
        postalCode,
        neighborhood,
        complement
      )
    )
  }

  get full(): string {
    const parts = [
      `${this.street}, ${this.number}`,
      `${this.city} - ${this.state}`,
      this.postalCode,
    ]

    if (this.complement) {
      parts.splice(1, 0, this.complement)
    }

    return parts.filter(Boolean).join(', ')
  }
}
