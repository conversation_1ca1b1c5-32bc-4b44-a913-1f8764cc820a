import { BilletEntity } from 'src/shared/database/typeorm/entities/billet.entity';
import { Repository } from 'typeorm';
import { IChargeCreateCelcoinWebhookDto } from '../dto/charge-create-celcoin-webhook.dto';
export declare class ChargeCreateCelcoinWebhookService {
    private readonly billetEntityRepository;
    constructor(billetEntityRepository: Repository<BilletEntity>);
    perform(input: IChargeCreateCelcoinWebhookDto): Promise<void>;
}
