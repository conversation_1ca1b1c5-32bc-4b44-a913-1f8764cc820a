import type { Either } from '../shared'

export interface IStorageGateway {
  /**
   * Faz o upload de um arquivo para o storage.
   * @param path Caminho do arquivo no storage (ex: 'users/123/profile.jpg')
   * @param content Conteúdo do arquivo como um Buffer ou Stream
   * @param contentType Tipo de conteúdo (MIME type)
   * @returns URL pública do arquivo (se aplicável)
   */
  uploadFile(
    path: string,
    content: Buffer | ReadableStream,
    contentType?: string
  ): Promise<Either<Error, string>>

  /**
   * Baixa um arquivo do storage.
   * @param path Caminho do arquivo no storage
   * @returns Buffer com o conteúdo do arquivo
   */
  downloadFile(path: string): Promise<Buffer>

  /**
   * Deleta um arquivo do storage.
   * @param path Caminho do arquivo no storage
   * @returns Sucesso ou falha
   */
  deleteFile(path: string): Promise<boolean>
}
