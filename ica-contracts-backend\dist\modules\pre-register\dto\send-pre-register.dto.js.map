{"version": 3, "file": "send-pre-register.dto.js", "sourceRoot": "/", "sources": ["modules/pre-register/dto/send-pre-register.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAA+D;AAC/D,yDAAyC;AACzC,qDAcyB;AACzB,+EAAqE;AAErE,MAAM,UAAU;CAoDf;AAjDC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yCACG;AAId;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;4CACM;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;yCACG;AAId;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;gDACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;gDACa;AAIrB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,8BAAY,GAAE;8BACA,IAAI;iDAAC;AAIpB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,8BAAY,GAAE;8BACF,IAAI;+CAAC;AAIlB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,8BAAY,GAAE;8BACF,IAAI;+CAAC;AAIlB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,2BAAS,GAAE;;6CACO;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;;iEACqB;AAItC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;;kEACsB;AAIvC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACU;AAGvB,MAAM,KAAK;CASV;AALC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,iCAAK,GAAE;IACP,IAAA,0BAAQ,GAAE;;kCACC;AAIZ;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;mCACE;AAGf,MAAa,kBAAkB;CAgC9B;AAhCD,gDAgCC;AA7BC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;qDACS;AAIlB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,yBAAO,GAAE;;iDACI;AAId;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,uCAAW,GAAE;;oDACG;AAIjB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,EAAC,mCAAe,CAAC;;uDACK;AAM7B;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC;8BACX,KAAK;iDAAC;AAMb;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACX,UAAU;sDAAC;AAIvB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mDACK", "sourcesContent": ["import { <PERSON><PERSON><PERSON>, IsCP<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'brazilian-class-validator';\r\nimport { Type } from 'class-transformer';\r\nimport {\r\n  IsBoolean,\r\n  IsDateString,\r\n  IsDefined,\r\n  IsEmail,\r\n  IsEnum,\r\n  IsInt,\r\n  IsNumber,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  IsUUID,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { AccountTypeEnum } from 'src/shared/enums/account-type.enum';\r\n\r\nclass Investment {\r\n  @IsDefined()\r\n  @IsNumber()\r\n  value: number;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  term: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  modality: string;\r\n\r\n  @IsDefined()\r\n  @IsNumber()\r\n  yield: number;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  purchaseWith: string;\r\n\r\n  @IsOptional()\r\n  @IsInt()\r\n  amountQuotes: number;\r\n\r\n  @IsDefined()\r\n  @IsDateString()\r\n  startContract: Date;\r\n\r\n  @IsDefined()\r\n  @IsDateString()\r\n  endContract: Date;\r\n\r\n  @IsDefined()\r\n  @IsDateString()\r\n  gracePeriod: Date;\r\n\r\n  @IsDefined()\r\n  @IsBoolean()\r\n  debenture: boolean;\r\n\r\n  @IsOptional()\r\n  @IsNumberString()\r\n  brokerParticipationPercentage: string;\r\n\r\n  @IsOptional()\r\n  @IsNumberString()\r\n  advisorParticipationPercentage: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  observations: string;\r\n}\r\n\r\nclass Owner {\r\n  @IsDefined()\r\n  @IsCPF()\r\n  @IsString()\r\n  cpf: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n}\r\n\r\nexport class SendPreRegisterDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  adviserId: string;\r\n\r\n  @IsDefined()\r\n  @IsEmail()\r\n  email: string;\r\n\r\n  @IsDefined()\r\n  @IsCPFOrCNPJ()\r\n  document: string;\r\n\r\n  @IsDefined()\r\n  @IsEnum(AccountTypeEnum)\r\n  accountType: AccountTypeEnum;\r\n\r\n  @IsOptional()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Owner)\r\n  owner: Owner;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Investment)\r\n  investment: Investment;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  signIca: string;\r\n}\r\n"]}