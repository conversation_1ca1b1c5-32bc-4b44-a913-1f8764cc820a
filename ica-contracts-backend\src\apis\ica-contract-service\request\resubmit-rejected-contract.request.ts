import {
  ExistingContractInvestmentRequest,
  AdvisorAssignmentRequest,
  BankAccountRequest,
  IndividualRequest,
  CompanyRequest,
  FileRequest,
} from './create-existing-contract.request';
import { PersonType, ContractType } from './create-new-contract.request';

export interface ResubmitRejectedContractRequest {
  personType: PersonType;
  contractType: ContractType;
  investment: ExistingContractInvestmentRequest;
  bankAccount?: BankAccountRequest;
  individual?: IndividualRequest;
  company?: CompanyRequest;
  proofOfPayment?: FileRequest;
  personalDocument?: FileRequest;
  contract?: FileRequest;
  proofOfResidence?: FileRequest;
  cardCnpj?: FileRequest;
}
