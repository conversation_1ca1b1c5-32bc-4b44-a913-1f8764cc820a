import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { endOfDay, startOfDay } from 'date-fns';
import { Movement } from 'src/apis/celcoin/responses/consult-extract-celcoin.response';
import { ConsultAccountExtractCelcoinService } from 'src/apis/celcoin/services/consult-extract-celcoin.service';
import { ManualRemoveService } from 'src/apis/icainvest-credit/services/manual-remove.service';
import { VirtualStatementService } from 'src/apis/icainvest-credit/services/virtual-statement.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { RechargeEntity } from 'src/shared/database/typeorm/entities/recharge.entity';
import { ServiceFeeEntity } from 'src/shared/database/typeorm/entities/service-fee.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Between, Equal, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { IConsultAccountExtract } from '../dto/consult-account-extract.dto';
import { TransactionType } from '../response/account-statement-response';

@Injectable()
export class AccountStatementService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(RechargeEntity)
    private rechargeDb: Repository<RechargeEntity>,
    @InjectRepository(TransactionEntity)
    private transactionRepository: Repository<TransactionEntity>,
    @InjectRepository(ServiceFeeEntity)
    private feeDb: Repository<ServiceFeeEntity>,
    @Inject(ConsultAccountExtractCelcoinService)
    private apiCelcoin: ConsultAccountExtractCelcoinService,
    @Inject(VirtualStatementService)
    private virtualStatementService: VirtualStatementService,
    @Inject(ManualRemoveService)
    private manualRemoveService: ManualRemoveService,
  ) {}

  async execute(data: IConsultAccountExtract, id: string): Promise<any> {
    if (!data.DateTo || !data.DateFrom) {
      const today = new Date();
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 6);

      data.DateTo = data.DateTo || today.toISOString();
      data.DateFrom = data.DateFrom || sevenDaysAgo.toISOString();
    }

    const dateTo = new Date(data.DateTo);
    const dateFrom = new Date(data.DateFrom);
    const diffInDays = Math.ceil(
      (dateTo.getTime() - dateFrom.getTime()) / (1000 * 60 * 60 * 24),
    );

    if (diffInDays > 7) {
      const adjustedDateFrom = new Date(dateTo);
      adjustedDateFrom.setDate(dateTo.getDate() - 6);
      data.DateFrom = adjustedDateFrom.toISOString();
    }

    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta nao encontrada');

    const page = data.Page || 1;
    const limit = data.LimitPerPage || String(50);

    const document =
      account.type === 'physical' ? account.owner.cpf : account.business.cnpj;

    const celcoinResponse = await this.apiCelcoin.consultExtract({
      Account: account.number,
      DocumentNumber: document,
      DateFrom: data.DateFrom,
      DateTo: data.DateTo,
      LimitPerPage: limit,
      Page: String(page),
    });

    const shouldFilter = !!data.transactionType;

    let { movements } = celcoinResponse.body;

    if (shouldFilter && Array.isArray(movements)) {
      const movs = movements as Movement[];
      movements = movs.filter((movement) =>
        movement.movementType.includes(data.transactionType as string),
      );
    }

    const virtualTransactions = await this.virtualStatementService.getStatement(
      {
        accountId: account.id,
        dateFrom: data.DateFrom,
        dateTo: data.DateTo,
      },
    );

    const recharges = await this.rechargeDb.find({
      where: {
        accountId: account.id,
        createdAt: Between(startOfDay(data.DateFrom), endOfDay(data.DateTo)),
      },
    });

    const rechargesIds = recharges.map((r) => r.externalId);

    const allTransactions = await Promise.all(
      movements.map(async (mov: Movement) => {
        const savedTransaction = await this.transactionRepository.findOne({
          where: [{ code: mov.clientCode }, { code: mov.id }],
        });

        if (rechargesIds.includes(mov.id)) {
          return {
            ...mov,
            description: 'P2P',
            id: uuidv4(),
            creditParty: {
              account: '*************',
              branch: '0001',
              taxId: '**************',
              name: 'ICA INVEST',
              accountType: 'TRAN',
              bank: '********',
            },
            debitParty: {
              account: account.number,
              branch: '0001',
              taxId:
                account.type === 'business'
                  ? account.business.cnpj
                  : account.owner.cpf,
              name:
                account.type === 'business'
                  ? account.business.companyName
                  : account.owner.name,
              accountType: 'TRAN',
              bank: '********',
            },
          };
        }

        if (!savedTransaction) {
          const parties = {
            creditParty:
              mov.description === 'RECARGA' &&
              mov.movementType === 'TEFTRANSFERIN'
                ? {
                    account: account.number,
                    branch: '0001',
                    taxId:
                      account.type === 'business'
                        ? account.business.cnpj
                        : account.owner.cpf,
                    name:
                      account.type === 'business'
                        ? account.business.companyName
                        : account.owner.name,
                    accountType: 'TRAN',
                    bank: '********',
                  }
                : {
                    account: '*************',
                    branch: '0001',
                    taxId: '**************',
                    name: 'ICABANK',
                    accountType: 'TRAN',
                    bank: '********',
                  },
            debitParty:
              mov.description === 'RECARGA' &&
              mov.movementType === 'TEFTRANSFERIN'
                ? {
                    account: '*************',
                    branch: '0001',
                    taxId: '**************',
                    name: 'ICABANK',
                    accountType: 'TRAN',
                    bank: '********',
                  }
                : {
                    account: account.number,
                    branch: '0001',
                    taxId:
                      account.type === 'business'
                        ? account.business.cnpj
                        : account.owner.cpf,
                    name:
                      account.type === 'business'
                        ? account.business.companyName
                        : account.owner.name,
                    accountType: 'TRAN',
                    bank: '********',
                  },
          };

          return {
            ...parties,
            id: mov.id,
            clientCode: mov.clientCode,
            type: TransactionType[mov.balanceType],
            description:
              mov.description === 'RECARGA' ? 'P2P' : mov.description,
            createDate: mov.createDate,
            status: mov.status,
            amount: mov.amount,
            movementType: mov.movementType,
          };
        }

        const transactionsParties = JSON.parse(
          savedTransaction.transferMetadata,
        );

        if (
          transactionsParties.debitParty?.taxId === '***********' ||
          transactionsParties.debitParty?.taxId === '**************'
        ) {
          return null;
        }

        return {
          ...mov,
          description: mov.description === 'RECARGA' ? 'P2P' : mov.description,
          creditParty: {
            account: transactionsParties?.creditParty?.account,
            branch: transactionsParties?.creditParty?.branch,
            taxId: transactionsParties?.creditParty?.taxId,
            name: transactionsParties?.creditParty?.name,
            accountType: transactionsParties?.creditParty?.accountType,
            bank: transactionsParties?.creditParty?.bank,
          },
          debitParty: {
            account: transactionsParties?.debitParty?.account,
            branch: transactionsParties?.debitParty?.branch,
            taxId: transactionsParties?.debitParty?.taxId,
            name: transactionsParties?.debitParty?.name,
            accountType: transactionsParties?.debitParty?.accountType,
            bank: transactionsParties?.debitParty?.bank ?? '',
          },
        };
      }),
    );

    const transactionsRemanining = [];
    if (virtualTransactions.length > 0) {
      const remaining = virtualTransactions.filter(
        (v) => v.type === 'CREDIT' || v.type === 'DEBIT',
      );

      if (remaining.length > 0) {
        remaining.forEach((tr) => {
          const vt = {
            id: tr.id,
            amount: Number(tr.amount),
            clientCode: account.id,
            description: 'P2P',
            createDate: `${tr.date}`,
            status: 'Saldo Liberado',
            movementType:
              tr.type === 'CREDIT' || tr.type === 'RECHARGE'
                ? 'PIXPAYMENTIN'
                : 'PIXPAYMENTOUT',
            creditParty:
              tr.type === 'DEBIT'
                ? {
                    account: '*************',
                    branch: '0001',
                    taxId: '**************',
                    name: 'ICABANK',
                    accountType: 'TRAN',
                    bank: '********',
                  }
                : {
                    account: account.number,
                    branch: '0001',
                    taxId:
                      account.type === 'business'
                        ? account.business.cnpj
                        : account.owner.cpf,
                    name:
                      account.type === 'business'
                        ? account.business.companyName
                        : account.owner.name,
                    accountType: 'TRAN',
                    bank: '********',
                  },
            debitParty:
              tr.type === 'CREDIT' || tr.type === 'RECHARGE'
                ? {
                    account: '*************',
                    branch: '0001',
                    taxId: '**************',
                    name: 'ICABANK',
                    accountType: 'TRAN',
                    bank: '********',
                  }
                : {
                    account: account.number,
                    branch: '0001',
                    taxId:
                      account.type === 'business'
                        ? account.business.cnpj
                        : account.owner.cpf,
                    name:
                      account.type === 'business'
                        ? account.business.companyName
                        : account.owner.name,
                    accountType: 'TRAN',
                    bank: '********',
                  },
          };
          transactionsRemanining.push(vt);
        });
      }
    }

    const transactions = allTransactions.filter(
      (t) => t !== null && t.movementType !== 'TEFTRANSFERIN',
    );

    const serviceFee = await this.feeDb.find({
      where: {
        account: {
          id: account.id,
        },
        created_at: Between(startOfDay(data.DateFrom), endOfDay(data.DateTo)),
      },
    });

    const fee = allTransactions
      .map((fee) => {
        if (!fee) return null;

        const service = serviceFee.find(
          (sf) => sf.external_id && fee.id === sf.external_id,
        );

        if (service) {
          return {
            id: fee.id,
            clientCode: fee.clientCode,
            createDate: fee.createDate,
            amount: fee.amount,
            status: 'Saldo Liberado',
            balanceType: 'DEBIT',
            movementType: 'SERVICEFEE',
            debitParty: {
              account: fee.debitParty.account,
              branch: fee.debitParty.branch,
              taxId: fee.debitParty.taxId,
              name: fee.debitParty.name,
              accountType: fee.debitParty.accountType,
              bank: fee.debitParty.bank,
            },
          };
        }
        return null;
      })
      .filter((transaction) => transaction !== null);

    const transactionBeforeManual = [
      ...fee,
      ...transactions.filter(
        (t) => !fee.some((f) => f.clientCode === t.clientCode),
      ),
      ...transactionsRemanining.filter(
        (t) => !fee.some((f) => f.clientCode === t.clientCode),
      ),
    ];

    const manualRemove = await this.manualRemoveService.getStatement(
      account.id,
    );

    const transactionMovements = transactionBeforeManual
      .map((t) => {
        if (t !== null && manualRemove.some((m) => m.transactionId === t.id)) {
          return null;
        }
        return t;
      })
      .filter((t) => t !== null && t.movementType !== 'TEFTRANSFERIN');

    return {
      movements: transactionMovements,
      totalItems: transactionMovements.length,
      totalPages: celcoinResponse.totalPages,
      currentPage: +page,
    };
  }

  private async delay(ms: number): Promise<void> {
    // eslint-disable-next-line no-promise-executor-return
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
