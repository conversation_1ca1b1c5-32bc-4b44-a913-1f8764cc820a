import { Modu<PERSON> } from '@nestjs/common';

import { AddManySignatoriesService } from './services/add-many-signatories.service';
import { AddSignatoryService } from './services/add-signatory.service';
import { AuthIntegrationService } from './services/auth.service';
import { FindSignatarieService } from './services/find-signatarie.service';
import { ListAllDocumentsService } from './services/list-all-documents.service';
import { DocumentInfoService } from './services/list-document.service';
import { SendDocumentService } from './services/send-document.service';
import { SendNotificationService } from './services/send-notification.service';

@Module({
  imports: [],
  providers: [
    AuthIntegrationService,
    SendDocumentService,
    AddSignatoryService,
    AddManySignatoriesService,
    ListAllDocumentsService,
    DocumentInfoService,
    FindSignatarieService,
    SendNotificationService,
  ],
  exports: [
    AuthIntegrationService,
    SendDocumentService,
    AddSignatoryService,
    AddManySignatoriesService,
    ListAllDocumentsService,
    DocumentInfoService,
    FindSignatarieService,
    SendNotificationService,
  ],
})
export class AssinaturaRbmModule {}
