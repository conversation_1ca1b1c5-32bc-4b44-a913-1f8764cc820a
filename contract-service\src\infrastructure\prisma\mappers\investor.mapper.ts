import { Investor } from "@/domain/entities/user";
import { type Either, left, right } from "@/domain/shared";
import { BankAccount } from "@/domain/value-objects";
import type { Prisma } from "@prisma/client";
import { BusinessMapper } from "./company.mapper";
import { IndividualMapper } from "./individual.mapper";

export type OwnerRoleRelationWithFullParty =
  Prisma.owner_role_relationGetPayload<{
    include: {
      pre_register: true;
      owner: {
        include: { address: true; account: true };
      };
      business: {
        include: {
          address: true;
          account: true;
          owner_business_relation: {
            include: {
              owner: {
                include: { address: true };
              };
            };
          };
        };
      };
    };
  }>;

export class InvestorMapper {
  static toDomain(
    raw: OwnerRoleRelationWithFullParty
  ): Either<Error, Investor> {
    if (!raw) return left(new Error("Relação de investidor não encontrada"));

    let bankAccountResult = BankAccount.create("", "", "", "");

    if (raw.pre_register && raw.pre_register.length > 0) {
      const data = raw.pre_register[0];
      bankAccountResult = BankAccount.create(
        data.bank ?? "",
        data.agency ?? "",
        data.account ?? "",
        data.pix ?? undefined
      );
    }

    if (bankAccountResult.isLeft()) {
      return left(bankAccountResult.value);
    }

    // Pessoa física
    if (raw.owner) {
      const result = IndividualMapper.toDomain(raw.owner);
      if (result.isLeft()) return left(result.value);

      return right(
        Investor.createFromIndividual(
          result.value,
          bankAccountResult.value,
          raw.id
        )
      );
    }

    // Pessoa jurídica
    if (raw.business) {
      const result = BusinessMapper.toDomain(raw.business);
      if (result.isLeft()) return left(result.value);
      return right(
        Investor.createFromCompany(
          result.value,
          bankAccountResult.value,
          raw.id
        )
      );
    }

    return left(
      new Error("Investidor deve ter proprietário ou empresa definida")
    );
  }

  /**
   * Mapeia dados do investidor para criação de contratos
   * Prioriza dados bancários do usuário (tabela account) e usa pre_register como fallback
   */
  static toDomainForContract(
    raw: OwnerRoleRelationWithFullParty,
    pixFromDto?: string
  ): Either<Error, Investor> {
    if (!raw) return left(new Error("Relação de investidor não encontrada"));

    let bankAccountResult = BankAccount.create("", "", "", "");

    // Priorizar dados bancários do usuário (tabela account)
    if (raw.owner && raw.owner.account && raw.owner.account.length > 0) {
      const account = raw.owner.account[0];
      bankAccountResult = BankAccount.create(
        account.bank ?? "",
        account.branch ?? "",
        account.number ?? "",
        pixFromDto // Usar PIX do DTO se fornecido
      );
    } else if (
      raw.business &&
      raw.business.account &&
      raw.business.account.length > 0
    ) {
      const account = raw.business.account[0];
      bankAccountResult = BankAccount.create(
        account.bank ?? "",
        account.branch ?? "",
        account.number ?? "",
        pixFromDto // Usar PIX do DTO se fornecido
      );
    } else if (raw.pre_register && raw.pre_register.length > 0) {
      // Fallback para pre_register se não houver dados na tabela account
      const data = raw.pre_register[0];
      bankAccountResult = BankAccount.create(
        data.bank ?? "",
        data.agency ?? "",
        data.account ?? "",
        data.pix ?? pixFromDto // Usar PIX do pre_register ou do DTO como fallback
      );
    } else {
      // Se não houver dados bancários, usar PIX do DTO se fornecido
      bankAccountResult = BankAccount.create("", "", "", pixFromDto);
    }

    if (bankAccountResult.isLeft()) {
      return left(bankAccountResult.value);
    }

    // Pessoa física
    if (raw.owner) {
      const result = IndividualMapper.toDomain(raw.owner);
      if (result.isLeft()) return left(result.value);

      return right(
        Investor.createFromIndividual(
          result.value,
          bankAccountResult.value,
          raw.id
        )
      );
    }

    // Pessoa jurídica
    if (raw.business) {
      const result = BusinessMapper.toDomain(raw.business);
      if (result.isLeft()) return left(result.value);
      return right(
        Investor.createFromCompany(
          result.value,
          bankAccountResult.value,
          raw.id
        )
      );
    }

    return left(
      new Error("Investidor deve ter proprietário ou empresa definida")
    );
  }
}
