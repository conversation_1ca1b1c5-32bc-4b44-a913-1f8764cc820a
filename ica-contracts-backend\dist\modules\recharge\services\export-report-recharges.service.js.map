{"version": 3, "file": "export-report-recharges.service.js", "sourceRoot": "/", "sources": ["modules/recharge/services/export-report-recharges.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAqD;AACrD,6CAAmD;AACnD,iCAA0B;AAC1B,uCAAkC;AAClC,6FAAoF;AACpF,qCAAqC;AAmBrC,IAAa,4BAA4B,GAAzC,MAAa,4BAA4B;IACvC,YAEU,SAAoC;QAApC,cAAS,GAAT,SAAS,CAA2B;IAC3C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAA8B;QAC1C,MAAM,KAAK,GAAiB,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,MAAM,eAAK;aAC1B,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,mBAAmB,EAAE;YACvD,MAAM,EAAE;gBACN,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB;YACD,OAAO,EAAE;gBACP,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;aACjC;SACF,CAAC;aACD,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAsB,CAAC;aACnD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,MAAM,IAAI,4BAAmB,CAAC,kBAAkB,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEL,MAAM,QAAQ,GAAG,SAAS;aACvB,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;aACjD,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;YACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC3C,SAAS,EAAE;oBACT,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,IAAI;iBACf;gBACD,KAAK,EAAE;oBACL,EAAE,EAAE,QAAQ,CAAC,SAAS;iBACvB;aACF,CAAC,CAAC;YACH,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;gBACzD,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI;gBACrD,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC1C,IAAI,EAAE,IAAA,iBAAM,EAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC;aAC1C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEL,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,iBAAiB,CAAC,IAAkB;QAClC,MAAM,IAAI,GAAG,IAAI;aACd,GAAG,CACF,CAAC,IAAI,EAAE,EAAE,CAAC;;gBAEF,IAAI,CAAC,IAAI;gBACT,IAAI,CAAC,QAAQ;kBACX,IAAI,CAAC,MAAM;gBACb,IAAI,CAAC,IAAI;;OAElB,CACA;aACA,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBA6BK,IAAI;;;;;OAKb,CAAC;IACN,CAAC;CACF,CAAA;AAjGY,oEAA4B;uCAA5B,4BAA4B;IAEpC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCACb,oBAAU;GAHpB,4BAA4B,CAiGxC", "sourcesContent": ["import { BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport axios from 'axios';\r\nimport { format } from 'date-fns';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ExportReportRechargesDto } from '../dto/export-report-recharges.dto';\r\n\r\ninterface IRechargeApi {\r\n  id: string;\r\n  accountId: string;\r\n  amount: string;\r\n  type: string;\r\n  date: Date;\r\n}\r\n\r\ninterface ITableData {\r\n  name: string;\r\n  amount: string;\r\n  date: string;\r\n  document: string;\r\n}\r\n\r\nexport class ExportReportRechargesService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n  ) {}\r\n\r\n  async perform(data: ExportReportRechargesDto) {\r\n    const table: ITableData[] = [];\r\n    const recharges = await axios\r\n      .get(`${process.env.TRANSACTIONS_URL}/transactions/all`, {\r\n        params: {\r\n          datefrom: data.from,\r\n          dateto: data.to,\r\n        },\r\n        headers: {\r\n          'x-api-key': process.env.API_KEY,\r\n        },\r\n      })\r\n      .then((response) => response.data as IRechargeApi[])\r\n      .catch((err) => {\r\n        throw new BadRequestException('Erro na consulta', err.message);\r\n      });\r\n\r\n    const promises = recharges\r\n      .filter((recharge) => recharge.type === data.type)\r\n      .map(async (recharge) => {\r\n        const account = await this.accountDb.findOne({\r\n          relations: {\r\n            owner: true,\r\n            business: true,\r\n          },\r\n          where: {\r\n            id: recharge.accountId,\r\n          },\r\n        });\r\n        table.push({\r\n          name: account.owner?.name || account.business.companyName,\r\n          document: account.owner?.cpf || account.business.cnpj,\r\n          amount: Number(recharge.amount).toFixed(2),\r\n          date: format(recharge.date, 'dd/MM/yyyy'),\r\n        });\r\n      });\r\n\r\n    await Promise.all(promises);\r\n    return this.generateHtmlTable(table);\r\n  }\r\n\r\n  generateHtmlTable(data: ITableData[]): string {\r\n    const rows = data\r\n      .map(\r\n        (item) => `\r\n        <tr>\r\n          <td>${item.name}</td>\r\n          <td>${item.document}</td>\r\n          <td>R$${item.amount}</td>\r\n          <td>${item.date}</td>\r\n        </tr>\r\n      `,\r\n      )\r\n      .join('');\r\n\r\n    return `\r\n        <html>\r\n        <head>\r\n          <style>\r\n            table {\r\n              width: 100%;\r\n              border-collapse: collapse;\r\n            }\r\n            th, td {\r\n              border: 1px solid black;\r\n              padding: 8px;\r\n              text-align: left;\r\n            }\r\n            th {\r\n              background-color: #f2f2f2;\r\n            }\r\n          </style>\r\n        </head>\r\n        <body>\r\n          <table>\r\n            <thead>\r\n              <tr>\r\n                <th>Nome</th>\r\n                <th>Documento</th>\r\n                <th>Valor</th>\r\n                <th>Data</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              ${rows}\r\n            </tbody>\r\n          </table>\r\n        </body>\r\n        </html>\r\n      `;\r\n  }\r\n}\r\n"]}