"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedQueryHelper = void 0;
class PaginatedQueryHelper {
    static getPaginationParams(query) {
        const page = +query.page;
        const limit = +query.limit;
        const skip = (page - 1) * limit;
        return { page, limit, skip };
    }
    static getDateRangeParams(query) {
        const start = query.dateFrom
            ? new Date(query.dateFrom)
            : new Date(new Date().getFullYear(), new Date().getMonth(), 1);
        const end = query.dateTo
            ? new Date(query.dateTo)
            : new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);
        return { start, end };
    }
    static createPaginatedResponse(data, total, page, limit) {
        return {
            data,
            meta: {
                total,
                page,
                lastPage: Math.ceil(total / limit),
                perPage: limit,
            },
        };
    }
}
exports.PaginatedQueryHelper = PaginatedQueryHelper;
//# sourceMappingURL=pagination-query.js.map