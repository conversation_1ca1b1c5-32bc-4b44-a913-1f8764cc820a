"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const report_entity_1 = require("../../../shared/database/typeorm/entities/report.entity");
const typeorm_2 = require("typeorm");
const report_registry_service_1 = require("./report-registry.service");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
let ReportService = class ReportService {
    constructor(registry, reportRepository, ownerRoleRelationRepository) {
        this.registry = registry;
        this.reportRepository = reportRepository;
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
    }
    async generateReport(type, data, roleId, userId) {
        const profile = await this.ownerRoleRelationRepository.findOne({
            where: [
                { id: roleId, owner: { id: userId } },
                { id: roleId, business: { id: userId } },
            ],
        });
        if (!profile) {
            throw new common_1.ForbiddenException('Não possui permissão');
        }
        const strategy = this.registry.resolve(type);
        const report = await strategy.generateReport(data);
        if (report) {
            await this.reportRepository.save({
                fileId: report.id,
                type,
                ownerRoleRelationId: profile.id,
            });
        }
        return report;
    }
};
exports.ReportService = ReportService;
exports.ReportService = ReportService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(report_entity_1.ReportEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [report_registry_service_1.ReportRegistryService,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ReportService);
//# sourceMappingURL=report.service.js.map