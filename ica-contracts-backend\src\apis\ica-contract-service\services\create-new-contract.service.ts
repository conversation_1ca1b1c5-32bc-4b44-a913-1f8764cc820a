import axios, { AxiosResponse, isAxiosError } from 'axios';
import { Injectable, HttpException, BadRequestException } from '@nestjs/common';
import { CreateNewContractDTO } from '../request/create-new-contract.request';

@Injectable()
export class CreateNewContractService {
  async perform(paylaod: CreateNewContractDTO): Promise<{
    id: string;
    status: string;
  }> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/new`;

      const {data} = await axios.post<
        CreateNewContractDTO,
        AxiosResponse<{ id: string; status: string }>
      >(url, paylaod);

      console.log('response',data );
      

      return data;
    } catch (error) {
      if (isAxiosError(error)) {
        if (error.status > 399 && error.status < 499) {
          throw new BadRequestException(error.response.data);
        }

        throw new HttpException(
          error.response.data.message,
          error.response.data.statusCode,
        ); 
      }
      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }
}
