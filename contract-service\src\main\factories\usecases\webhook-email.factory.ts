import { WebhookEmailUseCase } from '@/contexts/income-report/application/usecases/webhook-email.usecase';
import { PrismaEmailRepository } from '@/contexts/income-report/infrastructure/prisma/repositories/email.repository';

export function makeWebhookEmailUseCase() {
  const emailRepository = new PrismaEmailRepository();
  const useCase = new WebhookEmailUseCase(emailRepository);
  return useCase;
}
