{"version": 3, "file": "query.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-all-advisors-with-active-contracts/query.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAMyB;AAEzB,yDAAyC;AACzC,oFAAuE;AAMvE,MAAa,yCAAyC;IAAtD;QAME,SAAI,GAAY,CAAC,CAAC;QAMlB,UAAK,GAAY,EAAE,CAAC;IActB,CAAC;CAAA;AA1BD,8FA0BC;AApBC;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;uEACU;AAMlB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;wEACY;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2EACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yEACK;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qCAAgB,CAAC;;+EACM", "sourcesContent": ["import {\r\n  IsEnum,\r\n  IsInt,\r\n  IsOptional,\r\n  IsPositive,\r\n  IsString\r\n} from 'class-validator';\r\n\r\nimport { Type } from 'class-transformer';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\nimport {\r\n  IDateRangeQuery,\r\n  IPaginationQuery,\r\n} from '../../helpers/pagination-query';\r\n\r\nexport class GetAllAdvisorsWithActiveContractsQueryDto\r\n  implements IPaginationQuery, IDateRangeQuery {\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  page?: number = 1;\r\n\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  limit?: number = 10;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  dateFrom?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  dateTo?: string;\r\n\r\n  @IsOptional()\r\n  @IsEnum(ContractTypeEnum)\r\n  contractType: ContractTypeEnum;\r\n\r\n}\r\n"]}