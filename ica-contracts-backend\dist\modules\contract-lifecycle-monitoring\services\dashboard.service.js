"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const contract_events_enum_1 = require("../../../shared/enums/contract-events.enum");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
let DashboardService = class DashboardService {
    constructor(ownerRoleRelationRepository, contractRepository) {
        this.ownerRoleRelationRepository = ownerRoleRelationRepository;
        this.contractRepository = contractRepository;
    }
    async perform(userId) {
        await this.verifyAdminPermission(userId);
        const expiringContractsCount = await this.getExpiringContractsCount(30);
        const redemptionRequestedCount = await this.getContractsCountByLastEventStatus(contract_events_enum_1.ContractEventStatus.REDEMPTION_REQUESTED);
        const renewalRequestedCount = await this.getContractsCountByLastEventStatus(contract_events_enum_1.ContractEventStatus.RENEWAL_REQUESTED);
        const contractAddendumRequestedCount = await this.getContractsCountByLastEventStatus(contract_events_enum_1.ContractEventStatus.CONTRACT_ADDENDUM_REQUESTED);
        const contractAddendumConfirmedCount = await this.getContractsCountByLastEventStatus(contract_events_enum_1.ContractEventStatus.CONTRACT_ADDENDUM_CONFIRMED);
        const renewalConfirmedCount = await this.getContractsCountByLastEventStatus(contract_events_enum_1.ContractEventStatus.RENEWAL_CONFIRMED);
        const redemptionConfirmedCount = await this.getContractsCountByLastEventStatus(contract_events_enum_1.ContractEventStatus.REDEMPTION_CONFIRMED);
        return {
            expiringContractsCount,
            redemptionRequestedCount,
            renewalRequestedCount,
            contractAddendumRequestedCount,
            contractAddendumConfirmedCount,
            renewalConfirmedCount,
            redemptionConfirmedCount,
        };
    }
    async verifyAdminPermission(userId) {
        const isAdmin = await this.ownerRoleRelationRepository.findOne({
            relations: { role: true },
            where: [
                {
                    ownerId: userId,
                    role: {
                        name: (0, typeorm_2.In)([
                            roles_enum_1.RolesEnum.ADMIN,
                            roles_enum_1.RolesEnum.RETENTION,
                            roles_enum_1.RolesEnum.SUPERADMIN,
                        ]),
                    },
                },
                {
                    businessId: userId,
                    role: {
                        name: (0, typeorm_2.In)([
                            roles_enum_1.RolesEnum.ADMIN,
                            roles_enum_1.RolesEnum.RETENTION,
                            roles_enum_1.RolesEnum.SUPERADMIN,
                        ]),
                    },
                },
            ],
        });
        if (!isAdmin) {
            throw new common_1.ForbiddenException('Forbidden resource');
        }
    }
    async getExpiringContractsCount(days) {
        const dateThreshold = this.calculateDateThreshold(days);
        return this.contractRepository
            .createQueryBuilder('contract')
            .leftJoin('contract.investor', 'investor')
            .leftJoin('investor.role', 'role')
            .leftJoin('contract.events', 'event')
            .where('contract.endContract <= :dateThreshold', { dateThreshold })
            .andWhere('role.name = :roleName', { roleName: roles_enum_1.RolesEnum.INVESTOR })
            .andWhere('event.id IS NULL')
            .getCount();
    }
    async getContractsCountByLastEventStatus(status) {
        return this.contractRepository
            .createQueryBuilder('contract')
            .leftJoin('contract.investor', 'investor')
            .leftJoin('investor.role', 'role')
            .leftJoin('contract.events', 'event')
            .where('role.name = :roleName', { roleName: roles_enum_1.RolesEnum.INVESTOR })
            .andWhere((qb) => {
            const subQuery = qb
                .subQuery()
                .select('event_sub.id')
                .from('contract_event', 'event_sub')
                .where('event_sub.contract_id = contract.id')
                .orderBy('event_sub.eventDate', 'DESC')
                .addOrderBy('event_sub.createdAt', 'DESC')
                .limit(1)
                .getQuery();
            return `event.id = ${subQuery}`;
        })
            .andWhere('event.status = :status', { status })
            .getCount();
    }
    calculateDateThreshold(days) {
        const date = new Date();
        date.setDate(date.getDate() + days);
        return date;
    }
};
exports.DashboardService = DashboardService;
exports.DashboardService = DashboardService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], DashboardService);
//# sourceMappingURL=dashboard.service.js.map