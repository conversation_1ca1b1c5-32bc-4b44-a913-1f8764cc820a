import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { FavoriteTedEntity } from 'src/shared/database/typeorm/entities/favorite-ted.entity';
import { Repository } from 'typeorm';
import { DeleteFavoriteTedContactDto } from '../dto/delete-favorite-ted-contact.dto';
export declare class DeleteFavoriteTedContactService {
    private accountRepo;
    private favoriteTedRepo;
    constructor(accountRepo: Repository<AccountEntity>, favoriteTedRepo: Repository<FavoriteTedEntity>);
    execute(data: DeleteFavoriteTedContactDto, id: string): Promise<void>;
}
