import { TedCelcoinService } from 'src/apis/celcoin/services/ted-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
export declare class GetRecentTedsService {
    private transactionRepo;
    private accountRepo;
    private celcoinService;
    constructor(transactionRepo: Repository<TransactionEntity>, accountRepo: Repository<AccountEntity>, celcoinService: TedCelcoinService);
    perform(ownerId: string): Promise<{
        transactions: {
            id: string;
            amount: number;
            clientCode: string;
            description: string;
            creditParty: {
                bank: string;
                account: string;
                branch: string;
                taxId: string;
                name: string;
                accountType: string;
                personType: string;
            };
            debitParty: {
                account: string;
                branch: string;
                taxId: string;
                name: string;
                accountType: string;
                personType: string;
                bank: string;
            };
        }[];
        transactionsCount: number;
    }>;
}
