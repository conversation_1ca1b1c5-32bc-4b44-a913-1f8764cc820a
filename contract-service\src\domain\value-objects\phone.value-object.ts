import { type Either, left, right } from '../shared'

export class Phone {
  private constructor(private readonly _value: string) {}

  static create(phone: string): Either<Error, Phone> {
    const formattedPhone = phone.replace(/[^\d]/g, '')

    // if (!/^\d{10,13}$/.test(formattedPhone)) {
    //   return left(new Error(`Invalid phone number format ${formattedPhone}`))
    // }
    return right(new Phone(formattedPhone))
  }

  get value(): string {
    return this._value
  }

  get formatted(): string {
    return this._value.replace(/(\d{2})(\d{4,5})(\d{4})/, '($1) $2-$3')
  }
}
