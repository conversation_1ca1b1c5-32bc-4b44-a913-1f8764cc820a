{"version": 3, "file": "validate-adviser-owner.middleware.js", "sourceRoot": "/", "sources": ["shared/middlewares/validate-adviser-owner.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAgF;AAChF,6CAAmD;AAEnD,+CAAsC;AACtC,qCAA4C;AAE5C,wGAAkG;AAI3F,IAAM,8BAA8B,GAApC,MAAM,8BAA8B;IACzC,YAEU,QAA6C;QAA7C,aAAQ,GAAR,QAAQ,CAAqC;IACpD,CAAC;IACJ,KAAK,CAAC,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACvD,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,IAAA,qBAAM,EAAC,KAAK,CAAe,CAAC;QAE9C,MAAM,SAAS,GACb,GAAG,CAAC,IAAI,CAAC,SAAS;YAClB,GAAG,CAAC,IAAI,CAAC,MAAM;YACf,GAAG,CAAC,KAAK,CAAC,SAAS;YACnB,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;QAEnB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACxC,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI;aACX;YACD,KAAK,EAAE;gBACL;oBACE,OAAO,EAAE,IAAA,eAAK,EAAC,SAAS,CAAC,EAAE,CAAC;oBAC5B,EAAE,EAAE,IAAA,eAAK,EAAC,SAAS,CAAC;oBACpB,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;iBAChD;gBACD;oBACE,UAAU,EAAE,IAAA,eAAK,EAAC,SAAS,CAAC,EAAE,CAAC;oBAC/B,EAAE,EAAE,IAAA,eAAK,EAAC,SAAS,CAAC;oBACpB,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;iBAChD;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,2BAAkB,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;CACF,CAAA;AAvCY,wEAA8B;yCAA9B,8BAA8B;IAD1C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCACxB,oBAAU;GAHnB,8BAA8B,CAuC1C", "sourcesContent": ["import { ForbiddenException, Injectable, NestMiddleware } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { NextFunction, Request, Response } from 'express';\r\nimport { decode } from 'jsonwebtoken';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { OwnerRoleRelationEntity } from '../database/typeorm/entities/owner-role-relation.entity';\r\nimport { IDecodeJwt } from '../interfaces/decode-jwt.interface';\r\n\r\n@Injectable()\r\nexport class ValidateAdviserOwnerMiddleware implements NestMiddleware {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private perfilDb: Repository<OwnerRoleRelationEntity>,\r\n  ) {}\r\n  async use(req: Request, res: Response, next: NextFunction) {\r\n    const token = req.headers.authorization.split(' ')[1];\r\n    const decodeJwt = decode(token) as IDecodeJwt;\r\n\r\n    const adviserId =\r\n      req.body.adviserId ||\r\n      req.body.roleId ||\r\n      req.query.adviserId ||\r\n      req.query.roleId;\r\n\r\n    const owner = await this.perfilDb.findOne({\r\n      relations: {\r\n        role: true,\r\n      },\r\n      where: [\r\n        {\r\n          ownerId: Equal(decodeJwt.id),\r\n          id: Equal(adviserId),\r\n          role: [{ name: 'broker' }, { name: 'advisor' }],\r\n        },\r\n        {\r\n          businessId: Equal(decodeJwt.id),\r\n          id: Equal(adviserId),\r\n          role: [{ name: 'broker' }, { name: 'advisor' }],\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (!owner) {\r\n      throw new ForbiddenException();\r\n    }\r\n\r\n    next();\r\n  }\r\n}\r\n"]}