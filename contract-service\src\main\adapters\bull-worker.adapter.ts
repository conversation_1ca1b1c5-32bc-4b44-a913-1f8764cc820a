import type { <PERSON>WorkerGateway } from '@/domain/gateways'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import { type Job, Worker, type WorkerOptions } from 'bullmq'
import { PinoLoggerAdapter } from './pino-logger.adapter'

export class BullWorkerAdapter implements IWorkerGateway {
  private worker: Worker
  private handlers: Map<string, (data: any) => Promise<any>>
  private logger: LoggerGateway = new PinoLoggerAdapter()

  constructor(queueName: string, options?: WorkerOptions) {
    this.handlers = new Map()
    this.worker = new Worker(
      queueName,
      async (job: Job) => {
        const handler = this.handlers.get(job.name)
        if (handler) {
          return await handler(job.data)
        }
        throw new Error(`No handler found for job: ${job.name}`)
      },
      options
    )

    this.worker.on('failed', (job, err) => {
      this.logger.error(`Job ${job?.id} failed with error: ${err.message}`)
    })
  }

  async process<T, R>(
    jobName: string,
    handler: (data: T) => Promise<R>
  ): Promise<void> {
    this.handlers.set(jobName, handler)
  }

  async close(): Promise<void> {
    await this.worker.close()
  }
}
