import { ClaimCelcoinService } from 'src/apis/celcoin/services/claim-celcoin.service';
import { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';
import { Repository } from 'typeorm';
import { ClaimConfirmDto } from '../dto/claim-confirm.dto';
export declare class ClaimConfirmPixKeyService {
    private pixKeyRepository;
    private pixCelcoinService;
    constructor(pixKeyRepository: Repository<PixKeyEntity>, pixCelcoinService: ClaimCelcoinService);
    execute(input: ClaimConfirmDto, ownerId: string): Promise<any>;
}
