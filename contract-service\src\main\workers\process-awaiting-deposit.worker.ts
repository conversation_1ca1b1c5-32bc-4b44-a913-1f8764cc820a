import type { LoggerGateway } from '@/domain/gateways/logger.gateway';
import { QueueNames } from '../config/queue-names';
import { createWorkerAdapter } from '../factories/bullmq';
import { ContractStatus } from '@/domain/entities/contracts';
import { makeProcessAwaitingDepositUseCase } from '../factories/usecases/process-awaiting-deposit.factory';

export const setupProcessAwaitingDepositWorker = async (
  logger: LoggerGateway
) => {
  const worker = createWorkerAdapter(QueueNames.PROCESS_AWAITING_DEPOSIT);
  const processAwaitingDepositUseCase = makeProcessAwaitingDepositUseCase();

  await worker.process<{ id: string; status: ContractStatus }, any>(
    QueueNames.PROCESS_AWAITING_DEPOSIT,
    processAwaitingDepositUseCase.execute.bind(processAwaitingDepositUseCase)
  );

  logger.info('Awaiting deposit worker initialized');

  return worker;
};
