{"version": 3, "file": "internal-transaction.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/internal-transaction.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAMwB;AACxB,6CAAmD;AACnD,iCAAiC;AACjC,oHAAyG;AACzG,+GAA+G;AAC/G,6FAAoF;AACpF,qGAA4F;AAC5F,yGAA8F;AAC9F,qCAA4C;AAC5C,+BAA0B;AAMnB,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IACrC,YAEU,SAAoC,EAEpC,qBAAoD,EAEpD,cAA4C,EAE5C,QAAgC;QANhC,cAAS,GAAT,SAAS,CAA2B;QAEpC,0BAAqB,GAArB,qBAAqB,CAA+B;QAEpD,mBAAc,GAAd,cAAc,CAA8B;QAE5C,aAAQ,GAAR,QAAQ,CAAwB;IACvC,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,IAA4B,EAC5B,EAAU;QAEV,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAEnE,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAC5C,IAAI,CAAC,mBAAmB,EACxB,OAAO,CAAC,mBAAmB,CAC5B,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,MAAM,IAAI,8BAAqB,CAAC,+BAA+B,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAExD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAClD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QAErE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,WAAW,CACtE,KAAK,EAAE,OAAO,EAAE,EAAE;YAChB,MAAM,IAAI,GAAG,IAAA,SAAE,GAAE,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC;gBAClE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC3B,WAAW,EAAE;oBACX,OAAO,EAAE,IAAI,CAAC,aAAa;iBAC5B;gBACD,eAAe,EAAE,IAAI;gBACrB,UAAU,EAAE;oBACV,OAAO,EAAE,OAAO,CAAC,MAAM;iBACxB;gBACD,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG;gBACvB,GAAG,MAAM;gBACT,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC;YAEF,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,sCAAiB,EAAE;gBACvD,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,OAAO;gBACP,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,KAAK,EAAE,IAAI,CAAC,MAAM;gBAClB,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO;gBAC1C,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI;gBACpC,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;gBACxC,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK;gBACzC,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI;gBACpC,IAAI,EAAE,4DAA2B,CAAC,yBAAyB;gBAC3D,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;aACnD,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACvD,OAAO,WAAW,CAAC;QACrB,CAAC,CACF,CAAC;QAEF,OAAO;YACL,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,WAAW,EAAE;gBACX,OAAO,EAAE,WAAW,CAAC,cAAc;gBACnC,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,QAAQ,EAAE,WAAW,CAAC,eAAe;aACtC;YACD,UAAU,EAAE;gBACV,OAAO,EAAE,OAAO,CAAC,MAAM;gBACvB,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;gBACxD,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI;aACrD;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAnGY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;IAEpC,WAAA,IAAA,eAAM,EAAC,kDAAsB,CAAC,CAAA;qCALZ,oBAAU;QAEE,oBAAU;QAEjB,8DAA4B;QAElC,kDAAsB;GAT/B,0BAA0B,CAmGtC", "sourcesContent": ["import {\r\n  BadRequestException,\r\n  Inject,\r\n  Injectable,\r\n  NotFoundException,\r\n  UnauthorizedException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport * as bcrypt from 'bcrypt';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\n\r\nimport { InternalTransactionDto } from '../dto/internal-transaction.dto';\r\nimport { IInternalTransactionResponse } from '../response/internal-transaction.response';\r\n\r\n@Injectable()\r\nexport class InternalTransactionService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepository: Repository<TransactionEntity>,\r\n    @Inject(PixTransactionCelcoinService)\r\n    private celcoinService: PixTransactionCelcoinService,\r\n    @Inject(GetAccountLimitService)\r\n    private getLimit: GetAccountLimitService,\r\n  ) {}\r\n\r\n  async perform(\r\n    data: InternalTransactionDto,\r\n    id: string,\r\n  ): Promise<IInternalTransactionResponse> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    const isPasswordCorrect = await bcrypt.compare(\r\n      data.transactionPassword,\r\n      account.transactionPassword,\r\n    );\r\n\r\n    if (!isPasswordCorrect) {\r\n      throw new UnauthorizedException('Senha de transação incorreta.');\r\n    }\r\n\r\n    const balance = await this.getLimit.execute(account.id);\r\n\r\n    if (Number(data.amount) > Number(balance.dailyLimit))\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n\r\n    const transaction = await this.transactionRepository.manager.transaction(\r\n      async (manager) => {\r\n        const uuid = v4();\r\n        const { body: result } = await this.celcoinService.internalTransfer({\r\n          amount: Number(data.amount),\r\n          creditParty: {\r\n            account: data.creditAccount,\r\n          },\r\n          clientRequestId: uuid,\r\n          debitParty: {\r\n            account: account.number,\r\n          },\r\n          description: data.description,\r\n        });\r\n\r\n        const transferMetadata = {\r\n          ...result,\r\n          creditParty: result.creditParty,\r\n          debitParty: result.debitParty,\r\n        };\r\n\r\n        const newTransaction = manager.create(TransactionEntity, {\r\n          accountId: account.id,\r\n          account,\r\n          code: uuid,\r\n          description: data.description,\r\n          value: data.amount,\r\n          destinyAccount: result.creditParty.account,\r\n          destinyBank: result.creditParty.bank,\r\n          destinyBranch: result.creditParty.branch,\r\n          destinyDocument: result.creditParty.taxId,\r\n          destinyName: result.creditParty.name,\r\n          type: TransactionMovementTypeEnum.INTERNAL_TRANSFER_CASHOUT,\r\n          endToEndId: result.endToEndId,\r\n          transferMetadata: JSON.stringify(transferMetadata),\r\n        });\r\n        const transaction = await manager.save(newTransaction);\r\n        return transaction;\r\n      },\r\n    );\r\n\r\n    return {\r\n      id: transaction.id,\r\n      creditParty: {\r\n        account: transaction.destinyAccount,\r\n        name: transaction.destinyName,\r\n        document: transaction.destinyDocument,\r\n      },\r\n      debitParty: {\r\n        account: account.number,\r\n        name: account.owner.name || account.business.fantasyName,\r\n        document: account.owner.cpf || account.business.cnpj,\r\n      },\r\n    };\r\n  }\r\n}\r\n"]}