import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { addHours, format } from 'date-fns';
import { AccountCelcoinService } from 'src/apis/celcoin/services/account-celcoin.service';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { AddressEntity } from 'src/shared/database/typeorm/entities/address.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { RoleEntity } from 'src/shared/database/typeorm/entities/role.entity';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { generatePassword } from 'src/shared/functions/generate-password';
import { Repository, Equal } from 'typeorm';
import { v4 } from 'uuid';

import { CreateAccountDto } from '../dto/create-account.dto';

@Injectable()
export class CreateAccountPFService {
  constructor(
    @InjectRepository(OwnerEntity)
    private ownerDb: Repository<OwnerEntity>,
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(AddressEntity)
    private addressDb: Repository<AddressEntity>,
    @InjectRepository(AccountTransferLimitEntity)
    private accountLimitRepo: Repository<AccountTransferLimitEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private ownerRoleDb: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(RoleEntity)
    private roleDb: Repository<RoleEntity>,
    @Inject(AccountCelcoinService)
    private apiCelcoin: AccountCelcoinService,
  ) {}
  async perform(data: CreateAccountDto) {
    const password = data.password?.trim() || generatePassword();
    const passwordEncrypted = await bcrypt.hash(password, 10);

    const createCelcoin = await this.apiCelcoin.createPF({
      accountOnboardingType: 'BANKACCOUNT',
      birthDate: format(addHours(data.birthDate, 3), 'dd-MM-yyyy'),
      socialName: data.socialName.trim().toUpperCase(),
      clientCode: v4(),
      phoneNumber: data.phoneNumber.trim(),
      documentNumber: data.cpf.trim(),
      email: data.email.toLowerCase(),
      fullName: data.fullName.trim().toUpperCase(),
      motherName: data.motherName.trim().toUpperCase(),
      isPoliticallyExposedPerson: data.pep,
      address: {
        neighborhood: data.address.neighborhood,
        city: data.address.city,
        postalCode: data.address.cep,
        addressComplement: data.address.complement,
        number: data.address.number,
        state: data.address.state.trim().toUpperCase(),
        street: data.address.street,
      },
    });

    let owner: OwnerEntity;

    owner = await this.ownerDb.findOne({ where: { cpf: Equal(data.cpf) } });

    if (!owner) {
      const createdOwner = this.ownerDb.create({
        name: data.fullName.trim().toUpperCase(),
        cpf: data.cpf.trim(),
        email: data.email.toLowerCase(),
        phone: data.phoneNumber.trim(),
        motherName: data.motherName.trim().toUpperCase(),
        nickname: data.socialName.trim().toUpperCase(),
        dtBirth: data.birthDate,
        pep: String(data.pep),
        password: passwordEncrypted,
      });

      owner = await this.ownerDb.save(createdOwner);
    }

    await this.addressDb.save({
      owner,
      cep: data.address.cep,
      city: data.address.city,
      complement: data.address.complement,
      neighborhood: data.address.neighborhood,
      number: data.address.number,
      state: data.address.state.trim().toUpperCase(),
      street: data.address.street,
    });

    let account: AccountEntity;

    account = await this.accountDb.findOne({
      where: { ownerId: Equal(owner.id) },
    });

    if (!account) {
      account = await this.accountDb.save({
        owner,
        status: AccountStatusEnum.KYC_PENDING,
        type: 'physical',
        externalId: createCelcoin.body.onBoardingId,
        isTaxable: data.isTaxable,
      });
    } else {
      await this.accountDb.update(account.id, {
        externalId: createCelcoin.body.onBoardingId,
        number: null,
        branch: null,
      });
    }

    await this.accountLimitRepo.save({ account });

    const getRole = await this.roleDb.findOne({
      where: {
        name: data.role,
      },
    });

    const createOwnerRole = this.ownerRoleDb.create({
      ownerId: owner.id,
      roleId: getRole.id,
    });

    await this.ownerRoleDb.save(createOwnerRole);

    return {
      id: account.id,
    };
  }
}
