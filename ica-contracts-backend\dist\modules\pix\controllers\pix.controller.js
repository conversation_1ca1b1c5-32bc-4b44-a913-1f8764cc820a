"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const claim_cancel_dto_1 = require("../dto/claim-cancel.dto");
const claim_confirm_dto_1 = require("../dto/claim-confirm.dto");
const claim_get_dto_1 = require("../dto/claim-get.dto");
const create_pix_key_dto_1 = require("../dto/create-pix-key.dto");
const create_pix_qrcode_dynamic_dto_1 = require("../dto/create-pix-qrcode-dynamic.dto");
const create_pix_qrcode_static_dto_1 = require("../dto/create-pix-qrcode-static.dto");
const delete_pix_key_dto_1 = require("../dto/delete-pix-key.dto");
const get_pix_key_external_dto_1 = require("../dto/get-pix-key-external.dto");
const get_pix_participants_dto_1 = require("../dto/get-pix-participants.dto");
const read_qrcode_pix_dto_1 = require("../dto/read-qrcode-pix.dto");
const request_claim_pix_key_dto_1 = require("../dto/request-claim-pix-key.dto");
const transaction_pix_key_dto_1 = require("../dto/transaction-pix-key.dto");
const claim_cancel_pix_key_service_1 = require("../service/claim-cancel-pix-key.service");
const claim_confim_pix_key_service_1 = require("../service/claim-confim-pix-key.service");
const claim_get_pix_key_service_1 = require("../service/claim-get-pix-key.service");
const create_pix_key_service_1 = require("../service/create-pix-key.service");
const create_pix_qrcode_dynamic_service_1 = require("../service/create-pix-qrcode-dynamic.service");
const create_pix_qrcode_static_service_1 = require("../service/create-pix-qrcode-static.service");
const delete_pix_key_service_1 = require("../service/delete-pix-key.service");
const get_pix_key_external_service_1 = require("../service/get-pix-key-external.service");
const get_pix_key_service_1 = require("../service/get-pix-key.service");
const get_pix_participants_service_1 = require("../service/get-pix-participants.service");
const read_qrcode_pix_service_1 = require("../service/read-qrcode-pix.service");
const request_claim_pix_key_service_1 = require("../service/request-claim-pix-key.service");
const transaction_pix_key_service_1 = require("../service/transaction-pix-key.service");
let PixController = class PixController {
    constructor(createPixKeyService, getPixKeyService, getPixKeyExternalService, deletePixKeyService, getPixParticipantsService, createQRCodeStaticService, createQRCodeDynamicService, requestClaimPixKeyService, claimConfirmPixKeyService, readQrCodeService, claimCancelPixKeyService, claimGetPixKeyService, transactionPixKeyService) {
        this.createPixKeyService = createPixKeyService;
        this.getPixKeyService = getPixKeyService;
        this.getPixKeyExternalService = getPixKeyExternalService;
        this.deletePixKeyService = deletePixKeyService;
        this.getPixParticipantsService = getPixParticipantsService;
        this.createQRCodeStaticService = createQRCodeStaticService;
        this.createQRCodeDynamicService = createQRCodeDynamicService;
        this.requestClaimPixKeyService = requestClaimPixKeyService;
        this.claimConfirmPixKeyService = claimConfirmPixKeyService;
        this.readQrCodeService = readQrCodeService;
        this.claimCancelPixKeyService = claimCancelPixKeyService;
        this.claimGetPixKeyService = claimGetPixKeyService;
        this.transactionPixKeyService = transactionPixKeyService;
    }
    async createPixKey(body, request) {
        const pixKey = await this.createPixKeyService.perform(body, request.user.id);
        return pixKey;
    }
    async getPixKeys(request) {
        const pixKeys = await this.getPixKeyService.perform(request.user.id);
        return pixKeys;
    }
    async getPixKeyExternal(request, query) {
        try {
            const data = await this.getPixKeyExternalService.perform(query, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async deletePixKey(query, request) {
        try {
            const data = await this.deletePixKeyService.perform(query, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getPixParticipants(request, query) {
        try {
            const data = await this.getPixParticipantsService.execute(query);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async createQRStatic(request, body) {
        try {
            const data = await this.createQRCodeStaticService.perform(body, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async createQRDynamic(request, body) {
        try {
            const data = await this.createQRCodeDynamicService.perform(body, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async requestClaim(request, body) {
        await this.requestClaimPixKeyService.perform(body, request.user.id);
        return { reason: 'Reivindicação realizada com sucesso.' };
    }
    async ConfirmClaim(request, input) {
        await this.claimConfirmPixKeyService.execute(input, request.user.id);
        return { reason: 'Reivindicação realizada com sucesso.' };
    }
    async readQRCode(body) {
        try {
            const data = await this.readQrCodeService.perform(body);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async CancelClaim(request, input) {
        const response = await this.claimCancelPixKeyService.execute(input, request.user.id);
        return response;
    }
    async getClaim(request, input) {
        const res = await this.claimGetPixKeyService.execute(input, request.user.id);
        return res;
    }
    async transactionPixKey(request, body, twoFactorToken) {
        try {
            const data = await this.transactionPixKeyService.perform(body, request.user.id, false, twoFactorToken);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
};
exports.PixController = PixController;
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_pix_key_dto_1.CreatePixDto, Object]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "createPixKey", null);
__decorate([
    (0, common_1.Get)('get-all'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "getPixKeys", null);
__decorate([
    (0, common_1.Get)('get-external'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, get_pix_key_external_dto_1.GetPixKeyExternalDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "getPixKeyExternal", null);
__decorate([
    (0, common_1.Delete)('key'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [delete_pix_key_dto_1.DeletePixKeyDto, Object]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "deletePixKey", null);
__decorate([
    (0, common_1.Get)('participants'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, get_pix_participants_dto_1.GetPixParticipantsDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "getPixParticipants", null);
__decorate([
    (0, common_1.Post)('qrcode/static'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_pix_qrcode_static_dto_1.CreatePixQRCodeStaticDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "createQRStatic", null);
__decorate([
    (0, common_1.Post)('qrcode/dynamic'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_pix_qrcode_dynamic_dto_1.CreatePixQRCodeDynamicDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "createQRDynamic", null);
__decorate([
    (0, common_1.Post)('request-claim'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.INVESTOR, roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, request_claim_pix_key_dto_1.RequestClaimPixKeyInputDTO]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "requestClaim", null);
__decorate([
    (0, common_1.Post)('confirm-claim'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.INVESTOR, roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, claim_confirm_dto_1.ClaimConfirmDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "ConfirmClaim", null);
__decorate([
    (0, common_1.Post)('qrcode/read'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [read_qrcode_pix_dto_1.ReadQRCodePixDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "readQRCode", null);
__decorate([
    (0, common_1.Post)('cancel-claim'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.INVESTOR, roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, claim_cancel_dto_1.ClaimCancelDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "CancelClaim", null);
__decorate([
    (0, common_1.Post)('get-claim'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.INVESTOR, roles_enum_1.RolesEnum.ADVISOR, roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, claim_get_dto_1.ClaimGetDto]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "getClaim", null);
__decorate([
    (0, common_1.Post)('transaction'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ expectedType: transaction_pix_key_dto_1.TransactionPixKeyDto }))),
    __param(2, (0, common_1.Headers)('x-2fa-token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, transaction_pix_key_dto_1.TransactionPixKeyDto, String]),
    __metadata("design:returntype", Promise)
], PixController.prototype, "transactionPixKey", null);
exports.PixController = PixController = __decorate([
    (0, common_1.Controller)('pix'),
    __param(0, (0, common_1.Inject)(create_pix_key_service_1.CreatePixKeyService)),
    __param(1, (0, common_1.Inject)(get_pix_key_service_1.GetPixKeyService)),
    __param(2, (0, common_1.Inject)(get_pix_key_external_service_1.GetPixKeyExternalService)),
    __param(3, (0, common_1.Inject)(delete_pix_key_service_1.DeletePixKeyService)),
    __param(4, (0, common_1.Inject)(get_pix_participants_service_1.GetPixParticipantsService)),
    __param(5, (0, common_1.Inject)(create_pix_qrcode_static_service_1.CreatePixQRCodeStaticService)),
    __param(6, (0, common_1.Inject)(create_pix_qrcode_dynamic_service_1.CreatePixQRCodeDynamicService)),
    __param(7, (0, common_1.Inject)(request_claim_pix_key_service_1.RequestClaimPixKeyService)),
    __param(8, (0, common_1.Inject)(claim_confim_pix_key_service_1.ClaimConfirmPixKeyService)),
    __param(9, (0, common_1.Inject)(read_qrcode_pix_service_1.ReadQRCodePixService)),
    __param(10, (0, common_1.Inject)(claim_cancel_pix_key_service_1.ClaimCancelPixKeyService)),
    __metadata("design:paramtypes", [create_pix_key_service_1.CreatePixKeyService,
        get_pix_key_service_1.GetPixKeyService,
        get_pix_key_external_service_1.GetPixKeyExternalService,
        delete_pix_key_service_1.DeletePixKeyService,
        get_pix_participants_service_1.GetPixParticipantsService,
        create_pix_qrcode_static_service_1.CreatePixQRCodeStaticService,
        create_pix_qrcode_dynamic_service_1.CreatePixQRCodeDynamicService,
        request_claim_pix_key_service_1.RequestClaimPixKeyService,
        claim_confim_pix_key_service_1.ClaimConfirmPixKeyService,
        read_qrcode_pix_service_1.ReadQRCodePixService,
        claim_cancel_pix_key_service_1.ClaimCancelPixKeyService,
        claim_get_pix_key_service_1.ClaimGetPixKeyService,
        transaction_pix_key_service_1.TransactionPixKeyService])
], PixController);
//# sourceMappingURL=pix.controller.js.map