{"version": 3, "file": "superadmin.controller.js", "sourceRoot": "/", "sources": ["modules/superadmin/controllers/superadmin.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAYwB;AACxB,6CAA8C;AAE9C,wGAAwG;AACxG,sEAAuE;AAGvE,mGAAmG;AACnG,qGAAoG;AAGpG,gFAA8D;AAC9D,iFAAuE;AACvE,iEAAwD;AACxD,0EAAgE;AAChE,kEAAyD;AAIzD,uFAAoH;AACpH,6FAA0H;AAC1H,sFAAkH;AAClH,4FAAwH;AACxH,yFAAyH;AACzH,+FAA8H;AAC9H,wFAAsH;AACtH,8FAA2H;AAC3H,+FAAuF;AACvF,mFAAyE;AACzE,+HAAsH;AACtH,6HAAoH;AACpH,uGAAgG;AAChG,uFAAiF;AACjF,mIAA0H;AAC1H,iIAAwH;AACxH,mGAA4F;AAC5F,iGAA0F;AAC1F,qGAA8F;AAKvF,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,YACmB,iBAAoC,EACpC,yBAAoD,EACpD,wBAAkD,EAClD,wBAAkD,EAClD,0BAAsD,EACtD,uCAAgF,EAChF,yCAAoF,EACpF,wCAAkF,EAClF,0CAAsF,EACtF,uBAAgD,EAChD,2BAAwD,EACxD,4BAA0D,EAC1D,8BAA8D,EAC9D,6BAA4D;QAb5D,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,+BAA0B,GAA1B,0BAA0B,CAA4B;QACtD,4CAAuC,GAAvC,uCAAuC,CAAyC;QAChF,8CAAyC,GAAzC,yCAAyC,CAA2C;QACpF,6CAAwC,GAAxC,wCAAwC,CAA0C;QAClF,+CAA0C,GAA1C,0CAA0C,CAA4C;QACtF,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,gCAA2B,GAA3B,2BAA2B,CAA6B;QACxD,iCAA4B,GAA5B,4BAA4B,CAA8B;QAC1D,mCAA8B,GAA9B,8BAA8B,CAAgC;QAC9D,kCAA6B,GAA7B,6BAA6B,CAA+B;IAC5E,CAAC;IAGE,AAAN,KAAK,CAAC,gBAAgB;QACpB,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;IAChD,CAAC;IAKK,AAAN,KAAK,CAAC,UAAU,CACH,OAAqB,EAEhC,IAAmB;QAEnB,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAKK,AAAN,KAAK,CAAC,WAAW,CACJ,OAAqB,EAEhC,IAAmB;QAEnB,MAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAKK,AAAN,KAAK,CAAC,sBAAsB,CACf,GAAG,EACN,IAAgC;QAExC,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvE,CAAC;IAQK,AAAN,KAAK,CAAC,gCAAgC,CAC3B,KAA+C;QAExD,OAAO,IAAI,CAAC,uCAAuC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACrE,CAAC;IASK,AAAN,KAAK,CAAC,kCAAkC,CACzB,EAAU,EACd,KAAiD;QAE1D,OAAO,IAAI,CAAC,yCAAyC,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;IAQK,AAAN,KAAK,CAAC,iCAAiC,CAC5B,KAAgD;QAEzD,OAAO,IAAI,CAAC,wCAAwC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACtE,CAAC;IAUK,AAAN,KAAK,CAAC,mCAAmC,CAC1B,EAAU,EACd,KAAmD;QAE5D,OAAO,IAAI,CAAC,0CAA0C,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC5E,CAAC;IAGK,AAAN,KAAK,CAAC,MAAM,CAAY,OAAqB;QAC3C,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;IACtE,CAAC;IAGK,AAAN,KAAK,CAAC,kBAAkB,CAAmB,OAAe;QACxD,MAAM,qBAAqB,GAA0B,EAAE,OAAO,EAAE,CAAC;QACjE,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;IACvE,CAAC;IAGK,AAAN,KAAK,CAAC,mBAAmB,CACgB,OAAe;QAEtD,MAAM,cAAc,GAAyB,EAAE,OAAO,EAAE,CAAC;QAEzD,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IAC/D,CAAC;IAGK,AAAN,KAAK,CAAC,qBAAqB,CACc,OAAe;QAEtD,MAAM,oBAAoB,GAA2B,EAAE,OAAO,EAAE,CAAC;QAEjE,OAAO,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACvE,CAAC;IAGK,AAAN,KAAK,CAAC,oBAAoB,CACgB,OAAe;QAEvD,MAAM,oBAAoB,GAAoB,EAAE,OAAO,EAAE,CAAC;QAE1D,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACrE,CAAC;IAGK,AAAN,KAAK,CAAC,uBAAuB,CACV,MAAqB,EACf,YAA+B;QAEtD,OAAO,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC3E,CAAC;CACF,CAAA;AAzJY,oDAAoB;AAmBzB;IADL,IAAA,YAAG,EAAC,wBAAwB,CAAC;;;;4DAG7B;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,CAAC;IAC3B,IAAA,cAAK,EAAC,qBAAqB,CAAC;IAE1B,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,+BAAa;;sDAGpB;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,CAAC;IAC3B,IAAA,cAAK,EAAC,sBAAsB,CAAC;IAE3B,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,+BAAa;;uDAGpB;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,CAAC;IAC3B,IAAA,YAAG,EAAC,sBAAsB,CAAC;IAEzB,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;;;kEAGR;AAQK;IANL,IAAA,YAAG,EAAC,6BAA6B,CAAC;IAClC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,uCAAuC;QACpD,IAAI,EAAE,0DAA2C;KAClD,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;;qCAAQ,oDAAwC;;4EAGzD;AASK;IAPL,IAAA,YAAG,EAAC,iCAAiC,CAAC;IACtC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,yCAAyC;QACtD,IAAI,EAAE,2DAA4C;QAClD,OAAO,EAAE,IAAI;KACd,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,cAAK,GAAE,CAAA;;6CAAQ,sDAA0C;;8EAG3D;AAQK;IANL,IAAA,YAAG,EAAC,8BAA8B,CAAC;IACnC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,wCAAwC;QACrD,IAAI,EAAE,2DAA4C;KACnD,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;;qCAAQ,qDAAyC;;6EAG1D;AAUK;IARL,IAAA,YAAG,EAAC,kCAAkC,CAAC;IACvC,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EACT,oEAAoE;QACtE,IAAI,EAAE,6DAA8C;QACpD,OAAO,EAAE,IAAI;KACd,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,cAAK,GAAE,CAAA;;6CAAQ,wDAA4C;;+EAG7D;AAGK;IADL,IAAA,YAAG,EAAC,qBAAqB,CAAC;IACb,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;kDAEtB;AAGK;IADL,IAAA,YAAG,EAAC,+BAA+B,CAAC;IACX,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;;;;8DAGzC;AAGK;IADL,IAAA,YAAG,EAAC,gCAAgC,CAAC;IAEnC,WAAA,IAAA,cAAK,EAAC,SAAS,EAAE,IAAI,sBAAa,EAAE,CAAC,CAAA;;;;+DAKvC;AAGK;IADL,IAAA,YAAG,EAAC,yCAAyC,CAAC;IAE5C,WAAA,IAAA,cAAK,EAAC,SAAS,EAAE,IAAI,sBAAa,EAAE,CAAC,CAAA;;;;iEAKvC;AAGK;IADL,IAAA,YAAG,EAAC,iCAAiC,CAAC;IAEpC,WAAA,IAAA,cAAK,EAAC,UAAU,EAAE,IAAI,sBAAa,EAAE,CAAC,CAAA;;;;gEAKxC;AAGK;IADL,IAAA,YAAG,EAAC,8BAA8B,CAAC;IAEjC,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,cAAK,EAAC,cAAc,CAAC,CAAA;;;;mEAGvB;+BAxJU,oBAAoB;IAHhC,IAAA,mBAAU,GAAE;IACZ,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,CAAC;qCAGY,4CAAiB;QACT,0DAAyB;QAC1B,sDAAwB;QACxB,uDAAwB;QACtB,0DAA0B;QACb,uFAAuC;QACrC,2FAAyC;QAC1C,yFAAwC;QACtC,6FAA0C;QAC7D,oDAAuB;QACnB,6DAA2B;QAC1B,+DAA4B;QAC1B,mEAA8B;QAC/B,iEAA6B;GAfpE,oBAAoB,CAyJhC", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Get,\r\n  Param,\r\n  ParseUUIDPipe,\r\n  Patch,\r\n  Post,\r\n  Put,\r\n  Query,\r\n  Request,\r\n  UseGuards,\r\n} from '@nestjs/common';\r\nimport { ApiResponse } from '@nestjs/swagger';\r\nimport { GetAdvisorInvestorsDto } from 'src/modules/advisor/dto/get-advisor-investors-dto';\r\nimport { GetAdvisorInvestorsService } from 'src/modules/advisor/services/get-advisor-investors.service';\r\nimport { EditBrokerDto } from 'src/modules/broker/dto/edit-broker.dto';\r\nimport { GetBrokerAdvisorsDto } from 'src/modules/broker/dto/get-broker-advisors.dto';\r\nimport { GetInvestorsDto } from 'src/modules/broker/dto/get-investors.dto';\r\nimport { GetBrokerAdvisorsService } from 'src/modules/broker/services/get-broker-advisors.service';\r\nimport { GetBrokerInvestorService } from 'src/modules/broker/services/get-broker-investors.service';\r\nimport { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';\r\nimport { IEditInvestorSuperAdminDto } from 'src/modules/investor/dto/edit-investor-dto';\r\nimport { Roles } from 'src/shared/decorators/roles.decorator';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { RoleGuard } from 'src/shared/guards/role.guard';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\nimport { GetAdminSuperAdminDto } from '../dto/get-admin-super-admin-request.dto';\r\nimport { GetAllAdvisorsWithActiveContractsQueryDto } from '../dto/get-all-advisors-with-active-contracts/query.dto';\r\nimport { GetAllAdvisorsWithActiveContractsResponseDto } from '../dto/get-all-advisors-with-active-contracts/response.dto';\r\nimport { GetAllBrokersWithActiveContractsQueryDto } from '../dto/get-all-brokers-with-active-contracts/query.dto';\r\nimport { GetAllBrokersWithActiveContractsResponseDto } from '../dto/get-all-brokers-with-active-contracts/response.dto';\r\nimport { GetSingleAdvisorsWithActiveContractsQueryDto } from '../dto/get-single-advisor-with-active-contracts/query.dto';\r\nimport { GetSingleAdvisorWithActiveContractsResponseDto } from '../dto/get-single-advisor-with-active-contracts/response.dto';\r\nimport { GetSingleBrokerWithActiveContractsQueryDto } from '../dto/get-single-broker-with-active-contracts/query.dto';\r\nimport { GetSinleBrokerWithActiveContractsResponseDto } from '../dto/get-single-broker-with-active-contracts/response.dto';\r\nimport { GetAdminSuperAdminService } from '../services/find-admin-super-admin.service';\r\nimport { SuperAdminService } from '../services/find-super-admin.service';\r\nimport { GetAllAdvisorsWithActiveContractsService } from '../services/get-all-advisors-with-active-contracts.service';\r\nimport { GetAllBrokersWithActiveContractsService } from '../services/get-all-brokers-with-active-contracts.service';\r\nimport { GetContractsGrowthChartService } from '../services/get-contracts-growth-chart.service';\r\nimport { GetDashboardDataService } from '../services/get-dashboard-data.service';\r\nimport { GetSingleAdvisorWithActiveContractsService } from '../services/get-single-advisor-with-active-contracts.service';\r\nimport { GetSingleBrokerWithActiveContractsService } from '../services/get-single-broker-with-active-contracts.service';\r\nimport { SuperAdminEditAdvisorService } from '../services/super-admin-edit-advisor.service';\r\nimport { SuperAdminEditBrokerService } from '../services/super-admin-edit-broker.service';\r\nimport { SuperAdminEditInvestorService } from '../services/super-admin-edit-investor.service';\r\n\r\n@Controller()\r\n@UseGuards(JwtAuthGuard, RoleGuard)\r\n@Roles(RolesEnum.SUPERADMIN)\r\nexport class SuperAdminController {\r\n  constructor(\r\n    private readonly superAdminService: SuperAdminService,\r\n    private readonly getAdminSuperAdminService: GetAdminSuperAdminService,\r\n    private readonly getBrokerAdvisorsService: GetBrokerAdvisorsService,\r\n    private readonly getBrokerInvestorService: GetBrokerInvestorService,\r\n    private readonly getAdvisorInvestorsService: GetAdvisorInvestorsService,\r\n    private readonly getAllBrokersWithActiveContractsService: GetAllBrokersWithActiveContractsService,\r\n    private readonly getSingleBrokerWithActiveContractsService: GetSingleBrokerWithActiveContractsService,\r\n    private readonly getAllAdvisorsWithActiveContractsService: GetAllAdvisorsWithActiveContractsService,\r\n    private readonly getSingleAdvisorWithActiveContractsService: GetSingleAdvisorWithActiveContractsService,\r\n    private readonly getDashboardDataService: GetDashboardDataService,\r\n    private readonly superAdminEditBrokerService: SuperAdminEditBrokerService,\r\n    private readonly superAdminEditAdvisorService: SuperAdminEditAdvisorService,\r\n    private readonly getContractsGrowthChartService: GetContractsGrowthChartService,\r\n    private readonly superAdminEditInvestorService: SuperAdminEditInvestorService,\r\n  ) {}\r\n\r\n  @Get('/super-admin/dashboard')\r\n  async getDashboardData() {\r\n    return this.getDashboardDataService.perform();\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.SUPERADMIN)\r\n  @Patch('/super-admin/broker')\r\n  async editBroker(\r\n    @Request() request: IRequestUser,\r\n    @Body()\r\n    body: EditBrokerDto,\r\n  ) {\r\n    await this.superAdminEditBrokerService.perform(body);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.SUPERADMIN)\r\n  @Patch('/super-admin/advisor')\r\n  async editAdvisor(\r\n    @Request() request: IRequestUser,\r\n    @Body()\r\n    body: EditBrokerDto,\r\n  ) {\r\n    await this.superAdminEditAdvisorService.perform(body);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Roles(RolesEnum.SUPERADMIN)\r\n  @Put('/superadmin/investor')\r\n  async editInvestorSuperAdmin(\r\n    @Request() req,\r\n    @Body() body: IEditInvestorSuperAdminDto,\r\n  ) {\r\n    return this.superAdminEditInvestorService.perform(body, req.user.id);\r\n  }\r\n\r\n  @Get('/super-admin/active-brokers')\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Get all brokers with active contracts',\r\n    type: GetAllBrokersWithActiveContractsResponseDto,\r\n  })\r\n  async getAllBrokersWithActiveContracts(\r\n    @Query() query: GetAllBrokersWithActiveContractsQueryDto,\r\n  ) {\r\n    return this.getAllBrokersWithActiveContractsService.perform(query);\r\n  }\r\n\r\n  @Get('/super-admin/active-brokers/:id')\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Get single broker with active contracts',\r\n    type: GetSinleBrokerWithActiveContractsResponseDto,\r\n    isArray: true,\r\n  })\r\n  async getSingleBrokerWithActiveContracts(\r\n    @Param('id') id: string,\r\n    @Query() query: GetSingleBrokerWithActiveContractsQueryDto,\r\n  ) {\r\n    return this.getSingleBrokerWithActiveContractsService.perform(id, query);\r\n  }\r\n\r\n  @Get('/super-admin/active-advisors')\r\n  @ApiResponse({\r\n    status: 200,\r\n    description: 'Get all advisors with active contracts',\r\n    type: GetAllAdvisorsWithActiveContractsResponseDto,\r\n  })\r\n  async getAllAdvisorsWithActiveContracts(\r\n    @Query() query: GetAllAdvisorsWithActiveContractsQueryDto,\r\n  ) {\r\n    return this.getAllAdvisorsWithActiveContractsService.perform(query);\r\n  }\r\n\r\n  @Get('/super-admin/active-advisors/:id')\r\n  @ApiResponse({\r\n    status: 200,\r\n    description:\r\n      'Get single advisor with its broker active contract total value sum',\r\n    type: GetSingleAdvisorWithActiveContractsResponseDto,\r\n    isArray: true,\r\n  })\r\n  async getSingleAdvisorWithActiveContracts(\r\n    @Param('id') id: string,\r\n    @Query() query: GetSingleAdvisorsWithActiveContractsQueryDto,\r\n  ) {\r\n    return this.getSingleAdvisorWithActiveContractsService.perform(id, query);\r\n  }\r\n\r\n  @Get('/super-admin/admins')\r\n  async search(@Request() request: IRequestUser) {\r\n    return this.superAdminService.perform({ ownerId: request.user.id });\r\n  }\r\n\r\n  @Get('/super-admin/brokers/:ownerId')\r\n  async getAdminSuperAdmin(@Param('ownerId') ownerId: string) {\r\n    const getAdminSuperAdminDto: GetAdminSuperAdminDto = { ownerId };\r\n    return this.getAdminSuperAdminService.perform(getAdminSuperAdminDto);\r\n  }\r\n\r\n  @Get('/super-admin/advisors/:ownerId')\r\n  async getAdvisorsByBroker(\r\n    @Param('ownerId', new ParseUUIDPipe()) ownerId: string,\r\n  ) {\r\n    const getAdvisorsDto: GetBrokerAdvisorsDto = { ownerId };\r\n\r\n    return this.getBrokerAdvisorsService.perform(getAdvisorsDto);\r\n  }\r\n\r\n  @Get('/super-admin/investors/advisor/:ownerId')\r\n  async getInvestorsByAdvisor(\r\n    @Param('ownerId', new ParseUUIDPipe()) ownerId: string,\r\n  ) {\r\n    const getInvestorsByBroker: GetAdvisorInvestorsDto = { ownerId };\r\n\r\n    return this.getAdvisorInvestorsService.perform(getInvestorsByBroker);\r\n  }\r\n\r\n  @Get('/super-admin/investors/:ownerId')\r\n  async getInvestorsByBroker(\r\n    @Param('brokerId', new ParseUUIDPipe()) ownerId: string,\r\n  ) {\r\n    const getInvestorsByBroker: GetInvestorsDto = { ownerId };\r\n\r\n    return this.getBrokerInvestorService.perform(getInvestorsByBroker);\r\n  }\r\n\r\n  @Get('/superadmin/contracts-growth')\r\n  async getContractsGrowthChart(\r\n    @Query('period') period?: PeriodFilter,\r\n    @Query('contractType') contractType?: ContractTypeEnum,\r\n  ) {\r\n    return this.getContractsGrowthChartService.perform(period, contractType);\r\n  }\r\n}\r\n"]}