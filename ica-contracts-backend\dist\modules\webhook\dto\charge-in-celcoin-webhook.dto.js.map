{"version": 3, "file": "charge-in-celcoin-webhook.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/charge-in-celcoin-webhook.dto.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IChargeInCelcoinWebhookDto {\r\n  entity: 'charge-in';\r\n  createTimestamp: string;\r\n  status: 'CANCELLED' | 'CONFIRMED' | 'ERROR';\r\n  body: {\r\n    externalId: string;\r\n    transactionId: string;\r\n    status: string;\r\n    dataPagamento: string;\r\n    dataVencimento: string;\r\n    valorPago: number;\r\n    valorOriginal: number;\r\n    tipoPagamento: 'Pix' | 'Boleto';\r\n    split: { amount: number; account: number }[];\r\n  };\r\n}\r\n"]}