"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetAllAdvisorsWithActiveContractsService = void 0;
const cache_manager_1 = require("@nestjs/cache-manager");
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const contract_advisor_entity_1 = require("../../../shared/database/typeorm/entities/contract-advisor.entity");
const pagination_query_1 = require("../helpers/pagination-query");
const addendum_entity_1 = require("../../../shared/database/typeorm/entities/addendum.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
let GetAllAdvisorsWithActiveContractsService = class GetAllAdvisorsWithActiveContractsService {
    constructor(contractsAdvisorRepository, cacheManager) {
        this.contractsAdvisorRepository = contractsAdvisorRepository;
        this.cacheManager = cacheManager;
    }
    async perform(query) {
        const cacheKey = `active-advisors-${JSON.stringify(query)}`;
        const cached = await this.cacheManager.get(cacheKey);
        if (cached)
            return cached;
        const { page, limit, skip } = pagination_query_1.PaginatedQueryHelper.getPaginationParams(query);
        const filters = {
            isActive: true,
        };
        const totalQuery = this.contractsAdvisorRepository
            .createQueryBuilder('contract_advisor')
            .innerJoin('contract_advisor.advisor', 'advisor')
            .leftJoin('advisor.owner', 'owner')
            .leftJoin('advisor.business', 'business')
            .innerJoin('contract_advisor.contract', 'contract')
            .leftJoin('contract.signataries', 'signataries')
            .leftJoinAndSelect('contract.addendum', 'addendum', 'addendum.status = :status', { status: addendum_entity_1.AddendumStatus.FULLY_SIGNED })
            .select('COUNT(DISTINCT advisor.id)', 'count')
            .where(filters)
            .andWhere('contract.endContract >= :today', {
            today: new Date(),
        })
            .andWhere('contract.status = :contractStatus', {
            contractStatus: contract_status_enum_1.ContractStatusEnum.ACTIVE,
        });
        if (query.dateTo || query.dateFrom) {
            const { start, end } = pagination_query_1.PaginatedQueryHelper.getDateRangeParams(query);
            totalQuery.andWhere('contract_advisor.created_at BETWEEN :start AND :end', {
                start,
                end,
            });
        }
        if (query.contractType) {
            totalQuery.andWhere('signataries.investmentModality = :contractType', {
                contractType: query.contractType,
            });
        }
        const { count } = await totalQuery.getRawOne();
        const total = Number(count);
        const advisorsQuery = this.contractsAdvisorRepository
            .createQueryBuilder('contract_advisor')
            .innerJoin('contract_advisor.advisor', 'advisor')
            .leftJoin('advisor.owner', 'owner')
            .leftJoin('advisor.business', 'business')
            .innerJoin('contract_advisor.contract', 'contract')
            .leftJoin('contract.signataries', 'signataries')
            .leftJoinAndSelect('contract.addendum', 'addendum', 'addendum.status = :status', { status: addendum_entity_1.AddendumStatus.FULLY_SIGNED })
            .select([
            'contract_advisor.id_advisor as advisorid',
            'COALESCE(owner.name, business.companyName) as name',
            'COALESCE(owner.avatar, business.avatar) as avatar',
            'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0)) as totalvalue',
        ])
            .where(filters)
            .andWhere('contract.endContract >= :today', {
            today: new Date(),
        })
            .andWhere('contract.status = :contractStatus', {
            contractStatus: contract_status_enum_1.ContractStatusEnum.ACTIVE,
        });
        if (query.dateTo || query.dateFrom) {
            const { start, end } = pagination_query_1.PaginatedQueryHelper.getDateRangeParams(query);
            advisorsQuery.andWhere('contract_advisor.created_at BETWEEN :start AND :end', {
                start,
                end,
            });
        }
        if (query.contractType) {
            advisorsQuery.andWhere('signataries.investmentModality = :contractType', {
                contractType: query.contractType,
            });
        }
        const advisorsQueryResult = await advisorsQuery
            .groupBy('advisorId, owner.name, business.companyName, owner.avatar, business.avatar')
            .orderBy('totalvalue', 'DESC')
            .offset(skip)
            .limit(limit)
            .getRawMany();
        const result = advisorsQueryResult.map((advisor, index) => ({
            advisorId: advisor.advisorid,
            rank: index + 1 + (page - 1) * limit,
            name: advisor.name,
            avatar: advisor.avatar,
            totalValue: Number(advisor.totalvalue),
        }));
        const response = pagination_query_1.PaginatedQueryHelper.createPaginatedResponse(result, total, page, limit);
        await this.cacheManager.set(cacheKey, response, 60000);
        return response;
    }
};
exports.GetAllAdvisorsWithActiveContractsService = GetAllAdvisorsWithActiveContractsService;
exports.GetAllAdvisorsWithActiveContractsService = GetAllAdvisorsWithActiveContractsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_advisor_entity_1.ContractAdvisorEntity)),
    __param(1, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [typeorm_2.Repository, Object])
], GetAllAdvisorsWithActiveContractsService);
//# sourceMappingURL=get-all-advisors-with-active-contracts.service.js.map