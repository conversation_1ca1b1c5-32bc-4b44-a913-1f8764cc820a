import { NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { Repository } from 'typeorm';
import { OwnerRoleRelationEntity } from '../database/typeorm/entities/owner-role-relation.entity';
export declare class ValidateAdminMiddleware implements NestMiddleware {
    private perfilDb;
    constructor(perfilDb: Repository<OwnerRoleRelationEntity>);
    use(req: Request, res: Response, next: NextFunction): Promise<void>;
}
