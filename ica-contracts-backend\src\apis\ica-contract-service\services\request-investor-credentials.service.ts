import axios, { AxiosResponse, isAxiosError } from 'axios';
import { Injectable, HttpException, BadRequestException } from '@nestjs/common';

@Injectable()
export class RequestInvestorCredentialsService {
  async perform(
    dto: {contractId: string;},
  ): Promise<void> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/${dto.contractId}/investor/credentials`;

      const { data } = await axios.post<
        FormData,
        AxiosResponse<void>
      >(url, { headers: { 'Content-Type': 'multipart/form-data' } });

      return data;
    } catch (error) {
        console.log('error', error);
        
      if (isAxiosError(error)) {
        if (error.status > 399 && error.status < 499) {
          
          throw new BadRequestException(error.response.data.message.at(1));
        }

        throw new HttpException(
          error.response.data.message,
          error.response.data.statusCode,
        );
      }
      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }

}
