import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { GetInternalPixTransactionlDto } from '../dto/get-internal-pix-transaction.dto';
import { TransactionStatusDto } from '../dto/get-transaction-status.dto';
import { InternalTransactionDto } from '../dto/internal-transaction.dto';
import { ReversalPixDto } from '../dto/reversal-pix.dto';
import { TransactionPixKeyDto } from '../dto/transaction-pix-key.dto';
import { TransactionPixManualDto } from '../dto/transaction-pix-manual.dto';
import { GetInternalPixTransactionService } from '../service/get-internal-pix-transaction.service';
import { InternalTransactionService } from '../service/internal-transaction.service';
import { ReversalPixService } from '../service/reversal-pix.service';
import { TransactionPixKeyService } from '../service/transaction-pix-key.service';
import { TransactionPixManualService } from '../service/transaction-pix-manual.service';
import { TransactionPixStatusService } from '../service/transaction-pix-status.service';
export declare class TransactionPixController {
    private readonly transactionPixKeyService;
    private readonly transactionPixManualService;
    private readonly transactionPixStatusService;
    private readonly getInternalPixTransactionService;
    private readonly internalTransactionService;
    private readonly reversalPixService;
    constructor(transactionPixKeyService: TransactionPixKeyService, transactionPixManualService: TransactionPixManualService, transactionPixStatusService: TransactionPixStatusService, getInternalPixTransactionService: GetInternalPixTransactionService, internalTransactionService: InternalTransactionService, reversalPixService: ReversalPixService);
    transactionPixKey(request: IRequestUser, body: TransactionPixKeyDto, twoFactorToken: string): Promise<import("../response/transaction-pix-key-response").ITransactionPixKeyResponse>;
    transactionPixManual(request: IRequestUser, body: TransactionPixManualDto, twoFactorToken: string): Promise<import("../response/transaction-pix-key-response").ITransactionPixKeyResponse>;
    transactionStatus(request: IRequestUser, query: TransactionStatusDto): Promise<any>;
    getInternalPixTransaction(request: IRequestUser, query: GetInternalPixTransactionlDto): Promise<any>;
    internalTransaction(request: IRequestUser, data: InternalTransactionDto): Promise<import("../response/internal-transaction.response").IInternalTransactionResponse>;
    reversalPix(body: ReversalPixDto): Promise<import("../../../apis/celcoin/responses/reverse-pix-celcoin.response").IReversePixCelcoinResponse>;
}
