{"version": 3, "file": "charge-create-celcoin-webhook.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/charge-create-celcoin-webhook.dto.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IChargeCreateCelcoinWebhookDto {\r\n  entity: 'charge-create';\r\n  createTimestamp: string;\r\n  status: 'CREATED';\r\n  body: {\r\n    amount: number;\r\n    debtor: {\r\n      number: number;\r\n      city: string;\r\n      document: number;\r\n      postalCode: number;\r\n      name: string;\r\n      neighborhood: string;\r\n      state: string;\r\n      complement: string;\r\n      publicArea: string;\r\n    };\r\n    receiver: {\r\n      city: string;\r\n      document: number;\r\n      postalCode: number;\r\n      name: string;\r\n      state: string;\r\n      publicArea: string;\r\n      account: number;\r\n    };\r\n    instructions: {\r\n      fine: number;\r\n      interest: number;\r\n      discount: {\r\n        amount: number;\r\n        modality: 'fixed';\r\n        limitDate: string;\r\n      };\r\n    };\r\n    duedate: string;\r\n    boleto: {\r\n      bankNumber: number;\r\n      bankAccount: number;\r\n      bankEmissor: string;\r\n      bankLine: string;\r\n      bankAgency: number;\r\n      transactionId: number;\r\n      bankAssignor: string;\r\n      status: 'PENDING';\r\n      barCode: string;\r\n    };\r\n    externalId: string;\r\n    expirationAfterPayment: number;\r\n    transactionId: string;\r\n    pix: {\r\n      locationId: number;\r\n      transactionId: number;\r\n      key: string;\r\n      transactionIdentification: string;\r\n      status: 'PENDING';\r\n      emv: string;\r\n    };\r\n    status: 'PENDING';\r\n  };\r\n}\r\n"]}