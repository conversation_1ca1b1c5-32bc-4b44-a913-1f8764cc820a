{"version": 3, "file": "get-single-advisor-with-active-contracts.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/get-single-advisor-with-active-contracts.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,yDAAsD;AACtD,2CAAoD;AACpD,6CAAmD;AAInD,qCAA8C;AAI9C,kEAAmE;AACnE,+GAAqG;AACrG,+FAAsF;AACtF,qFAA2E;AAGpE,IAAM,0CAA0C,GAAhD,MAAM,0CAA0C;IACrD,YAEU,0BAA6D,EACtC,YAAmB;QAD1C,+BAA0B,GAA1B,0BAA0B,CAAmC;QACtC,iBAAY,GAAZ,YAAY,CAAO;IACjD,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,SAAiB,EACjB,KAAmD;QAEnD,MAAM,QAAQ,GAAG,4BAA4B,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;QAElF,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAEzB,QAAQ,CAAC,CAAC;QAEd,IAAI,MAAM;YAAE,OAAO,MAAM,CAAC;QAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GACzB,uCAAoB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,OAAO,GAAG;YACd,SAAS,EAAE,SAAS;SACrB,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,0BAA0B;aAC/C,kBAAkB,CAAC,kBAAkB,CAAC;aACtC,SAAS,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAClD,SAAS,CAAC,mBAAmB,EAAE,UAAU,CAAC;aAC1C,QAAQ,CAAC,gBAAgB,EAAE,eAAe,CAAC;aAC3C,QAAQ,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;aACjD,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC,6BAA6B,EAAE,OAAO,CAAC;aAC9C,KAAK,CAAC,OAAO,CAAC;aACd,QAAQ,CAAC,gCAAgC,EAAE;YAC1C,KAAK,EAAE,IAAI,IAAI,EAAE;SAClB,CAAC;aACD,QAAQ,CAAC,mCAAmC,EAAE;YAC7C,cAAc,EAAE,yCAAkB,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEL,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,uCAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEtE,UAAU,CAAC,QAAQ,CACjB,qDAAqD,EACrD;gBACE,KAAK;gBACL,GAAG;aACJ,CACF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,UAAU,CAAC,QAAQ,CAAC,gDAAgD,EAAE;gBACpE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,CAAC,SAAS,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE5B,MAAM,oBAAoB,GAAG,IAAI,CAAC,0BAA0B;aACzD,kBAAkB,CAAC,kBAAkB,CAAC;aACtC,SAAS,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAClD,SAAS,CAAC,mBAAmB,EAAE,UAAU,CAAC;aAC1C,QAAQ,CAAC,gBAAgB,EAAE,eAAe,CAAC;aAC3C,QAAQ,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;aACjD,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC;YACN,2BAA2B;YAC3B,oEAAoE;YACpE,mEAAmE;YACnE,gEAAgE;YAChE,iCAAiC;YACjC,yGAAyG;SAC1G,CAAC;aACD,KAAK,CAAC,OAAO,CAAC;aACd,QAAQ,CAAC,gCAAgC,EAAE;YAC1C,KAAK,EAAE,IAAI,IAAI,EAAE;SAClB,CAAC;aACD,QAAQ,CAAC,mCAAmC,EAAE;YAC7C,cAAc,EAAE,yCAAkB,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEL,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,uCAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEtE,oBAAoB,CAAC,QAAQ,CAC3B,qDAAqD,EACrD;gBACE,KAAK;gBACL,GAAG;aACJ,CACF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,oBAAoB,CAAC,QAAQ,CAC3B,gDAAgD,EAChD;gBACE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CACF,CAAC;QACJ,CAAC;QAED,MAAM,0BAA0B,GAAG,MAAM,oBAAoB;aAC1D,OAAO,CACN,4KAA4K,CAC7K;aACA,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;aACtC,MAAM,CAAC,IAAI,CAAC;aACZ,KAAK,CAAC,KAAK,CAAC;aACZ,UAAU,EAOP,CAAC;QAEP,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B;aACvD,kBAAkB,CAAC,kBAAkB,CAAC;aACtC,SAAS,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAClD,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,QAAQ,CAAC,mBAAmB,EAAE,UAAU,EAAE,2BAA2B,EAAE;YACtE,MAAM,EAAE,gCAAc,CAAC,YAAY;SACpC,CAAC;aACD,MAAM,CACL,kFAAkF,EAClF,OAAO,CACR;aACA,KAAK,CAAC,OAAO,CAAC;aACd,QAAQ,CAAC,gCAAgC,EAAE;YAC1C,KAAK,EAAE,IAAI,IAAI,EAAE;SAClB,CAAC;aACD,QAAQ,CAAC,mCAAmC,EAAE;YAC7C,cAAc,EAAE,yCAAkB,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEL,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,uCAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACtE,kBAAkB,CAAC,QAAQ,CACzB,qDAAqD,EACrD;gBACE,KAAK;gBACL,GAAG;aACJ,CACF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,kBAAkB,CAAC,QAAQ,CACzB,gDAAgD,EAChD;gBACE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CACF,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,kBAAkB,CAAC,SAAS,EAAE,CAAC;QAEtE,MAAM,MAAM,GAAG,0BAA0B,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC3D,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,mBAAmB,EAAE,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YACzD,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC;SACrC,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,uCAAoB,CAAC,uBAAuB,CAC3D,MAAM,EACN,KAAK,EACL,IAAI,EACJ,KAAK,CACN,CAAC;QAEF,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAtMY,gGAA0C;qDAA1C,0CAA0C;IADtD,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,+CAAqB,CAAC,CAAA;IAEvC,WAAA,IAAA,eAAM,EAAC,6BAAa,CAAC,CAAA;qCADc,oBAAU;GAHrC,0CAA0C,CAsMtD", "sourcesContent": ["import { CACHE_MANAGER } from '@nestjs/cache-manager';\r\nimport { Inject, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Cache } from 'cache-manager';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { Between, Repository } from 'typeorm';\r\n\r\nimport { GetSingleAdvisorsWithActiveContractsQueryDto } from '../dto/get-single-advisor-with-active-contracts/query.dto';\r\nimport { GetSingleAdvisorWithActiveContractsResponseDto } from '../dto/get-single-advisor-with-active-contracts/response.dto';\r\nimport { PaginatedQueryHelper } from '../helpers/pagination-query';\r\nimport { ContractAdvisorEntity } from 'src/shared/database/typeorm/entities/contract-advisor.entity';\r\nimport { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\n\r\n@Injectable()\r\nexport class GetSingleAdvisorWithActiveContractsService {\r\n  constructor(\r\n    @InjectRepository(ContractAdvisorEntity)\r\n    private contractsAdvisorRepository: Repository<ContractAdvisorEntity>,\r\n    @Inject(CACHE_MANAGER) private cacheManager: Cache,\r\n  ) {}\r\n\r\n  async perform(\r\n    advisorId: string,\r\n    query: GetSingleAdvisorsWithActiveContractsQueryDto,\r\n  ) {\r\n    const cacheKey = `active-advisor-contracts-${advisorId}-${JSON.stringify(query)}`;\r\n\r\n    const cached =\r\n      await this.cacheManager.get<\r\n        GetSingleAdvisorWithActiveContractsResponseDto[]\r\n      >(cacheKey);\r\n\r\n    if (cached) return cached;\r\n\r\n    const { page, limit, skip } =\r\n      PaginatedQueryHelper.getPaginationParams(query);\r\n\r\n    const filters = {\r\n      advisorId: advisorId,\r\n    };\r\n\r\n    const totalQuery = this.contractsAdvisorRepository\r\n      .createQueryBuilder('contract_advisor')\r\n      .innerJoin('contract_advisor.contract', 'contract')\r\n      .innerJoin('contract.investor', 'investor')\r\n      .leftJoin('investor.owner', 'investorowner')\r\n      .leftJoin('investor.business', 'investorbusiness')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select('COUNT(DISTINCT contract.id)', 'count')\r\n      .where(filters)\r\n      .andWhere('contract.endContract >= :today', {\r\n        today: new Date(),\r\n      })\r\n      .andWhere('contract.status = :contractStatus', {\r\n        contractStatus: ContractStatusEnum.ACTIVE,\r\n      });\r\n\r\n    if (query.dateTo || query.dateFrom) {\r\n      const { start, end } = PaginatedQueryHelper.getDateRangeParams(query);\r\n\r\n      totalQuery.andWhere(\r\n        'contract_advisor.created_at BETWEEN :start AND :end',\r\n        {\r\n          start,\r\n          end,\r\n        },\r\n      );\r\n    }\r\n\r\n    if (query.contractType) {\r\n      totalQuery.andWhere('signataries.investmentModality = :contractType', {\r\n        contractType: query.contractType,\r\n      });\r\n    }\r\n\r\n    const { count } = await totalQuery.getRawOne();\r\n    const total = Number(count);\r\n\r\n    const activeInvestorsQuery = this.contractsAdvisorRepository\r\n      .createQueryBuilder('contract_advisor')\r\n      .innerJoin('contract_advisor.contract', 'contract')\r\n      .innerJoin('contract.investor', 'investor')\r\n      .leftJoin('investor.owner', 'investorowner')\r\n      .leftJoin('investor.business', 'investorbusiness')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select([\r\n        'investor.id as investorid',\r\n        'COALESCE(investorowner.name, investorbusiness.companyName) as name',\r\n        'COALESCE(investorowner.avatar, investorbusiness.avatar) as avatar',\r\n        'COALESCE(investorowner.cpf, investorbusiness.cnpj) as document',\r\n        'contract.createdAt as createdat',\r\n        'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0)) as totalcontractamount',\r\n      ])\r\n      .where(filters)\r\n      .andWhere('contract.endContract >= :today', {\r\n        today: new Date(),\r\n      })\r\n      .andWhere('contract.status = :contractStatus', {\r\n        contractStatus: ContractStatusEnum.ACTIVE,\r\n      });\r\n\r\n    if (query.dateTo || query.dateFrom) {\r\n      const { start, end } = PaginatedQueryHelper.getDateRangeParams(query);\r\n\r\n      activeInvestorsQuery.andWhere(\r\n        'contract_advisor.created_at BETWEEN :start AND :end',\r\n        {\r\n          start,\r\n          end,\r\n        },\r\n      );\r\n    }\r\n\r\n    if (query.contractType) {\r\n      activeInvestorsQuery.andWhere(\r\n        'signataries.investmentModality = :contractType',\r\n        {\r\n          contractType: query.contractType,\r\n        },\r\n      );\r\n    }\r\n\r\n    const activeInvestorsQueryResult = await activeInvestorsQuery\r\n      .groupBy(\r\n        'investor.id, investorowner.name, investorbusiness.companyName, investorowner.avatar, investorbusiness.avatar, investorowner.cpf, investorbusiness.cnpj, contract.createdAt',\r\n      )\r\n      .orderBy('totalcontractamount', 'DESC')\r\n      .offset(skip)\r\n      .limit(limit)\r\n      .getRawMany<{\r\n        investorid: string;\r\n        name: string;\r\n        avatar: string;\r\n        document: string;\r\n        totalcontractamount: string;\r\n        createdat: Date;\r\n      }>();\r\n\r\n    const totalCapturedQuery = this.contractsAdvisorRepository\r\n      .createQueryBuilder('contract_advisor')\r\n      .innerJoin('contract_advisor.contract', 'contract')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoin('contract.addendum', 'addendum', 'addendum.status = :status', {\r\n        status: AddendumStatus.FULLY_SIGNED,\r\n      })\r\n      .select(\r\n        'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0))',\r\n        'total',\r\n      )\r\n      .where(filters)\r\n      .andWhere('contract.endContract >= :today', {\r\n        today: new Date(),\r\n      })\r\n      .andWhere('contract.status = :contractStatus', {\r\n        contractStatus: ContractStatusEnum.ACTIVE,\r\n      });\r\n\r\n    if (query.dateTo || query.dateFrom) {\r\n      const { start, end } = PaginatedQueryHelper.getDateRangeParams(query);\r\n      totalCapturedQuery.andWhere(\r\n        'contract_advisor.created_at BETWEEN :start AND :end',\r\n        {\r\n          start,\r\n          end,\r\n        },\r\n      );\r\n    }\r\n\r\n    if (query.contractType) {\r\n      totalCapturedQuery.andWhere(\r\n        'signataries.investmentModality = :contractType',\r\n        {\r\n          contractType: query.contractType,\r\n        },\r\n      );\r\n    }\r\n\r\n    const { total: totalCaptured } = await totalCapturedQuery.getRawOne();\r\n\r\n    const result = activeInvestorsQueryResult.map((investor) => ({\r\n      investorId: investor.investorid,\r\n      name: investor.name,\r\n      avatar: investor.avatar,\r\n      document: investor.document,\r\n      createdAt: investor.createdat,\r\n      totalContractAmount: Number(investor.totalcontractamount),\r\n      totalCaptured: Number(totalCaptured),\r\n    }));\r\n\r\n    const response = PaginatedQueryHelper.createPaginatedResponse(\r\n      result,\r\n      total,\r\n      page,\r\n      limit,\r\n    );\r\n\r\n    await this.cacheManager.set(cacheKey, response, 60000);\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}