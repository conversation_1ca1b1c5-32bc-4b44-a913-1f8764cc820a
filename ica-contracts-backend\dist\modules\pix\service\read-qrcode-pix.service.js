"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadQRCodePixService = void 0;
const common_1 = require("@nestjs/common");
const pix_qrcode_celcoin_service_1 = require("../../../apis/celcoin/services/pix-qrcode-celcoin.service");
let ReadQRCodePixService = class ReadQRCodePixService {
    constructor(pixQRCodeCelcoin) {
        this.pixQRCodeCelcoin = pixQRCodeCelcoin;
    }
    async perform(data) {
        const responseCelcoin = await this.pixQRCodeCelcoin.readQRCode({
            emv: data.emv,
        });
        return responseCelcoin;
    }
};
exports.ReadQRCodePixService = ReadQRCodePixService;
exports.ReadQRCodePixService = ReadQRCodePixService = __decorate([
    __param(0, (0, common_1.Inject)(pix_qrcode_celcoin_service_1.PixQRCodeCelcoinService)),
    __metadata("design:paramtypes", [pix_qrcode_celcoin_service_1.PixQRCodeCelcoinService])
], ReadQRCodePixService);
//# sourceMappingURL=read-qrcode-pix.service.js.map