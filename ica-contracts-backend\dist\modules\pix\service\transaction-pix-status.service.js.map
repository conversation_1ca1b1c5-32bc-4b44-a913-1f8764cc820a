{"version": 3, "file": "transaction-pix-status.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/transaction-pix-status.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,uCAAoC;AACpC,oHAAyG;AACzG,6FAAoF;AACpF,qGAA4F;AAC5F,qCAA4C;AAKrC,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAEU,SAAoC,EAEpC,qBAAoD,EAEpD,cAA4C;QAJ5C,cAAS,GAAT,SAAS,CAA2B;QAEpC,0BAAqB,GAArB,qBAAqB,CAA+B;QAEpD,mBAAc,GAAd,cAAc,CAA8B;IACnD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAA0B,EAAE,EAAU;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAEnE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;aACZ;SACF,CAAC,CAAC;QAEH,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC;YACzE,UAAU,EAAE,WAAW,CAAC,IAAI;SAC7B,CAAC,CAAC;QAEH,OAAO;YACL,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,MAAM,EAAE,WAAW,CAAC,KAAK;YACzB,UAAU,EAAE,WAAW,CAAC,IAAI;YAC5B,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,cAAc,EAAE,WAAW,CAAC,IAAI;YAChC,WAAW,EAAE,WAAW,CAAC,IAAI;YAC7B,WAAW,EAAE;gBACX,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,GAAG,EAAE,WAAW,CAAC,UAAU;gBAC3B,OAAO,EAAE,WAAW,CAAC,cAAc;gBACnC,MAAM,EAAE,WAAW,CAAC,aAAa;gBACjC,KAAK,EAAE,WAAW,CAAC,eAAe;gBAClC,IAAI,EAAE,WAAW,CAAC,WAAW;gBAC7B,WAAW,EAAE,WAAW,CAAC,kBAAkB;aAC5C;YACD,UAAU,EAAE;gBACV,OAAO,EAAE,OAAO,CAAC,MAAM;gBACvB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI;gBAClD,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;gBACzD,WAAW,EAAE,MAAM;aACpB;YACD,SAAS,EAAE,IAAA,mBAAQ,EAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAC9C,MAAM;SACP,CAAC;IACJ,CAAC;CACF,CAAA;AA9DY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;qCAHlB,oBAAU;QAEE,oBAAU;QAEjB,8DAA4B;GAP3C,2BAA2B,CA8DvC", "sourcesContent": ["import { Inject, Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { addHours } from 'date-fns';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { TransactionStatusDto } from '../dto/get-transaction-status.dto';\r\n\r\n@Injectable()\r\nexport class TransactionPixStatusService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepository: Repository<TransactionEntity>,\r\n    @Inject(PixTransactionCelcoinService)\r\n    private celcoinService: PixTransactionCelcoinService,\r\n  ) {}\r\n\r\n  async execute(data: TransactionStatusDto, id: string): Promise<any> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    const transaction = await this.transactionRepository.findOne({\r\n      where: {\r\n        id: data.id,\r\n      },\r\n    });\r\n\r\n    const { body: result } = await this.celcoinService.getTransactionPixStatus({\r\n      clientCode: transaction.code,\r\n    });\r\n\r\n    return {\r\n      id: transaction.id,\r\n      amount: transaction.value,\r\n      clientCode: transaction.code,\r\n      endToEndId: transaction.endToEndId,\r\n      initiationType: transaction.type,\r\n      paymentType: transaction.type,\r\n      creditParty: {\r\n        bank: transaction.destinyBank,\r\n        key: transaction.destinyKey,\r\n        account: transaction.destinyAccount,\r\n        branch: transaction.destinyBranch,\r\n        taxId: transaction.destinyDocument,\r\n        name: transaction.destinyName,\r\n        accountType: transaction.destinyAccountType,\r\n      },\r\n      debitParty: {\r\n        account: account.number,\r\n        branch: account.branch,\r\n        taxId: account.owner?.cpf || account.business.cnpj,\r\n        name: account.owner?.name || account.business.companyName,\r\n        accountType: 'TRAN',\r\n      },\r\n      createdAt: addHours(transaction.createdAt, -3),\r\n      result,\r\n    };\r\n  }\r\n}\r\n"]}