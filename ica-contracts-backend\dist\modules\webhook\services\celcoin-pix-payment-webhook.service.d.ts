import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { ICelcoinPixPaymentWebhookDto } from '../dto/celcoin-pix-payment-webhook.dto';
export declare class CelcoinPixPaymentWebhookService {
    private accountRepository;
    private transactionRepository;
    constructor(accountRepository: Repository<AccountEntity>, transactionRepository: Repository<TransactionEntity>);
    perform(input: ICelcoinPixPaymentWebhookDto): Promise<void>;
}
