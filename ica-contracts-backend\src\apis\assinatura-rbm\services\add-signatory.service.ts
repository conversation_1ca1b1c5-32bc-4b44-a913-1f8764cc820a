import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import * as FormData from 'form-data';

import { IAdicionarSignatarioRequest } from '../requests/add-signatory.request';
import { AuthIntegrationService } from './auth.service';

@Injectable()
export class AddSignatoryService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly logger = new Logger(AddSignatoryService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authIntegrationService: AuthIntegrationService,
  ) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/documentos`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async addSignatory(data: IAdicionarSignatarioRequest) {
    const authResponse =
      await this.authIntegrationService.authenticateIntegration();
    const { token } = authResponse.jwt;

    const formData = new FormData();
    formData.append('cpfCnpj', data.cpfCnpj);
    formData.append('nome', data.nome);
    formData.append('tipoAss', data.tipoAss);
    formData.append('tipoAut', 'email');

    if (data.tipoAssComplemento)
      formData.append('tipoAssComplemento', data.tipoAssComplemento);
    if (data.grupoAssinaturaId)
      formData.append('grupoAssinaturaId', data.grupoAssinaturaId);
    if (data.ordemAssinatura)
      formData.append('ordemAssinatura', data.ordemAssinatura);
    if (data.email) formData.append('email', data.email);
    if (data.celular) formData.append('celular', data.celular);
    if (data.linkAssinaturaEmail)
      formData.append('linkAssinaturaEmail', data.linkAssinaturaEmail);
    if (data.linkAssinaturaSMS)
      formData.append('linkAssinaturaSMS', data.linkAssinaturaSMS);
    if (data.dataNasc) formData.append('dataNasc', data.dataNasc);
    if (data.selfieNecessaria)
      formData.append('selfieNecessaria', data.selfieNecessaria);
    if (data.certificadoDigitalNecessario)
      formData.append(
        'certificadoDigitalNecessario',
        data.certificadoDigitalNecessario,
      );
    if (data.documentoFrenteNecessario)
      formData.append(
        'documentoFrenteNecessario',
        data.documentoFrenteNecessario,
      );
    if (data.documentoVersoNecessario)
      formData.append(
        'documentoVersoNecessario',
        data.documentoVersoNecessario,
      );
    if (data.selfieComDocumentoNecessario)
      formData.append(
        'selfieComDocumentoNecessario',
        data.selfieComDocumentoNecessario,
      );
    if (data.serproRealizarValidacaoFacial)
      formData.append(
        'serproRealizarValidacaoFacial',
        data.serproRealizarValidacaoFacial,
      );
    if (data.faceMatch) formData.append('faceMatch', data.faceMatch);
    if (data.mensagem) formData.append('mensagem', data.mensagem);

    try {
      const response = await this.axiosInstance.post(
        `/addSig/${data.id}`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.data.erro) {
        this.logger.error(
          `Erro ao adicionar signatário: ${response.data.message}`,
        );
        throw new InternalServerErrorException(response.data.message);
      }

      return response.data;
    } catch (error) {
      this.logger.error('Erro ao adicionar signatário', error);

      if (error.response) {
        throw new InternalServerErrorException(
          `Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'Nenhuma resposta recebida da API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Erro na requisição: ${error.message}`,
        );
      }
    }
  }
}
