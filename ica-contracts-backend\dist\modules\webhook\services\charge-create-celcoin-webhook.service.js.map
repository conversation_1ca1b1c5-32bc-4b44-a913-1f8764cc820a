{"version": 3, "file": "charge-create-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/charge-create-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,2FAAkF;AAClF,iFAAuE;AACvE,mDAA2C;AAC3C,qCAAqC;AAK9B,IAAM,iCAAiC,GAAvC,MAAM,iCAAiC;IAC5C,YAEmB,sBAAgD;QAAhD,2BAAsB,GAAtB,sBAAsB,CAA0B;IAChE,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,KAAqC;QACjD,eAAM,CAAC,IAAI,CACT,mEAAmE,EACnE,KAAK,CACN,CAAC;QAEF,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAEzB,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE;oBACL,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa;oBACvC,MAAM,EAAE,qCAAgB,CAAC,UAAU;iBACpC;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CACT,sEAAsE,EACtE,MAAM,CACP,CAAC;YAEF,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CACtC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,EACjB;oBACE,MAAM,EAAE,qCAAgB,CAAC,MAAM;oBAC/B,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;oBAClC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;iBAC5C,CACF,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAtCY,8EAAiC;4CAAjC,iCAAiC;IAD7C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,4BAAY,CAAC,CAAA;qCACU,oBAAU;GAH1C,iCAAiC,CAsC7C", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { BilletEntity } from 'src/shared/database/typeorm/entities/billet.entity';\r\nimport { BoletoStatusEnum } from 'src/shared/enums/boleto-status.enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IChargeCreateCelcoinWebhookDto } from '../dto/charge-create-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class ChargeCreateCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(BilletEntity)\r\n    private readonly billetEntityRepository: Repository<BilletEntity>,\r\n  ) {}\r\n  async perform(input: IChargeCreateCelcoinWebhookDto) {\r\n    logger.info(\r\n      'ChargeCreateCelcoinWebhookService.perform() -> charge-create body',\r\n      input,\r\n    );\r\n\r\n    const { status } = input;\r\n\r\n    if (status === 'CREATED') {\r\n      const boleto = await this.billetEntityRepository.findOne({\r\n        where: {\r\n          transactionId: input.body.transactionId,\r\n          status: BoletoStatusEnum.PROCESSING,\r\n        },\r\n      });\r\n\r\n      logger.info(\r\n        'ChargeCreateCelcoinWebhookService.perform() -> charge-create: boleto',\r\n        boleto,\r\n      );\r\n\r\n      if (boleto) {\r\n        await this.billetEntityRepository.update(\r\n          { id: boleto.id },\r\n          {\r\n            status: BoletoStatusEnum.ACTIVE,\r\n            barcode: input.body.boleto.barCode,\r\n            metadata: JSON.stringify(input.body.boleto),\r\n          },\r\n        );\r\n      }\r\n    }\r\n  }\r\n}\r\n"]}