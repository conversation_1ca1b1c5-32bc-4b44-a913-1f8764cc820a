{"version": 3, "file": "get-pix-key-external.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/get-pix-key-external.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsD;AAEtD,MAAa,oBAAoB;CAIhC;AAJD,oDAIC;AADC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;iDACC", "sourcesContent": ["import { IsDefined, IsString } from 'class-validator';\r\n\r\nexport class GetPixKeyExternalDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  key: string;\r\n}\r\n"]}