import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as AWS from 'aws-sdk';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

@Injectable()
export class SendProfileImageService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
  ) {}
  async perform(id: string, file: Express.Multer.File) {
    const account = await this.accountDb.findOne({
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
      relations: {
        owner: true,
        business: true,
      },
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });

    const aleatoryNumber = Math.random() * 1000;

    const uploadS3 = await s3
      .upload({
        Bucket: 'new-invest-profile',
        Key: `${account.id}-${aleatoryNumber}.jpg`,
        Body: file.buffer,
        ACL: 'public-read',
        ContentType: 'image/jpeg',
      })
      .promise();

    await this.accountDb.update(account.id, {
      profileImage: uploadS3.Location,
    });
  }
}
