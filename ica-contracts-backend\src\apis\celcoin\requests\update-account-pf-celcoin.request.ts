export interface IAddressDTO {
  postalCode?: string;
  street?: string;
  number?: string;
  addressComplement?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  longitude?: string;
  latitude?: string;
}

export interface IUpdateCustomerApiRequest {
  Account?: string;
  DocumentNumber?: string;
  phoneNumber?: string;
  email?: string;
  socialName?: string;
  isPoliticallyExposedPerson?: boolean;
  address?: IAddressDTO;
  birthDate?: string;
}
