import { Inject, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  endOfDay,
  endOfMonth,
  isWeekend,
  startOfDay,
  startOfMonth,
} from 'date-fns';
import { BalanceCelcoinService } from 'src/apis/celcoin/services/balance-celcoin.service';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { RechargeEntity } from 'src/shared/database/typeorm/entities/recharge.entity';
import { ServiceFeeEntity } from 'src/shared/database/typeorm/entities/service-fee.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { ServiceFeePaymentStatusEnum } from 'src/shared/enums/service-fee-payment-status.enum';
import { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';
import { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';
import { Equal, Repository, Between } from 'typeorm';

export class GetAccountLimitService {
  constructor(
    @InjectRepository(AccountEntity)
    private readonly accountRepo: Repository<AccountEntity>,
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
    @InjectRepository(TransactionEntity)
    private transactionDb: Repository<TransactionEntity>,
    @InjectRepository(RechargeEntity)
    private rechargeDb: Repository<RechargeEntity>,
    @InjectRepository(ServiceFeeEntity)
    private serviceFee: Repository<ServiceFeeEntity>,
    @Inject(BalanceCelcoinService)
    private balanceCelcoin: BalanceCelcoinService,
  ) {}

  async execute(ownerId: string) {
    const account = await this.accountRepo.findOne({
      relations: { owner: true, business: true },
      where: [{ businessId: Equal(ownerId) }, { ownerId }, { id: ownerId }],
    });

    if (!account) throw new NotFoundException('Conta não encontrada!');

    let dailyLimit = 0;

    const document =
      account.type === 'physical' ? account.owner.cpf : account.business.cnpj;

    const balance = await this.balanceCelcoin.getBalance({
      DocumentNumber: document,
      Account: account.number,
    });

    let newDailyLimit = isWeekend(new Date()) ? 0 : Number(balance.body.amount);

    const recharges = await this.rechargeDb.find({
      where: {
        accountId: account.id,
        createdAt: Between(startOfDay(new Date()), endOfDay(new Date())),
      },
    });

    if (recharges.length > 0) {
      recharges.map((recharge) => {
        dailyLimit += Number(recharge.value);
      });
    }

    const dailyLimits = await this.accountLimitRepo.find({
      where: {
        accountId: account.id,
        createdAt: Between(startOfDay(new Date()), endOfDay(new Date())),
        active: true,
      },
    });

    if (dailyLimits.length > 0) {
      dailyLimits.map((limit) => {
        dailyLimit += Number(limit.dailyLimit);
        newDailyLimit += Number(limit.dailyLimit);
      });
    }

    const transactions = await this.transactionDb.find({
      where: [
        {
          accountId: account.id,
          type: TransactionMovementTypeEnum.PIX_CASHOUT,
          createdAt: Between(startOfDay(new Date()), endOfDay(new Date())),
          status: TransactionStatusEnum.DONE,
        },
        {
          accountId: account.id,
          type: TransactionMovementTypeEnum.TED_CASHOUT,
          createdAt: Between(startOfDay(new Date()), endOfDay(new Date())),
          status: TransactionStatusEnum.DONE,
        },
        {
          accountId: account.id,
          type: TransactionMovementTypeEnum.BOLETO,
          createdAt: Between(startOfDay(new Date()), endOfDay(new Date())),
          status: TransactionStatusEnum.DONE,
        },
        {
          accountId: account.id,
          type: TransactionMovementTypeEnum.INTERNAL_TRANSFER_CASHOUT,
          createdAt: Between(startOfDay(new Date()), endOfDay(new Date())),
          status: TransactionStatusEnum.DONE,
        },
        {
          accountId: account.id,
          type: TransactionMovementTypeEnum.PIX_MANUAL,
          createdAt: Between(startOfDay(new Date()), endOfDay(new Date())),
          status: TransactionStatusEnum.DONE,
        },
      ],
    });

    if (transactions.length > 0) {
      transactions.map((transaction) => {
        dailyLimit -= Number(transaction.value);
      });
    }

    const serviceFee = await this.serviceFee.find({
      relations: {
        account: true,
      },
      where: {
        account: {
          id: account.id,
        },
        payment_status: ServiceFeePaymentStatusEnum.PAID,
        last_payment_date: Between(
          startOfDay(new Date()),
          endOfDay(new Date()),
        ),
        created_at: Between(startOfMonth(new Date()), endOfMonth(new Date())),
      },
    });

    if (serviceFee.length > 0) {
      serviceFee.map((serviceFee) => {
        dailyLimit -= Number(serviceFee.debt_amount);
      });
    }

    let accountLimits = await this.accountLimitRepo.findOne({
      where: {
        accountId: account.id,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (!accountLimits) {
      accountLimits = await this.accountLimitRepo.save({
        accountId: account.id,
        dailyLimit: 0,
      });
    }

    return {
      dailyLimit: newDailyLimit.toFixed(2),
      oldLimit: dailyLimit,
      monthlyLimit: accountLimits.monthlyLimit,
      dailyNightLimit: accountLimits.dailyNightLimit,
      realBalance: balance.body.amount,
    };
  }
}
