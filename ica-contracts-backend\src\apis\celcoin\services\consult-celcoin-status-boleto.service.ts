import { Inject } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';

import { IConsultCelcoinBoletoResponse } from '../responses/consult-celcoin-boleto.response';
import { AuthCelcoinService } from './auth-celcoin.service';

export class ConsultCelcoinStatusBoleto {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async consultBoleto(transactionId: string) {
    const { accessToken: token } = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const { data }: AxiosResponse<IConsultCelcoinBoletoResponse> =
      await axios.get(
        `${process.env.CELCOIN_URL}/api-integration-baas-webservice/v1/charge?TransactionId=${transactionId}`,
        { headers: { Authorization: `Bearer ${token}` }, httpsAgent },
      );

    return data;
  }
}
