{"version": 3, "file": "broker.controller.js", "sourceRoot": "/", "sources": ["modules/broker/controllers/broker.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAYwB;AACxB,gFAA8D;AAC9D,iEAAwD;AACxD,0EAAgE;AAChE,kEAAyD;AAEzD,yFAAoF;AACpF,gEAA2D;AAC3D,4DAAuD;AACvD,6EAAwE;AACxE,yEAAoE;AACpE,yFAAmF;AACnF,2FAAoF;AACpF,uEAAkE;AAClE,qEAAuE;AACvE,qFAAgF;AAChF,qGAA+F;AAC/F,2HAAkH;AAClH,oEAA+D;AAC/D,mGAA4F;AAC5F,yHAAiH;AACjH,+FAAqG;AACrG,kGAAgG;AAChG,uGAAiG;AACjG,qHAA6G;AAE7G,iFAAuE;AACvE,mFAA0F;AAC1F,+EAAsF;AAK/E,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YACmB,mBAAwC,EACxC,wBAAkD,EAClD,wBAAkD,EAClD,iBAAoC,EACpC,mBAAwC,EACxC,gBAAkC,EAClC,sBAA8C,EAC9C,uBAAgD,EAChD,8BAA8D,EAC9D,sCAA8E,EAC9E,4BAA0D,EAC1D,uCAAgF,EAChF,sCAA8E,EAC9E,+BAAgE,EAChE,oCAA0E,EAC1E,gCAAkE;QAflE,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,mCAA8B,GAA9B,8BAA8B,CAAgC;QAC9D,2CAAsC,GAAtC,sCAAsC,CAAwC;QAC9E,iCAA4B,GAA5B,4BAA4B,CAA8B;QAC1D,4CAAuC,GAAvC,uCAAuC,CAAyC;QAChF,2CAAsC,GAAtC,sCAAsC,CAAwC;QAC9E,oCAA+B,GAA/B,+BAA+B,CAAiC;QAChE,yCAAoC,GAApC,oCAAoC,CAAsC;QAC1E,qCAAgC,GAAhC,gCAAgC,CAAkC;IAClF,CAAC;IAIE,AAAN,KAAK,CAAC,MAAM,CAAS,eAAgC;QACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAGvE,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACxD,MAAM,EAAE,uBAAuB,EAAE,GAAG,OAAO,CAAC,iEAAiE,CAAC,CAAC;QAC/G,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAGrC,MAAM,2BAA2B,GAAG,IAAI,CAAC,mBAAmB,CAAC,6BAA6B,CAAC,CAAC;QAC5F,MAAM,cAAc,GAAG,MAAM,2BAA2B,CAAC,OAAO,CAAC;YAC/D,KAAK,EAAE;gBACL,OAAO,EAAE,MAAM,CAAC,EAAE;gBAClB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aACzB;YACD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SAC1B,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,EAAE,CAAC;YAEpB,MAAM,sBAAsB,GAAG,MAAM,2BAA2B,CAAC,OAAO,CAAC;gBACvE,KAAK,EAAE;oBACL,UAAU,EAAE,MAAM,CAAC,EAAE;oBACrB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBACzB;gBACD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,yDAAyD,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC;gBAClG,OAAO;oBACL,GAAG,MAAM;oBACT,gBAAgB,EAAE,sBAAsB,CAAC,EAAE;oBAC3C,OAAO,EAAE,sDAAsD,sBAAsB,CAAC,EAAE,0BAA0B;iBACnH,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,yDAAyD,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;YAC1F,OAAO;gBACL,GAAG,MAAM;gBACT,gBAAgB,EAAE,cAAc,CAAC,EAAE;gBACnC,OAAO,EAAE,sDAAsD,cAAc,CAAC,EAAE,0BAA0B;aAC3G,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAIK,AAAN,KAAK,CAAC,kBAAkB,CAAY,OAAoB;QACtD,MAAM,cAAc,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;QAEpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC3E,OAAO,MAAM,CAAC;IAChB,CAAC;IAIK,AAAN,KAAK,CAAC,kBAAkB,CAAY,OAAoB;QACtD,MAAM,cAAc,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;QAEpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC3E,OAAO,MAAM,CAAC;IAChB,CAAC;IAIK,AAAN,KAAK,CAAC,UAAU,CACH,OAAoB,EAE/B,IAAmB;QAEnB,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAGK,AAAN,KAAK,CAAC,YAAY,CACL,OAAoB,EAE/B,IAAmB;QAEnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7E,OAAO,MAAM,CAAC;IAChB,CAAC;IAGK,AAAN,KAAK,CAAC,SAAS,CAAY,OAAoB;QAC7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YACvD,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;SACzB,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IAKK,AAAN,KAAK,CAAC,mBAAmB,CACZ,OAAoB,EACtB,OAA+B;QAExC,OAAO,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;IAKK,AAAN,KAAK,CAAC,gBAAgB,CAAoB,QAAgB;QACxD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpE,OAAO,MAAM,CAAC;IAChB,CAAC;IAKK,AAAN,KAAK,CAAC,OAAO,CAAoB,QAAgB;QAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3E,OAAO,MAAM,CAAC;IAChB,CAAC;IAIK,AAAN,KAAK,CAAC,WAAW,CAAoB,QAAgB;QACnD,IAAI,CAAC;YAEH,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACxD,MAAM,EAAE,uBAAuB,EAAE,GAAG,OAAO,CAAC,iEAAiE,CAAC,CAAC;YAC/G,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YAErC,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,QAAQ,CAAC,CAAC;YAG/D,MAAM,2BAA2B,GAAG,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;YAGzF,MAAM,SAAS,GAAG,MAAM,2BAA2B,CAAC,OAAO,CAAC;gBAC1D,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE;gBAC9B,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACnE,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;oBACrC,EAAE,EAAE,SAAS,CAAC,EAAE;oBAChB,OAAO,EAAE,SAAS,CAAC,OAAO;oBAC1B,UAAU,EAAE,SAAS,CAAC,UAAU;oBAChC,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI;iBAC/B,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,2BAA2B,CAAC,OAAO,CAAC;gBAC7D,KAAK,EAAE;oBACL,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC;oBACnB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBACzB;gBACD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAEzE,OAAO;gBACL,QAAQ;gBACR,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;oBACrB,EAAE,EAAE,SAAS,CAAC,EAAE;oBAChB,OAAO,EAAE,SAAS,CAAC,OAAO;oBAC1B,UAAU,EAAE,SAAS,CAAC,UAAU;oBAChC,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI;iBAC/B,CAAC,CAAC,CAAC,IAAI;gBACR,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC;oBAC3B,EAAE,EAAE,YAAY,CAAC,EAAE;oBACnB,OAAO,EAAE,YAAY,CAAC,OAAO;oBAC7B,UAAU,EAAE,YAAY,CAAC,UAAU;oBACnC,MAAM,EAAE,YAAY,CAAC,MAAM;oBAC3B,QAAQ,EAAE,YAAY,CAAC,IAAI,EAAE,IAAI;iBAClC,CAAC,CAAC,CAAC,IAAI;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACrC,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,YAAY,CAAoB,QAAgB;QACpD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sBAAa,CACrB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,EAC1C,KAAK,CAAC,MAAM,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,0BAA0B,CACX,QAAgB,EAC1B,IAA0C;QAEnD,OAAO,IAAI,CAAC,uCAAuC,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC9E,CAAC;IAIK,AAAN,KAAK,CAAC,oBAAoB,CACX,kBAA0B,EACpB,QAAgB;QAEnC,OAAO,IAAI,CAAC,sCAAsC,CAAC,OAAO,CACxD,kBAAkB,EAClB,QAAQ,CACT,CAAC;IACJ,CAAC;IAIK,AAAN,KAAK,CAAC,qBAAqB,CACN,QAAgB,EAC3B,IAAuB;QAE/B,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;IAIK,AAAN,KAAK,CAAC,8BAA8B,CACf,QAAgB,EAC3B,IAAuB;QAE/B,OAAO,IAAI,CAAC,sCAAsC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAIK,AAAN,KAAK,CAAC,uBAAuB,CACR,QAAgB,EAC3B,IAAuB;QAE/B,OAAO,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtE,CAAC;IAKK,AAAN,KAAK,CAAC,uBAAuB,CACL,WAAmB,EACxB,MAAqB,EACf,YAA+B;QAEtD,OAAO,IAAI,CAAC,oCAAoC,CAAC,OAAO,CACtD,WAAW,EACX,MAAM,EACN,YAAY,CACb,CAAC;IACJ,CAAC;CACF,CAAA;AA7RY,4CAAgB;AAsBrB;IAFL,IAAA,aAAI,GAAE;IACN,IAAA,uBAAK,EAAC,sBAAS,CAAC,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC;IAC/B,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAkB,mCAAe;;8CA8CpD;AAIK;IAFL,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IACvB,IAAA,YAAG,EAAC,UAAU,CAAC;IACU,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;0DAKlC;AAIK;IAFL,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IACvB,IAAA,YAAG,EAAC,YAAY,CAAC;IACQ,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;0DAKlC;AAIK;IAFL,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IACvB,IAAA,cAAK,EAAC,EAAE,CAAC;IAEP,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,+BAAa;;kDAGpB;AAGK;IADL,IAAA,YAAG,EAAC,UAAU,CAAC;IAEb,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;6CACD,+BAAa;;oDAIpB;AAGK;IADL,IAAA,YAAG,EAAC,WAAW,CAAC;IACA,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;iDAKzB;AAKK;IAHL,IAAA,YAAG,EAAC,yBAAyB,CAAC;IAC9B,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IAErB,WAAA,IAAA,gBAAO,GAAE,CAAA;IACT,WAAA,IAAA,cAAK,GAAE,CAAA;;6CAAU,2CAAsB;;2DAGzC;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IACvB,IAAA,YAAG,EAAC,6BAA6B,CAAC;IACX,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;;;;wDAGxC;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IACvB,IAAA,YAAG,EAAC,qCAAqC,CAAC;IAC5B,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;;;;+CAG/B;AAIK;IAFL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,YAAG,EAAC,iBAAiB,CAAC;IACJ,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;;;;mDAgEnC;AAIK;IAFL,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,YAAG,EAAC,WAAW,CAAC;IACG,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;;;;oDASpC;AAIK;IAFL,IAAA,YAAG,EAAC,qCAAqC,CAAC;IAC1C,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IAErB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,cAAK,GAAE,CAAA;;6CAAQ,wEAAmC;;kEAGpD;AAIK;IAFL,IAAA,YAAG,EAAC,yCAAyC,CAAC;IAC9C,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IAErB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;;;;4DAMnB;AAIK;IAFL,IAAA,aAAI,EAAC,2BAA2B,CAAC;IACjC,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IAErB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAO,uCAAiB;;6DAGhC;AAIK;IAFL,IAAA,aAAI,EAAC,qCAAqC,CAAC;IAC3C,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IAErB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAO,uCAAiB;;sEAGhC;AAIK;IAFL,IAAA,aAAI,EAAC,6BAA6B,CAAC;IACnC,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IAErB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAO,uCAAiB;;+DAGhC;AAKK;IAHL,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;IAClC,IAAA,uBAAK,EAAC,sBAAS,CAAC,MAAM,CAAC;IACvB,IAAA,YAAG,EAAC,gCAAgC,CAAC;IAEnC,WAAA,IAAA,cAAK,EAAC,aAAa,CAAC,CAAA;IACpB,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,cAAK,EAAC,cAAc,CAAC,CAAA;;;;+DAOvB;2BA5RU,gBAAgB;IAF5B,IAAA,mBAAU,EAAC,QAAQ,CAAC;IACpB,IAAA,kBAAS,EAAC,6BAAY,EAAE,sBAAS,CAAC;qCAGO,2CAAmB;QACd,sDAAwB;QACxB,uDAAwB;QAC/B,uCAAiB;QACf,2CAAmB;QACtB,qCAAgB;QACV,0CAAsB;QACrB,mDAAuB;QAChB,kEAA8B;QACtB,qFAAsC;QAChD,+DAA4B;QACjB,wEAAuC;QACxC,oFAAsC;QAC7C,oEAA+B;QAC1B,gFAAoC;QACxC,yDAAgC;GAjB1E,gBAAgB,CA6R5B", "sourcesContent": ["import {\r\n  Body,\r\n  Controller,\r\n  Get,\r\n  HttpException,\r\n  Param,\r\n  Patch,\r\n  Post,\r\n  Put,\r\n  Query,\r\n  Request,\r\n  UseGuards,\r\n} from '@nestjs/common';\r\nimport { Roles } from 'src/shared/decorators/roles.decorator';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\nimport { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';\r\nimport { RoleGuard } from 'src/shared/guards/role.guard';\r\n\r\nimport { EditInvestorService } from '../../investor/services/edit-investor.service';\r\nimport { CreateBrokerDto } from '../dto/create-broker.dto';\r\nimport { EditBrokerDto } from '../dto/edit-broker.dto';\r\nimport { CreateBrokerService } from '../services/create-broker.service';\r\nimport { EditBrokerService } from '../services/edit-broker.service';\r\nimport { GetBrokerAdvisorsService } from '../services/get-broker-advisors.service';\r\nimport { GetBrokerInvestorService } from '../services/get-broker-investors.service';\r\nimport { GetBrokerService } from '../services/get-broker.service';\r\nimport { BrokerDashboardService } from '../services/dashboard.service';\r\nimport { ContractsCaptureService } from '../services/contracts-capture.service';\r\nimport { ContractsMonthlyCaptureService } from '../services/contracts-monthly-capture.service';\r\nimport { GetOneScheduledPaymentForBrokerService } from '../services/get-one-scheduled-payment-for-broker.service';\r\nimport { GenerateReportDto } from '../dto/generate-report.dto';\r\nimport { GeneratePaymentReportService } from '../services/generate-payment-reports.service';\r\nimport { GenerateScheduledPaymentsReportService } from '../services/generate-scheduled-payments-reports.service';\r\nimport { ListIncomePaymentScheduledBrokerService } from '../services/list-payment-scheduled.service';\r\nimport { ListIncomePaymentScheduledBrokerDto } from '../dto/list-payment-scheduled.request.dto';\r\nimport { GenerateContractsReportsService } from '../services/generate-contracts-reports.service';\r\nimport { GetBrokerContractsGrowthChartService } from '../services/get-broker-contracts-growth-chart.service';\r\nimport { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\nimport { ListActiveInvestorsDto } from 'src/modules/income-report/dto/list-investors.dto';\r\nimport { ListActiveInvestorsBrokerService } from '../services/list-investors.service';\r\nimport { IRequestUser } from 'src/shared/interfaces/request-user.interface';\r\n\r\n@Controller('broker')\r\n@UseGuards(JwtAuthGuard, RoleGuard)\r\nexport class BrokerController {\r\n  constructor(\r\n    private readonly createBrokerService: CreateBrokerService,\r\n    private readonly getBrokerAdvisorsService: GetBrokerAdvisorsService,\r\n    private readonly getBrokerInvestorService: GetBrokerInvestorService,\r\n    private readonly editBrokerService: EditBrokerService,\r\n    private readonly editInvestorService: EditInvestorService,\r\n    private readonly getBrokerService: GetBrokerService,\r\n    private readonly brokerDashboardService: BrokerDashboardService,\r\n    private readonly contractsCaptureService: ContractsCaptureService,\r\n    private readonly contractsMonthlyCaptureService: ContractsMonthlyCaptureService,\r\n    private readonly getOneScheduledPaymentForBrokerService: GetOneScheduledPaymentForBrokerService,\r\n    private readonly generatePaymentReportService: GeneratePaymentReportService,\r\n    private readonly listIncomePaymentScheduledBrokerService: ListIncomePaymentScheduledBrokerService,\r\n    private readonly generateScheduledPaymentsReportService: GenerateScheduledPaymentsReportService,\r\n    private readonly generateContractsReportsService: GenerateContractsReportsService,\r\n    private readonly getBrokerContractsGrowthChartService: GetBrokerContractsGrowthChartService,\r\n    private readonly listActiveInvestorsBrokerService: ListActiveInvestorsBrokerService,\r\n  ) {}\r\n\r\n  @Post()\r\n  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)\r\n  async create(@Body() createBrokerDto: CreateBrokerDto) {\r\n    const result = await this.createBrokerService.perform(createBrokerDto);\r\n\r\n    // Buscar o ID da relação criada para retornar na resposta\r\n    const { InjectRepository } = require('@nestjs/typeorm');\r\n    const { OwnerRoleRelationEntity } = require('src/shared/database/typeorm/entities/owner-role-relation.entity');\r\n    const { Equal } = require('typeorm');\r\n\r\n    // Buscar a relação criada\r\n    const ownerRoleRelationRepository = this.createBrokerService['ownerRoleRelationRepository'];\r\n    const brokerRelation = await ownerRoleRelationRepository.findOne({\r\n      where: {\r\n        ownerId: result.id,\r\n        role: { name: 'broker' }\r\n      },\r\n      relations: { role: true }\r\n    });\r\n\r\n    if (!brokerRelation) {\r\n      // Tentar buscar por businessId se não encontrou por ownerId\r\n      const brokerRelationBusiness = await ownerRoleRelationRepository.findOne({\r\n        where: {\r\n          businessId: result.id,\r\n          role: { name: 'broker' }\r\n        },\r\n        relations: { role: true }\r\n      });\r\n\r\n      if (brokerRelationBusiness) {\r\n        console.log('🎯 Broker created! Use this ID for advisor vinculation:', brokerRelationBusiness.id);\r\n        return {\r\n          ...result,\r\n          brokerRelationId: brokerRelationBusiness.id,\r\n          message: `Broker created successfully! Use brokerRelationId (${brokerRelationBusiness.id}) to vinculate advisors.`\r\n        };\r\n      }\r\n    } else {\r\n      console.log('🎯 Broker created! Use this ID for advisor vinculation:', brokerRelation.id);\r\n      return {\r\n        ...result,\r\n        brokerRelationId: brokerRelation.id,\r\n        message: `Broker created successfully! Use brokerRelationId (${brokerRelation.id}) to vinculate advisors.`\r\n      };\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n  @Roles(RolesEnum.BROKER)\r\n  @Get('/advisor')\r\n  async getBrokersAdvisors(@Request() request:IRequestUser) {\r\n    const getAdvisorsDto = { ownerId: request.user.id };\r\n\r\n    const result = await this.getBrokerAdvisorsService.perform(getAdvisorsDto);\r\n    return result;\r\n  }\r\n\r\n  @Roles(RolesEnum.BROKER)\r\n  @Get('/investors')\r\n  async getBrokersInvestor(@Request() request:IRequestUser) {\r\n    const getAdvisorsDto = { ownerId: request.user.id };\r\n\r\n    const result = await this.getBrokerInvestorService.perform(getAdvisorsDto);\r\n    return result;\r\n  }\r\n\r\n  @Roles(RolesEnum.BROKER)\r\n  @Patch('')\r\n  async editBroker(\r\n    @Request() request:IRequestUser,\r\n    @Body()\r\n    body: EditBrokerDto,\r\n  ) {\r\n    await this.editBrokerService.perform(body);\r\n  }\r\n\r\n  @Put('investor')\r\n  async editInvestor(\r\n    @Request() request:IRequestUser,\r\n    @Body()\r\n    body: EditBrokerDto,\r\n  ) {\r\n    const result = await this.editInvestorService.perform(body, request.user.id);\r\n    return result;\r\n  }\r\n\r\n  @Get('dashboard')\r\n  async dashboard(@Request() request:IRequestUser) {\r\n    const result = await this.brokerDashboardService.perform({\r\n      ownerId: request.user.id,\r\n    });\r\n    return result;\r\n  }\r\n\r\n  @Get('income-report/investors')\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.BROKER)\r\n  async findActiveInvestors(\r\n    @Request() request:IRequestUser,\r\n    @Query() filters: ListActiveInvestorsDto,\r\n  ) {\r\n    return this.listActiveInvestorsBrokerService.perform(request.user.id, filters);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.BROKER)\r\n  @Get('contracts-capture/:brokerId')\r\n  async contractsCapture(@Param('brokerId') brokerId: string) {\r\n    const result = await this.contractsCaptureService.perform(brokerId);\r\n    return result;\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.BROKER)\r\n  @Get('contracts-monthly-capture/:brokerId')\r\n  async grafics(@Param('brokerId') brokerId: string) {\r\n    const result = await this.contractsMonthlyCaptureService.perform(brokerId);\r\n    return result;\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Get('debug/:brokerId')\r\n  async debugBroker(@Param('brokerId') brokerId: string) {\r\n    try {\r\n      // Importar os repositórios necessários para debug\r\n      const { InjectRepository } = require('@nestjs/typeorm');\r\n      const { OwnerRoleRelationEntity } = require('src/shared/database/typeorm/entities/owner-role-relation.entity');\r\n      const { Equal } = require('typeorm');\r\n\r\n      console.log('Debug - Searching for broker with ID:', brokerId);\r\n\r\n      // Fazer consulta direta para debug\r\n      const ownerRoleRelationRepository = this.getBrokerService['ownerRoleRelationRepository'];\r\n\r\n      // Consulta sem filtro de role\r\n      const anyRecord = await ownerRoleRelationRepository.findOne({\r\n        where: { id: Equal(brokerId) },\r\n        relations: { role: true }\r\n      });\r\n\r\n      console.log('Debug - Any record found:', anyRecord ? 'YES' : 'NO');\r\n      if (anyRecord) {\r\n        console.log('Debug - Record details:', {\r\n          id: anyRecord.id,\r\n          ownerId: anyRecord.ownerId,\r\n          businessId: anyRecord.businessId,\r\n          roleId: anyRecord.roleId,\r\n          roleName: anyRecord.role?.name\r\n        });\r\n      }\r\n\r\n      // Consulta com filtro de role\r\n      const brokerRecord = await ownerRoleRelationRepository.findOne({\r\n        where: {\r\n          id: Equal(brokerId),\r\n          role: { name: 'broker' }\r\n        },\r\n        relations: { role: true }\r\n      });\r\n\r\n      console.log('Debug - Broker record found:', brokerRecord ? 'YES' : 'NO');\r\n\r\n      return {\r\n        brokerId,\r\n        anyRecord: anyRecord ? {\r\n          id: anyRecord.id,\r\n          ownerId: anyRecord.ownerId,\r\n          businessId: anyRecord.businessId,\r\n          roleId: anyRecord.roleId,\r\n          roleName: anyRecord.role?.name\r\n        } : null,\r\n        brokerRecord: brokerRecord ? {\r\n          id: brokerRecord.id,\r\n          ownerId: brokerRecord.ownerId,\r\n          businessId: brokerRecord.businessId,\r\n          roleId: brokerRecord.roleId,\r\n          roleName: brokerRecord.role?.name\r\n        } : null\r\n      };\r\n    } catch (error) {\r\n      console.error('Debug error:', error);\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Get(':brokerId')\r\n  async getOneBroker(@Param('brokerId') brokerId: string) {\r\n    try {\r\n      return await this.getBrokerService.perform(brokerId);\r\n    } catch (error) {\r\n      throw new HttpException(\r\n        { error: error.response || error.message },\r\n        error.status,\r\n      );\r\n    }\r\n  }\r\n\r\n  @Get('/:brokerId/income-payment-scheduled')\r\n  @Roles(RolesEnum.BROKER)\r\n  async listPaymentScheduledBroker(\r\n    @Param('brokerId') brokerId: string,\r\n    @Query() data?: ListIncomePaymentScheduledBrokerDto,\r\n  ) {\r\n    return this.listIncomePaymentScheduledBrokerService.perform(brokerId, data);\r\n  }\r\n\r\n  @Get('/:brokerId/income-payment-scheduled/:id')\r\n  @Roles(RolesEnum.BROKER)\r\n  async listPaymentScheduled(\r\n    @Param('id') paymentScheduledId: string,\r\n    @Param('brokerId') brokerId: string,\r\n  ) {\r\n    return this.getOneScheduledPaymentForBrokerService.perform(\r\n      paymentScheduledId,\r\n      brokerId,\r\n    );\r\n  }\r\n\r\n  @Post('/:brokerId/payment-report')\r\n  @Roles(RolesEnum.BROKER)\r\n  async generatePaymentReport(\r\n    @Param('brokerId') brokerId: string,\r\n    @Body() data: GenerateReportDto,\r\n  ) {\r\n    return this.generatePaymentReportService.perform(data, brokerId);\r\n  }\r\n\r\n  @Post('/:brokerId/scheduled-payment-report')\r\n  @Roles(RolesEnum.BROKER)\r\n  async generateScheduledPaymentReport(\r\n    @Param('brokerId') brokerId: string,\r\n    @Body() data: GenerateReportDto,\r\n  ) {\r\n    return this.generateScheduledPaymentsReportService.perform(data, brokerId);\r\n  }\r\n\r\n  @Post('/:brokerId/contracts-report')\r\n  @Roles(RolesEnum.BROKER)\r\n  async generateContractsReport(\r\n    @Param('brokerId') brokerId: string,\r\n    @Body() data: GenerateReportDto,\r\n  ) {\r\n    return this.generateContractsReportsService.perform(data, brokerId);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard, RoleGuard)\r\n  @Roles(RolesEnum.BROKER)\r\n  @Get('/:ownerRoleId/contracts-growth')\r\n  async getContractsGrowthChart(\r\n    @Param('ownerRoleId') ownerRoleId: string,\r\n    @Query('period') period?: PeriodFilter,\r\n    @Query('contractType') contractType?: ContractTypeEnum,\r\n  ) {\r\n    return this.getBrokerContractsGrowthChartService.perform(\r\n      ownerRoleId,\r\n      period,\r\n      contractType,\r\n    );\r\n  }\r\n}\r\n"]}