"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportReportRechargesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const axios_1 = require("axios");
const date_fns_1 = require("date-fns");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const typeorm_2 = require("typeorm");
let ExportReportRechargesService = class ExportReportRechargesService {
    constructor(accountDb) {
        this.accountDb = accountDb;
    }
    async perform(data) {
        const table = [];
        const recharges = await axios_1.default
            .get(`${process.env.TRANSACTIONS_URL}/transactions/all`, {
            params: {
                datefrom: data.from,
                dateto: data.to,
            },
            headers: {
                'x-api-key': process.env.API_KEY,
            },
        })
            .then((response) => response.data)
            .catch((err) => {
            throw new common_1.BadRequestException('Erro na consulta', err.message);
        });
        const promises = recharges
            .filter((recharge) => recharge.type === data.type)
            .map(async (recharge) => {
            const account = await this.accountDb.findOne({
                relations: {
                    owner: true,
                    business: true,
                },
                where: {
                    id: recharge.accountId,
                },
            });
            table.push({
                name: account.owner?.name || account.business.companyName,
                document: account.owner?.cpf || account.business.cnpj,
                amount: Number(recharge.amount).toFixed(2),
                date: (0, date_fns_1.format)(recharge.date, 'dd/MM/yyyy'),
            });
        });
        await Promise.all(promises);
        return this.generateHtmlTable(table);
    }
    generateHtmlTable(data) {
        const rows = data
            .map((item) => `
        <tr>
          <td>${item.name}</td>
          <td>${item.document}</td>
          <td>R$${item.amount}</td>
          <td>${item.date}</td>
        </tr>
      `)
            .join('');
        return `
        <html>
        <head>
          <style>
            table {
              width: 100%;
              border-collapse: collapse;
            }
            th, td {
              border: 1px solid black;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f2f2f2;
            }
          </style>
        </head>
        <body>
          <table>
            <thead>
              <tr>
                <th>Nome</th>
                <th>Documento</th>
                <th>Valor</th>
                <th>Data</th>
              </tr>
            </thead>
            <tbody>
              ${rows}
            </tbody>
          </table>
        </body>
        </html>
      `;
    }
};
exports.ExportReportRechargesService = ExportReportRechargesService;
exports.ExportReportRechargesService = ExportReportRechargesService = __decorate([
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ExportReportRechargesService);
//# sourceMappingURL=export-report-recharges.service.js.map