{"version": 3, "file": "ted.module.js", "sourceRoot": "/", "sources": ["modules/ted/ted.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,wDAAkD;AAClD,8DAAwD;AAExD,2GAAqG;AACrG,sFAAgF;AAChF,wFAAkF;AAClF,iEAA6D;AAC7D,wGAAiG;AACjG,kGAA2F;AAC3F,gFAA0E;AAC1E,gEAA2D;AAC3D,oGAA6F;AAC7F,kEAA6D;AAoBtD,IAAM,SAAS,GAAf,MAAM,SAAS;CAAG,CAAA;AAAZ,8BAAS;oBAAT,SAAS;IAlBrB,IAAA,eAAM,EAAC;QACN,WAAW,EAAE,CAAC,8BAAa,CAAC;QAC5B,OAAO,EAAE;YACP,4BAAY;YACZ,wBAAU;YACV,0DAA0B;YAC1B,4CAAmB;SACpB;QACD,SAAS,EAAE;YACT,iCAAc;YACd,+BAAa;YACb,8CAAoB;YACpB,iEAA6B;YAC7B,+DAA4B;YAC5B,qEAA+B;YAC/B,8CAAoB;SACrB;KACF,CAAC;GACW,SAAS,CAAG", "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { ApisModule } from 'src/apis/apis.module';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { AccountTransferLimitModule } from '../account-transfer-limit/account-transfer-limit.module';\r\nimport { TwoFactorAuthModule } from '../two-factor-auth/two-factor-auth.module';\r\nimport { TwoFactorAuthService } from '../two-factor-auth/two-factor-auth.service';\r\nimport { TedController } from './controllers/ted.controller';\r\nimport { DeleteFavoriteTedContactService } from './services/delete-favorite-ted-contact.service';\r\nimport { GetFavoriteTedContactService } from './services/get-favorite-ted-contact.service';\r\nimport { GetRecentTedsService } from './services/get-recent-teds.service';\r\nimport { GetTedService } from './services/get-ted.service';\r\nimport { SaveFavoriteTedContactService } from './services/save-favorite-ted-contact.service';\r\nimport { SendTedService } from './services/send-ted.service';\r\n\r\n@Module({\r\n  controllers: [TedController],\r\n  imports: [\r\n    SharedModule,\r\n    ApisModule,\r\n    AccountTransferLimitModule,\r\n    TwoFactorAuthModule,\r\n  ],\r\n  providers: [\r\n    SendTedService,\r\n    GetTedService,\r\n    GetRecentTedsService,\r\n    SaveFavoriteTedContactService,\r\n    GetFavoriteTedContactService,\r\n    DeleteFavoriteTedContactService,\r\n    TwoFactorAuthService,\r\n  ],\r\n})\r\nexport class TedModule {}\r\n"]}