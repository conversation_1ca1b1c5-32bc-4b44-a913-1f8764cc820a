import type { GetIncomeReportDetailsUseCase } from '@/contexts/income-report/application/usecases/get-income-report-details.usecase'
import { badRequest, ok, serverError } from '../helpers/http-helper'
import type { HttpRequest, IController } from '../protocols'

export class DetailsIncomeReportController implements IController {
  constructor(private useCase: GetIncomeReportDetailsUseCase) {}

  async handle(request: HttpRequest) {
    if (!request.body) {
      return badRequest(new Error('Body não informado'))
    }

    if (!request.body?.investorId) {
      return badRequest(new Error('Investidores não informado'))
    }

    const currencyYear = new Date().getFullYear()
    if (request.body.year >= currencyYear) {
      return badRequest(
        new Error('O ano informado deve ser menor que o ano atual.')
      )
    }

    try {
      const response = await this.useCase.execute(
        request.body.investorId,
        request.body.year
      )

      return ok(response)
    } catch (error: any) {
      return serverError(error)
    }
  }
}
