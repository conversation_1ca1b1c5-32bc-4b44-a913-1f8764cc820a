import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { InternalTransactionDto } from '../dto/internal-transaction.dto';
import { IInternalTransactionResponse } from '../response/internal-transaction.response';
export declare class InternalTransactionService {
    private accountDb;
    private transactionRepository;
    private celcoinService;
    private getLimit;
    constructor(accountDb: Repository<AccountEntity>, transactionRepository: Repository<TransactionEntity>, celcoinService: PixTransactionCelcoinService, getLimit: GetAccountLimitService);
    perform(data: InternalTransactionDto, id: string): Promise<IInternalTransactionResponse>;
}
