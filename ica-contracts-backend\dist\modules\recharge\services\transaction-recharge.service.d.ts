import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { CreateTransactionsService } from 'src/apis/icainvest-credit/services/create-virtual-transaction.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { RechargeEntity } from 'src/shared/database/typeorm/entities/recharge.entity';
import { Repository } from 'typeorm';
import { TransactionRechargeDto } from '../dto/transaction-recharge.dto';
export declare class TransactionRechargeService {
    private accountDb;
    private rechargeDb;
    private transactionPixApi;
    private readonly createTransactionsService;
    constructor(accountDb: Repository<AccountEntity>, rechargeDb: Repository<RechargeEntity>, transactionPixApi: PixTransactionCelcoinService, createTransactionsService: CreateTransactionsService);
    perform(data: TransactionRechargeDto): Promise<import("../../../apis/icainvest-credit/responses/create-transaction.response").ICreateTransactionResponse>;
}
