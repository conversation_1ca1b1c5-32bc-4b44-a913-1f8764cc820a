{"version": 3, "file": "charge-in-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/charge-in-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,2FAAkF;AAClF,iFAAuE;AACvE,qCAAqC;AAK9B,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IACxC,YAEmB,sBAAgD;QAAhD,2BAAsB,GAAtB,sBAAsB,CAA0B;IAChE,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,KAAiC;QAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAEzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YACvD,KAAK,EAAE;gBACL,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa;aACxC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CACT,4BAA4B,KAAK,CAAC,IAAI,CAAC,aAAa,iBAAiB,CACtE,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;gBAChE,KAAK,EAAE;oBACL,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa;oBACvC,MAAM,EAAE,qCAAgB,CAAC,MAAM;iBAChC;aACF,CAAC,CAAC;YAEH,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CACtC,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE,EAC1B,EAAE,MAAM,EAAE,qCAAgB,CAAC,IAAI,EAAE,CAClC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;YACjD,OAAO,CAAC,GAAG,CACT,kBAAkB,MAAM,CAAC,EAAE,8BAA8B,EACzD,KAAK,CACN,CAAC;YACF,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CACtC,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,EACjB,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CACzB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAhDY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,4BAAY,CAAC,CAAA;qCACU,oBAAU;GAH1C,6BAA6B,CAgDzC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { BilletEntity } from 'src/shared/database/typeorm/entities/billet.entity';\r\nimport { BoletoStatusEnum } from 'src/shared/enums/boleto-status.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { IChargeInCelcoinWebhookDto } from '../dto/charge-in-celcoin-webhook.dto';\r\n\r\n@Injectable()\r\nexport class ChargeInCelcoinWebhookService {\r\n  constructor(\r\n    @InjectRepository(BilletEntity)\r\n    private readonly billetEntityRepository: Repository<BilletEntity>,\r\n  ) {}\r\n  async perform(input: IChargeInCelcoinWebhookDto) {\r\n    const { status } = input;\r\n\r\n    const boleto = await this.billetEntityRepository.findOne({\r\n      where: {\r\n        transactionId: input.body.transactionId,\r\n      },\r\n    });\r\n\r\n    if (!boleto) {\r\n      console.log(\r\n        `Boleto com transactionId:${input.body.transactionId} não encontrado`,\r\n      );\r\n      return;\r\n    }\r\n\r\n    if (status === 'CONFIRMED') {\r\n      const boletoConfirmed = await this.billetEntityRepository.findOne({\r\n        where: {\r\n          transactionId: input.body.transactionId,\r\n          status: BoletoStatusEnum.ACTIVE,\r\n        },\r\n      });\r\n\r\n      if (boleto) {\r\n        await this.billetEntityRepository.update(\r\n          { id: boletoConfirmed.id },\r\n          { status: BoletoStatusEnum.PAID },\r\n        );\r\n      }\r\n    }\r\n\r\n    if (status === 'CANCELLED' || status === 'ERROR') {\r\n      console.log(\r\n        `Boleto com id: ${boleto.id} webhook chegou com payload:`,\r\n        input,\r\n      );\r\n      await this.billetEntityRepository.update(\r\n        { id: boleto.id },\r\n        { status: input.status },\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}