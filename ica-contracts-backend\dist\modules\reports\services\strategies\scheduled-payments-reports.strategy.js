"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduledPaymentsReportsStrategy = void 0;
const scheduled_payments_reports_strategy_abstraction_1 = require("./scheduled-payments-reports.strategy.abstraction");
class ScheduledPaymentsReportsStrategy extends scheduled_payments_reports_strategy_abstraction_1.ScheduledPaymentsReportsStrategyAbstraction {
    async generateReport(data) {
        const period = this.getPeriodDate(data.period);
        const scheduledPayments = await this.findByPeriod(period.startDate, period.endDate);
        const transactions = this.transformToInvestorFormat(scheduledPayments);
        if (transactions.length === 0) {
            return null;
        }
        const pdf = await this.generatePdf.generatePdf({
            periodType: data.period,
            transactions,
            type: 'pending',
        });
        return this.savePdfFile(pdf);
    }
}
exports.ScheduledPaymentsReportsStrategy = ScheduledPaymentsReportsStrategy;
//# sourceMappingURL=scheduled-payments-reports.strategy.js.map