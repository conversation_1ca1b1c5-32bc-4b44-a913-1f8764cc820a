{"version": 3, "file": "create-pix-key.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/create-pix-key.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA0E;AAC1E,uEAA6D;AAE7D,MAAa,YAAY;CAQxB;AARD,oCAQC;AALC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,EAAC,2BAAW,CAAC;;6CACC;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yCACC", "sourcesContent": ["import { IsDefined, IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { KeyTypeEnum } from 'src/shared/enums/key-type.enum';\r\n\r\nexport class CreatePixDto {\r\n  @IsDefined()\r\n  @IsEnum(KeyTypeEnum)\r\n  keyType: KeyTypeEnum;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  key: string;\r\n}\r\n"]}