"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetOnePreRegisterService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const pre_register_entity_1 = require("../../../shared/database/typeorm/entities/pre-register.entity");
const typeorm_2 = require("typeorm");
let GetOnePreRegisterService = class GetOnePreRegisterService {
    constructor(preRegisterDb) {
        this.preRegisterDb = preRegisterDb;
    }
    async perform(data) {
        const preRegister = await this.preRegisterDb.findOne({
            where: {
                adviserId: (0, typeorm_2.Equal)(data.adviserId),
                id: (0, typeorm_2.Equal)(data.id),
            },
        });
        if (!preRegister) {
            throw new common_1.BadRequestException('Nenhum pre registro encontrado com esse id');
        }
        return preRegister;
    }
};
exports.GetOnePreRegisterService = GetOnePreRegisterService;
exports.GetOnePreRegisterService = GetOnePreRegisterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(pre_register_entity_1.PreRegisterEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], GetOnePreRegisterService);
//# sourceMappingURL=get-one-pre-register.service.js.map