{"version": 3, "file": "br-holiday.provider.js", "sourceRoot": "/", "sources": ["shared/providers/br-holiday.provider.ts"], "names": [], "mappings": ";;;AACA,2CAAuC;AAE1B,QAAA,UAAU,GAAG,YAAY,CAAC;AAE1B,QAAA,iBAAiB,GAAa;IACzC,OAAO,EAAE,kBAAU;IACnB,UAAU,EAAE,GAAG,EAAE;QACf,OAAO,IAAI,sBAAS,EAAE,CAAC;IACzB,CAAC;CACF,CAAC", "sourcesContent": ["import { Provider } from '@nestjs/common';\r\nimport { BRHoliday } from 'br-holiday';\r\n\r\nexport const BR_HOLIDAY = 'BR_HOLIDAY';\r\n\r\nexport const BrHolidayProvider: Provider = {\r\n  provide: BR_HOLIDAY,\r\n  useFactory: () => {\r\n    return new BRHoliday();\r\n  },\r\n};\r\n"]}