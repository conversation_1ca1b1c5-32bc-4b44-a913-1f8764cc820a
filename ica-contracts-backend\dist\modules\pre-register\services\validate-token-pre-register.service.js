"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidateTokenPreRegisterService = void 0;
const common_1 = require("@nestjs/common");
const jsonwebtoken_1 = require("jsonwebtoken");
let ValidateTokenPreRegisterService = class ValidateTokenPreRegisterService {
    perform(token) {
        const decoded = (0, jsonwebtoken_1.decode)(token);
        return {
            email: decoded.investorEmail,
            document: decoded.investorDocument,
            owner: decoded.investorOwnerCpf
                ? {
                    name: decoded.investorOwnerName,
                    cpf: decoded.investorOwnerCpf,
                }
                : undefined,
            investment: {
                value: decoded.investmentValue,
                term: decoded.investmentTerm,
                modality: decoded.investmentModality,
                yield: decoded.investmentYield,
                purchaseWith: decoded.investmentPurchaseWith,
                amountQuotes: decoded.investmentAmountQuotes,
                startContract: decoded.investmentStartContract,
                endContract: decoded.investmentEndContract,
                gracePeriod: decoded.investmentGracePeriod,
                debenture: decoded.investmentDebenture,
                observations: decoded.investmentObservations,
                brokerParticipationPercentage: decoded.brokerParticipationPercentage,
                advisorParticipationPercentage: decoded.advisorParticipationPercentage,
            },
            signIca: decoded.signIca ?? '',
        };
    }
};
exports.ValidateTokenPreRegisterService = ValidateTokenPreRegisterService;
exports.ValidateTokenPreRegisterService = ValidateTokenPreRegisterService = __decorate([
    (0, common_1.Injectable)()
], ValidateTokenPreRegisterService);
//# sourceMappingURL=validate-token-pre-register.service.js.map