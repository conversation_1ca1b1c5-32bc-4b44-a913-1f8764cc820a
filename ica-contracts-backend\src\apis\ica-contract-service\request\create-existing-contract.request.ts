export enum PaymentMethod {
  PIX = 'pix',
  BANK_TRANSFER = 'bank_transfer',
  BOLETO = 'boleto',
}

export enum InvestorProfile {
  CONSERVATIVE = 'conservative',
  MODERATE = 'moderate',
  AGGRESSIVE = 'aggressive',
}

export enum CompanyLegalType {
  MEI = 'MEI',
  EI = 'EI',
  EIRELI = 'EIRELI',
  LTDA = 'LTDA',
  SLU = 'SLU',
  SA = 'SA',
  SS = 'SS',
  CONSORCIO = 'CONSORCIO',
}

export enum PersonType {
  PF = 'PF',
  PJ = 'PJ',
}

export enum ContractType {
  MUTUO = 'MUTUO',
  SCP = 'SCP',
}

// Interface para o endereço, utilizada tanto em pessoa física quanto jurídica.
export interface AddressRequest {
  street: string;
  number: string;
  city: string;
  state: string;
  postalCode: string;
  neighborhood: string;
  complement?: string;
}

// Dados do investidor pessoa física.
export interface IndividualRequest {
  fullName: string;
  cpf: string;
  rg: string;
  issuingAgency: string;
  nationality: string;
  occupation: string;
  birthDate: string;
  email: string;
  phone: string;
  motherName: string;
  address: AddressRequest;
}

// Dados referentes à empresa.
export interface CompanyRequest {
  corporateName: string;
  cnpj: string;
  type: CompanyLegalType;
  address: AddressRequest;
  representative: IndividualRequest;
}

// Exemplo de interface para o bankAccount.
// Atenção: adapte as propriedades conforme definido no BankAccountSchema real.
export interface BankAccountRequest {
  bank: string;
  agency: string;
  account: string;
  accountType: string;
  pix?: string;
}

// Definição para o schema de investimento do contrato existente.
export interface ExistingContractInvestmentRequest {
  amount: number;
  monthlyRate: number;
  paymentMethod: PaymentMethod;
  profile: InvestorProfile;
  startDate: string;
  endDate: string;
  durationInMonths: number;
  isDebenture: boolean;
  quotaQuantity?: string;
}

// Interface para os dados do assessor, conforme AdvisorAssignmentSchema.
export interface AdvisorAssignmentRequest {
  advisorId: string;
  rate: number;
}

// Estrutura para os arquivos (PDF, PNG, JPEG, JPG), utilizada em proofOfPayment, personalDocument e contract.
export interface FileRequest {
  mimetype: string; // 'application/pdf', 'image/png', 'image/jpeg', 'image/jpg'
  buffer: Buffer;
}

// Interface principal que o BFF utilizará para enviar a request para o microserviço.
export interface CreateExistingContractRequest {
  personType: PersonType;
  contractType: ContractType;
  brokerId: string;
  investment: ExistingContractInvestmentRequest;
  advisors: AdvisorAssignmentRequest[];
  bankAccount: BankAccountRequest;
  individual?: IndividualRequest;
  company?: CompanyRequest;
  proofOfPayment: FileRequest;
  personalDocument: FileRequest;
  proofOfResidence: FileRequest;
  companyDocument: FileRequest;
  contract: FileRequest;
}
