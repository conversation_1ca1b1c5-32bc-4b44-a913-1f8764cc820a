{"version": 3, "file": "create-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/create-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,6CAAmD;AACnD,oGAAyF;AACzF,6FAAoF;AACpF,6FAAmF;AACnF,qCAA4C;AAI5C,IAAa,mBAAmB,GAAhC,MAAa,mBAAmB;IAC9B,YAEU,SAAoC,EAEpC,gBAA0C,EAE1C,GAAyB;QAJzB,cAAS,GAAT,SAAS,CAA2B;QAEpC,qBAAgB,GAAhB,gBAAgB,CAA0B;QAE1C,QAAG,GAAH,GAAG,CAAsB;IAChC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAmB,EAAE,EAAU;QAC3C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACjE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC;gBACpD,OAAO,EAAE,OAAO,CAAC,MAAM;gBACvB,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,6BAAY,EAAE;gBAC7C,GAAG,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG;gBACvB,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE1C,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA7CY,kDAAmB;8BAAnB,mBAAmB;IAE3B,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,eAAM,EAAC,8CAAoB,CAAC,CAAA;qCAHV,oBAAU;QAEH,oBAAU;QAEvB,8CAAoB;GAPxB,mBAAmB,CA6C/B", "sourcesContent": ["import { Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { CreatePixDto } from '../dto/create-pix-key.dto';\r\n\r\nexport class CreatePixKeyService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(PixKeyEntity)\r\n    private pixKeyRepository: Repository<PixKeyEntity>,\r\n    @Inject(PixKeyCelcoinService)\r\n    private pix: PixKeyCelcoinService,\r\n  ) {}\r\n\r\n  async perform(input: CreatePixDto, id: string) {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) {\r\n      throw new NotFoundException('Conta não encontrada.');\r\n    }\r\n\r\n    return this.pixKeyRepository.manager.transaction(async (manager) => {\r\n      const apiPixKey = await this.pix.createAccountPixKeys({\r\n        account: account.number,\r\n        key: input.key,\r\n        keyType: input.keyType,\r\n      });\r\n\r\n      const newPixKey = manager.create(PixKeyEntity, {\r\n        key: apiPixKey.body.key,\r\n        accountId: account.id,\r\n        typeKey: input.keyType,\r\n      });\r\n\r\n      const pix = await manager.save(newPixKey);\r\n\r\n      return pix;\r\n    });\r\n  }\r\n}\r\n"]}