"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvestorModule = void 0;
const common_1 = require("@nestjs/common");
const validate_investor_middleware_1 = require("../../shared/middlewares/validate-investor.middleware");
const shared_module_1 = require("../../shared/shared.module");
const owner_module_1 = require("../owner/owner.module");
const investor_controller_1 = require("./controllers/investor.controller");
const movement_controller_1 = require("./controllers/movement.controller");
const create_investor_service_1 = require("./services/create-investor.service");
const create_movement_service_1 = require("./services/create-movement.service");
const dashboard_service_1 = require("./services/dashboard.service");
const edit_investor_service_1 = require("./services/edit-investor.service");
const get_investor_info_service_1 = require("./services/get-investor-info.service");
const get_moviments_service_1 = require("./services/get-moviments.service");
const get_one_moviment_service_1 = require("./services/get-one-moviment.service");
const update_investors_service_1 = require("./services/update-investors.service");
let InvestorModule = class InvestorModule {
    configure(consumer) {
        consumer
            .apply(validate_investor_middleware_1.ValidateInvestorMiddleware)
            .forRoutes({ path: 'investor/movements', method: common_1.RequestMethod.GET });
    }
};
exports.InvestorModule = InvestorModule;
exports.InvestorModule = InvestorModule = __decorate([
    (0, common_1.Module)({
        imports: [shared_module_1.SharedModule, owner_module_1.OwnerModule],
        providers: [
            create_investor_service_1.CreateInvestorService,
            get_investor_info_service_1.GetInvestorInfoService,
            create_movement_service_1.CreateMovementService,
            get_moviments_service_1.GetMovementsService,
            get_one_moviment_service_1.GetMovementService,
            dashboard_service_1.InvestorDashboardService,
            edit_investor_service_1.EditInvestorService,
            update_investors_service_1.UpdateInvestor,
        ],
        controllers: [investor_controller_1.InvestorController, movement_controller_1.MovementController],
        exports: [edit_investor_service_1.EditInvestorService],
    })
], InvestorModule);
//# sourceMappingURL=investor.module.js.map