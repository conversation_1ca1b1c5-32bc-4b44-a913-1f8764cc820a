import { EmailStatus } from '../../domain/entities/email';
import type { EmailRepository } from '../../domain/repositories/email.respository';
import type { WebhookEmailDto } from '../dto/webhook-email.dto';

export class WebhookEmailUseCase {
  constructor(private readonly emailRepository: EmailRepository) {}

  async execute({
    data: { email_id, status },
  }: WebhookEmailDto): Promise<void> {
    const email = await this.emailRepository.findByExternalId(email_id);
    if (!email) {
      return;
    }

    let statusData: EmailStatus = EmailStatus.PENDING;

    if (status === 'SENT') statusData = EmailStatus.SUCCESS;
    if (status === 'DELIVERING') statusData = EmailStatus.PENDING;
    if (status === 'ERRORED') statusData = EmailStatus.FAILED;

    email.status = statusData;

    await this.emailRepository.update(email);
  }
}
