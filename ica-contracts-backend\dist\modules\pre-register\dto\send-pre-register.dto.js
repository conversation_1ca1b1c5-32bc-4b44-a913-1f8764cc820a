"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendPreRegisterDto = void 0;
const brazilian_class_validator_1 = require("brazilian-class-validator");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const account_type_enum_1 = require("../../../shared/enums/account-type.enum");
class Investment {
}
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], Investment.prototype, "value", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Investment.prototype, "term", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Investment.prototype, "modality", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], Investment.prototype, "yield", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Investment.prototype, "purchaseWith", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], Investment.prototype, "amountQuotes", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], Investment.prototype, "startContract", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], Investment.prototype, "endContract", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], Investment.prototype, "gracePeriod", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], Investment.prototype, "debenture", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumberString)(),
    __metadata("design:type", String)
], Investment.prototype, "brokerParticipationPercentage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumberString)(),
    __metadata("design:type", String)
], Investment.prototype, "advisorParticipationPercentage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Investment.prototype, "observations", void 0);
class Owner {
}
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, brazilian_class_validator_1.IsCPF)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Owner.prototype, "cpf", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], Owner.prototype, "name", void 0);
class SendPreRegisterDto {
}
exports.SendPreRegisterDto = SendPreRegisterDto;
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], SendPreRegisterDto.prototype, "adviserId", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], SendPreRegisterDto.prototype, "email", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, brazilian_class_validator_1.IsCPFOrCNPJ)(),
    __metadata("design:type", String)
], SendPreRegisterDto.prototype, "document", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsEnum)(account_type_enum_1.AccountTypeEnum),
    __metadata("design:type", String)
], SendPreRegisterDto.prototype, "accountType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => Owner),
    __metadata("design:type", Owner)
], SendPreRegisterDto.prototype, "owner", void 0);
__decorate([
    (0, class_validator_1.IsDefined)(),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => Investment),
    __metadata("design:type", Investment)
], SendPreRegisterDto.prototype, "investment", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SendPreRegisterDto.prototype, "signIca", void 0);
//# sourceMappingURL=send-pre-register.dto.js.map