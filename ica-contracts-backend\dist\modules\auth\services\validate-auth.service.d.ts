import { JwtService } from '@nestjs/jwt';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Repository } from 'typeorm';
import { LoginDto } from '../dto/login.dto';
import { IAuthResponse } from '../interfaces/auth-response.interface';
export declare class ValidateAuthService {
    private ownerDb;
    private businessDb;
    private jwtService;
    constructor(ownerDb: Repository<OwnerEntity>, businessDb: Repository<BusinessEntity>, jwtService: JwtService);
    perform(data: LoginDto): Promise<IAuthResponse>;
    private cpfOrCnpj;
    private generateToken;
    private generateRefreshToken;
    getUserInfo(userId: string): Promise<{
        id: string;
        name: string;
        email: string;
        document: string;
        type: "owner";
        roles: {
            roleId: string;
            roleName: string;
            partPercent: string;
        }[];
    } | {
        id: string;
        name: string;
        email: string;
        document: string;
        type: "business";
        roles: {
            roleId: string;
            roleName: string;
            partPercent: string;
        }[];
    }>;
}
