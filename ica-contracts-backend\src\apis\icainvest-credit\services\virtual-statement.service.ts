import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { VIRTUAL_TRANS_URL } from 'src/shared/helpers/constants';
import { logger } from 'src/shared/logger';

import { IVirtualStatementResponse } from '../responses/virtual-statement.response';

@Injectable()
export class VirtualStatementService {
  async getStatement(params: {
    accountId: string;
    dateFrom: string;
    dateTo: string;
  }): Promise<IVirtualStatementResponse[]> {
    try {
      const url = new URL(
        `${VIRTUAL_TRANS_URL}/transactions/${params.accountId}`,
      );

      url.searchParams.set('DateFrom', params.dateFrom);
      url.searchParams.set('DateTo', params.dateTo);

      const response = await axios.get<IVirtualStatementResponse[]>(
        url.toString(),
        {
          headers: {
            'X-Api-Key': 'c9e0d900-edd3-45f7-9e69-abd96edbf724',
          },
        },
      );

      return response.data;
    } catch (error) {
      logger.info(`VirtualStatementService.getStatement() -> ${error}`);
      return [] as IVirtualStatementResponse[];
    }
  }
}
