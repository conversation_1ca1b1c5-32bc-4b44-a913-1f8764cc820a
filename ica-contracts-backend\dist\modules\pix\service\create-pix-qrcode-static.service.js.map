{"version": 3, "file": "create-pix-qrcode-static.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/create-pix-qrcode-static.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,6CAAmD;AACnD,0GAA+F;AAC/F,6FAAoF;AACpF,mGAAyF;AACzF,qCAA4C;AAC5C,+BAA0B;AAK1B,IAAa,4BAA4B,GAAzC,MAAa,4BAA4B;IACvC,YAEU,SAAoC,EAEpC,aAA0C,EAE1C,gBAAyC;QAJzC,cAAS,GAAT,SAAS,CAA2B;QAEpC,kBAAa,GAAb,aAAa,CAA6B;QAE1C,qBAAgB,GAAhB,gBAAgB,CAAyB;IAChD,CAAC;IACJ,KAAK,CAAC,OAAO,CACX,IAA8B,EAC9B,EAAU;QAEV,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CACzB,gDAAgD,CACjD,CAAC;QACJ,CAAC;QACD,MAAM,aAAa,GAAG,IAAA,SAAE,GAAE,CAAC;QAC3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;YACjE,qBAAqB,EAAE,IAAI,CAAC,IAAI;YAChC,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACxB,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,oBAAoB;gBACxD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACxB,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;aACrC;YACD,yBAAyB,EAAE,aAAa;YACxC,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,UAAU,EAAE,aAAa;YACzB,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,WAAW,CAAC,QAAQ;YACzB,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC;SACjD,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEtC,OAAO;YACL,GAAG,EAAE,WAAW,CAAC,QAAQ;SAC1B,CAAC;IACJ,CAAC;CACF,CAAA;AAxDY,oEAA4B;uCAA5B,4BAA4B;IAEpC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,mCAAe,CAAC,CAAA;IAEjC,WAAA,IAAA,eAAM,EAAC,oDAAuB,CAAC,CAAA;qCAHb,oBAAU;QAEN,oBAAU;QAEP,oDAAuB;GAPxC,4BAA4B,CAwDxC", "sourcesContent": ["import { Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixQRCodeCelcoinService } from 'src/apis/celcoin/services/pix-qrcode-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { PixQRCodeEntity } from 'src/shared/database/typeorm/entities/pix-qrcode.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\n\r\nimport { CreatePixQRCodeStaticDto } from '../dto/create-pix-qrcode-static.dto';\r\nimport { ICreatePixQRCodeStaticResponse } from '../response/create-pix-qrcode-static.response';\r\n\r\nexport class CreatePixQRCodeStaticService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(PixQRCodeEntity)\r\n    private pixQRCodeRepo: Repository<PixQRCodeEntity>,\r\n    @Inject(PixQRCodeCelcoinService)\r\n    private pixQRCodeCelcoin: PixQRCodeCelcoinService,\r\n  ) {}\r\n  async perform(\r\n    data: CreatePixQRCodeStaticDto,\r\n    id: string,\r\n  ): Promise<ICreatePixQRCodeStaticResponse> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n    if (!account) {\r\n      throw new NotFoundException(\r\n        'Conta não encontrada ou não vinculada ao owner',\r\n      );\r\n    }\r\n    const transactionId = v4();\r\n    const apiResponse = await this.pixQRCodeCelcoin.createQRCodeStatic({\r\n      additionalInformation: data.info,\r\n      key: data.key,\r\n      merchant: {\r\n        city: data.merchant.city,\r\n        merchantCategoryCode: data.merchant.merchantCategoryCode,\r\n        name: data.merchant.name,\r\n        postalCode: data.merchant.postalCode,\r\n      },\r\n      transactionIdentification: transactionId,\r\n      amount: data.amount,\r\n    });\r\n\r\n    const create = this.pixQRCodeRepo.create({\r\n      accountId: account.id,\r\n      externalId: transactionId,\r\n      type: 'STATIC',\r\n      emv: apiResponse.emvqrcps,\r\n      transactionId: String(apiResponse.transactionId),\r\n    });\r\n    await this.pixQRCodeRepo.save(create);\r\n\r\n    return {\r\n      emv: apiResponse.emvqrcps,\r\n    };\r\n  }\r\n}\r\n"]}