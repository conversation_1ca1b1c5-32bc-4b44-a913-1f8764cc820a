import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { Repository } from 'typeorm';

@Injectable()
export class DiscountAccountLimitService {
  constructor(
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
  ) {}

  async execute(accountId: string, discountValue: number) {
    const accountLimit = await this.accountLimitRepo.findOneBy({
      accountId,
    });
    const now = new Date().getHours();

    accountLimit.monthlyLimit -= discountValue;
    accountLimit.dailyLimit -= discountValue;
    if (now >= 21) accountLimit.dailyNightLimit -= discountValue;

    await this.accountLimitRepo.save(accountLimit);
  }
}
