import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { GetInternalPixTransactionlDto } from '../dto/get-internal-pix-transaction.dto';
export declare class GetInternalPixTransactionService {
    private accountDb;
    private transactionRepository;
    private celcoinService;
    constructor(accountDb: Repository<AccountEntity>, transactionRepository: Repository<TransactionEntity>, celcoinService: PixTransactionCelcoinService);
    execute(data: GetInternalPixTransactionlDto, id: string): Promise<any>;
}
