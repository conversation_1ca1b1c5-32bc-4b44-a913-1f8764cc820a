"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionPixKeyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const date_fns_1 = require("date-fns");
const moment = require("moment");
require("moment-timezone");
const pix_transaction_celcoin_service_1 = require("../../../apis/celcoin/services/pix-transaction-celcoin.service");
const virtual_balance_service_1 = require("../../../apis/icainvest-credit/services/virtual-balance.service");
const get_account_limit_service_1 = require("../../account-transfer-limit/services/get-account-limit.service");
const account_transfer_limits_entity_1 = require("../../../shared/database/typeorm/entities/account-transfer-limits.entity");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const transaction_movement_type_enum_1 = require("../../../shared/enums/transaction-movement-type.enum");
const transaction_status_enum_1 = require("../../../shared/enums/transaction-status-enum");
const logger_1 = require("../../../shared/logger");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const config_1 = require("@nestjs/config");
const two_factor_auth_service_1 = require("../../two-factor-auth/two-factor-auth.service");
const br_holiday_provider_1 = require("../../../shared/providers/br-holiday.provider");
let TransactionPixKeyService = class TransactionPixKeyService {
    constructor(accountRepository, accountLimitRepo, transactionRepository, celcoinService, virtualBalanceService, getLimit, configService, twoFactorAuthService, brHoliday) {
        this.accountRepository = accountRepository;
        this.accountLimitRepo = accountLimitRepo;
        this.transactionRepository = transactionRepository;
        this.celcoinService = celcoinService;
        this.virtualBalanceService = virtualBalanceService;
        this.getLimit = getLimit;
        this.configService = configService;
        this.twoFactorAuthService = twoFactorAuthService;
        this.brHoliday = brHoliday;
    }
    async perform(data, id, isAutomate, twoFactorToken) {
        const account = await this.accountRepository.findOne({
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
            relations: { owner: true, business: true },
        });
        if (!account)
            throw new common_1.NotFoundException('Conta não encontrada.');
        if (!isAutomate) {
            const startHour = this.configService.get('TRANSACTION_START_HOUR');
            const endHour = this.configService.get('TRANSACTION_END_HOUR');
            const currentDate = moment().utc().tz('America/Sao_Paulo');
            const currentHour = currentDate.hour();
            const currentDay = currentDate.day();
            if (currentHour >= endHour || currentHour < startHour) {
                throw new common_1.BadRequestException('Transações não permitidas entre 19h e 08h.');
            }
            const isHoliday = await this.brHoliday.isHoliday(currentDate.format('YYYY-MM-DD'));
            if (isHoliday || currentDay === 0 || currentDay === 6) {
                throw new common_1.BadRequestException('Por motivos de segurança, as transações via PIX e TED na plataforma ICA Invest estão disponíveis apenas em dias úteis, das 08h às 19h.');
            }
            const balance = await this.getLimit.execute(account.id);
            if (Number(data.amount) > Number(balance.dailyLimit))
                throw new common_1.BadRequestException('Limite de transferencia excedido!');
            await this.twoFactorAuthService.verifyTwoFactorAuthentication({
                userId: id,
                token: twoFactorToken,
            });
            let transaction;
            if (!isAutomate &&
                data.transferDate &&
                !moment(data.transferDate).isSame(new Date()) &&
                moment(data.transferDate).isAfter(new Date())) {
                transaction = await this.scheduleTransaction(data, account);
            }
            else {
                transaction = await this.transactionRepository.manager.transaction(async (manager) => {
                    const uuid = (0, uuid_1.v4)();
                    const { body: result } = await this.celcoinService.transactionPixKey({
                        amount: Number(data.amount),
                        initiationType: 'DICT',
                        paymentType: 'IMMEDIATE',
                        remittanceInformation: data.description,
                        transactionType: 'TRANSFER',
                        urgency: 'HIGH',
                        endToEndId: data.endToEnd,
                        clientCode: uuid,
                        creditParty: {
                            key: data.external?.key,
                            accountType: data.external?.accountType,
                            bank: data.external?.bank,
                            name: data.external?.name,
                        },
                        debitParty: {
                            account: account.number,
                        },
                    });
                    const transferMetadata = {
                        creditParty: result.creditParty,
                        debitParty: result.debitParty,
                    };
                    const newTransaction = manager.create(transaction_entity_1.TransactionEntity, {
                        accountId: account.id,
                        account,
                        code: uuid,
                        description: data.description,
                        value: data.amount,
                        destinyBank: result.creditParty?.bank,
                        destinyName: result.creditParty?.name,
                        status: transaction_status_enum_1.TransactionStatusEnum.PENDENT,
                        type: transaction_movement_type_enum_1.TransactionMovementTypeEnum.PIX_CASHOUT,
                        endToEndId: result.endToEndId,
                        transferMetadata: JSON.stringify(transferMetadata),
                    });
                    const transaction = await manager.save(newTransaction);
                    return transaction;
                });
            }
            return {
                id: transaction.id,
                status: transaction.status,
                creditParty: {
                    account: transaction.destinyAccount,
                    name: transaction.destinyName,
                    document: transaction.destinyDocument,
                },
                debitParty: {
                    account: account.number,
                    name: account.owner?.name || account.business.fantasyName,
                    document: account.owner?.cpf || account.business.cnpj,
                },
                createdAt: (0, date_fns_1.addHours)(transaction.createdAt, -3),
            };
        }
    }
    async isValidTransaction(account, amount) {
        let accountLimit = await this.accountLimitRepo.findOne({
            where: { accountId: account.id },
        });
        if (!accountLimit)
            accountLimit = await this.accountLimitRepo.save({
                accountId: account.id,
            });
        logger_1.logger.info(`TransactionPixKeyService.isValidTransaction() -> [amount: ${amount}, entity: ${JSON.stringify(accountLimit)} ]`);
        if (!accountLimit.active)
            return;
        const virtualBalance = await this.virtualBalanceService.checkBalance({
            accountId: account.id,
        });
        const amountNumber = parseFloat(amount);
        if (virtualBalance && amountNumber > virtualBalance.amount)
            throw new common_1.BadRequestException('Limite de transferencia excedido!');
        const amountIsValidOnGeneralLimit = amountNumber <= accountLimit.generalTransferLimit;
        const amountIsValidOnMonthLimit = amountNumber <= accountLimit.monthlyLimit;
        const amountIsValidOnDailyLimit = amountNumber <= accountLimit.dailyLimit;
        const now = new Date().getHours();
        const amountIsValidOnNightlLimit = now >= 23
            ? amountNumber <= accountLimit.dailyNightLimit - accountLimit.dailyLimit
            : true;
        logger_1.logger.info('TransactionPixKeyService.isValidTransaction() ->', {
            amountIsValidOnGeneralLimit,
            amountIsValidOnMonthLimit,
            amountIsValidOnDailyLimit,
            amountIsValidOnNightlLimit,
        });
        if (!amountIsValidOnNightlLimit)
            throw new common_1.BadRequestException('Limite de transferencia noturna excedido!');
        if (!(amountIsValidOnGeneralLimit &&
            amountIsValidOnMonthLimit &&
            amountIsValidOnDailyLimit)) {
            throw new common_1.BadRequestException('Limite de transferencia excedido!');
        }
    }
    async scheduleTransaction(data, account) {
        return this.transactionRepository.save({
            accountId: account.id,
            account,
            code: (0, uuid_1.v4)(),
            description: data.description,
            value: data.amount,
            transferDate: moment(data.transferDate),
            endToEndId: data.endToEnd,
            status: transaction_status_enum_1.TransactionStatusEnum.SCHEDULED,
            destinyAccount: data.external.account,
            destinyAccountType: data.external.accountType,
            destinyBank: data.external.bank,
            destinyBranch: data.external.branch,
            destinyDocument: data.external.document,
            destinyName: data.external.name,
            destinyKey: data.external.key,
            type: transaction_movement_type_enum_1.TransactionMovementTypeEnum.PIX_CASHOUT,
        });
    }
};
exports.TransactionPixKeyService = TransactionPixKeyService;
exports.TransactionPixKeyService = TransactionPixKeyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(account_transfer_limits_entity_1.AccountTransferLimitEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __param(3, (0, common_1.Inject)(pix_transaction_celcoin_service_1.PixTransactionCelcoinService)),
    __param(4, (0, common_1.Inject)(virtual_balance_service_1.VirtualBalanceService)),
    __param(5, (0, common_1.Inject)(get_account_limit_service_1.GetAccountLimitService)),
    __param(8, (0, common_1.Inject)(br_holiday_provider_1.BR_HOLIDAY)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        pix_transaction_celcoin_service_1.PixTransactionCelcoinService,
        virtual_balance_service_1.VirtualBalanceService,
        get_account_limit_service_1.GetAccountLimitService,
        config_1.ConfigService,
        two_factor_auth_service_1.TwoFactorAuthService, Function])
], TransactionPixKeyService);
//# sourceMappingURL=transaction-pix-key.service.js.map