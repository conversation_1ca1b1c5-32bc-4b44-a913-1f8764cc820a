"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const update_statistic_dto_1 = require("../dto/update-statistic.dto");
const update_statistic_service_1 = require("../services/update-statistic.service");
let StatisticController = class StatisticController {
    constructor(updateStatisticService) {
        this.updateStatisticService = updateStatisticService;
    }
    async updateStatistic(updateDto) {
        const updatedStatistic = await this.updateStatisticService.perform(updateDto);
        return updatedStatistic;
    }
};
exports.StatisticController = StatisticController;
__decorate([
    (0, common_1.Put)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADMIN),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_statistic_dto_1.UpdateStatisticDto]),
    __metadata("design:returntype", Promise)
], StatisticController.prototype, "updateStatistic", null);
exports.StatisticController = StatisticController = __decorate([
    (0, common_1.Controller)('statistics'),
    __metadata("design:paramtypes", [update_statistic_service_1.UpdateStatisticService])
], StatisticController);
//# sourceMappingURL=statistic.controller.js.map