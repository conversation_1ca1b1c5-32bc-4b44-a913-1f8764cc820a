import { Global, Module } from '@nestjs/common';

import { AssinaturaRbmModule } from './assinatura-rbm/assinatura-rbm.module';
import { CelcoinModule } from './celcoin/celcoin.module';
import { IcaContractModule } from './ica-contract/ica-contract.module';
import { IcaReportModule } from './ica-report/ica-report.module';
import { IcaInvestCreditModule } from './icainvest-credit/icainvest-credit.module';
import { IcaContractServiceModule } from './ica-contract-service/ica-contract-service.module';
@Global()
@Module({
  imports: [
    CelcoinModule,
    IcaInvestCreditModule,
    AssinaturaRbmModule,
    IcaContractModule,
    IcaReportModule,
    IcaContractServiceModule,
  ],
  exports: [
    CelcoinModule,
    IcaInvestCreditModule,
    AssinaturaRbmModule,
    IcaContractModule,
    IcaReportModule,
    IcaContractServiceModule,
  ],
})
export class ApisModule {}
