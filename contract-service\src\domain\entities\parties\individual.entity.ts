import { type Either, left, right } from "@/domain/shared/either";
import type { Email } from "@/domain/value-objects";
import type { Address } from "@/domain/value-objects";
import { Address as AddressVO } from "@/domain/value-objects/address.value-object";
import type { Cpf } from "@/domain/value-objects/cpf.value-object";
import { Cpf as CpfVO } from "@/domain/value-objects/cpf.value-object";
import { Email as EmailVO } from "@/domain/value-objects/email.value-object";
import type { Phone } from "@/domain/value-objects/phone.value-object";
import { Phone as PhoneVO } from "@/domain/value-objects/phone.value-object";
import { Party } from "./party.entity";

export class Individual extends Party {
  private constructor(
    id: string,
    name: string,
    phone: Phone,
    email: Email,
    private cpf: Cpf,
    private birthDate: Date,
    private address: Address,
    private motherName: string,
    private rg: string,
    private occupation?: string,
    private nationality?: string,
    private issuingAgency?: string
  ) {
    super(
      {
        name,
        email,
        phone,
        address,
        motherName,
      },
      id
    );
  }

  getRg(): string {
    return this.rg;
  }
  getDocument(): string {
    return this.cpf.value;
  }

  static create(
    id: string,
    name: string,
    raw: {
      cpf: string;
      email: string;
      phone: string;
      birthDate: Date;
      motherName: string;
      rg: string;
      address: {
        street: string;
        city: string;
        state: string;
        postalCode: string;
        number: string;
        neighborhood: string;
        complement?: string;
      };
      occupation?: string;
      nationality?: string;
      issuingAgency?: string;
    }
  ): Either<Error, Individual> {
    const cpfResult = CpfVO.create(raw.cpf);
    const emailResult = EmailVO.create(raw.email);
    const phoneResult = PhoneVO.create(raw.phone);
    const addressResult = AddressVO.create(
      raw.address.street,
      raw.address.number,
      raw.address.city,
      raw.address.state,
      raw.address.postalCode,
      raw.address.neighborhood,
      raw.address.complement
    );

    if (cpfResult.isLeft()) return left(cpfResult.value);
    if (emailResult.isLeft()) return left(emailResult.value);
    if (phoneResult.isLeft()) return left(phoneResult.value);
    if (addressResult.isLeft()) return left(addressResult.value);

    return right(
      new Individual(
        id,
        name,
        phoneResult.value,
        emailResult.value,
        cpfResult.value,
        raw.birthDate,
        addressResult.value,
        raw.motherName,
        raw.rg ?? "000000",
        raw.occupation,
        raw.nationality,
        raw.issuingAgency
      )
    );
  }

  getCpf(): string {
    return this.cpf.value;
  }

  getFormattedCpf(): string {
    return this.cpf.formatted();
  }

  getBirthDate(): Date {
    return this.birthDate;
  }

  getOccupation(): string {
    return this.occupation ?? "";
  }

  getPlaceOfBirth(): string {
    throw new Error("Method not implemented.");
  }

  getAddress() {
    return this.address;
  }

  getMotherName(): string {
    return this.motherName;
  }

  getIssuingAgency(): string | undefined {
    return this.issuingAgency;
  }

  getNationality(): string | undefined {
    return this.nationality;
  }

  updateIndividualData(
    name: string,
    phone: Phone,
    email: Email,
    cpf: Cpf,
    birthDate: Date,
    address: Address,
    motherName: string,
    rg: string,
    occupation?: string,
    nationality?: string,
    issuingAgency?: string
  ): void {
    this.updateData(name, email, phone);
    this.updateAddress(address);
    this.address = address;
    this.cpf = cpf;
    this.birthDate = birthDate;
    this.occupation = occupation;
    this.rg = rg;
    this.motherName = motherName;
    this.nationality = nationality;
    this.issuingAgency = issuingAgency;
  }
}
