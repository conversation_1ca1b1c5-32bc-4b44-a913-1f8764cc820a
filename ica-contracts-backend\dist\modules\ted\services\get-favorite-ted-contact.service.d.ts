import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { FavoriteTedEntity } from 'src/shared/database/typeorm/entities/favorite-ted.entity';
import { Repository } from 'typeorm';
export declare class GetFavoriteTedContactService {
    private accountRepo;
    private favoriteTedRepo;
    constructor(accountRepo: Repository<AccountEntity>, favoriteTedRepo: Repository<FavoriteTedEntity>);
    execute(id: string): Promise<FavoriteTedEntity[]>;
}
