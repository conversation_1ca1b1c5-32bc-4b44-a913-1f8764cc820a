"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVirtualRechargeService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const create_virtual_transaction_service_1 = require("../../../apis/icainvest-credit/services/create-virtual-transaction.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const typeorm_2 = require("typeorm");
let CreateVirtualRechargeService = class CreateVirtualRechargeService {
    constructor(createTransactionsService, accountDb) {
        this.createTransactionsService = createTransactionsService;
        this.accountDb = accountDb;
    }
    async perform(transaction) {
        const account = await this.accountDb.findOne({
            relations: {
                recharge: true,
            },
            where: {
                id: transaction.accountId,
            },
        });
        if (!account) {
            throw new common_1.BadRequestException('Conta não encontrada para recarga.');
        }
        const externalResponse = await this.createTransactionsService.createTransaction({
            accountId: transaction.accountId,
            amount: transaction.amount,
            type: transaction.type,
        });
        return externalResponse;
    }
};
exports.CreateVirtualRechargeService = CreateVirtualRechargeService;
exports.CreateVirtualRechargeService = CreateVirtualRechargeService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __metadata("design:paramtypes", [create_virtual_transaction_service_1.CreateTransactionsService,
        typeorm_2.Repository])
], CreateVirtualRechargeService);
//# sourceMappingURL=create-virtual-recharge.service.js.map