{"version": 3, "file": "delete-pix-key.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/delete-pix-key.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8D;AAE9D,MAAa,eAAe;CAK3B;AALD,0CAKC;AADC;IAHC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;;2CACE", "sourcesContent": ["import { IsDefined, IsString, IsUUID } from 'class-validator';\r\n\r\nexport class DeletePixKeyDto {\r\n  @IsString()\r\n  @IsDefined()\r\n  @IsUUID()\r\n  id: string;\r\n}\r\n"]}