import { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';
import { Repository } from 'typeorm';
import { RequestClaimPixKeyInputDTO } from '../dto/request-claim-pix-key.dto';
export declare class RequestClaimPixKeyService {
    private pixKeyRepository;
    private ownerRepository;
    private pixCelcoinService;
    constructor(pixKeyRepository: Repository<PixKeyEntity>, ownerRepository: Repository<OwnerEntity>, pixCelcoinService: PixKeyCelcoinService);
    perform(input: RequestClaimPixKeyInputDTO, userId: string): Promise<any>;
}
