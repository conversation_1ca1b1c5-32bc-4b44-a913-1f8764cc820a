export class YieldStatement {
  constructor(
    private _month: number,
    private _year: number,
    private _amount: number,
    private _paymentDate: Date
  ) {}

  get amount(): number {
    return this._amount
  }

  get month(): number {
    return this._month
  }

  get year(): number {
    return this._year
  }

  get paymentDate(): Date {
    return this._paymentDate
  }

  set amount(amount: number) {
    if (amount < 0) {
      throw new Error('Amount must be greater than zero')
    }

    this._amount = amount
  }

  set month(month: number) {
    if (month < 1 || month > 12) {
      throw new Error('Month must be between 1 and 12')
    }

    this._month = month
  }

  set year(year: number) {
    if (year > 1900 && year < 2100) {
      throw new Error('Year must be greater than 1900')
    }

    this._year = year
  }

  set paymentDate(paymentDate: Date) {
    this._paymentDate = paymentDate
  }
}
