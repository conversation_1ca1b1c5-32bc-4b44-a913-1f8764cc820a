import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { FavoriteTedEntity } from 'src/shared/database/typeorm/entities/favorite-ted.entity';
import { Repository } from 'typeorm';
import { SaveFavoriteTedContactDto } from '../dto/save-favorite-ted-contact.dto';
export declare class SaveFavoriteTedContactService {
    private accountRepo;
    private favoriteTedRepo;
    constructor(accountRepo: Repository<AccountEntity>, favoriteTedRepo: Repository<FavoriteTedEntity>);
    execute(data: SaveFavoriteTedContactDto, id: string): Promise<void>;
    private createFavoriteTedContact;
}
