import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

import {
  ExistingContractInvestmentDto,
  IndividualDto,
  CompanyDto,
  FileDto,
  PersonType,
  ContractType,
} from './create-existing-contract.dto';

export class BankAccountDto {
  @IsString()
  @IsOptional()
  bank: string;

  @IsString()
  @IsOptional()
  agency: string;

  @IsString()
  @IsOptional()
  account: string;

  @IsString()
  @IsOptional()
  accountType: string;

  @IsString()
  @IsOptional()
  pix: string;
}
export class ResubmitContractDto {
  @IsString({ message: 'Role é obrigatório' })
  role: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  contractId?: string;

  @ApiProperty({ enum: PersonType })
  @IsEnum(PersonType)
  personType: PersonType;

  @ApiProperty({ enum: ContractType })
  @IsEnum(ContractType)
  @IsOptional()
  contractType: ContractType;

  @ApiProperty({ type: ExistingContractInvestmentDto })
  @IsObject()
  @ValidateNested()
  @IsOptional()
  @Type(() => ExistingContractInvestmentDto)
  investment: ExistingContractInvestmentDto;

  @ApiProperty({ type: BankAccountDto })
  @IsObject()
  @ValidateNested()
  @IsOptional()
  @Type(() => BankAccountDto)
  bankAccount?: BankAccountDto;

  @ApiProperty({ type: IndividualDto })
  @ValidateIf((o) => o.personType === PersonType.PF)
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => IndividualDto)
  individual?: IndividualDto;

  @ApiProperty({ type: CompanyDto })
  @ValidateIf((o) => o.personType === PersonType.PJ)
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => CompanyDto)
  company?: CompanyDto;

  @ApiProperty({ type: FileDto })
  @ValidateNested()
  @IsOptional()
  @Type(() => FileDto)
  proofOfPayment?: FileDto;

  @ApiPropertyOptional({ type: FileDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => FileDto)
  personalDocument?: FileDto;

  @ApiPropertyOptional({ type: FileDto })
  @ValidateNested()
  @IsOptional()
  @Type(() => FileDto)
  contract?: FileDto;

  @ApiPropertyOptional({ type: FileDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => FileDto)
  proofOfResidence?: FileDto;

  @ApiPropertyOptional({ type: FileDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => FileDto)
  cardCnpj?: FileDto;
  
}
