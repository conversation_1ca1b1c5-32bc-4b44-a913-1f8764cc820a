{"version": 3, "file": "get-all-brokers-with-active-contracts.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/get-all-brokers-with-active-contracts.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,yDAAsD;AACtD,2CAAoD;AACpD,6CAAmD;AAEnD,+FAAsF;AACtF,qCAA6E;AAI7E,kEAAmE;AACnE,+FAAsF;AACtF,qFAA2E;AAC3E,iEAAwD;AAGjD,IAAM,uCAAuC,GAA7C,MAAM,uCAAuC;IAClD,YAEU,WAAuC,EAChB,YAAmB;QAD1C,gBAAW,GAAX,WAAW,CAA4B;QAChB,iBAAY,GAAZ,YAAY,CAAO;IACjD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAA+C;QAC3D,MAAM,QAAQ,GAAG,kBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;QAE3D,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CACzB,QAAQ,CACT,CAAC;QAEJ,IAAI,MAAM;YAAE,OAAO,MAAM,CAAC;QAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GACzB,uCAAoB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,OAAO,GAA4B;YACvC,WAAW,EAAE,IAAA,yBAAe,EAAC,IAAI,IAAI,EAAE,CAAC;YACxC,MAAM,EAAE,yCAAkB,CAAC,MAAM;SAClC,CAAC;QAEF,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,uCAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEtE,OAAO,CAAC,aAAa,GAAG,IAAA,iBAAO,EAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;aAChC,kBAAkB,CAAC,UAAU,CAAC;aAC9B,SAAS,CAAC,4BAA4B,EAAE,KAAK,CAAC;aAC9C,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC;aAC7B,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;aAC9B,QAAQ,CAAC,cAAc,EAAE,UAAU,CAAC;aACpC,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC,wBAAwB,EAAE,OAAO,CAAC;aACzC,KAAK,CAAC,OAAO,CAAC;aACd,QAAQ,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAE7D,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,UAAU,CAAC,QAAQ,CAAC,gDAAgD,EAAE;gBACpE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,CAAC,SAAS,EAAE,CAAC;QAE/C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE5B,MAAM,2BAA2B,GAAG,IAAI,CAAC,WAAW;aACjD,kBAAkB,CAAC,UAAU,CAAC;aAC9B,SAAS,CAAC,4BAA4B,EAAE,KAAK,CAAC;aAC9C,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC;aAC7B,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;aAC9B,QAAQ,CAAC,cAAc,EAAE,UAAU,CAAC;aACpC,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC;YACN,oBAAoB;YACpB,oDAAoD;YACpD,mDAAmD;YACnD,gGAAgG;SACjG,CAAC;aACD,OAAO,CACN,2EAA2E,CAC5E;aACA,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;aAC7B,KAAK,CAAC,OAAO,CAAC;aACd,QAAQ,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,sBAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAE7D,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,2BAA2B,CAAC,QAAQ,CAClC,gDAAgD,EAChD;gBACE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CACF,CAAC;QACJ,CAAC;QAED,MAAM,iCAAiC,GAAG,MAAM,2BAA2B;aACxE,MAAM,CAAC,IAAI,CAAC;aACZ,KAAK,CAAC,KAAK,CAAC;aACZ,UAAU,EAAE,CAAC;QAEhB,MAAM,MAAM,GAAG,iCAAiC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACvE,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK;YACpC,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,UAAU,EAAE,CAAC,MAAM,CAAC,UAAU;SAC/B,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GACZ,uCAAoB,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAE3E,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAjHY,0FAAuC;kDAAvC,uCAAuC;IADnD,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,eAAM,EAAC,6BAAa,CAAC,CAAA;qCADD,oBAAU;GAHtB,uCAAuC,CAiHnD", "sourcesContent": ["import { CACHE_MANAGER } from '@nestjs/cache-manager';\r\nimport { Inject, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Cache } from 'cache-manager';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { Between, FindOperator, MoreThanOrEqual, Repository } from 'typeorm';\r\n\r\nimport { GetAllBrokersWithActiveContractsQueryDto } from '../dto/get-all-brokers-with-active-contracts/query.dto';\r\nimport { GetAllBrokersWithActiveContractsResponseDto } from '../dto/get-all-brokers-with-active-contracts/response.dto';\r\nimport { PaginatedQueryHelper } from '../helpers/pagination-query';\r\nimport { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\nimport { RolesEnum } from 'src/shared/enums/roles.enum';\r\n\r\n@Injectable()\r\nexport class GetAllBrokersWithActiveContractsService {\r\n  constructor(\r\n    @InjectRepository(ContractEntity)\r\n    private contractsDb: Repository<ContractEntity>,\r\n    @Inject(CACHE_MANAGER) private cacheManager: Cache,\r\n  ) {}\r\n\r\n  async perform(query: GetAllBrokersWithActiveContractsQueryDto) {\r\n    const cacheKey = `active-brokers-${JSON.stringify(query)}`;\r\n\r\n    const cached =\r\n      await this.cacheManager.get<GetAllBrokersWithActiveContractsResponseDto>(\r\n        cacheKey,\r\n      );\r\n\r\n    if (cached) return cached;\r\n\r\n    const { page, limit, skip } =\r\n      PaginatedQueryHelper.getPaginationParams(query);\r\n\r\n    const filters: Record<string, unknown> = {\r\n      endContract: MoreThanOrEqual(new Date()),\r\n      status: ContractStatusEnum.ACTIVE,\r\n    };\r\n\r\n    if (query.dateTo || query.dateFrom) {\r\n      const { start, end } = PaginatedQueryHelper.getDateRangeParams(query);\r\n\r\n      filters.startContract = Between(start, end);\r\n    }\r\n\r\n    const totalQuery = this.contractsDb\r\n      .createQueryBuilder('contract')\r\n      .innerJoin('contract.ownerRoleRelation', 'orr')\r\n      .innerJoin('orr.role', 'role')\r\n      .leftJoin('orr.owner', 'owner')\r\n      .leftJoin('orr.business', 'business')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select('COUNT(DISTINCT orr.id)', 'count')\r\n      .where(filters)\r\n      .andWhere('role.name = :role', { role: RolesEnum.BROKER });\r\n\r\n    if (query.contractType) {\r\n      totalQuery.andWhere('signataries.investmentModality = :contractType', {\r\n        contractType: query.contractType,\r\n      });\r\n    }\r\n\r\n    const { count } = await totalQuery.getRawOne();\r\n\r\n    const total = Number(count);\r\n\r\n    const brokersDirectContractsQuery = this.contractsDb\r\n      .createQueryBuilder('contract')\r\n      .innerJoin('contract.ownerRoleRelation', 'orr')\r\n      .innerJoin('orr.role', 'role')\r\n      .leftJoin('orr.owner', 'owner')\r\n      .leftJoin('orr.business', 'business')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select([\r\n        'orr.id as brokerid',\r\n        'COALESCE(owner.name, business.companyName) as name',\r\n        'COALESCE(owner.avatar, business.avatar) as avatar',\r\n        'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0)) as totalvalue',\r\n      ])\r\n      .groupBy(\r\n        'brokerId, owner.name, business.companyName, owner.avatar, business.avatar',\r\n      )\r\n      .orderBy('totalValue', 'DESC')\r\n      .where(filters)\r\n      .andWhere('role.name = :role', { role: RolesEnum.BROKER });\r\n\r\n    if (query.contractType) {\r\n      brokersDirectContractsQuery.andWhere(\r\n        'signataries.investmentModality = :contractType',\r\n        {\r\n          contractType: query.contractType,\r\n        },\r\n      );\r\n    }\r\n\r\n    const brokersDirectContractsQueryResult = await brokersDirectContractsQuery\r\n      .offset(skip)\r\n      .limit(limit)\r\n      .getRawMany();\r\n\r\n    const result = brokersDirectContractsQueryResult.map((broker, index) => ({\r\n      brokerId: broker.brokerid,\r\n      rank: index + 1 + (page - 1) * limit,\r\n      name: broker.name,\r\n      avatar: broker.avatar,\r\n      totalValue: +broker.totalvalue,\r\n    }));\r\n\r\n    const response: GetAllBrokersWithActiveContractsResponseDto =\r\n      PaginatedQueryHelper.createPaginatedResponse(result, total, page, limit);\r\n\r\n    await this.cacheManager.set(cacheKey, response, 60000);\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}