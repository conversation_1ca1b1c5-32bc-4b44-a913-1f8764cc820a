import type { Either } from '@/domain/shared';
import type { ITransactionContext } from '@/application/interfaces';

export interface ContractDeletion {
  id: string;
  contractId: string;
  deletedById: string;
  reason: string;
  deletedAt: Date;
  updatedAt: Date;
}

export interface IContractDeletionRepository {
  create(
    data: Omit<ContractDeletion, 'id' | 'deletedAt' | 'updatedAt'>,
    tx?: ITransactionContext
  ): Promise<Either<Error, ContractDeletion>>;
}
