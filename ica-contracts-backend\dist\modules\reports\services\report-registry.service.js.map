{"version": 3, "file": "report-registry.service.js", "sourceRoot": "/", "sources": ["modules/reports/services/report-registry.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAiE;AAK1D,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAA3B;QACY,eAAU,GAAiC,IAAI,GAAG,EAAE,CAAC;IAaxE,CAAC;IAXC,QAAQ,CAAC,IAAY,EAAE,QAAyB;QAC9C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,OAAO,CAAC,IAAY;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,4BAAmB,CAAC,kCAAkC,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AAdY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;GACA,qBAAqB,CAcjC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\n\r\nimport { IReportStrategy } from '../interfaces/report-strategy.interface';\r\n\r\n@Injectable()\r\nexport class ReportRegistryService {\r\n  private readonly strategies: Map<string, IReportStrategy> = new Map();\r\n\r\n  register(type: string, strategy: IReportStrategy) {\r\n    this.strategies.set(type, strategy);\r\n  }\r\n\r\n  resolve(type: string): IReportStrategy {\r\n    const strategy = this.strategies.get(type);\r\n    if (!strategy) {\r\n      throw new BadRequestException('Tipo de relatório não encontrado');\r\n    }\r\n    return strategy;\r\n  }\r\n}\r\n"]}