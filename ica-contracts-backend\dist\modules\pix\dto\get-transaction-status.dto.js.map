{"version": 3, "file": "get-transaction-status.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/get-transaction-status.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8D;AAE9D,MAAa,oBAAoB;CAKhC;AALD,oDAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,GAAE;IACR,IAAA,0BAAQ,GAAE;;gDACA", "sourcesContent": ["import { IsDefined, IsString, IsUUID } from 'class-validator';\r\n\r\nexport class TransactionStatusDto {\r\n  @IsDefined()\r\n  @IsUUID()\r\n  @IsString()\r\n  id: string;\r\n}\r\n"]}