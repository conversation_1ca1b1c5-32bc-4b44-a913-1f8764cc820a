import { PixQRCodeCelcoinService } from 'src/apis/celcoin/services/pix-qrcode-celcoin.service';
import { ReadQRCodePixDto } from '../dto/read-qrcode-pix.dto';
export declare class ReadQRCodePixService {
    private pixQRCodeCelcoin;
    constructor(pixQRCodeCelcoin: PixQRCodeCelcoinService);
    perform(data: ReadQRCodePixDto): Promise<import("../../../apis/celcoin/responses/read-qrcode-celcoin.response").IReadQrCodeCelcoinResponse>;
}
