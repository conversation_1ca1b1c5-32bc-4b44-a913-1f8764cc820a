{"version": 3, "file": "dashboard.service.js", "sourceRoot": "/", "sources": ["modules/contract-lifecycle-monitoring/services/dashboard.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAgE;AAChE,6CAAmD;AACnD,qCAAyC;AAEzC,+FAA2F;AAC3F,qHAA+G;AAC/G,qFAAiF;AACjF,iEAA6D;AAItD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YAEmB,2BAAgE,EAEhE,kBAA8C;QAF9C,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,uBAAkB,GAAlB,kBAAkB,CAA4B;IAC9D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,MAAc;QAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEzC,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;QAExE,MAAM,wBAAwB,GAC5B,MAAM,IAAI,CAAC,kCAAkC,CAC3C,0CAAmB,CAAC,oBAAoB,CACzC,CAAC;QAEJ,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,kCAAkC,CACzE,0CAAmB,CAAC,iBAAiB,CACtC,CAAC;QAEF,MAAM,8BAA8B,GAClC,MAAM,IAAI,CAAC,kCAAkC,CAC3C,0CAAmB,CAAC,2BAA2B,CAChD,CAAC;QAEJ,MAAM,8BAA8B,GAClC,MAAM,IAAI,CAAC,kCAAkC,CAC3C,0CAAmB,CAAC,2BAA2B,CAChD,CAAC;QAEJ,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,kCAAkC,CACzE,0CAAmB,CAAC,iBAAiB,CACtC,CAAC;QAEF,MAAM,wBAAwB,GAC5B,MAAM,IAAI,CAAC,kCAAkC,CAC3C,0CAAmB,CAAC,oBAAoB,CACzC,CAAC;QAEJ,OAAO;YACL,sBAAsB;YACtB,wBAAwB;YACxB,qBAAqB;YACrB,8BAA8B;YAC9B,8BAA8B;YAC9B,qBAAqB;YACrB,wBAAwB;SACzB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAc;QAChD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC7D,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;YACzB,KAAK,EAAE;gBACL;oBACE,OAAO,EAAE,MAAM;oBACf,IAAI,EAAE;wBACJ,IAAI,EAAE,IAAA,YAAE,EAAC;4BACP,sBAAS,CAAC,KAAK;4BACf,sBAAS,CAAC,SAAS;4BACnB,sBAAS,CAAC,UAAU;yBACrB,CAAC;qBACH;iBACF;gBACD;oBACE,UAAU,EAAE,MAAM;oBAClB,IAAI,EAAE;wBACJ,IAAI,EAAE,IAAA,YAAE,EAAC;4BACP,sBAAS,CAAC,KAAK;4BACf,sBAAS,CAAC,SAAS;4BACnB,sBAAS,CAAC,UAAU;yBACrB,CAAC;qBACH;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,2BAAkB,CAAC,oBAAoB,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,IAAY;QAClD,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAExD,OAAO,IAAI,CAAC,kBAAkB;aAC3B,kBAAkB,CAAC,UAAU,CAAC;aAC9B,QAAQ,CAAC,mBAAmB,EAAE,UAAU,CAAC;aACzC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC;aACjC,QAAQ,CAAC,iBAAiB,EAAE,OAAO,CAAC;aACpC,KAAK,CAAC,wCAAwC,EAAE,EAAE,aAAa,EAAE,CAAC;aAClE,QAAQ,CAAC,uBAAuB,EAAE,EAAE,QAAQ,EAAE,sBAAS,CAAC,QAAQ,EAAE,CAAC;aACnE,QAAQ,CAAC,kBAAkB,CAAC;aAC5B,QAAQ,EAAE,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,kCAAkC,CAC9C,MAA2B;QAE3B,OAAO,IAAI,CAAC,kBAAkB;aAC3B,kBAAkB,CAAC,UAAU,CAAC;aAC9B,QAAQ,CAAC,mBAAmB,EAAE,UAAU,CAAC;aACzC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC;aACjC,QAAQ,CAAC,iBAAiB,EAAE,OAAO,CAAC;aACpC,KAAK,CAAC,uBAAuB,EAAE,EAAE,QAAQ,EAAE,sBAAS,CAAC,QAAQ,EAAE,CAAC;aAChE,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE;YACf,MAAM,QAAQ,GAAG,EAAE;iBAChB,QAAQ,EAAE;iBACV,MAAM,CAAC,cAAc,CAAC;iBACtB,IAAI,CAAC,gBAAgB,EAAE,WAAW,CAAC;iBACnC,KAAK,CAAC,qCAAqC,CAAC;iBAC5C,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;iBACtC,UAAU,CAAC,qBAAqB,EAAE,MAAM,CAAC;iBACzC,KAAK,CAAC,CAAC,CAAC;iBACR,QAAQ,EAAE,CAAC;YACd,OAAO,cAAc,QAAQ,EAAE,CAAC;QAClC,CAAC,CAAC;aACD,QAAQ,CAAC,wBAAwB,EAAE,EAAE,MAAM,EAAE,CAAC;aAC9C,QAAQ,EAAE,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAAC,IAAY;QACzC,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAhIY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCADa,oBAAU;QAEnB,oBAAU;GALtC,gBAAgB,CAgI5B", "sourcesContent": ["import { ForbiddenException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { In, Repository } from 'typeorm';\r\n\r\nimport { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';\r\nimport { OwnerRoleRelationEntity } from '../../../shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { ContractEventStatus } from '../../../shared/enums/contract-events.enum';\r\nimport { RolesEnum } from '../../../shared/enums/roles.enum';\r\nimport { DashboardDto } from '../dto/dashboard.dto';\r\n\r\n@Injectable()\r\nexport class DashboardService {\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n  ) {}\r\n\r\n  async perform(userId: string): Promise<DashboardDto> {\r\n    await this.verifyAdminPermission(userId);\r\n\r\n    const expiringContractsCount = await this.getExpiringContractsCount(30);\r\n\r\n    const redemptionRequestedCount =\r\n      await this.getContractsCountByLastEventStatus(\r\n        ContractEventStatus.REDEMPTION_REQUESTED,\r\n      );\r\n\r\n    const renewalRequestedCount = await this.getContractsCountByLastEventStatus(\r\n      ContractEventStatus.RENEWAL_REQUESTED,\r\n    );\r\n\r\n    const contractAddendumRequestedCount =\r\n      await this.getContractsCountByLastEventStatus(\r\n        ContractEventStatus.CONTRACT_ADDENDUM_REQUESTED,\r\n      );\r\n\r\n    const contractAddendumConfirmedCount =\r\n      await this.getContractsCountByLastEventStatus(\r\n        ContractEventStatus.CONTRACT_ADDENDUM_CONFIRMED,\r\n      );\r\n\r\n    const renewalConfirmedCount = await this.getContractsCountByLastEventStatus(\r\n      ContractEventStatus.RENEWAL_CONFIRMED,\r\n    );\r\n\r\n    const redemptionConfirmedCount =\r\n      await this.getContractsCountByLastEventStatus(\r\n        ContractEventStatus.REDEMPTION_CONFIRMED,\r\n      );\r\n\r\n    return {\r\n      expiringContractsCount,\r\n      redemptionRequestedCount,\r\n      renewalRequestedCount,\r\n      contractAddendumRequestedCount,\r\n      contractAddendumConfirmedCount,\r\n      renewalConfirmedCount,\r\n      redemptionConfirmedCount,\r\n    };\r\n  }\r\n\r\n  private async verifyAdminPermission(userId: string): Promise<void> {\r\n    const isAdmin = await this.ownerRoleRelationRepository.findOne({\r\n      relations: { role: true },\r\n      where: [\r\n        {\r\n          ownerId: userId,\r\n          role: {\r\n            name: In([\r\n              RolesEnum.ADMIN,\r\n              RolesEnum.RETENTION,\r\n              RolesEnum.SUPERADMIN,\r\n            ]),\r\n          },\r\n        },\r\n        {\r\n          businessId: userId,\r\n          role: {\r\n            name: In([\r\n              RolesEnum.ADMIN,\r\n              RolesEnum.RETENTION,\r\n              RolesEnum.SUPERADMIN,\r\n            ]),\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (!isAdmin) {\r\n      throw new ForbiddenException('Forbidden resource');\r\n    }\r\n  }\r\n\r\n  private async getExpiringContractsCount(days: number): Promise<number> {\r\n    const dateThreshold = this.calculateDateThreshold(days);\r\n\r\n    return this.contractRepository\r\n      .createQueryBuilder('contract')\r\n      .leftJoin('contract.investor', 'investor')\r\n      .leftJoin('investor.role', 'role')\r\n      .leftJoin('contract.events', 'event')\r\n      .where('contract.endContract <= :dateThreshold', { dateThreshold })\r\n      .andWhere('role.name = :roleName', { roleName: RolesEnum.INVESTOR })\r\n      .andWhere('event.id IS NULL')\r\n      .getCount();\r\n  }\r\n\r\n  private async getContractsCountByLastEventStatus(\r\n    status: ContractEventStatus,\r\n  ): Promise<number> {\r\n    return this.contractRepository\r\n      .createQueryBuilder('contract')\r\n      .leftJoin('contract.investor', 'investor')\r\n      .leftJoin('investor.role', 'role')\r\n      .leftJoin('contract.events', 'event')\r\n      .where('role.name = :roleName', { roleName: RolesEnum.INVESTOR })\r\n      .andWhere((qb) => {\r\n        const subQuery = qb\r\n          .subQuery()\r\n          .select('event_sub.id')\r\n          .from('contract_event', 'event_sub')\r\n          .where('event_sub.contract_id = contract.id')\r\n          .orderBy('event_sub.eventDate', 'DESC')\r\n          .addOrderBy('event_sub.createdAt', 'DESC')\r\n          .limit(1)\r\n          .getQuery();\r\n        return `event.id = ${subQuery}`;\r\n      })\r\n      .andWhere('event.status = :status', { status })\r\n      .getCount();\r\n  }\r\n\r\n  private calculateDateThreshold(days: number): Date {\r\n    const date = new Date();\r\n    date.setDate(date.getDate() + days);\r\n    return date;\r\n  }\r\n}\r\n"]}