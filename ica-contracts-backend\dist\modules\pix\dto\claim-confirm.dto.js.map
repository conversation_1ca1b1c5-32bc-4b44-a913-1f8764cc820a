{"version": 3, "file": "claim-confirm.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/claim-confirm.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA0E;AAC1E,+EAAqE;AAErE,MAAa,eAAe;CAQ3B;AARD,0CAQC;AALC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;4CACC;AAIZ;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,mCAAe,CAAC;;+CACA", "sourcesContent": ["import { IsDefined, IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { ClaimReasonEnum } from 'src/shared/enums/claim-reason.enum';\r\n\r\nexport class ClaimConfirmDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  key: string;\r\n\r\n  @IsOptional()\r\n  @IsEnum(ClaimReasonEnum)\r\n  reason: ClaimReasonEnum;\r\n}\r\n"]}