import type { WebhookEmailDto } from '@/contexts/income-report/application/dto/webhook-email.dto'
import type { WebhookEmailUseCase } from '@/contexts/income-report/application/usecases/webhook-email.usecase'
import { badRequest, ok } from '../helpers/http-helper'
import type { HttpRequest, HttpResponse, IController } from '../protocols'
import type { IValidator } from '../validation/validator'

export class RequestWebhookController implements IController {
  constructor(
    private useCase: WebhookEmailUseCase,
    private readonly validator: IValidator<WebhookEmailDto>
  ) {}

  async handle(request: HttpRequest<WebhookEmailDto>): Promise<HttpResponse> {
    const validationResult = this.validator.validate(request.body)
    if (validationResult.isLeft()) {
      const error = validationResult.value
      return badRequest({ error: error.name, message: error.errors })
    }

    const response = await this.useCase.execute(validationResult.value)

    return ok(response)
  }
}
