import { type OnModuleInit } from '@nestjs/common';
import { ReportRegistryService } from './services/report-registry.service';
import { AcquisitionReportStrategy } from './services/strategies/acquisition-reports.strategy';
import { PaymentReportsStrategy } from './services/strategies/payment-reports.strategy';
import { ScheduledPaymentsReportsStrategy } from './services/strategies/scheduled-payments-reports.strategy';
export declare class ReportModule implements OnModuleInit {
    private readonly registry;
    private readonly acquisitionReportStrategy;
    private readonly scheduledPaymentsReportsStrategy;
    private readonly paymentReportsStrategy;
    constructor(registry: ReportRegistryService, acquisitionReportStrategy: AcquisitionReportStrategy, scheduledPaymentsReportsStrategy: ScheduledPaymentsReportsStrategy, paymentReportsStrategy: PaymentReportsStrategy);
    onModuleInit(): void;
}
