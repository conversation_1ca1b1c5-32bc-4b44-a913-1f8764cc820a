"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TedModule = void 0;
const common_1 = require("@nestjs/common");
const apis_module_1 = require("../../apis/apis.module");
const shared_module_1 = require("../../shared/shared.module");
const account_transfer_limit_module_1 = require("../account-transfer-limit/account-transfer-limit.module");
const two_factor_auth_module_1 = require("../two-factor-auth/two-factor-auth.module");
const two_factor_auth_service_1 = require("../two-factor-auth/two-factor-auth.service");
const ted_controller_1 = require("./controllers/ted.controller");
const delete_favorite_ted_contact_service_1 = require("./services/delete-favorite-ted-contact.service");
const get_favorite_ted_contact_service_1 = require("./services/get-favorite-ted-contact.service");
const get_recent_teds_service_1 = require("./services/get-recent-teds.service");
const get_ted_service_1 = require("./services/get-ted.service");
const save_favorite_ted_contact_service_1 = require("./services/save-favorite-ted-contact.service");
const send_ted_service_1 = require("./services/send-ted.service");
let TedModule = class TedModule {
};
exports.TedModule = TedModule;
exports.TedModule = TedModule = __decorate([
    (0, common_1.Module)({
        controllers: [ted_controller_1.TedController],
        imports: [
            shared_module_1.SharedModule,
            apis_module_1.ApisModule,
            account_transfer_limit_module_1.AccountTransferLimitModule,
            two_factor_auth_module_1.TwoFactorAuthModule,
        ],
        providers: [
            send_ted_service_1.SendTedService,
            get_ted_service_1.GetTedService,
            get_recent_teds_service_1.GetRecentTedsService,
            save_favorite_ted_contact_service_1.SaveFavoriteTedContactService,
            get_favorite_ted_contact_service_1.GetFavoriteTedContactService,
            delete_favorite_ted_contact_service_1.DeleteFavoriteTedContactService,
            two_factor_auth_service_1.TwoFactorAuthService,
        ],
    })
], TedModule);
//# sourceMappingURL=ted.module.js.map