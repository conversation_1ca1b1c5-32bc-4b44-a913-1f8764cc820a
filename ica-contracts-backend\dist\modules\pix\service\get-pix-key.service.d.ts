import { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';
import { IGetPixKeyReponse } from '../response/get-pix-key-response';
export declare class GetPixKeyService {
    private accountDb;
    private celcoinService;
    constructor(accountDb: Repository<AccountEntity>, celcoinService: PixKeyCelcoinService);
    perform(id: string): Promise<IGetPixKeyReponse[]>;
}
