"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendPreRegisterService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const jsonwebtoken_1 = require("jsonwebtoken");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const pre_register_entity_1 = require("../../../shared/database/typeorm/entities/pre-register.entity");
const typeorm_2 = require("typeorm");
let SendPreRegisterService = class SendPreRegisterService {
    constructor(preRegisterDb, accountDb, contractRepository) {
        this.preRegisterDb = preRegisterDb;
        this.accountDb = accountDb;
        this.contractRepository = contractRepository;
    }
    async perform(data) {
        const secretKey = process.env.PRE_REGISTER_TOKEN;
        const expirationTime = '1d';
        const preRegister = await this.preRegisterDb.findOneBy({
            document: (0, typeorm_2.Equal)(data.document),
        });
        if (preRegister)
            throw new common_1.BadRequestException('Ja existe um pre contrato para este documento');
        const account = await this.accountDb.findOne({
            relations: {
                owner: true,
                business: true,
            },
            where: [
                {
                    owner: {
                        cpf: (0, typeorm_2.Equal)(data.document),
                    },
                },
                {
                    business: {
                        cnpj: (0, typeorm_2.Equal)(data.document),
                    },
                },
            ],
        });
        if (account)
            throw new common_1.BadRequestException('Ja existe uma conta para este documento');
        const existContract = await this.contractRepository.findOne({
            where: {
                investor: [
                    {
                        owner: {
                            cpf: (0, typeorm_2.Equal)(data.document),
                        },
                    },
                    {
                        business: {
                            cnpj: (0, typeorm_2.Equal)(data.document),
                        },
                    },
                ],
            },
        });
        if (existContract)
            throw new common_1.BadRequestException('Já existe um contrato ativo para este documento');
        const token = (0, jsonwebtoken_1.sign)({
            adviserId: data.adviserId,
            investorEmail: data.email,
            investorDocument: data.document,
            investmentValue: data.investment.value,
            investmentTerm: data.investment.term,
            investmentModality: data.investment.modality,
            investmentYield: data.investment.yield,
            investmentPurchaseWith: data.investment.purchaseWith,
            investmentAmountQuotes: data.investment?.amountQuotes,
            investmentStartContract: data.investment.startContract,
            investmentEndContract: data.investment.endContract,
            investmentGracePeriod: data.investment.gracePeriod,
            investmentDebenture: data.investment.debenture,
            investmentObservations: data.investment.observations,
            investorOwnerCpf: data.owner?.cpf,
            investorAccountType: data.accountType,
            investorOwnerName: data.owner?.name,
            signIca: data.signIca,
            brokerParticipationPercentage: data.investment?.brokerParticipationPercentage,
            advisorParticipationPercentage: data.investment?.advisorParticipationPercentage,
        }, secretKey, {
            expiresIn: expirationTime,
        });
        return {
            token,
        };
    }
};
exports.SendPreRegisterService = SendPreRegisterService;
exports.SendPreRegisterService = SendPreRegisterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(pre_register_entity_1.PreRegisterEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], SendPreRegisterService);
//# sourceMappingURL=send-pre-register.service.js.map