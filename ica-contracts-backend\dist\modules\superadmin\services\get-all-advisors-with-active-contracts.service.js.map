{"version": 3, "file": "get-all-advisors-with-active-contracts.service.js", "sourceRoot": "/", "sources": ["modules/superadmin/services/get-all-advisors-with-active-contracts.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,yDAAsD;AACtD,2CAAoD;AACpD,6CAAmD;AAEnD,qCAAsD;AAEtD,+GAAqG;AAGrG,kEAAmE;AACnE,+FAAsF;AACtF,qFAA2E;AAGpE,IAAM,wCAAwC,GAA9C,MAAM,wCAAwC;IACnD,YAEU,0BAA6D,EACtC,YAAmB;QAD1C,+BAA0B,GAA1B,0BAA0B,CAAmC;QACtC,iBAAY,GAAZ,YAAY,CAAO;IACjD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAgD;QAC5D,MAAM,QAAQ,GAAG,mBAAmB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;QAE5D,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CACzB,QAAQ,CACT,CAAC;QAEJ,IAAI,MAAM;YAAE,OAAO,MAAM,CAAC;QAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GACzB,uCAAoB,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAElD,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,0BAA0B;aAC/C,kBAAkB,CAAC,kBAAkB,CAAC;aACtC,SAAS,CAAC,0BAA0B,EAAE,SAAS,CAAC;aAChD,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC;aAClC,QAAQ,CAAC,kBAAkB,EAAE,UAAU,CAAC;aACxC,SAAS,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAClD,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC,4BAA4B,EAAE,OAAO,CAAC;aAC7C,KAAK,CAAC,OAAO,CAAC;aACd,QAAQ,CAAC,gCAAgC,EAAE;YAC1C,KAAK,EAAE,IAAI,IAAI,EAAE;SAClB,CAAC;aACD,QAAQ,CAAC,mCAAmC,EAAE;YAC7C,cAAc,EAAE,yCAAkB,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEL,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,uCAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACtE,UAAU,CAAC,QAAQ,CACjB,qDAAqD,EACrD;gBACE,KAAK;gBACL,GAAG;aACJ,CACF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,UAAU,CAAC,QAAQ,CAAC,gDAAgD,EAAE;gBACpE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,UAAU,CAAC,SAAS,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE5B,MAAM,aAAa,GAAG,IAAI,CAAC,0BAA0B;aAClD,kBAAkB,CAAC,kBAAkB,CAAC;aACtC,SAAS,CAAC,0BAA0B,EAAE,SAAS,CAAC;aAChD,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC;aAClC,QAAQ,CAAC,kBAAkB,EAAE,UAAU,CAAC;aACxC,SAAS,CAAC,2BAA2B,EAAE,UAAU,CAAC;aAClD,QAAQ,CAAC,sBAAsB,EAAE,aAAa,CAAC;aAC/C,iBAAiB,CAChB,mBAAmB,EACnB,UAAU,EACV,2BAA2B,EAC3B,EAAE,MAAM,EAAE,gCAAc,CAAC,YAAY,EAAE,CACxC;aACA,MAAM,CAAC;YACN,0CAA0C;YAC1C,oDAAoD;YACpD,mDAAmD;YACnD,gGAAgG;SACjG,CAAC;aACD,KAAK,CAAC,OAAO,CAAC;aACd,QAAQ,CAAC,gCAAgC,EAAE;YAC1C,KAAK,EAAE,IAAI,IAAI,EAAE;SAClB,CAAC;aACD,QAAQ,CAAC,mCAAmC,EAAE;YAC7C,cAAc,EAAE,yCAAkB,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEL,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,uCAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEtE,aAAa,CAAC,QAAQ,CACpB,qDAAqD,EACrD;gBACE,KAAK;gBACL,GAAG;aACJ,CACF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,aAAa,CAAC,QAAQ,CAAC,gDAAgD,EAAE;gBACvE,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,mBAAmB,GAAG,MAAM,aAAa;aAC5C,OAAO,CACN,4EAA4E,CAC7E;aACA,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;aAC7B,MAAM,CAAC,IAAI,CAAC;aACZ,KAAK,CAAC,KAAK,CAAC;aACZ,UAAU,EAKP,CAAC;QAEP,MAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAC1D,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK;YACpC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;SACvC,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GACZ,uCAAoB,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAE3E,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AA5IY,4FAAwC;mDAAxC,wCAAwC;IADpD,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,+CAAqB,CAAC,CAAA;IAEvC,WAAA,IAAA,eAAM,EAAC,6BAAa,CAAC,CAAA;qCADc,oBAAU;GAHrC,wCAAwC,CA4IpD", "sourcesContent": ["import { CACHE_MANAGER } from '@nestjs/cache-manager';\r\nimport { Inject, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Cache } from 'cache-manager';\r\nimport { MoreThanOrEqual, Repository } from 'typeorm';\r\n\r\nimport { ContractAdvisorEntity } from 'src/shared/database/typeorm/entities/contract-advisor.entity';\r\nimport { GetAllAdvisorsWithActiveContractsQueryDto } from '../dto/get-all-advisors-with-active-contracts/query.dto';\r\nimport { GetAllAdvisorsWithActiveContractsResponseDto } from '../dto/get-all-advisors-with-active-contracts/response.dto';\r\nimport { PaginatedQueryHelper } from '../helpers/pagination-query';\r\nimport { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';\r\nimport { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';\r\n\r\n@Injectable()\r\nexport class GetAllAdvisorsWithActiveContractsService {\r\n  constructor(\r\n    @InjectRepository(ContractAdvisorEntity)\r\n    private contractsAdvisorRepository: Repository<ContractAdvisorEntity>,\r\n    @Inject(CACHE_MANAGER) private cacheManager: Cache,\r\n  ) {}\r\n\r\n  async perform(query: GetAllAdvisorsWithActiveContractsQueryDto) {\r\n    const cacheKey = `active-advisors-${JSON.stringify(query)}`;\r\n\r\n    const cached =\r\n      await this.cacheManager.get<GetAllAdvisorsWithActiveContractsResponseDto>(\r\n        cacheKey,\r\n      );\r\n\r\n    if (cached) return cached;\r\n\r\n    const { page, limit, skip } =\r\n      PaginatedQueryHelper.getPaginationParams(query);\r\n\r\n    const filters = {\r\n      isActive: true,\r\n    };\r\n\r\n    const totalQuery = this.contractsAdvisorRepository\r\n      .createQueryBuilder('contract_advisor')\r\n      .innerJoin('contract_advisor.advisor', 'advisor')\r\n      .leftJoin('advisor.owner', 'owner')\r\n      .leftJoin('advisor.business', 'business')\r\n      .innerJoin('contract_advisor.contract', 'contract')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select('COUNT(DISTINCT advisor.id)', 'count')\r\n      .where(filters)\r\n      .andWhere('contract.endContract >= :today', {\r\n        today: new Date(),\r\n      })\r\n      .andWhere('contract.status = :contractStatus', {\r\n        contractStatus: ContractStatusEnum.ACTIVE,\r\n      });\r\n\r\n    if (query.dateTo || query.dateFrom) {\r\n      const { start, end } = PaginatedQueryHelper.getDateRangeParams(query);\r\n      totalQuery.andWhere(\r\n        'contract_advisor.created_at BETWEEN :start AND :end',\r\n        {\r\n          start,\r\n          end,\r\n        },\r\n      );\r\n    }\r\n\r\n    if (query.contractType) {\r\n      totalQuery.andWhere('signataries.investmentModality = :contractType', {\r\n        contractType: query.contractType,\r\n      });\r\n    }\r\n\r\n    const { count } = await totalQuery.getRawOne();\r\n    const total = Number(count);\r\n\r\n    const advisorsQuery = this.contractsAdvisorRepository\r\n      .createQueryBuilder('contract_advisor')\r\n      .innerJoin('contract_advisor.advisor', 'advisor')\r\n      .leftJoin('advisor.owner', 'owner')\r\n      .leftJoin('advisor.business', 'business')\r\n      .innerJoin('contract_advisor.contract', 'contract')\r\n      .leftJoin('contract.signataries', 'signataries')\r\n      .leftJoinAndSelect(\r\n        'contract.addendum',\r\n        'addendum',\r\n        'addendum.status = :status',\r\n        { status: AddendumStatus.FULLY_SIGNED },\r\n      )\r\n      .select([\r\n        'contract_advisor.id_advisor as advisorid',\r\n        'COALESCE(owner.name, business.companyName) as name',\r\n        'COALESCE(owner.avatar, business.avatar) as avatar',\r\n        'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0)) as totalvalue',\r\n      ])\r\n      .where(filters)\r\n      .andWhere('contract.endContract >= :today', {\r\n        today: new Date(),\r\n      })\r\n      .andWhere('contract.status = :contractStatus', {\r\n        contractStatus: ContractStatusEnum.ACTIVE,\r\n      });\r\n\r\n    if (query.dateTo || query.dateFrom) {\r\n      const { start, end } = PaginatedQueryHelper.getDateRangeParams(query);\r\n\r\n      advisorsQuery.andWhere(\r\n        'contract_advisor.created_at BETWEEN :start AND :end',\r\n        {\r\n          start,\r\n          end,\r\n        },\r\n      );\r\n    }\r\n\r\n    if (query.contractType) {\r\n      advisorsQuery.andWhere('signataries.investmentModality = :contractType', {\r\n        contractType: query.contractType,\r\n      });\r\n    }\r\n\r\n    const advisorsQueryResult = await advisorsQuery\r\n      .groupBy(\r\n        'advisorId, owner.name, business.companyName, owner.avatar, business.avatar',\r\n      )\r\n      .orderBy('totalvalue', 'DESC')\r\n      .offset(skip)\r\n      .limit(limit)\r\n      .getRawMany<{\r\n        advisorid: string;\r\n        name: string;\r\n        avatar: string;\r\n        totalvalue: string;\r\n      }>();\r\n\r\n    const result = advisorsQueryResult.map((advisor, index) => ({\r\n      advisorId: advisor.advisorid,\r\n      rank: index + 1 + (page - 1) * limit,\r\n      name: advisor.name,\r\n      avatar: advisor.avatar,\r\n      totalValue: Number(advisor.totalvalue),\r\n    }));\r\n\r\n    const response: GetAllAdvisorsWithActiveContractsResponseDto =\r\n      PaginatedQueryHelper.createPaginatedResponse(result, total, page, limit);\r\n\r\n    await this.cacheManager.set(cacheKey, response, 60000);\r\n\r\n    return response;\r\n  }\r\n}\r\n"]}