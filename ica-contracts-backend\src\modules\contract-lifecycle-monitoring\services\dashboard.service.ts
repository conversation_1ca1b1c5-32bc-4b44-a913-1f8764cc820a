import { ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';

import { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from '../../../shared/database/typeorm/entities/owner-role-relation.entity';
import { ContractEventStatus } from '../../../shared/enums/contract-events.enum';
import { RolesEnum } from '../../../shared/enums/roles.enum';
import { DashboardDto } from '../dto/dashboard.dto';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(OwnerRoleRelationEntity)
    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
  ) {}

  async perform(userId: string): Promise<DashboardDto> {
    await this.verifyAdminPermission(userId);

    const expiringContractsCount = await this.getExpiringContractsCount(30);

    const redemptionRequestedCount =
      await this.getContractsCountByLastEventStatus(
        ContractEventStatus.REDEMPTION_REQUESTED,
      );

    const renewalRequestedCount = await this.getContractsCountByLastEventStatus(
      ContractEventStatus.RENEWAL_REQUESTED,
    );

    const contractAddendumRequestedCount =
      await this.getContractsCountByLastEventStatus(
        ContractEventStatus.CONTRACT_ADDENDUM_REQUESTED,
      );

    const contractAddendumConfirmedCount =
      await this.getContractsCountByLastEventStatus(
        ContractEventStatus.CONTRACT_ADDENDUM_CONFIRMED,
      );

    const renewalConfirmedCount = await this.getContractsCountByLastEventStatus(
      ContractEventStatus.RENEWAL_CONFIRMED,
    );

    const redemptionConfirmedCount =
      await this.getContractsCountByLastEventStatus(
        ContractEventStatus.REDEMPTION_CONFIRMED,
      );

    return {
      expiringContractsCount,
      redemptionRequestedCount,
      renewalRequestedCount,
      contractAddendumRequestedCount,
      contractAddendumConfirmedCount,
      renewalConfirmedCount,
      redemptionConfirmedCount,
    };
  }

  private async verifyAdminPermission(userId: string): Promise<void> {
    const isAdmin = await this.ownerRoleRelationRepository.findOne({
      relations: { role: true },
      where: [
        {
          ownerId: userId,
          role: {
            name: In([
              RolesEnum.ADMIN,
              RolesEnum.RETENTION,
              RolesEnum.SUPERADMIN,
            ]),
          },
        },
        {
          businessId: userId,
          role: {
            name: In([
              RolesEnum.ADMIN,
              RolesEnum.RETENTION,
              RolesEnum.SUPERADMIN,
            ]),
          },
        },
      ],
    });

    if (!isAdmin) {
      throw new ForbiddenException('Forbidden resource');
    }
  }

  private async getExpiringContractsCount(days: number): Promise<number> {
    const dateThreshold = this.calculateDateThreshold(days);

    return this.contractRepository
      .createQueryBuilder('contract')
      .leftJoin('contract.investor', 'investor')
      .leftJoin('investor.role', 'role')
      .leftJoin('contract.events', 'event')
      .where('contract.endContract <= :dateThreshold', { dateThreshold })
      .andWhere('role.name = :roleName', { roleName: RolesEnum.INVESTOR })
      .andWhere('event.id IS NULL')
      .getCount();
  }

  private async getContractsCountByLastEventStatus(
    status: ContractEventStatus,
  ): Promise<number> {
    return this.contractRepository
      .createQueryBuilder('contract')
      .leftJoin('contract.investor', 'investor')
      .leftJoin('investor.role', 'role')
      .leftJoin('contract.events', 'event')
      .where('role.name = :roleName', { roleName: RolesEnum.INVESTOR })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('event_sub.id')
          .from('contract_event', 'event_sub')
          .where('event_sub.contract_id = contract.id')
          .orderBy('event_sub.eventDate', 'DESC')
          .addOrderBy('event_sub.createdAt', 'DESC')
          .limit(1)
          .getQuery();
        return `event.id = ${subQuery}`;
      })
      .andWhere('event.status = :status', { status })
      .getCount();
  }

  private calculateDateThreshold(days: number): Date {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date;
  }
}
