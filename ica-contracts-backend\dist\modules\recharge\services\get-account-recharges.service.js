"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetAccountRecharges = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const get_account_transactions_service_1 = require("../../../apis/icainvest-credit/services/get-account-transactions.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const typeorm_2 = require("typeorm");
let GetAccountRecharges = class GetAccountRecharges {
    constructor(accountDb, getAccountTransactionsService) {
        this.accountDb = accountDb;
        this.getAccountTransactionsService = getAccountTransactionsService;
    }
    async perform(data) {
        const account = await this.accountDb.findOne({
            relations: {
                recharge: true,
            },
            where: {
                id: data.accountId,
            },
        });
        if (!account) {
            throw new common_1.BadRequestException('Conta não encontrada para recarga.');
        }
        const transactions = await this.getAccountTransactionsService.perform({ id: account.id });
        let filteredTransactions;
        const { startDate, endDate } = data;
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            if (start > end) {
                throw new common_1.BadRequestException('A data inicial não pode ser posterior à data final.');
            }
            filteredTransactions = this.filterTransactionsByDate(transactions, start, end);
        }
        else {
            filteredTransactions = transactions;
        }
        return this.groupTransactionsByType(filteredTransactions);
    }
    filterTransactionsByDate(transactions, start, end) {
        return transactions.filter((transaction) => {
            const transactionDate = new Date(transaction.date);
            return transactionDate >= start && transactionDate <= end;
        });
    }
    groupTransactionsByType(transactions) {
        const debitTransactions = transactions.filter((transaction) => transaction.type === 'DEBIT');
        const creditTransactions = transactions.filter((transaction) => transaction.type === 'CREDIT');
        const rechargeTransactions = transactions.filter((transaction) => transaction.type === 'RECHARGE');
        return {
            debits: debitTransactions,
            credits: creditTransactions,
            recharges: rechargeTransactions,
        };
    }
};
exports.GetAccountRecharges = GetAccountRecharges;
exports.GetAccountRecharges = GetAccountRecharges = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        get_account_transactions_service_1.GetAccountTransactionsService])
], GetAccountRecharges);
//# sourceMappingURL=get-account-recharges.service.js.map