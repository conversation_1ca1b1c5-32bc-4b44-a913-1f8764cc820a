import axios from 'axios';
import { Injectable, HttpException } from '@nestjs/common';
import { DetailsReportInvestorDto } from 'src/modules/income-report/dto/details-report-investor.dto';

@Injectable()
export class DetailsReportInvestorService {
  async perform({ investorId, year }: DetailsReportInvestorDto): Promise<void> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/details-income-report`;

      const resp = await axios.post(url, { investorId, year });
      return resp.data;
    } catch (error) {
      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }
}
