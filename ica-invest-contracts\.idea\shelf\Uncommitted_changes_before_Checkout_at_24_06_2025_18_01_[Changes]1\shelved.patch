Index: src/components/Select/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { ArrowDownIcon, ChevronDownIcon, ChevronUpIcon } from \"@heroicons/react/24/outline\";\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\ninterface Option {\r\n  label: string;\r\n  value: string;\r\n}\r\n\r\ninterface IProps {\r\n  selected: string\r\n  setSelected: (d: any) => void\r\n  options: Option[]\r\n  width?: string\r\n  size?: 'small' | 'normal'\r\n  align?: 'center' | 'start'\r\n  zIndex?: number\r\n}\r\n\r\nexport default function Select({ \r\n  options, \r\n  selected, \r\n  setSelected, \r\n  width = '100%', \r\n  size = 'normal', \r\n  align, \r\n  zIndex = 10,\r\n}: IProps) {\r\n  const [isOpen, setIsOpen] = useState<boolean>();\r\n  const [optSelected, setOptSelected] = useState(selected)\r\n\r\n  useEffect(() => {\r\n    setOptSelected(options.filter(opt => opt.value === selected)[0]?.label)\r\n  }, [])\r\n  \r\n  useEffect(() => {\r\n    setOptSelected(options.filter(opt => opt.value === selected)[0]?.label)\r\n  }, [selected])\r\n\r\n  const toggleDropdown = () => setIsOpen((prev) => !prev);\r\n\r\n  const handleOptionClick = (opt: {value: string, label: string}) => {\r\n    setOptSelected(opt.label)\r\n    setSelected(opt.value);\r\n    setIsOpen(false);\r\n  };\r\n\r\n  const returnTypeBG = (option: string) => {\r\n    if(size === 'normal') {\r\n      if(selected === option) {\r\n        return \"bg-[#3A3A3A80] mx-2\"\r\n      }\r\n    } else {\r\n      if(selected === option) {\r\n        return \"bg-[#7c787880] mx-2\"\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className={`relative w-[${width}]`}>\r\n      <button\r\n        style={{\r\n          zIndex: zIndex + 1\r\n        }}\r\n        className={`w-full justify-between relative text-left ${size === 'normal' ? 'bg-[#3A3A3A]' : 'bg-[#303030]'} ${size === 'normal' ? 'border px-2 py-2 text-sm' : ' py-2 px-4 text-sm'} text-white rounded-md shadow-md focus:outline-none flex items-center`}\r\n        onClick={toggleDropdown}\r\n      >\r\n        {optSelected}\r\n        <div className={`ml-2 ${isOpen === true && 'animate-flipIn rotate-180'} ${isOpen === false && 'animate-flipOut'}`}>\r\n          <ChevronDownIcon width={20} />\r\n        </div>\r\n      </button>\r\n      <div style={{zIndex: zIndex, top: '20px'}} className={`absolute w-full ${size === 'normal' ? 'bg-[#ADADAD]' : 'bg-[#242424]'} rounded-b-md shadow-lg py-2 ${isOpen ? 'animate-openY' : 'hidden'} `}>\r\n        <div className=\"mt-5 max-h-40 overflow-auto scroll-mx-1\">\r\n          {options.map((option, index) => {\r\n            return (\r\n              <div\r\n                key={option.value}\r\n                className={`px-3 py-2 ${align === 'center' && 'text-center'} text-white cursor-pointer text-sm rounded-lg ${size === 'normal' ? 'hover:bg-[#3a3a3a50]' : 'hover:bg-[#52525250]'} hover:mx-2 my-1 transition-transform ${returnTypeBG(option.value)} ${index === options.length ? 'rounded-b-lg' : ''}`}\r\n                onClick={() => handleOptionClick(option)}\r\n              >\r\n                {option.label}\r\n              </div>\r\n            )\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/Select/index.tsx b/src/components/Select/index.tsx
--- a/src/components/Select/index.tsx	(revision 2bc0ab946d1dfaf07ee4b51f553087d96a5ed19b)
+++ b/src/components/Select/index.tsx	(date 1750766113223)
@@ -47,11 +47,11 @@
   const returnTypeBG = (option: string) => {
     if(size === 'normal') {
       if(selected === option) {
-        return "bg-[#3A3A3A80] mx-2"
+        return "bg-[#FF9900] mx-2 text-black"
       }
     } else {
       if(selected === option) {
-        return "bg-[#7c787880] mx-2"
+        return "bg-[#FF9900] mx-2 text-black"
       }
     }
   }
@@ -70,13 +70,13 @@
           <ChevronDownIcon width={20} />
         </div>
       </button>
-      <div style={{zIndex: zIndex, top: '20px'}} className={`absolute w-full ${size === 'normal' ? 'bg-[#ADADAD]' : 'bg-[#242424]'} rounded-b-md shadow-lg py-2 ${isOpen ? 'animate-openY' : 'hidden'} `}>
+      <div style={{zIndex: zIndex, top: '20px'}} className={`absolute w-full ${size === 'normal' ? 'bg-[#1C1C1C]' : 'bg-[#242424]'} rounded-b-md shadow-lg py-2 ${isOpen ? 'animate-openY' : 'hidden'} border border-[#3A3A3A]`}>
         <div className="mt-5 max-h-40 overflow-auto scroll-mx-1">
           {options.map((option, index) => {
             return (
               <div
                 key={option.value}
-                className={`px-3 py-2 ${align === 'center' && 'text-center'} text-white cursor-pointer text-sm rounded-lg ${size === 'normal' ? 'hover:bg-[#3a3a3a50]' : 'hover:bg-[#52525250]'} hover:mx-2 my-1 transition-transform ${returnTypeBG(option.value)} ${index === options.length ? 'rounded-b-lg' : ''}`}
+                className={`px-3 py-2 ${align === 'center' && 'text-center'} text-white cursor-pointer text-sm rounded-lg ${size === 'normal' ? 'hover:bg-[#3A3A3A]' : 'hover:bg-[#525252]'} hover:mx-2 my-1 transition-transform ${returnTypeBG(option.value)} ${index === options.length ? 'rounded-b-lg' : ''}`}
                 onClick={() => handleOptionClick(option)}
               >
                 {option.label}
Index: src/components/FilterModal/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import { AdjustmentsHorizontalIcon, ChevronDownIcon, ChevronUpIcon, XCircleIcon } from \"@heroicons/react/24/outline\"\r\nimport InputSearch from \"../InputSearch\"\r\nimport { useState } from \"react\"\r\nimport Select from \"../Select\"\r\nimport { Button } from \"../ui/button\"\r\nimport { DatePicker } from \"../ui/datePicker\"\r\nimport SelectCustom from \"../SelectCustom\"\r\nimport filterStatusContracts from '@/constants/filterStatusContract'\r\n\r\ninterface IProps {\r\n  activeModal: boolean\r\n  setActiveModal: (active: boolean) => void\r\n  inputPlaceholder?: string\r\n  filterData?: {\r\n    startData: string\r\n    endData?: string\r\n    input?: string\r\n    type?: string\r\n    filterOptionSelected?: string\r\n    filterOptions?: {\r\n      label: string\r\n      value: string\r\n    }[]\r\n  }\r\n  hidenButton?: boolean\r\n  children?: React.ReactNode\r\n  setFilterData?: (data: any) => void\r\n  handleSearch?: () => void\r\n}\r\n\r\nconst filterTypeContractOptions = [\r\n  {\r\n    label: \"Todos\",\r\n    value: \"all\"\r\n  },\r\n  {\r\n    label: \"Contratos SCP\",\r\n    value: \"SCP\"\r\n  },\r\n  {\r\n    label: \"Contratos Mútuo\",\r\n    value: \"MUTUO\"\r\n  },\r\n]\r\n\r\nexport default function FilterModal({activeModal, setActiveModal, filterData, setFilterData, handleSearch, inputPlaceholder = \"Pesquisar\", children, hidenButton}: IProps) {\r\n  const renderInputs = () => {\r\n    if(children) {\r\n      return (\r\n        <div className=\"mt-5\">{children}</div>\r\n      )\r\n    } else {\r\n      if(filterData && setFilterData) {\r\n        return (\r\n          <div className=\"flex flex-col justify-between mt-2\">\r\n            <div className='mb-4 flex flex-row justify-between gap-2'>\r\n              {\r\n                filterData.startData !== undefined && (\r\n                  <div className='mb-2 md:mb-0 w-[48%]'>\r\n                    <p className='text-xs'>Início</p>\r\n                    <DatePicker value={filterData.startData} setValue={(date) => {\r\n                      setFilterData({\r\n                        ...filterData,\r\n                        startData: date,\r\n                      })\r\n                    }}/>\r\n                  </div>\r\n                )\r\n              }\r\n              {\r\n                filterData.endData !== undefined && (\r\n                  <div className='mb-2 md:mb-0 w-[48%]'>\r\n                    <p className='text-xs'>Fim</p>\r\n                    <DatePicker value={filterData.endData} setValue={(date) => {\r\n                      setFilterData({\r\n                        ...filterData,\r\n                        endData: date\r\n                      })\r\n                    }}/>\r\n                  </div>\r\n                )\r\n              }\r\n            </div>\r\n            {\r\n              filterData.type !== undefined && (\r\n                <div className=\"w-full\">\r\n                  <p className='text-xs'>Tipo de contrato</p>\r\n                  <SelectCustom\r\n                    value={filterData.type}\r\n                    onChange={(e) => {\r\n                      setFilterData({...filterData, type: e.target.value})\r\n                    }}\r\n                  >\r\n                    {filterTypeContractOptions.map((option, index) => (\r\n                      <option key={index} value={option.value}>{option.label}</option>\r\n                    ))}\r\n                  </SelectCustom>\r\n                </div>\r\n              )\r\n            }\r\n            {\r\n              filterData.filterOptionSelected !== undefined && (\r\n                <div className=\"w-full mt-4\">\r\n                  <p className='text-xs'>Status do contrato</p>\r\n                  <SelectCustom\r\n                    value={filterData.filterOptionSelected}\r\n                    onChange={(e) => {\r\n                      console.log(e.target.value)\r\n                      setFilterData({\r\n                        ...filterData,\r\n                        filterOptionSelected: e.target.value\r\n                      })\r\n                    }}\r\n                  >\r\n                    {filterStatusContracts.map((status, index) => (\r\n                      <option key={index} value={status.value}>{status.label}</option>\r\n                    ))}\r\n                  </SelectCustom>\r\n                </div>\r\n              )\r\n            }\r\n          </div>\r\n        )\r\n      }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className='flex flex-col md:flex-row md:items-center items-end relative gap-5 justify-end'>\r\n      {\r\n        filterData?.input !== undefined && (\r\n          <div className='md:w-80 mb-2 md:mb-0'>\r\n            <InputSearch\r\n              handleSearch={() => {\r\n                if(handleSearch) handleSearch()\r\n              }}\r\n              placeholder={inputPlaceholder}\r\n              setValue={(e) => {\r\n                if(setFilterData) {\r\n                  setFilterData({\r\n                    ...filterData,\r\n                    input: e\r\n                  })\r\n                }\r\n              }}\r\n              value={filterData.input}\r\n            />\r\n          </div>\r\n      )\r\n      }\r\n      <div className='flex w-24 md:items-center p-2 rounded-lg bg-[#3A3A3A] cursor-pointer' onClick={(() => setActiveModal(!activeModal))}>\r\n        <AdjustmentsHorizontalIcon width={15} color='#fff' />\r\n        <p className='px-2 text-sm'>Filtros</p>\r\n        {\r\n          activeModal ? <ChevronUpIcon width={15} color='#fff' /> : <ChevronDownIcon width={15} color='#fff' />\r\n        }\r\n      </div>\r\n      {\r\n        activeModal && (\r\n          <div className='absolute md:w-[300px]  bg-[#3A3A3A] p-5 top-10 rounded-tl-lg rounded-b-lg z-10'>\r\n            <div className='flex w-full justify-between items-center'>\r\n              <p className='text-base'>Filtros</p>\r\n              <div className='cursor-pointer' onClick={() => setActiveModal(false)}>\r\n                <XCircleIcon  width={20}/>\r\n              </div>\r\n            </div>\r\n            {\r\n\r\n            }\r\n            {\r\n              renderInputs()\r\n            }\r\n            {\r\n              !hidenButton && (\r\n                <div className='m-auto mt-5'>\r\n                  <Button\r\n                    onClick={() => {\r\n                      if (handleSearch) handleSearch()\r\n                      setActiveModal(false)\r\n                    }}\r\n                    className=\"w-full\"\r\n                  >\r\n                    Aplicar\r\n                  </Button>\r\n                </div>\r\n              )\r\n            }\r\n          </div>\r\n        )\r\n      }\r\n    </div>\r\n  )\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/FilterModal/index.tsx b/src/components/FilterModal/index.tsx
--- a/src/components/FilterModal/index.tsx	(revision 2bc0ab946d1dfaf07ee4b51f553087d96a5ed19b)
+++ b/src/components/FilterModal/index.tsx	(date 1750765968308)
@@ -1,6 +1,6 @@
 import { AdjustmentsHorizontalIcon, ChevronDownIcon, ChevronUpIcon, XCircleIcon } from "@heroicons/react/24/outline"
 import InputSearch from "../InputSearch"
-import { useState } from "react"
+import { useState, useEffect } from "react"
 import Select from "../Select"
 import { Button } from "../ui/button"
 import { DatePicker } from "../ui/datePicker"
@@ -43,7 +43,42 @@
   },
 ]
 
-export default function FilterModal({activeModal, setActiveModal, filterData, setFilterData, handleSearch, inputPlaceholder = "Pesquisar", children, hidenButton}: IProps) {
+export default function FilterModal({activeModal, setActiveModal, filterData, setFilterData, handleSearch, inputPlaceholder = "Pesquisar", children, hidenButton = true}: IProps) {
+  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);
+
+  const handleInputChange = (value: string) => {
+    if(setFilterData) {
+      setFilterData({
+        ...filterData,
+        input: value
+      })
+    }
+    
+    // Limpar timer anterior se existir
+    if (debounceTimer) {
+      clearTimeout(debounceTimer);
+    }
+    
+    // Criar novo timer para debounce
+    const timer = setTimeout(() => {
+      if(handleSearch) {
+        handleSearch()
+        setActiveModal(false)
+      }
+    }, 500);
+    
+    setDebounceTimer(timer);
+  };
+
+  // Limpar timer quando componente for desmontado
+  useEffect(() => {
+    return () => {
+      if (debounceTimer) {
+        clearTimeout(debounceTimer);
+      }
+    };
+  }, [debounceTimer]);
+
   const renderInputs = () => {
     if(children) {
       return (
@@ -63,6 +98,10 @@
                         ...filterData,
                         startData: date,
                       })
+                      if(handleSearch) {
+                        handleSearch()
+                        setActiveModal(false)
+                      }
                     }}/>
                   </div>
                 )
@@ -76,6 +115,10 @@
                         ...filterData,
                         endData: date
                       })
+                      if(handleSearch) {
+                        handleSearch()
+                        setActiveModal(false)
+                      }
                     }}/>
                   </div>
                 )
@@ -89,6 +132,10 @@
                     value={filterData.type}
                     onChange={(e) => {
                       setFilterData({...filterData, type: e.target.value})
+                      if(handleSearch) {
+                        handleSearch()
+                        setActiveModal(false)
+                      }
                     }}
                   >
                     {filterTypeContractOptions.map((option, index) => (
@@ -110,6 +157,10 @@
                         ...filterData,
                         filterOptionSelected: e.target.value
                       })
+                      if(handleSearch) {
+                        handleSearch()
+                        setActiveModal(false)
+                      }
                     }}
                   >
                     {filterStatusContracts.map((status, index) => (
@@ -131,18 +182,9 @@
         filterData?.input !== undefined && (
           <div className='md:w-80 mb-2 md:mb-0'>
             <InputSearch
-              handleSearch={() => {
-                if(handleSearch) handleSearch()
-              }}
+              handleSearch={() => {}}
               placeholder={inputPlaceholder}
-              setValue={(e) => {
-                if(setFilterData) {
-                  setFilterData({
-                    ...filterData,
-                    input: e
-                  })
-                }
-              }}
+              setValue={handleInputChange}
               value={filterData.input}
             />
           </div>
@@ -165,26 +207,8 @@
               </div>
             </div>
             {
-
-            }
-            {
               renderInputs()
             }
-            {
-              !hidenButton && (
-                <div className='m-auto mt-5'>
-                  <Button
-                    onClick={() => {
-                      if (handleSearch) handleSearch()
-                      setActiveModal(false)
-                    }}
-                    className="w-full"
-                  >
-                    Aplicar
-                  </Button>
-                </div>
-              )
-            }
           </div>
         )
       }
