{"version": 3, "file": "delete-favorite-pix.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/delete-favorite-pix.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8D;AAE9D,MAAa,oBAAoB;CAKhC;AALD,oDAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;gDACE", "sourcesContent": ["import { IsDefined, IsString, IsUUID } from 'class-validator';\r\n\r\nexport class DeleteFavoritePixDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsUUID()\r\n  id: string;\r\n}\r\n"]}