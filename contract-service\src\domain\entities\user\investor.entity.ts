import { AggregateRoot, type Either, left, right } from "@/domain/shared";
import type { BaseEntityProps } from "@/domain/shared/base-entity";
import { Cnpj, Cpf, Email } from "@/domain/value-objects";
import { Address } from "@/domain/value-objects";
import { BankAccount } from "@/domain/value-objects";
import { CompanyType } from "@/domain/value-objects";
import { Phone } from "@/domain/value-objects";
import { Company, Individual, type Party } from "../parties";

interface InvestorProps extends BaseEntityProps {
  party: Party;
  bankAccount: BankAccount;
}

export class Investor extends AggregateRoot<InvestorProps> {
  private constructor(props: InvestorProps, id?: string) {
    super(props, id);
  }

  /**
   * Cria um investidor como pessoa física
   */
  static createFromIndividual(
    individual: Individual,
    bankAccount: BankAccount,
    id?: string
  ): Investor {
    return new Investor({ party: individual, bankAccount }, id);
  }

  /**
   * Cria um investidor como pessoa jurídica
   */
  static createFromCompany(
    company: Company,
    bankAccount: BankAccount,
    id?: string
  ): Investor {
    return new Investor({ party: company, bankAccount }, id);
  }

  /**
   * Método seguro para criar um investidor PF
   */
  static createIndividual(
    id: string,
    name: string,
    email: string,
    phone: string,
    cpf: string,
    birthDate: Date,
    motherName: string,
    addressRaw: {
      street: string;
      number: string;
      city: string;
      state: string;
      postalCode: string;
      neighborhood: string;
      complement?: string;
    },
    bankRaw: {
      bank: string;
      agency: string;
      account: string;
      pix?: string;
    },
    rg: string,
    occupation?: string,
    issuingAgency?: string,
    nationality?: string
  ): Either<Error, Investor> {
    const individualResult = Individual.create(id, name, {
      address: addressRaw,
      birthDate,
      cpf,
      email,
      motherName,
      phone,
      issuingAgency,
      nationality,
      occupation,
      rg,
    });

    if (individualResult.isLeft()) {
      return left(individualResult.value);
    }

    const bankAccountResult = BankAccount.create(
      bankRaw.bank,
      bankRaw.agency,
      bankRaw.account,
      bankRaw.pix
    );

    if (bankAccountResult.isLeft()) {
      return left(bankAccountResult.value);
    }

    return right(
      new Investor({
        party: individualResult.value,
        bankAccount: bankAccountResult.value,
      })
    );
  }

  /**
   * Método seguro para criar investidor PJ
   */
  static createCompany(
    id: string,
    name: string,
    email: string,
    phone: string,
    cnpj: string,
    legalRepresentative: Individual,
    addressRaw: {
      street: string;
      number: string;
      city: string;
      state: string;
      postalCode: string;
      neighborhood: string;
      complement?: string;
    },
    companyTypeRaw: string,
    bankRaw: {
      bank: string;
      agency: string;
      account: string;
      pix?: string;
    }
  ): Either<Error, Investor> {
    const emailResult = Email.create(email);
    const phoneResult = Phone.create(phone);
    const cnpjResult = Cnpj.create(cnpj);
    const addressResult = Address.create(
      addressRaw.street,
      addressRaw.number,
      addressRaw.city,
      addressRaw.state,
      addressRaw.postalCode,
      addressRaw.neighborhood,
      addressRaw.complement
    );
    const typeResult = CompanyType.create(companyTypeRaw);

    if (emailResult.isLeft()) return left(emailResult.value);
    if (phoneResult.isLeft()) return left(phoneResult.value);
    if (cnpjResult.isLeft()) return left(cnpjResult.value);
    if (addressResult.isLeft()) return left(addressResult.value);
    if (typeResult.isLeft()) return left(typeResult.value);

    const bankAccountResult = BankAccount.create(
      bankRaw.bank,
      bankRaw.agency,
      bankRaw.account,
      bankRaw.pix
    );

    if (bankAccountResult.isLeft()) {
      return left(bankAccountResult.value);
    }

    const company = new Company(
      id,
      name,
      emailResult.value,
      phoneResult.value,
      cnpjResult.value,
      legalRepresentative,
      addressResult.value,
      typeResult.value,
      ""
    );

    return right(
      new Investor({
        party: company,
        bankAccount: bankAccountResult.value,
      })
    );
  }

  getAccount() {
    return this.props.bankAccount;
  }

  getCpf(): string {
    return this.props.party.getCpf();
  }

  getCnpj(): string | undefined {
    if (this.props.party instanceof Company) {
      return this.props.party.getCnpj();
    }
  }

  getName(): string {
    return this.props.party.getName();
  }

  getEmail(): string {
    return this.props.party.getEmail();
  }

  getPhone(): string {
    return this.props.party.getPhone();
  }

  isCompany(): boolean {
    return this.props.party instanceof Company;
  }

  isIndividual(): boolean {
    return this.props.party instanceof Individual;
  }

  getParty(): Party {
    return this.props.party;
  }

  update(
    name: string,
    email: string,
    phone: string,
    addressRaw: {
      street: string;
      number: string;
      city: string;
      state: string;
      postalCode: string;
      neighborhood: string;
      complement?: string;
    },
    data: {
      cpf?: string;
      birthDate?: Date;
      motherName?: string;
      occupation?: string;
      nationality?: string;
      rg?: string;
      issuingAgency?: string;
      cnpj?: string;
      companyType?: CompanyType;
      legalRepresentative?: Individual;
      personalDocumentUrl?: string;
    }
  ): Either<Error, void> {
    const emailResult = Email.create(email);
    const phoneResult = Phone.create(phone);
    const addressResult = Address.create(
      addressRaw.street,
      addressRaw.number,
      addressRaw.city,
      addressRaw.state,
      addressRaw.postalCode,
      addressRaw.neighborhood,
      addressRaw.complement
    );

    // Validar resultados
    if (emailResult.isLeft()) return left(emailResult.value);
    if (phoneResult.isLeft()) return left(phoneResult.value);
    if (addressResult.isLeft()) return left(addressResult.value);

    if (
      this.props.party instanceof Individual &&
      data.cpf &&
      data.birthDate &&
      data.motherName
    ) {
      const cpfResult = Cpf.create(data.cpf);
      if (cpfResult.isLeft()) return left(cpfResult.value);

      this.props.party.updateIndividualData(
        name,
        phoneResult.value,
        emailResult.value,
        cpfResult.value,
        data.birthDate,
        addressResult.value,
        data.motherName,
        data.rg ?? "",
        data.occupation,
        data.nationality,
        data.issuingAgency
      );
    } else if (
      this.props.party instanceof Company &&
      data.cnpj &&
      data.legalRepresentative &&
      data.companyType
    ) {
      const cnpjResult = Cnpj.create(data.cnpj);
      if (cnpjResult.isLeft()) return left(cnpjResult.value);

      this.props.party.updateCompany(
        name,
        emailResult.value,
        phoneResult.value,
        cnpjResult.value,
        data.legalRepresentative,
        addressResult.value,
        data.companyType
      );
      this.props.party.updateRepresentativeAddress(addressResult.value);
    }

    return right(undefined);
  }

  updateBankAccount(
    bank: string,
    agency: string,
    account: string,
    pix?: string
  ): Either<Error, void> {
    const bankAccountResult = BankAccount.create(bank, agency, account, pix);

    if (bankAccountResult.isLeft()) {
      return left(bankAccountResult.value);
    }

    this.props.bankAccount = bankAccountResult.value;
    this.touch();

    return right(undefined);
  }
}
