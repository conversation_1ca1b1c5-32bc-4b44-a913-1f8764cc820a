"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InternalTransactionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const bcrypt = require("bcrypt");
const pix_transaction_celcoin_service_1 = require("../../../apis/celcoin/services/pix-transaction-celcoin.service");
const get_account_limit_service_1 = require("../../account-transfer-limit/services/get-account-limit.service");
const account_entity_1 = require("../../../shared/database/typeorm/entities/account.entity");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const transaction_movement_type_enum_1 = require("../../../shared/enums/transaction-movement-type.enum");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
let InternalTransactionService = class InternalTransactionService {
    constructor(accountDb, transactionRepository, celcoinService, getLimit) {
        this.accountDb = accountDb;
        this.transactionRepository = transactionRepository;
        this.celcoinService = celcoinService;
        this.getLimit = getLimit;
    }
    async perform(data, id) {
        const account = await this.accountDb.findOne({
            relations: {
                business: true,
                owner: true,
            },
            where: [
                { ownerId: (0, typeorm_2.Equal)(id) },
                { businessId: (0, typeorm_2.Equal)(id) },
                { id: (0, typeorm_2.Equal)(id) },
            ],
        });
        if (!account)
            throw new common_1.NotFoundException('Conta não encontrada.');
        const isPasswordCorrect = await bcrypt.compare(data.transactionPassword, account.transactionPassword);
        if (!isPasswordCorrect) {
            throw new common_1.UnauthorizedException('Senha de transação incorreta.');
        }
        const balance = await this.getLimit.execute(account.id);
        if (Number(data.amount) > Number(balance.dailyLimit))
            throw new common_1.BadRequestException('Limite de transferencia excedido!');
        const transaction = await this.transactionRepository.manager.transaction(async (manager) => {
            const uuid = (0, uuid_1.v4)();
            const { body: result } = await this.celcoinService.internalTransfer({
                amount: Number(data.amount),
                creditParty: {
                    account: data.creditAccount,
                },
                clientRequestId: uuid,
                debitParty: {
                    account: account.number,
                },
                description: data.description,
            });
            const transferMetadata = {
                ...result,
                creditParty: result.creditParty,
                debitParty: result.debitParty,
            };
            const newTransaction = manager.create(transaction_entity_1.TransactionEntity, {
                accountId: account.id,
                account,
                code: uuid,
                description: data.description,
                value: data.amount,
                destinyAccount: result.creditParty.account,
                destinyBank: result.creditParty.bank,
                destinyBranch: result.creditParty.branch,
                destinyDocument: result.creditParty.taxId,
                destinyName: result.creditParty.name,
                type: transaction_movement_type_enum_1.TransactionMovementTypeEnum.INTERNAL_TRANSFER_CASHOUT,
                endToEndId: result.endToEndId,
                transferMetadata: JSON.stringify(transferMetadata),
            });
            const transaction = await manager.save(newTransaction);
            return transaction;
        });
        return {
            id: transaction.id,
            creditParty: {
                account: transaction.destinyAccount,
                name: transaction.destinyName,
                document: transaction.destinyDocument,
            },
            debitParty: {
                account: account.number,
                name: account.owner.name || account.business.fantasyName,
                document: account.owner.cpf || account.business.cnpj,
            },
        };
    }
};
exports.InternalTransactionService = InternalTransactionService;
exports.InternalTransactionService = InternalTransactionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.AccountEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __param(2, (0, common_1.Inject)(pix_transaction_celcoin_service_1.PixTransactionCelcoinService)),
    __param(3, (0, common_1.Inject)(get_account_limit_service_1.GetAccountLimitService)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        pix_transaction_celcoin_service_1.PixTransactionCelcoinService,
        get_account_limit_service_1.GetAccountLimitService])
], InternalTransactionService);
//# sourceMappingURL=internal-transaction.service.js.map