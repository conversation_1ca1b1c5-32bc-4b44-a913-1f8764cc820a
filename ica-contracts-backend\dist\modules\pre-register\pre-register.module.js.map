{"version": 3, "file": "pre-register.module.js", "sourceRoot": "/", "sources": ["modules/pre-register/pre-register.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAKwB;AACxB,kHAA0G;AAC1G,sIAAsH;AACtH,8DAAwD;AAExD,mFAA8E;AAC9E,kFAA4E;AAC5E,gGAAyF;AACzF,gGAAyF;AACzF,0FAAmF;AACnF,oFAA8E;AAC9E,wGAAiG;AAc1F,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,SAAS,CAAC,QAA4B;QACpC,QAAQ;aACL,KAAK,CAAC,kEAA8B,CAAC;aACrC,SAAS,CAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,MAAM,EAAE,sBAAa,CAAC,IAAI,EAAE,CAAC,CAAC;QAExE,QAAQ,CAAC,KAAK,CAAC,8EAAgC,CAAC,CAAC,SAAS,CACxD;YACE,IAAI,EAAE,6BAA6B;YACnC,MAAM,EAAE,sBAAa,CAAC,GAAG;SAC1B,EACD;YACE,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,sBAAa,CAAC,IAAI;SAC3B,CACF,CAAC;IACJ,CAAC;CACF,CAAA;AAjBY,8CAAiB;4BAAjB,iBAAiB;IAZ7B,IAAA,eAAM,EAAC;QACN,WAAW,EAAE,CAAC,+CAAqB,CAAC;QACpC,SAAS,EAAE;YACT,gDAAqB;YACrB,6DAA2B;YAC3B,uDAAwB;YACxB,kDAAsB;YACtB,6DAA2B;YAC3B,qEAA+B;SAChC;QACD,OAAO,EAAE,CAAC,4BAAY,CAAC;KACxB,CAAC;GACW,iBAAiB,CAiB7B", "sourcesContent": ["import {\r\n  MiddlewareConsumer,\r\n  Module,\r\n  NestModule,\r\n  RequestMethod,\r\n} from '@nestjs/common';\r\nimport { ValidateAdviserOwnerMiddleware } from 'src/shared/middlewares/validate-adviser-owner.middleware';\r\nimport { ValidateLinkTokenOwnerMiddleware } from 'src/shared/middlewares/validate-link-token-pre-register.middleware';\r\nimport { SharedModule } from 'src/shared/shared.module';\r\n\r\nimport { PreRegisterController } from './controllers/pre-register.controller';\r\nimport { AddPreRegisterService } from './services/add-pre-register.service';\r\nimport { DirectAddPreRegisterService } from './services/direct-add-pre-register.service';\r\nimport { GetFilterPreRegisterService } from './services/get-filter-pre-register.service';\r\nimport { GetOnePreRegisterService } from './services/get-one-pre-register.service';\r\nimport { SendPreRegisterService } from './services/send-pre-register.service';\r\nimport { ValidateTokenPreRegisterService } from './services/validate-token-pre-register.service';\r\n\r\n@Module({\r\n  controllers: [PreRegisterController],\r\n  providers: [\r\n    AddPreRegisterService,\r\n    GetFilterPreRegisterService,\r\n    GetOnePreRegisterService,\r\n    SendPreRegisterService,\r\n    DirectAddPreRegisterService,\r\n    ValidateTokenPreRegisterService,\r\n  ],\r\n  imports: [SharedModule],\r\n})\r\nexport class PreRegisterModule implements NestModule {\r\n  configure(consumer: MiddlewareConsumer) {\r\n    consumer\r\n      .apply(ValidateAdviserOwnerMiddleware)\r\n      .forRoutes({ path: 'pre-register/send', method: RequestMethod.POST });\r\n\r\n    consumer.apply(ValidateLinkTokenOwnerMiddleware).forRoutes(\r\n      {\r\n        path: 'pre-register/token-validate',\r\n        method: RequestMethod.GET,\r\n      },\r\n      {\r\n        path: 'pre-register/direct-add',\r\n        method: RequestMethod.POST,\r\n      },\r\n    );\r\n  }\r\n}\r\n"]}