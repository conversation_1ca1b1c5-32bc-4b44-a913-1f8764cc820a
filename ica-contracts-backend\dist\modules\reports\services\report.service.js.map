{"version": 3, "file": "report.service.js", "sourceRoot": "/", "sources": ["modules/reports/services/report.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAgE;AAChE,6CAAmD;AAEnD,2FAAkF;AAClF,qCAAqC;AAErC,uEAAkE;AAClE,qHAA0G;AAGnG,IAAM,aAAa,GAAnB,MAAM,aAAa;IACxB,YACmB,QAA+B,EAExC,gBAA0C,EAE1C,2BAAgE;QAJvD,aAAQ,GAAR,QAAQ,CAAuB;QAExC,qBAAgB,GAAhB,gBAAgB,CAA0B;QAE1C,gCAA2B,GAA3B,2BAA2B,CAAqC;IACvE,CAAC;IAEJ,KAAK,CAAC,cAAc,CAClB,IAAY,EACZ,IAAS,EACT,MAAc,EACd,MAAc;QAGd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE;gBACL,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;gBACrC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;aACzC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,2BAAkB,CAAC,sBAAsB,CAAC,CAAA;QACtD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBAC/B,MAAM,EAAE,MAAM,CAAC,EAAE;gBACjB,IAAI;gBACJ,mBAAmB,EAAE,OAAO,CAAC,EAAE;aAChC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAA;AAvCY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,0BAAgB,EAAC,4BAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;qCAHf,+CAAqB;QAEtB,oBAAU;QAEC,oBAAU;GANtC,aAAa,CAuCzB", "sourcesContent": ["import { ForbiddenException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { FileEntity } from 'src/shared/database/typeorm/entities/files-entity';\r\nimport { ReportEntity } from 'src/shared/database/typeorm/entities/report.entity';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ReportRegistryService } from './report-registry.service';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\n\r\n@Injectable()\r\nexport class ReportService {\r\n  constructor(\r\n    private readonly registry: ReportRegistryService,\r\n    @InjectRepository(ReportEntity)\r\n    private reportRepository: Repository<ReportEntity>,\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n  ) {}\r\n\r\n  async generateReport(\r\n    type: string,\r\n    data: any,\r\n    roleId: string,\r\n    userId: string,\r\n  ): Promise<FileEntity | null> {\r\n    \r\n    const profile = await this.ownerRoleRelationRepository.findOne({\r\n      where: [\r\n        { id: roleId, owner: { id: userId } },\r\n        { id: roleId, business: { id: userId } },\r\n      ],\r\n    });\r\n\r\n    if (!profile) {\r\n      throw new ForbiddenException('Não possui permissão')\r\n    }\r\n\r\n    const strategy = this.registry.resolve(type);\r\n    const report = await strategy.generateReport(data);\r\n    if (report) {\r\n      await this.reportRepository.save({\r\n        fileId: report.id,\r\n        type,\r\n        ownerRoleRelationId: profile.id,\r\n      });\r\n    }\r\n\r\n    return report;\r\n  }\r\n}\r\n"]}