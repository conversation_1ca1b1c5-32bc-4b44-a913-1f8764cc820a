import { Injectable, UnprocessableEntityException } from '@nestjs/common';

import { CreateExistingContractDto } from '../../dto/create-existing-contract.dto';
import { CreateExistingContractContext } from './existing-contract-handler';
import { CheckExistingContractHandler } from './concrete-handlers/check-existing-contract.handler';

import { ContractHandler } from './concrete-handlers/create-contract.handler';
import { ValidateAdvisorHandler } from './concrete-handlers/validate-advisors.handler';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';

@Injectable()
export class CreateExistingContractService {
  constructor(
    private readonly checkExistingContractHandler: CheckExistingContractHandler,
    private readonly validateAdvisorHandler: ValidateAdvisorHandler,
    private readonly contractHandler: ContractHandler,
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
  ) {}

  public async perform(data: CreateExistingContractDto, userId: string) {

    const user = await this.entityManager.findOne(OwnerRoleRelationEntity, {
      where: [
        { owner: { id: userId }, role: { name: data.role } },
        { business: { id: userId }, role: { name: data.role } },
      ],
      relations: { role: true, owner: true, business: true },
    });

    if (!user) {
      throw new UnprocessableEntityException('Usuário não encontrado');
    }


    let context: CreateExistingContractContext | null = null;

    if (user.role.name === RolesEnum.SUPERADMIN) {
      context = new CreateExistingContractContext(data, data.brokerId);
    }

    if (user.role.name === RolesEnum.BROKER) {
      context = new CreateExistingContractContext(data, user.id);
    }

    if (user.role.name === RolesEnum.ADVISOR) {
      const broker = await this.entityManager.findOne(WalletsViewsEntity, {
        where: { bottomId: user.id, upper: { role: { name: 'broker' } } },
      });
      if (!broker) {
        throw new UnprocessableEntityException('Broker não encontrado');
      }
      context = new CreateExistingContractContext(data, broker.upperId);

      context.dto.advisors = [
        {
          advisorId: user.id,
          rate: user.partPercent ? Number(user.partPercent) : 1,
        },
      ];
    }

    this.checkExistingContractHandler
      .setNext(this.validateAdvisorHandler)
      .setNext(this.contractHandler);

    await this.checkExistingContractHandler.handle(context);

    return {
      contractId: context.contract.contractId,
    };
  }
}
