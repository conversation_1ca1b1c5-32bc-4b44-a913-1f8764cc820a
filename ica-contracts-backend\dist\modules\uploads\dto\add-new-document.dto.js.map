{"version": 3, "file": "add-new-document.dto.js", "sourceRoot": "/", "sources": ["modules/uploads/dto/add-new-document.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsE;AACtE,+EAAqE;AAErE,MAAa,iBAAiB;CAS7B;AATD,8CASC;AALC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;6CACE;AAIX;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,wBAAM,EAAC,mCAAe,CAAC;;+CACF", "sourcesContent": ["import { IsDefined, IsEnum, IsString, IsUUID } from 'class-validator';\r\nimport { TypeUploadsEnum } from 'src/shared/enums/type-uploads.enum';\r\n\r\nexport class AddNewDocumentDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsUUID()\r\n  id: string;\r\n\r\n  @IsDefined()\r\n  @IsEnum(TypeUploadsEnum)\r\n  type: TypeUploadsEnum;\r\n}\r\n"]}