export interface IDocument {
  id: number;
  titulo: string;
  titulo_modificado: string;
  extensao: string;
  status: string;
  tag: string;
  enviar_link_documento_notificacao: string;
  numero_externo: string;
  ordem_assinatura_utilizar: string;
  finalizacao_automatica: string;
  quantidade_registros: number;
  dataLimite: string;
  empresa_id: number;
  created_at: string;
  path_original: string;
  path_padronizado: string;
  path_assinado: string;
  hash: string;
  historico: any[];
  assinaturas: string;
}

export interface ISendDocumentResponse {
  erro: boolean;
  message: string;
  payload: {
    documento: IDocument;
  };
}
