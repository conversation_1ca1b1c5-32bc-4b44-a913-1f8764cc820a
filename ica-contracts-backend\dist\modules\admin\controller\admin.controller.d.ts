import { EditBrokerDto } from '../../broker/dto/edit-broker.dto';
import { EditInvestorService } from '../../investor/services/edit-investor.service';
import { AdminDashboardService } from '../services/dashboard.service';
import { EditAdminService } from '../services/edit-admin.service';
import { GetAdminService } from '../services/get-admin.service';
import { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { GetAdminContractsGrowthChartService } from '../services/get-admin-contracts-growth-chart.service';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
import type { ChangePasswordDto } from '../dto/change-password.dto';
import { ChangePasswordAdminService } from '../services/change-password.service';
import { ListContractsDto } from '../dto/list-contracts.dto';
import { ListContractsAdminService } from '../services/list-contracts.service';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
export declare class AdminController {
    private readonly getAdminService;
    private readonly dashboardService;
    private readonly editInvestorService;
    private readonly editAdminService;
    private readonly getAdminContractsGrowthChartService;
    private readonly changePasswordService;
    private readonly listContractsAdminService;
    constructor(getAdminService: GetAdminService, dashboardService: AdminDashboardService, editInvestorService: EditInvestorService, editAdminService: EditAdminService, getAdminContractsGrowthChartService: GetAdminContractsGrowthChartService, changePasswordService: ChangePasswordAdminService, listContractsAdminService: ListContractsAdminService);
    search(request: IRequestUser): Promise<{
        admin: {
            adminId: string;
            ownerId: string;
            broker: any[];
        };
    }>;
    dashboard(request: IRequestUser): Promise<{
        distributedIncome: number;
        scpWithdraws: number;
        p2pWithdraws: number;
        p2pContractNumber: number;
        scpContractNumber: number;
        p2pContractAmount: number;
        scpContractAmount: number;
        activeQuotes: number;
        shareholder: number;
        activeInvestorsNumber: number;
    }>;
    editInvestor(request: IRequestUser, body: EditBrokerDto): Promise<void>;
    editBroker(request: IRequestUser, body: EditBrokerDto): Promise<void>;
    changePassword(body: ChangePasswordDto): Promise<{
        message: string;
        email: string;
        plainPassword: string;
    }>;
    getContractsGrowthChart(ownerRoleId: string, period?: PeriodFilter, contractType?: ContractTypeEnum): Promise<{
        period: PeriodFilter;
        data: any[];
    }>;
    listContractsAdmin(request: any, data: ListContractsDto): Promise<{
        total: number;
        paginaAtual: number;
        totalPaginas: number;
        totalPorPagina: number;
        documentos: {
            investorId: string;
            valorInvestimento: number;
            prazoInvestimento: string;
            rendimentoInvestimento: number;
            nomeInvestidor: string;
            documentoInvestidor: string;
            tags: string;
            compradoCom: string;
            cotas: number;
            periodoCarencia: string;
            consultorResponsavel: string;
            idContrato: string;
            statusContrato: string;
            inicioContrato: Date;
            fimContrato: Date;
            contratoPdf: string;
            comprovamentePagamento: string;
            advisorParticipationPercentage: string;
            brokerParticipationPercentage: string;
            addendum: {
                addendumFiles: {
                    type: string;
                    url: string;
                }[];
                id: number;
                status: import("../../../shared/database/typeorm/entities/addendum.entity").AddendumStatus;
                contractId: string;
                externalId: string;
                contract: import("typeorm").Relation<import("../../../shared/database/typeorm/entities/contract.entity").ContractEntity>;
                applicationDate: Date;
                yieldRate: number;
                value: number;
                reason: string;
                notes: string;
                expiresIn: string;
                createdAt: Date;
                updatedAt: Date;
                addendumNotifications: import("../../../shared/database/typeorm/entities/notification.entity").NotificationEntity[];
            }[];
        }[];
    }>;
}
