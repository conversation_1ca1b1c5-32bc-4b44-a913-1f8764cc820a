import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';
import { Repository } from 'typeorm';
import { AddPreRegisterDto } from '../dto/add-pre-register.dto';
export declare class AddPreRegisterService {
    private preRegisterDb;
    private accountDb;
    constructor(preRegisterDb: Repository<PreRegisterEntity>, accountDb: Repository<AccountEntity>);
    perform(data: AddPreRegisterDto, token: string): Promise<void>;
}
