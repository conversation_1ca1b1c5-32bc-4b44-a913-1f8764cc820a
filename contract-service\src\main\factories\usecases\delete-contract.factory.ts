import { DeleteContractUseCase } from "@/application/usecases/contracts/delete-contract.usecase";
import { FetchHttpClient } from "@/infrastructure/http/fetch-http-client";
import prisma from "@/infrastructure/prisma/client";
import { PrismaUnitOfWork } from "@/infrastructure/prisma/prisma-unit-of-work";
import {
  PrismaContractDeletionRepository,
  PrismaContractRepository,
} from "@/infrastructure/prisma/repositories";
import { PinoLoggerAdapter } from "@/main/adapters/pino-logger.adapter";
import { SignatureAdapter } from "@/main/adapters/rbm/signature.adapter";

export function makeDeleteContractUseCase() {
  const contractRepository = new PrismaContractRepository();
  const contractDeletionRepository = new PrismaContractDeletionRepository();
  const fetchHttpClient = new FetchHttpClient();
  const rmbApi = new SignatureAdapter(fetchHttpClient);
  const logger = new PinoLoggerAdapter();
  const unitOfWork = new PrismaUnitOfWork(prisma);

  return new DeleteContractUseCase(
    contractRepository,
    contractDeletionRepository,
    rmbApi,
    unitOfWork,
    logger
  );
}
