"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const edit_broker_dto_1 = require("../../broker/dto/edit-broker.dto");
const edit_investor_service_1 = require("../../investor/services/edit-investor.service");
const dashboard_service_1 = require("../services/dashboard.service");
const edit_admin_service_1 = require("../services/edit-admin.service");
const get_admin_service_1 = require("../services/get-admin.service");
const get_admin_contracts_growth_chart_service_1 = require("../services/get-admin-contracts-growth-chart.service");
const contract_type_enum_1 = require("../../../shared/enums/contract-type.enum");
const change_password_service_1 = require("../services/change-password.service");
const list_contracts_dto_1 = require("../dto/list-contracts.dto");
const list_contracts_service_1 = require("../services/list-contracts.service");
let AdminController = class AdminController {
    constructor(getAdminService, dashboardService, editInvestorService, editAdminService, getAdminContractsGrowthChartService, changePasswordService, listContractsAdminService) {
        this.getAdminService = getAdminService;
        this.dashboardService = dashboardService;
        this.editInvestorService = editInvestorService;
        this.editAdminService = editAdminService;
        this.getAdminContractsGrowthChartService = getAdminContractsGrowthChartService;
        this.changePasswordService = changePasswordService;
        this.listContractsAdminService = listContractsAdminService;
    }
    async search(request) {
        const results = await this.getAdminService.perform({
            ownerId: request.user.id,
        });
        return results;
    }
    async dashboard(request) {
        const result = await this.dashboardService.perform({
            ownerId: request.user.id,
        });
        return result;
    }
    async editInvestor(request, body) {
        const result = await this.editInvestorService.perform(body, request.user.id);
        return result;
    }
    async editBroker(request, body) {
        const userId = request.user.id;
        await this.editAdminService.perform(body, userId);
    }
    async changePassword(body) {
        console.log('chamou controller');
        try {
            const result = await this.changePasswordService.perform(body);
            return result;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getContractsGrowthChart(ownerRoleId, period, contractType) {
        return this.getAdminContractsGrowthChartService.perform(ownerRoleId, period, contractType);
    }
    async listContractsAdmin(request, data) {
        return this.listContractsAdminService.perform(data, request.user.id);
    }
};
exports.AdminController = AdminController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.ADMIN),
    (0, common_1.Get)('brokers'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "search", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, common_1.Get)('dashboard'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "dashboard", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)('investor'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, edit_broker_dto_1.EditBrokerDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "editInvestor", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADMIN),
    (0, common_1.Patch)(''),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, edit_broker_dto_1.EditBrokerDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "editBroker", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN),
    (0, common_1.Put)('change-password'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Function]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "changePassword", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADMIN),
    (0, common_1.Get)('/:ownerRoleId/contracts-growth'),
    __param(0, (0, common_1.Param)('ownerRoleId')),
    __param(1, (0, common_1.Query)('period')),
    __param(2, (0, common_1.Query)('contractType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getContractsGrowthChart", null);
__decorate([
    (0, common_1.Get)('/list-contracts'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.ADMIN),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, list_contracts_dto_1.ListContractsDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "listContractsAdmin", null);
exports.AdminController = AdminController = __decorate([
    (0, common_1.Controller)('admin'),
    __metadata("design:paramtypes", [get_admin_service_1.GetAdminService,
        dashboard_service_1.AdminDashboardService,
        edit_investor_service_1.EditInvestorService,
        edit_admin_service_1.EditAdminService,
        get_admin_contracts_growth_chart_service_1.GetAdminContractsGrowthChartService,
        change_password_service_1.ChangePasswordAdminService,
        list_contracts_service_1.ListContractsAdminService])
], AdminController);
//# sourceMappingURL=admin.controller.js.map