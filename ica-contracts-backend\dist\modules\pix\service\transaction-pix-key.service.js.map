{"version": 3, "file": "transaction-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/transaction-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAKwB;AACxB,6CAAmD;AAEnD,uCAAoC;AACpC,iCAAiC;AACjC,2BAAyB;AACzB,oHAAyG;AACzG,6GAAmG;AACnG,+GAA+G;AAC/G,6HAAiH;AACjH,6FAAoF;AACpF,qGAA4F;AAC5F,yGAA8F;AAC9F,2FAAiF;AACjF,mDAA2C;AAC3C,qCAA4C;AAC5C,+BAA0B;AAC1B,2CAA+C;AAC/C,2FAA2F;AAG3F,uFAAsE;AAK/D,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACnC,YAEU,iBAA4C,EAEnC,gBAAwD,EAEjE,qBAAoD,EAEpD,cAA4C,EAE5C,qBAA4C,EAE5C,QAAgC,EAChC,aAA4B,EACnB,oBAA0C,EACtB,SAAoB;QAbjD,sBAAiB,GAAjB,iBAAiB,CAA2B;QAEnC,qBAAgB,GAAhB,gBAAgB,CAAwC;QAEjE,0BAAqB,GAArB,qBAAqB,CAA+B;QAEpD,mBAAc,GAAd,cAAc,CAA8B;QAE5C,0BAAqB,GAArB,qBAAqB,CAAuB;QAE5C,aAAQ,GAAR,QAAQ,CAAwB;QAChC,kBAAa,GAAb,aAAa,CAAe;QACnB,yBAAoB,GAApB,oBAAoB,CAAsB;QACtB,cAAS,GAAT,SAAS,CAAW;IACxD,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,IAA0B,EAC1B,EAAU,EACV,UAAoB,EACpB,cAAuB;QAEvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;YACD,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC3C,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAEnE,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CACtC,wBAAwB,CACzB,CAAC;YACF,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,sBAAsB,CAAC,CAAC;YAEvE,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC;YAC3D,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YAErC,IAAI,WAAW,IAAI,OAAO,IAAI,WAAW,GAAG,SAAS,EAAE,CAAC;gBACtD,MAAM,IAAI,4BAAmB,CAC3B,4CAA4C,CAC7C,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAC9C,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CACjC,CAAC;YAEF,IAAI,SAAS,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,4BAAmB,CAC3B,wIAAwI,CACzI,CAAC;YACJ,CAAC;YAWD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAExD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAClD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;YAGrE,MAAM,IAAI,CAAC,oBAAoB,CAAC,6BAA6B,CAAC;gBAC5D,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,cAAc;aACtB,CAAC,CAAC;YAEH,IAAI,WAA8B,CAAC;YAEnC,IACE,CAAC,UAAU;gBACX,IAAI,CAAC,YAAY;gBACjB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,EAC7C,CAAC;gBACD,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,WAAW,CAChE,KAAK,EAAE,OAAO,EAAE,EAAE;oBAChB,MAAM,IAAI,GAAG,IAAA,SAAE,GAAE,CAAC;oBAClB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GACpB,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;wBAC1C,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;wBAC3B,cAAc,EAAE,MAAM;wBACtB,WAAW,EAAE,WAAW;wBACxB,qBAAqB,EAAE,IAAI,CAAC,WAAW;wBACvC,eAAe,EAAE,UAAU;wBAC3B,OAAO,EAAE,MAAM;wBACf,UAAU,EAAE,IAAI,CAAC,QAAQ;wBACzB,UAAU,EAAE,IAAI;wBAChB,WAAW,EAAE;4BACX,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG;4BACvB,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW;4BACvC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI;4BACzB,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI;yBAC1B;wBACD,UAAU,EAAE;4BACV,OAAO,EAAE,OAAO,CAAC,MAAM;yBACxB;qBACF,CAAC,CAAC;oBAEL,MAAM,gBAAgB,GAAG;wBACvB,WAAW,EAAE,MAAM,CAAC,WAAW;wBAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;qBAC9B,CAAC;oBAEF,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,sCAAiB,EAAE;wBACvD,SAAS,EAAE,OAAO,CAAC,EAAE;wBACrB,OAAO;wBACP,IAAI,EAAE,IAAI;wBACV,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,KAAK,EAAE,IAAI,CAAC,MAAM;wBAClB,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,IAAI;wBACrC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,IAAI;wBACrC,MAAM,EAAE,+CAAqB,CAAC,OAAO;wBACrC,IAAI,EAAE,4DAA2B,CAAC,WAAW;wBAC7C,UAAU,EAAE,MAAM,CAAC,UAAU;wBAC7B,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;qBACnD,CAAC,CAAC;oBACH,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBAEvD,OAAO,WAAW,CAAC;gBACrB,CAAC,CACF,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,WAAW,EAAE;oBACX,OAAO,EAAE,WAAW,CAAC,cAAc;oBACnC,IAAI,EAAE,WAAW,CAAC,WAAW;oBAC7B,QAAQ,EAAE,WAAW,CAAC,eAAe;iBACtC;gBACD,UAAU,EAAE;oBACV,OAAO,EAAE,OAAO,CAAC,MAAM;oBACvB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;oBACzD,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI;iBACtD;gBACD,SAAS,EAAE,IAAA,mBAAQ,EAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IACO,KAAK,CAAC,kBAAkB,CAAC,OAAsB,EAAE,MAAc;QACrE,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY;YACf,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBAC9C,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB,CAAC,CAAC;QAEL,eAAM,CAAC,IAAI,CACT,6DAA6D,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CACjH,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,MAAM;YAAE,OAAO;QAEjC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC;YACnE,SAAS,EAAE,OAAO,CAAC,EAAE;SACtB,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAExC,IAAI,cAAc,IAAI,YAAY,GAAG,cAAc,CAAC,MAAM;YACxD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QAErE,MAAM,2BAA2B,GAC/B,YAAY,IAAI,YAAY,CAAC,oBAAoB,CAAC;QAEpD,MAAM,yBAAyB,GAAG,YAAY,IAAI,YAAY,CAAC,YAAY,CAAC;QAE5E,MAAM,yBAAyB,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;QAE1E,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,0BAA0B,GAC9B,GAAG,IAAI,EAAE;YACP,CAAC,CAAC,YAAY,IAAI,YAAY,CAAC,eAAe,GAAG,YAAY,CAAC,UAAU;YACxE,CAAC,CAAC,IAAI,CAAC;QAEX,eAAM,CAAC,IAAI,CAAC,kDAAkD,EAAE;YAC9D,2BAA2B;YAC3B,yBAAyB;YACzB,yBAAyB;YACzB,0BAA0B;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,0BAA0B;YAC7B,MAAM,IAAI,4BAAmB,CAC3B,2CAA2C,CAC5C,CAAC;QAEJ,IACE,CAAC,CACC,2BAA2B;YAC3B,yBAAyB;YACzB,yBAAyB,CAC1B,EACD,CAAC;YACD,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,IAA0B,EAC1B,OAAsB;QAEtB,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACrC,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,OAAO;YACP,IAAI,EAAE,IAAA,SAAE,GAAE;YACV,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;YACvC,UAAU,EAAE,IAAI,CAAC,QAAQ;YACzB,MAAM,EAAE,+CAAqB,CAAC,SAAS;YACvC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;YACrC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;YAC7C,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YAC/B,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YACnC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACvC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YAC/B,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG;YAC7B,IAAI,EAAE,4DAA2B,CAAC,WAAW;SAC9C,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAlPY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,2DAA0B,CAAC,CAAA;IAE5C,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;IAEpC,WAAA,IAAA,eAAM,EAAC,+CAAqB,CAAC,CAAA;IAE7B,WAAA,IAAA,eAAM,EAAC,kDAAsB,CAAC,CAAA;IAI9B,WAAA,IAAA,eAAM,EAAC,gCAAU,CAAC,CAAA;qCAbQ,oBAAU;QAEF,oBAAU;QAEd,oBAAU;QAEjB,8DAA4B;QAErB,+CAAqB;QAElC,kDAAsB;QACjB,sBAAa;QACG,8CAAoB;GAflD,wBAAwB,CAkPpC", "sourcesContent": ["/* eslint-disable import-helpers/order-imports */\r\nimport {\r\n  BadRequestException,\r\n  Inject,\r\n  Injectable,\r\n  NotFoundException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\n// import * as bcrypt from 'bcrypt';\r\nimport { addHours } from 'date-fns';\r\nimport * as moment from 'moment';\r\nimport 'moment-timezone';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { VirtualBalanceService } from 'src/apis/icainvest-credit/services/virtual-balance.service';\r\nimport { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';\r\nimport { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';\r\nimport { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { TwoFactorAuthService } from 'src/modules/two-factor-auth/two-factor-auth.service';\r\n\r\nimport type { BRHoliday } from 'br-holiday';\r\nimport { BR_HOLIDAY } from 'src/shared/providers/br-holiday.provider';\r\nimport { TransactionPixKeyDto } from '../dto/transaction-pix-key.dto';\r\nimport { ITransactionPixKeyResponse } from '../response/transaction-pix-key-response';\r\n\r\n@Injectable()\r\nexport class TransactionPixKeyService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountRepository: Repository<AccountEntity>,\r\n    @InjectRepository(AccountTransferLimitEntity)\r\n    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepository: Repository<TransactionEntity>,\r\n    @Inject(PixTransactionCelcoinService)\r\n    private celcoinService: PixTransactionCelcoinService,\r\n    @Inject(VirtualBalanceService)\r\n    private virtualBalanceService: VirtualBalanceService,\r\n    @Inject(GetAccountLimitService)\r\n    private getLimit: GetAccountLimitService,\r\n    private configService: ConfigService,\r\n    private readonly twoFactorAuthService: TwoFactorAuthService,\r\n    @Inject(BR_HOLIDAY) private readonly brHoliday: BRHoliday,\r\n  ) {}\r\n\r\n  async perform(\r\n    data: TransactionPixKeyDto,\r\n    id: string,\r\n    isAutomate?: boolean,\r\n    twoFactorToken?: string,\r\n  ): Promise<ITransactionPixKeyResponse> {\r\n    const account = await this.accountRepository.findOne({\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n      relations: { owner: true, business: true },\r\n    });\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    if (!isAutomate) {\r\n      const startHour = this.configService.get<number>(\r\n        'TRANSACTION_START_HOUR',\r\n      );\r\n      const endHour = this.configService.get<number>('TRANSACTION_END_HOUR');\r\n\r\n      const currentDate = moment().utc().tz('America/Sao_Paulo');\r\n      const currentHour = currentDate.hour();\r\n      const currentDay = currentDate.day(); // 0 = Domingo, 6 = Sábado\r\n\r\n      if (currentHour >= endHour || currentHour < startHour) {\r\n        throw new BadRequestException(\r\n          'Transações não permitidas entre 19h e 08h.',\r\n        );\r\n      }\r\n\r\n      const isHoliday = await this.brHoliday.isHoliday(\r\n        currentDate.format('YYYY-MM-DD'),\r\n      );\r\n\r\n      if (isHoliday || currentDay === 0 || currentDay === 6) {\r\n        throw new BadRequestException(\r\n          'Por motivos de segurança, as transações via PIX e TED na plataforma ICA Invest estão disponíveis apenas em dias úteis, das 08h às 19h.',\r\n        );\r\n      }\r\n\r\n      // const isPasswordCorrect = await bcrypt.compare(\r\n      //   data.transactionPassword,\r\n      //   account.transactionPassword,\r\n      // );\r\n\r\n      // if (!isPasswordCorrect) {\r\n      //   throw new UnauthorizedException('Senha de transação incorreta.');\r\n      // }\r\n\r\n      const balance = await this.getLimit.execute(account.id);\r\n\r\n      if (Number(data.amount) > Number(balance.dailyLimit))\r\n        throw new BadRequestException('Limite de transferencia excedido!');\r\n\r\n      // await this.isValidTransaction(account, data.amount);\r\n      await this.twoFactorAuthService.verifyTwoFactorAuthentication({\r\n        userId: id,\r\n        token: twoFactorToken,\r\n      });\r\n\r\n      let transaction: TransactionEntity;\r\n\r\n      if (\r\n        !isAutomate &&\r\n        data.transferDate &&\r\n        !moment(data.transferDate).isSame(new Date()) &&\r\n        moment(data.transferDate).isAfter(new Date())\r\n      ) {\r\n        transaction = await this.scheduleTransaction(data, account);\r\n      } else {\r\n        transaction = await this.transactionRepository.manager.transaction(\r\n          async (manager) => {\r\n            const uuid = v4();\r\n            const { body: result } =\r\n              await this.celcoinService.transactionPixKey({\r\n                amount: Number(data.amount),\r\n                initiationType: 'DICT',\r\n                paymentType: 'IMMEDIATE',\r\n                remittanceInformation: data.description,\r\n                transactionType: 'TRANSFER',\r\n                urgency: 'HIGH',\r\n                endToEndId: data.endToEnd,\r\n                clientCode: uuid,\r\n                creditParty: {\r\n                  key: data.external?.key,\r\n                  accountType: data.external?.accountType,\r\n                  bank: data.external?.bank,\r\n                  name: data.external?.name,\r\n                },\r\n                debitParty: {\r\n                  account: account.number,\r\n                },\r\n              });\r\n\r\n            const transferMetadata = {\r\n              creditParty: result.creditParty,\r\n              debitParty: result.debitParty,\r\n            };\r\n\r\n            const newTransaction = manager.create(TransactionEntity, {\r\n              accountId: account.id,\r\n              account,\r\n              code: uuid,\r\n              description: data.description,\r\n              value: data.amount,\r\n              destinyBank: result.creditParty?.bank,\r\n              destinyName: result.creditParty?.name,\r\n              status: TransactionStatusEnum.PENDENT,\r\n              type: TransactionMovementTypeEnum.PIX_CASHOUT,\r\n              endToEndId: result.endToEndId,\r\n              transferMetadata: JSON.stringify(transferMetadata),\r\n            });\r\n            const transaction = await manager.save(newTransaction);\r\n\r\n            return transaction;\r\n          },\r\n        );\r\n      }\r\n\r\n      return {\r\n        id: transaction.id,\r\n        status: transaction.status,\r\n        creditParty: {\r\n          account: transaction.destinyAccount,\r\n          name: transaction.destinyName,\r\n          document: transaction.destinyDocument,\r\n        },\r\n        debitParty: {\r\n          account: account.number,\r\n          name: account.owner?.name || account.business.fantasyName,\r\n          document: account.owner?.cpf || account.business.cnpj,\r\n        },\r\n        createdAt: addHours(transaction.createdAt, -3),\r\n      };\r\n    }\r\n  }\r\n  private async isValidTransaction(account: AccountEntity, amount: string) {\r\n    let accountLimit = await this.accountLimitRepo.findOne({\r\n      where: { accountId: account.id },\r\n    });\r\n\r\n    if (!accountLimit)\r\n      accountLimit = await this.accountLimitRepo.save({\r\n        accountId: account.id,\r\n      });\r\n\r\n    logger.info(\r\n      `TransactionPixKeyService.isValidTransaction() -> [amount: ${amount}, entity: ${JSON.stringify(accountLimit)} ]`,\r\n    );\r\n\r\n    if (!accountLimit.active) return;\r\n\r\n    const virtualBalance = await this.virtualBalanceService.checkBalance({\r\n      accountId: account.id,\r\n    });\r\n\r\n    const amountNumber = parseFloat(amount);\r\n\r\n    if (virtualBalance && amountNumber > virtualBalance.amount)\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n\r\n    const amountIsValidOnGeneralLimit =\r\n      amountNumber <= accountLimit.generalTransferLimit;\r\n\r\n    const amountIsValidOnMonthLimit = amountNumber <= accountLimit.monthlyLimit;\r\n\r\n    const amountIsValidOnDailyLimit = amountNumber <= accountLimit.dailyLimit;\r\n\r\n    const now = new Date().getHours();\r\n    const amountIsValidOnNightlLimit =\r\n      now >= 23\r\n        ? amountNumber <= accountLimit.dailyNightLimit - accountLimit.dailyLimit\r\n        : true;\r\n\r\n    logger.info('TransactionPixKeyService.isValidTransaction() ->', {\r\n      amountIsValidOnGeneralLimit,\r\n      amountIsValidOnMonthLimit,\r\n      amountIsValidOnDailyLimit,\r\n      amountIsValidOnNightlLimit,\r\n    });\r\n\r\n    if (!amountIsValidOnNightlLimit)\r\n      throw new BadRequestException(\r\n        'Limite de transferencia noturna excedido!',\r\n      );\r\n\r\n    if (\r\n      !(\r\n        amountIsValidOnGeneralLimit &&\r\n        amountIsValidOnMonthLimit &&\r\n        amountIsValidOnDailyLimit\r\n      )\r\n    ) {\r\n      throw new BadRequestException('Limite de transferencia excedido!');\r\n    }\r\n  }\r\n\r\n  private async scheduleTransaction(\r\n    data: TransactionPixKeyDto,\r\n    account: AccountEntity,\r\n  ) {\r\n    return this.transactionRepository.save({\r\n      accountId: account.id,\r\n      account,\r\n      code: v4(),\r\n      description: data.description,\r\n      value: data.amount,\r\n      transferDate: moment(data.transferDate),\r\n      endToEndId: data.endToEnd,\r\n      status: TransactionStatusEnum.SCHEDULED,\r\n      destinyAccount: data.external.account,\r\n      destinyAccountType: data.external.accountType,\r\n      destinyBank: data.external.bank,\r\n      destinyBranch: data.external.branch,\r\n      destinyDocument: data.external.document,\r\n      destinyName: data.external.name,\r\n      destinyKey: data.external.key,\r\n      type: TransactionMovementTypeEnum.PIX_CASHOUT,\r\n    });\r\n  }\r\n}\r\n"]}