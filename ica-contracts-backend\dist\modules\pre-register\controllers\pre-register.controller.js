"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreRegisterController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const add_pre_register_dto_1 = require("../dto/add-pre-register.dto");
const direct_add_pre_register_dto_1 = require("../dto/direct-add-pre-register.dto");
const get_filter_pre_register_dto_1 = require("../dto/get-filter-pre-register.dto");
const get_one_pre_register_dto_1 = require("../dto/get-one-pre-register.dto");
const send_pre_register_dto_1 = require("../dto/send-pre-register.dto");
const add_pre_register_service_1 = require("../services/add-pre-register.service");
const direct_add_pre_register_service_1 = require("../services/direct-add-pre-register.service");
const get_filter_pre_register_service_1 = require("../services/get-filter-pre-register.service");
const get_one_pre_register_service_1 = require("../services/get-one-pre-register.service");
const send_pre_register_service_1 = require("../services/send-pre-register.service");
const validate_token_pre_register_service_1 = require("../services/validate-token-pre-register.service");
let PreRegisterController = class PreRegisterController {
    constructor(addService, getFilterService, getOneService, sendService, directAddService, validateTokenService) {
        this.addService = addService;
        this.getFilterService = getFilterService;
        this.getOneService = getOneService;
        this.sendService = sendService;
        this.directAddService = directAddService;
        this.validateTokenService = validateTokenService;
    }
    async add(body, headers) {
        try {
            const response = await this.addService.perform(body, headers.token);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getOne(query) {
        try {
            const response = await this.getOneService.perform(query);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getFilter(query) {
        try {
            const response = await this.getFilterService.perform(query);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async send(body) {
        try {
            const response = await this.sendService.perform(body);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async directAdd(body, headers) {
        try {
            const response = this.directAddService.perform(body, headers.token);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async tokenValidate(headers) {
        try {
            const response = this.validateTokenService.perform(headers.token);
            return response;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
};
exports.PreRegisterController = PreRegisterController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER, roles_enum_1.RolesEnum.ADVISOR),
    (0, common_1.Post)('add'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [add_pre_register_dto_1.AddPreRegisterDto, Object]),
    __metadata("design:returntype", Promise)
], PreRegisterController.prototype, "add", null);
__decorate([
    (0, common_1.Get)('one'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_one_pre_register_dto_1.GetOnePreRegisterDto]),
    __metadata("design:returntype", Promise)
], PreRegisterController.prototype, "getOne", null);
__decorate([
    (0, common_1.Get)('filter'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_filter_pre_register_dto_1.GetFilterPreRegisterDto]),
    __metadata("design:returntype", Promise)
], PreRegisterController.prototype, "getFilter", null);
__decorate([
    (0, common_1.Post)('send'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [send_pre_register_dto_1.SendPreRegisterDto]),
    __metadata("design:returntype", Promise)
], PreRegisterController.prototype, "send", null);
__decorate([
    (0, common_1.Post)('direct-add'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [direct_add_pre_register_dto_1.DirectAddPreRegisterDto, Object]),
    __metadata("design:returntype", Promise)
], PreRegisterController.prototype, "directAdd", null);
__decorate([
    (0, common_1.Get)('token-validate'),
    __param(0, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PreRegisterController.prototype, "tokenValidate", null);
exports.PreRegisterController = PreRegisterController = __decorate([
    (0, common_1.Controller)('pre-register'),
    __param(0, (0, common_1.Inject)(add_pre_register_service_1.AddPreRegisterService)),
    __param(1, (0, common_1.Inject)(get_filter_pre_register_service_1.GetFilterPreRegisterService)),
    __param(2, (0, common_1.Inject)(get_one_pre_register_service_1.GetOnePreRegisterService)),
    __param(3, (0, common_1.Inject)(send_pre_register_service_1.SendPreRegisterService)),
    __param(4, (0, common_1.Inject)(direct_add_pre_register_service_1.DirectAddPreRegisterService)),
    __param(5, (0, common_1.Inject)(validate_token_pre_register_service_1.ValidateTokenPreRegisterService)),
    __metadata("design:paramtypes", [add_pre_register_service_1.AddPreRegisterService,
        get_filter_pre_register_service_1.GetFilterPreRegisterService,
        get_one_pre_register_service_1.GetOnePreRegisterService,
        send_pre_register_service_1.SendPreRegisterService,
        direct_add_pre_register_service_1.DirectAddPreRegisterService,
        validate_token_pre_register_service_1.ValidateTokenPreRegisterService])
], PreRegisterController);
//# sourceMappingURL=pre-register.controller.js.map