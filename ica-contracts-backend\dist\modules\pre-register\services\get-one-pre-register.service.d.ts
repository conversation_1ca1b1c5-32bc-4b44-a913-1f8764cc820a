import { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';
import { Repository } from 'typeorm';
import { GetOnePreRegisterDto } from '../dto/get-one-pre-register.dto';
export declare class GetOnePreRegisterService {
    private preRegisterDb;
    constructor(preRegisterDb: Repository<PreRegisterEntity>);
    perform(data: GetOnePreRegisterDto): Promise<PreRegisterEntity>;
}
