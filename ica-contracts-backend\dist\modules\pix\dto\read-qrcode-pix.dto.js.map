{"version": 3, "file": "read-qrcode-pix.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/read-qrcode-pix.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsD;AAEtD,MAAa,gBAAgB;CAI5B;AAJD,4CAIC;AADC;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,GAAE;;6CACA", "sourcesContent": ["import { IsDefined, IsString } from 'class-validator';\r\n\r\nexport class ReadQRCodePixDto {\r\n  @IsString()\r\n  @IsDefined()\r\n  emv: string;\r\n}\r\n"]}