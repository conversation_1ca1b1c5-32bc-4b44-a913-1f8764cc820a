"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentReportsStrategy = void 0;
const payment_reports_strategy_abstraction_1 = require("./payment-reports.strategy.abstraction");
class PaymentReportsStrategy extends payment_reports_strategy_abstraction_1.PaymentReportsStrategyAbstraction {
    async generateReport(data) {
        const period = this.getPeriodDate(data.period);
        const payments = await this.findByPeriod(period.startDate, period.endDate);
        const transactions = this.transformPaymentsToReportFormat(payments);
        if (transactions.length === 0) {
            return null;
        }
        const pdf = await this.generatePdfPayload(data, transactions);
        return this.savePDF(pdf);
    }
}
exports.PaymentReportsStrategy = PaymentReportsStrategy;
//# sourceMappingURL=payment-reports.strategy.js.map