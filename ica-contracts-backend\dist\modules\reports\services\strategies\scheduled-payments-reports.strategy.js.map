{"version": 3, "file": "scheduled-payments-reports.strategy.js", "sourceRoot": "/", "sources": ["modules/reports/services/strategies/scheduled-payments-reports.strategy.ts"], "names": [], "mappings": ";;;AAEA,uHAAgH;AAEhH,MAAa,gCAAiC,SAAQ,6FAA2C;IAC/F,KAAK,CAAC,cAAc,CAAC,IAAuB;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,YAAY,CAC/C,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,OAAO,CACf,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CACjD,iBAAiB,CAClB,CAAC;QAEF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;YAC7C,UAAU,EAAE,IAAI,CAAC,MAAM;YACvB,YAAY;YACZ,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;CACF;AAzBD,4EAyBC", "sourcesContent": ["import { FileEntity } from 'src/shared/database/typeorm/entities/files-entity';\r\nimport { GenerateReportDto } from '../../dto/generate-report.dto';\r\nimport { ScheduledPaymentsReportsStrategyAbstraction } from './scheduled-payments-reports.strategy.abstraction';\r\n\r\nexport class ScheduledPaymentsReportsStrategy extends ScheduledPaymentsReportsStrategyAbstraction {\r\n  async generateReport(data: GenerateReportDto): Promise<FileEntity | null> {\r\n    const period = this.getPeriodDate(data.period);\r\n\r\n    const scheduledPayments = await this.findByPeriod(\r\n      period.startDate,\r\n      period.endDate,\r\n    );\r\n\r\n    const transactions = this.transformToInvestorFormat(\r\n      scheduledPayments,\r\n    );\r\n\r\n    if (transactions.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    const pdf = await this.generatePdf.generatePdf({\r\n      periodType: data.period,\r\n      transactions,\r\n      type: 'pending',\r\n    });\r\n\r\n    return this.savePdfFile(pdf);\r\n  }\r\n}\r\n"]}