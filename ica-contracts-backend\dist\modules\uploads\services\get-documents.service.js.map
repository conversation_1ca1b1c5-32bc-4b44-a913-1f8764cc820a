{"version": 3, "file": "get-documents.service.js", "sourceRoot": "/", "sources": ["modules/uploads/services/get-documents.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAqD;AACrD,6CAAmD;AACnD,6FAAoF;AACpF,6FAAoF;AACpF,qCAA4C;AAI5C,IAAa,mBAAmB,GAAhC,MAAa,mBAAmB;IAC9B,YAEU,SAAoC,EAEpC,SAAoC;QAFpC,cAAS,GAAT,SAAS,CAA2B;QAEpC,cAAS,GAAT,SAAS,CAA2B;IAC3C,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAqB;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,KAAK,EAAE;gBACL,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBAC3B,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE;aAC/B;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,4BAAmB,CAAC,0BAA0B,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACvC,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,IAAI;gBACV,GAAG,EAAE,IAAI;aACV;YACD,KAAK,EAAE;gBACL,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB;SACF,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAA;AAhCY,kDAAmB;8BAAnB,mBAAmB;IAE3B,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCADb,oBAAU;QAEV,oBAAU;GALpB,mBAAmB,CAgC/B", "sourcesContent": ["import { BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { UploadsEntity } from 'src/shared/database/typeorm/entities/uploads.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { GetDocumentsDto } from '../dto/get-documents.dto';\r\n\r\nexport class GetDocumentsService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(UploadsEntity)\r\n    private uploadsDb: Repository<UploadsEntity>,\r\n  ) {}\r\n  async perform(data: GetDocumentsDto) {\r\n    const account = await this.accountDb.findOne({\r\n      where: [\r\n        { id: Equal(data.id) },\r\n        { ownerId: Equal(data.id) },\r\n        { businessId: Equal(data.id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new BadRequestException('Nenhuma conta encontrada');\r\n\r\n    const upload = await this.uploadsDb.find({\r\n      select: {\r\n        id: true,\r\n        type: true,\r\n        url: true,\r\n      },\r\n      where: {\r\n        businessId: account.businessId,\r\n        ownerId: account.ownerId,\r\n      },\r\n    });\r\n\r\n    return upload;\r\n  }\r\n}\r\n"]}