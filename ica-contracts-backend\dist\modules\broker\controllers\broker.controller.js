"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrokerController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../../shared/decorators/roles.decorator");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const role_guard_1 = require("../../../shared/guards/role.guard");
const edit_investor_service_1 = require("../../investor/services/edit-investor.service");
const create_broker_dto_1 = require("../dto/create-broker.dto");
const edit_broker_dto_1 = require("../dto/edit-broker.dto");
const create_broker_service_1 = require("../services/create-broker.service");
const edit_broker_service_1 = require("../services/edit-broker.service");
const get_broker_advisors_service_1 = require("../services/get-broker-advisors.service");
const get_broker_investors_service_1 = require("../services/get-broker-investors.service");
const get_broker_service_1 = require("../services/get-broker.service");
const dashboard_service_1 = require("../services/dashboard.service");
const contracts_capture_service_1 = require("../services/contracts-capture.service");
const contracts_monthly_capture_service_1 = require("../services/contracts-monthly-capture.service");
const get_one_scheduled_payment_for_broker_service_1 = require("../services/get-one-scheduled-payment-for-broker.service");
const generate_report_dto_1 = require("../dto/generate-report.dto");
const generate_payment_reports_service_1 = require("../services/generate-payment-reports.service");
const generate_scheduled_payments_reports_service_1 = require("../services/generate-scheduled-payments-reports.service");
const list_payment_scheduled_service_1 = require("../services/list-payment-scheduled.service");
const list_payment_scheduled_request_dto_1 = require("../dto/list-payment-scheduled.request.dto");
const generate_contracts_reports_service_1 = require("../services/generate-contracts-reports.service");
const get_broker_contracts_growth_chart_service_1 = require("../services/get-broker-contracts-growth-chart.service");
const contract_type_enum_1 = require("../../../shared/enums/contract-type.enum");
const list_investors_dto_1 = require("../../income-report/dto/list-investors.dto");
const list_investors_service_1 = require("../services/list-investors.service");
let BrokerController = class BrokerController {
    constructor(createBrokerService, getBrokerAdvisorsService, getBrokerInvestorService, editBrokerService, editInvestorService, getBrokerService, brokerDashboardService, contractsCaptureService, contractsMonthlyCaptureService, getOneScheduledPaymentForBrokerService, generatePaymentReportService, listIncomePaymentScheduledBrokerService, generateScheduledPaymentsReportService, generateContractsReportsService, getBrokerContractsGrowthChartService, listActiveInvestorsBrokerService) {
        this.createBrokerService = createBrokerService;
        this.getBrokerAdvisorsService = getBrokerAdvisorsService;
        this.getBrokerInvestorService = getBrokerInvestorService;
        this.editBrokerService = editBrokerService;
        this.editInvestorService = editInvestorService;
        this.getBrokerService = getBrokerService;
        this.brokerDashboardService = brokerDashboardService;
        this.contractsCaptureService = contractsCaptureService;
        this.contractsMonthlyCaptureService = contractsMonthlyCaptureService;
        this.getOneScheduledPaymentForBrokerService = getOneScheduledPaymentForBrokerService;
        this.generatePaymentReportService = generatePaymentReportService;
        this.listIncomePaymentScheduledBrokerService = listIncomePaymentScheduledBrokerService;
        this.generateScheduledPaymentsReportService = generateScheduledPaymentsReportService;
        this.generateContractsReportsService = generateContractsReportsService;
        this.getBrokerContractsGrowthChartService = getBrokerContractsGrowthChartService;
        this.listActiveInvestorsBrokerService = listActiveInvestorsBrokerService;
    }
    create(createBrokerDto) {
        return this.createBrokerService.perform(createBrokerDto);
    }
    async getBrokersAdvisors(request) {
        const getAdvisorsDto = { ownerId: request.user.id };
        const result = await this.getBrokerAdvisorsService.perform(getAdvisorsDto);
        return result;
    }
    async getBrokersInvestor(request) {
        const getAdvisorsDto = { ownerId: request.user.id };
        const result = await this.getBrokerInvestorService.perform(getAdvisorsDto);
        return result;
    }
    async editBroker(request, body) {
        await this.editBrokerService.perform(body);
    }
    async editInvestor(request, body) {
        const result = await this.editInvestorService.perform(body, request.user.id);
        return result;
    }
    async dashboard(request) {
        const result = await this.brokerDashboardService.perform({
            ownerId: request.user.id,
        });
        return result;
    }
    async findActiveInvestors(request, filters) {
        return this.listActiveInvestorsBrokerService.perform(request.user.id, filters);
    }
    async contractsCapture(brokerId) {
        const result = await this.contractsCaptureService.perform(brokerId);
        return result;
    }
    async grafics(brokerId) {
        const result = await this.contractsMonthlyCaptureService.perform(brokerId);
        return result;
    }
    async getOneBroker(brokerId) {
        try {
            return await this.getBrokerService.perform(brokerId);
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async listPaymentScheduledBroker(brokerId, data) {
        return this.listIncomePaymentScheduledBrokerService.perform(brokerId, data);
    }
    async listPaymentScheduled(paymentScheduledId, brokerId) {
        return this.getOneScheduledPaymentForBrokerService.perform(paymentScheduledId, brokerId);
    }
    async generatePaymentReport(brokerId, data) {
        return this.generatePaymentReportService.perform(data, brokerId);
    }
    async generateScheduledPaymentReport(brokerId, data) {
        return this.generateScheduledPaymentsReportService.perform(data, brokerId);
    }
    async generateContractsReport(brokerId, data) {
        return this.generateContractsReportsService.perform(data, brokerId);
    }
    async getContractsGrowthChart(ownerRoleId, period, contractType) {
        return this.getBrokerContractsGrowthChartService.perform(ownerRoleId, period, contractType);
    }
};
exports.BrokerController = BrokerController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.SUPERADMIN, roles_enum_1.RolesEnum.ADMIN),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_broker_dto_1.CreateBrokerDto]),
    __metadata("design:returntype", void 0)
], BrokerController.prototype, "create", null);
__decorate([
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    (0, common_1.Get)('/advisor'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "getBrokersAdvisors", null);
__decorate([
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    (0, common_1.Get)('/investors'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "getBrokersInvestor", null);
__decorate([
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    (0, common_1.Patch)(''),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, edit_broker_dto_1.EditBrokerDto]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "editBroker", null);
__decorate([
    (0, common_1.Put)('investor'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, edit_broker_dto_1.EditBrokerDto]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "editInvestor", null);
__decorate([
    (0, common_1.Get)('dashboard'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "dashboard", null);
__decorate([
    (0, common_1.Get)('income-report/investors'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, list_investors_dto_1.ListActiveInvestorsDto]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "findActiveInvestors", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    (0, common_1.Get)('contracts-capture/:brokerId'),
    __param(0, (0, common_1.Param)('brokerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "contractsCapture", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    (0, common_1.Get)('contracts-monthly-capture/:brokerId'),
    __param(0, (0, common_1.Param)('brokerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "grafics", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)(':brokerId'),
    __param(0, (0, common_1.Param)('brokerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "getOneBroker", null);
__decorate([
    (0, common_1.Get)('/:brokerId/income-payment-scheduled'),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Param)('brokerId')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, list_payment_scheduled_request_dto_1.ListIncomePaymentScheduledBrokerDto]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "listPaymentScheduledBroker", null);
__decorate([
    (0, common_1.Get)('/:brokerId/income-payment-scheduled/:id'),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('brokerId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "listPaymentScheduled", null);
__decorate([
    (0, common_1.Post)('/:brokerId/payment-report'),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Param)('brokerId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, generate_report_dto_1.GenerateReportDto]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "generatePaymentReport", null);
__decorate([
    (0, common_1.Post)('/:brokerId/scheduled-payment-report'),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Param)('brokerId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, generate_report_dto_1.GenerateReportDto]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "generateScheduledPaymentReport", null);
__decorate([
    (0, common_1.Post)('/:brokerId/contracts-report'),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    __param(0, (0, common_1.Param)('brokerId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, generate_report_dto_1.GenerateReportDto]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "generateContractsReport", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    (0, roles_decorator_1.Roles)(roles_enum_1.RolesEnum.BROKER),
    (0, common_1.Get)('/:ownerRoleId/contracts-growth'),
    __param(0, (0, common_1.Param)('ownerRoleId')),
    __param(1, (0, common_1.Query)('period')),
    __param(2, (0, common_1.Query)('contractType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], BrokerController.prototype, "getContractsGrowthChart", null);
exports.BrokerController = BrokerController = __decorate([
    (0, common_1.Controller)('broker'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, role_guard_1.RoleGuard),
    __metadata("design:paramtypes", [create_broker_service_1.CreateBrokerService,
        get_broker_advisors_service_1.GetBrokerAdvisorsService,
        get_broker_investors_service_1.GetBrokerInvestorService,
        edit_broker_service_1.EditBrokerService,
        edit_investor_service_1.EditInvestorService,
        get_broker_service_1.GetBrokerService,
        dashboard_service_1.BrokerDashboardService,
        contracts_capture_service_1.ContractsCaptureService,
        contracts_monthly_capture_service_1.ContractsMonthlyCaptureService,
        get_one_scheduled_payment_for_broker_service_1.GetOneScheduledPaymentForBrokerService,
        generate_payment_reports_service_1.GeneratePaymentReportService,
        list_payment_scheduled_service_1.ListIncomePaymentScheduledBrokerService,
        generate_scheduled_payments_reports_service_1.GenerateScheduledPaymentsReportService,
        generate_contracts_reports_service_1.GenerateContractsReportsService,
        get_broker_contracts_growth_chart_service_1.GetBrokerContractsGrowthChartService,
        list_investors_service_1.ListActiveInvestorsBrokerService])
], BrokerController);
//# sourceMappingURL=broker.controller.js.map