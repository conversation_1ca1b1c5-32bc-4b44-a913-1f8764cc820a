import { AddNewDocumentDto } from '../dto/add-new-document.dto';
import { GetDocumentsDto } from '../dto/get-documents.dto';
import { AddNewDocumentService } from '../services/add-new-document.service';
import { GetDocumentsService } from '../services/get-documents.service';
export declare class UploadsController {
    private addDocService;
    private getDocService;
    constructor(addDocService: AddNewDocumentService, getDocService: GetDocumentsService);
    uploadDocuments(body: AddNewDocumentDto, file: Express.Multer.File): Promise<{
        message: string;
    }>;
    getDocuments(body: GetDocumentsDto): Promise<import("../../../shared/database/typeorm/entities/uploads.entity").UploadsEntity[]>;
}
