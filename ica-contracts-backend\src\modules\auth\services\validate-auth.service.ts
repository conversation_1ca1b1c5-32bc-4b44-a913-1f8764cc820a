import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Repository } from 'typeorm';

import { LoginDto } from '../dto/login.dto';
import { IAuthResponse } from '../interfaces/auth-response.interface';

interface IGenerateTokenPayload {
  id: string;
  cpf: string;
  email: string;
  roles: Array<string>;
}

@Injectable()
export class ValidateAuthService {
  constructor(
    @InjectRepository(OwnerEntity)
    private ownerDb: Repository<OwnerEntity>,
    @InjectRepository(BusinessEntity)
    private businessDb: Repository<BusinessEntity>,
    private jwtService: JwtService,
  ) {}

  async perform(data: LoginDto): Promise<IAuthResponse> {
    if (!data.password && !data.refreshToken) {
      throw new BadRequestException(
        'Password ou RefreshToken deve ser fornecido',
      );
    }

    const typeDocument = this.cpfOrCnpj(data.document);

    if (typeDocument === 'cpf') {
      const owner = await this.ownerDb.findOne({
        relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],
        where: {
          cpf: data.document,
        },
      });

      if (owner) {
        if (owner.account && owner.account[0]?.isExternal) {
          throw new UnauthorizedException();
        }

        const roles: Array<string> = owner.ownerRoleRelation.map(
          (role) => role.role.name,
        );

        if (data.refreshToken) {
          const matchRefreshToken = await bcrypt.compare(
            data.refreshToken,
            owner.refreshToken,
          );
          if (matchRefreshToken) {
            const token = this.generateToken({
              cpf: owner.cpf,
              email: owner.email,
              id: owner.id,
              roles,
            });

            // Buscar informações das relações de role para incluir na resposta
            const userRoleRelations = owner.ownerRoleRelation.map(relation => ({
              roleId: relation.id, // Este é o ID que deve ser usado para vinculações
              roleName: relation.role.name,
              partPercent: relation.partPercent,
            }));

            return {
              accessToken: token,
              user: {
                id: owner.id,
                name: owner.name,
                email: owner.email,
                document: owner.cpf,
                type: 'owner',
                roles: userRoleRelations,
              },
            };
          }
          throw new BadRequestException('Usuário ou refreshToken inválidos');
        }

        const validPassword = await bcrypt.compare(
          data.password,
          owner.password,
        );
        if (validPassword) {
          const token = this.generateToken({
            cpf: owner.cpf,
            email: owner.email,
            id: owner.id,
            roles,
          });
          const refreshToken = await this.generateRefreshToken({
            id: owner.id,
            document: owner.cpf,
            name: owner.name,
            email: owner.email,
          });

          // Buscar informações das relações de role para incluir na resposta
          const userRoleRelations = owner.ownerRoleRelation.map(relation => ({
            roleId: relation.id, // Este é o ID que deve ser usado para vinculações
            roleName: relation.role.name,
            partPercent: relation.partPercent,
          }));

          return {
            accessToken: token,
            refreshToken,
            user: {
              id: owner.id,
              name: owner.name,
              email: owner.email,
              document: owner.cpf,
              type: 'owner',
              roles: userRoleRelations,
            },
          };
        }

        throw new BadRequestException('Usuário ou senha inválidos');
      } else {
        throw new BadRequestException('Usuário ou senha inválidos');
      }
    }

    if (typeDocument === 'cnpj') {
      const business = await this.businessDb.findOne({
        relations: ['ownerRoleRelation', 'ownerRoleRelation.role', 'account'],
        where: {
          cnpj: data.document,
        },
      });

      if (business) {
        if (business.account && business.account[0]?.isExternal) {
          throw new UnauthorizedException();
        }

        const roles: Array<string> = business.ownerRoleRelation.map(
          (role) => role.role.name,
        );

        if (data.refreshToken) {
          const matchRefreshToken = await bcrypt.compare(
            data.refreshToken,
            business.refreshToken,
          );
          if (matchRefreshToken) {
            const token = this.generateToken({
              cpf: business.cnpj,
              email: business.email,
              id: business.id,
              roles,
            });

            // Buscar informações das relações de role para incluir na resposta
            const userRoleRelations = business.ownerRoleRelation.map(relation => ({
              roleId: relation.id, // Este é o ID que deve ser usado para vinculações
              roleName: relation.role.name,
              partPercent: relation.partPercent,
            }));

            return {
              accessToken: token,
              user: {
                id: business.id,
                name: business.companyName,
                email: business.email,
                document: business.cnpj,
                type: 'business',
                roles: userRoleRelations,
              },
            };
          }
          throw new BadRequestException('Usuário ou refreshToken inválidos');
        }

        const validPassword = await bcrypt.compare(
          data.password,
          business.password,
        );
        if (validPassword) {
          const token = this.generateToken({
            cpf: business.cnpj,
            email: business.email,
            id: business.id,
            roles,
          });
          const refreshToken = await this.generateRefreshToken({
            id: business.id,
            document: business.cnpj,
            name: business.companyName,
            email: business.email,
          });

          // Buscar informações das relações de role para incluir na resposta
          const userRoleRelations = business.ownerRoleRelation.map(relation => ({
            roleId: relation.id, // Este é o ID que deve ser usado para vinculações
            roleName: relation.role.name,
            partPercent: relation.partPercent,
          }));

          return {
            accessToken: token,
            refreshToken,
            user: {
              id: business.id,
              name: business.companyName,
              email: business.email,
              document: business.cnpj,
              type: 'business',
              roles: userRoleRelations,
            },
          };
        }

        throw new BadRequestException('Usuário ou senha inválidos');
      } else {
        throw new BadRequestException('Usuário ou senha inválidos');
      }
    }
  }

  private cpfOrCnpj(document: string): string {
    if (document.length > 11) {
      return 'cnpj';
    }
    return 'cpf';
  }

  private generateToken(data: IGenerateTokenPayload) {
    const token = this.jwtService.sign(data, {
      secret: process.env.JWT_SECRET,
      expiresIn: 3600,
    });
    return token;
  }

  private async generateRefreshToken(data: any) {
    const payload = {
      id: data.id,
      document: data.document,
      name: data.name,
      email: data.email,
    };

    const token = this.jwtService.sign(payload, {
      secret: process.env.JWT_SECRET,
      expiresIn: '1y',
    });

    const cryptToken = await bcrypt.hash(token, 10);

    const typeDocument = this.cpfOrCnpj(data.document);
    if (typeDocument === 'cpf') {
      await this.ownerDb.update(data.id, {
        refreshToken: cryptToken,
      });
    } else if (typeDocument === 'cnpj') {
      await this.businessDb.update(data.id, {
        refreshToken: cryptToken,
      });
    }
    return token;
  }

  async getUserInfo(userId: string) {
    // Primeiro tentar buscar como owner
    const owner = await this.ownerDb.findOne({
      where: { id: userId },
      relations: {
        ownerRoleRelation: {
          role: true,
        },
      },
    });

    if (owner) {
      const userRoleRelations = owner.ownerRoleRelation.map(relation => ({
        roleId: relation.id, // Este é o ID que deve ser usado para vinculações
        roleName: relation.role.name,
        partPercent: relation.partPercent,
      }));

      return {
        id: owner.id,
        name: owner.name,
        email: owner.email,
        document: owner.cpf,
        type: 'owner' as const,
        roles: userRoleRelations,
      };
    }

    // Se não encontrou como owner, tentar como business
    const business = await this.businessDb.findOne({
      where: { id: userId },
      relations: {
        ownerRoleRelation: {
          role: true,
        },
      },
    });

    if (business) {
      const userRoleRelations = business.ownerRoleRelation.map(relation => ({
        roleId: relation.id, // Este é o ID que deve ser usado para vinculações
        roleName: relation.role.name,
        partPercent: relation.partPercent,
      }));

      return {
        id: business.id,
        name: business.companyName,
        email: business.email,
        document: business.cnpj,
        type: 'business' as const,
        roles: userRoleRelations,
      };
    }

    throw new BadRequestException('Usuário não encontrado');
  }
}
