import { AggregateRoot } from '@/domain/shared'
import type { BaseEntityProps } from '@/domain/shared/base-entity'
import { Company, Individual, type Party } from '../parties'

interface AdvisorProps extends BaseEntityProps {
  party: Party
  brokerId: string
  participationRate: number
}

export class Advisor extends AggregateRoot<AdvisorProps> {
  private constructor(props: AdvisorProps, id?: string) {
    super(props, id)
  }

  static createFromIndividual(
    individual: Individual,
    brokerId: string,
    participationRate: number,
    id: string
  ): Advisor {
    return new Advisor({ party: individual, brokerId, participationRate }, id)
  }

  static createFromCompany(
    company: Company,
    brokerId: string,
    participationRate: number,
    id: string
  ): Advisor {
    return new Advisor({ party: company, brokerId, participationRate }, id)
  }

  getCnpj(): string | undefined {
    if (this.props.party instanceof Company) {
      return this.props.party.getCnpj()
    }
  }

  getCpf(): string {
    return this.props.party.getCpf()
  }

  getName(): string {
    return this.props.party.getName()
  }

  getEmail(): string {
    return this.props.party.getEmail()
  }

  getPhone(): string {
    return this.props.party.getPhone()
  }

  getBrokerId(): string {
    return this.props.brokerId
  }

  isCompany(): boolean {
    return this.props.party instanceof Company
  }

  isIndividual(): boolean {
    return this.props.party instanceof Individual
  }

  getParty(): Party {
    return this.props.party
  }

  getParticipationRate() {
    return this.props.participationRate
  }

  setParticipationRate(value: number) {
    this.props.participationRate = value
  }
}
