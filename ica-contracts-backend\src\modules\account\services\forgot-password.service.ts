import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { ocultEmail } from 'src/shared/functions/ocult-email';
import { ocultPhone } from 'src/shared/functions/ocult-phone';
import { Equal, Repository } from 'typeorm';

import { ForgotPasswordDto } from '../dto/forgot-password.dto';

@Injectable()
export class ForgotPasswordService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
  ) {}

  async perform(data: ForgotPasswordDto) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          business: {
            cnpj: Equal(data.document),
          },
        },
        {
          owner: {
            cpf: Equal(data.document),
          },
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    const phone =
      account.type === 'physical'
        ? account.owner.phone
        : account.business.ownerBusinessRelation[0].owner.phone;

    const email =
      account.type === 'physical'
        ? account.owner.email
        : account.business.email;

    const maskPhone = ocultPhone(phone);

    const maskEmail = ocultEmail(email);

    return {
      phone: maskPhone,
      email: maskEmail,
    };
  }
}
