{"version": 3, "file": "add-pre-register.service.js", "sourceRoot": "/", "sources": ["modules/pre-register/services/add-pre-register.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAiE;AACjE,6CAAmD;AACnD,+CAAsC;AACtC,6FAAoF;AACpF,uGAA6F;AAC7F,6FAAkF;AAClF,qCAA4C;AASrC,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAChC,YAEU,aAA4C,EAE5C,SAAoC;QAFpC,kBAAa,GAAb,aAAa,CAA+B;QAE5C,cAAS,GAAT,SAAS,CAA2B;IAC3C,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAuB,EAAE,KAAa;QAClD,MAAM,OAAO,GAAG,IAAA,qBAAM,EAAC,KAAK,CAAa,CAAC;QAE1C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,QAAQ,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrC,MAAM,EAAE,gDAAqB,CAAC,MAAM;aACrC;SACF,CAAC,CAAC;QAEH,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,IAAI,4BAAmB,CAC3B,qDAAqD,CACtD,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,KAAK,EAAE;gBACL;oBACE,KAAK,EAAE;wBACL,GAAG,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;qBAC1B;iBACF;gBACD;oBACE,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,QAAQ,CAAC;qBAC3B;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;YAC1C,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAClC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;YAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ;YAC5C,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YACpC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;YACtC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;YACtC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,MAAM,EAAE,gDAAqB,CAAC,MAAM;YACpC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY;YAC3C,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW;YACxC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY;YAC1C,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AAtEY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCADT,oBAAU;QAEd,oBAAU;GALpB,qBAAqB,CAsEjC", "sourcesContent": ["import { BadRequestException, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { decode } from 'jsonwebtoken';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';\r\nimport { PreRegisterStatusEnum } from 'src/shared/enums/pre-register-status.enum';\r\nimport { Repository, Equal } from 'typeorm';\r\n\r\nimport { AddPreRegisterDto } from '../dto/add-pre-register.dto';\r\n\r\ninterface IDecoded {\r\n  adviserId: string;\r\n}\r\n\r\n@Injectable()\r\nexport class AddPreRegisterService {\r\n  constructor(\r\n    @InjectRepository(PreRegisterEntity)\r\n    private preRegisterDb: Repository<PreRegisterEntity>,\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n  ) {}\r\n  async perform(data: AddPreRegisterDto, token: string) {\r\n    const decoded = decode(token) as IDecoded;\r\n\r\n    const preRegister = await this.preRegisterDb.findOne({\r\n      where: {\r\n        document: Equal(data.document.trim()),\r\n        status: PreRegisterStatusEnum.ACTIVE,\r\n      },\r\n    });\r\n\r\n    if (preRegister) {\r\n      throw new BadRequestException(\r\n        'Ja existe um pre registro ativo para este documento',\r\n      );\r\n    }\r\n\r\n    const account = await this.accountDb.findOne({\r\n      where: [\r\n        {\r\n          owner: {\r\n            cpf: Equal(data.document),\r\n          },\r\n        },\r\n        {\r\n          business: {\r\n            cnpj: Equal(data.document),\r\n          },\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (account) {\r\n      throw new BadRequestException('Ja existe uma conta com este documento');\r\n    }\r\n\r\n    const create = this.preRegisterDb.create({\r\n      adviserId: decoded.adviserId,\r\n      addressComplement: data.address.complement,\r\n      addressNumber: data.address.number,\r\n      neighborhood: data.address.neighborhood,\r\n      zipCode: data.address.zipCode,\r\n      city: data.address.city,\r\n      document: data.document,\r\n      dtBirth: data.dtBirth,\r\n      email: data.email,\r\n      investmentModality: data.investment.modality,\r\n      investmentTerm: data.investment.term,\r\n      investmentValue: data.investment.value,\r\n      investmentYield: data.investment.yield,\r\n      name: data.name,\r\n      phoneNumber: data.phoneNumber,\r\n      rg: data.rg,\r\n      observations: data.observations,\r\n      status: PreRegisterStatusEnum.ACTIVE,\r\n      amountQuotes: data.investment?.amountQuotes,\r\n      gracePeriod: data.investment.gracePeriod,\r\n      purchaseWith: data.investment.purchaseWith,\r\n      state: data.address.state,\r\n      motherName: data.motherName,\r\n    });\r\n\r\n    await this.preRegisterDb.save(create);\r\n  }\r\n}\r\n"]}