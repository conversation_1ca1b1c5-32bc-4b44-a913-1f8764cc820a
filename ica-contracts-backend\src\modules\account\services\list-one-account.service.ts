import { Inject, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { GetAccountLimitService } from 'src/modules/account-transfer-limit/services/get-account-limit.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { GetBalanceService } from './get-balance.service';

export class ListOneAccountService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @Inject(GetAccountLimitService)
    private getLimit: GetAccountLimitService,
    @Inject(GetBalanceService)
    private getBalance: GetBalanceService,
  ) {}

  async perform(id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        business: { ownerBusinessRelation: { owner: true } },
      },
      where: {
        id: Equal(id),
      },
      select: {
        id: true,
        number: true,
        status: true,
        branch: true,
        type: true,
        owner: {
          id: true,
          name: true,
          nickname: true,
          cpf: true,
          email: true,
          endContract: true,
          totalInvested: true,
          address: true,
          dtBirth: true,
        },
        business: {
          id: true,
          companyName: true,
          fantasyName: true,
          cnpj: true,
          email: true,
          endContract: true,
          totalInvested: true,
          address: true,
          ownerBusinessRelation: {
            id: true,
            owner: {
              id: true,
              name: true,
              nickname: true,
              cpf: true,
              email: true,
            },
          },
        },
      },
    });

    if (!account) throw new NotFoundException('Conta não encontrada');

    const limit = await this.getLimit.execute(account.id);

    const balance = await this.getBalance.perform({
      userId: account.id,
    });
    return {
      account,
      limits: limit,
      balance,
    };
  }
}
