{"version": 3, "file": "get-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/get-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,6CAAmD;AACnD,oGAAyF;AACzF,6FAAoF;AACpF,mFAAyE;AACzE,qCAA4C;AAKrC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YAEU,SAAoC,EAEpC,cAAoC;QAFpC,cAAS,GAAT,SAAS,CAA2B;QAEpC,mBAAc,GAAd,cAAc,CAAsB;IAC3C,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IACE,CAAC,OAAO;YACR,CAAC,OAAO,CAAC,MAAM;YACf,OAAO,CAAC,MAAM,KAAK,uCAAiB,CAAC,MAAM;YAE3C,MAAM,IAAI,0BAAiB,CAAC,uCAAuC,CAAC,CAAC;QAEvE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;YACnE,aAAa,EAAE,OAAO,CAAC,MAAM;SAC9B,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,EAAE,CAAC;QAElB,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC;YAEvB,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CACxC,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC,GAAG,KAAK,GAAG,CAC7C,CAAC;YAEF,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,CAAC;gBAE9B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC7B,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,IAAI,EAAE,MAAM,CAAC,OAAO;YACpB,GAAG,EAAE,MAAM,CAAC,GAAG;SAChB,CAAC,CAAC,CAAC;IACN,CAAC;CACF,CAAA;AAtDY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,eAAM,EAAC,8CAAoB,CAAC,CAAA;qCADV,oBAAU;QAEL,8CAAoB;GALnC,gBAAgB,CAsD5B", "sourcesContent": ["import { Inject, Injectable, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixKeyCelcoinService } from 'src/apis/celcoin/services/pix-key-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { AccountStatusEnum } from 'src/shared/enums/account-status.enum';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { IGetPixKeyReponse } from '../response/get-pix-key-response';\r\n\r\n@Injectable()\r\nexport class GetPixKeyService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @Inject(PixKeyCelcoinService)\r\n    private celcoinService: PixKeyCelcoinService,\r\n  ) {}\r\n  async perform(id: string): Promise<IGetPixKeyReponse[]> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n        pixKey: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (\r\n      !account ||\r\n      !account.number ||\r\n      account.status !== AccountStatusEnum.ACTIVE\r\n    )\r\n      throw new NotFoundException('Conta não encontrada ou nāo aprovada.');\r\n\r\n    const { body: result } = await this.celcoinService.getAccountPixKeys({\r\n      accountNumber: account.number,\r\n    });\r\n\r\n    const output = [];\r\n\r\n    for (const pixKey of result.listKeys) {\r\n      const { key } = pixKey;\r\n\r\n      const matchingPixKey = account.pixKey.find(\r\n        (accountPixKey) => accountPixKey.key === key,\r\n      );\r\n\r\n      if (matchingPixKey) {\r\n        pixKey.id = matchingPixKey.id;\r\n\r\n        output.push(pixKey);\r\n      }\r\n    }\r\n\r\n    return output.map((pixKey) => ({\r\n      id: pixKey.id,\r\n      type: pixKey.keyType,\r\n      key: pixKey.key,\r\n    }));\r\n  }\r\n}\r\n"]}