export interface IncomeReport {
  id: string;
  investor_id: string;
  reference_year: string;
  status: string;
  file_id?: string;
  inportReportUrl?: string;
}

export enum IncomeReportStatus {
  DRAFT = 'DRAFT',
  SENDING_TO_REPORT_API = 'SENDING_TO_REPORT_API',
  REPORT_CREATED = 'REPORT_CREATED',
  SEND_EMAIL = 'SEND_EMAIL',
  ERROR_REPORT_API = 'ERROR_REPORT_API',
  ERROR_UPLOAD_REPORT_FILE = 'ERROR_UPLOAD_REPORT_FILE',
  ERROR_CREATE_EMAIL = 'ERROR_CREATE_EMAIL',
}
