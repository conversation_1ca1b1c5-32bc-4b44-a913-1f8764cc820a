{"version": 3, "file": "add-new-document.service.js", "sourceRoot": "/", "sources": ["modules/uploads/services/add-new-document.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAqD;AACrD,6CAAmD;AACnD,+BAA+B;AAC/B,qHAA0G;AAC1G,6FAAoF;AACpF,qCAA4C;AAC5C,+BAAoC;AAIpC,IAAa,qBAAqB,GAAlC,MAAa,qBAAqB;IAGhC,YAEU,2BAAgE,EAEhE,SAAoC;QAFpC,gCAA2B,GAA3B,2BAA2B,CAAqC;QAEhE,cAAS,GAAT,SAAS,CAA2B;QAE5C,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;YACnB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;YACvC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;YAC3C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;SAC/B,CAAC,CAAC;IACL,CAAC;IACD,KAAK,CAAC,OAAO,CAAC,IAAuB,EAAE,IAAyB;QAC9D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC;YAC/D,KAAK,EAAE;gBACL,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;gBAC9C,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;aAClD;YACD,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC3C,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,4BAAmB,CAAC,0BAA0B,CAAC,CAAC;QAE1E,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,UAAU,CAC/B,IAAI,EACJ,SAAS,CAAC,QAAQ,EAAE,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE,GAAG,CACjD,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC1C,KAAK,EAAE;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B;SACF,CAAC,CAAC;QAEH,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE;gBACrC,GAAG;aACJ,CAAC,CAAC;YACH,OAAO;gBACL,OAAO,EAAE,sBAAsB;aAChC,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YACnC,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG;SACJ,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAElC,OAAO;YACL,OAAO,EAAE,kBAAkB;SAC5B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,IAAyB,EACzB,MAAc;QAEd,MAAM,QAAQ,GAAG,GAAG,MAAM,IAAI,IAAA,SAAM,GAAE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9D,MAAM,MAAM,GAAG;YACb,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;YAC7B,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,WAAW,EAAE,IAAI,CAAC,QAAQ;YAC1B,GAAG,EAAE,aAAa;SACnB,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YAC5D,OAAO,YAAY,CAAC,QAAQ,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,4BAAmB,CAC3B,2CAA2C,CAC5C,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AArFY,sDAAqB;gCAArB,qBAAqB;IAI7B,WAAA,IAAA,0BAAgB,EAAC,oDAAuB,CAAC,CAAA;IAEzC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;qCADK,oBAAU;QAE5B,oBAAU;GAPpB,qBAAqB,CAqFjC", "sourcesContent": ["import { BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport * as AWS from 'aws-sdk';\r\nimport { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';\r\nimport { UploadsEntity } from 'src/shared/database/typeorm/entities/uploads.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nimport { AddNewDocumentDto } from '../dto/add-new-document.dto';\r\n\r\nexport class AddNewDocumentService {\r\n  private readonly s3: AWS.S3;\r\n\r\n  constructor(\r\n    @InjectRepository(OwnerRoleRelationEntity)\r\n    private ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,\r\n    @InjectRepository(UploadsEntity)\r\n    private uploadsDb: Repository<UploadsEntity>,\r\n  ) {\r\n    this.s3 = new AWS.S3({\r\n      accessKeyId: process.env.AWS_ACCESS_KEY,\r\n      secretAccessKey: process.env.AWS_SECRET_KEY,\r\n      region: process.env.AWS_REGION,\r\n    });\r\n  }\r\n  async perform(data: AddNewDocumentDto, file: Express.Multer.File) {\r\n    const ownerRole = await this.ownerRoleRelationRepository.findOne({\r\n      where: [\r\n        { id: Equal(data.id) },\r\n        { owner: { account: { id: Equal(data.id) } } },\r\n        { business: { account: { id: Equal(data.id) } } },\r\n      ],\r\n      relations: { owner: true, business: true },\r\n    });\r\n\r\n    if (!ownerRole) throw new BadRequestException('Nenhum perfil encontrado');\r\n\r\n    const url = await this.uploadToS3(\r\n      file,\r\n      ownerRole.business?.cnpj || ownerRole.owner?.cpf,\r\n    );\r\n\r\n    const upload = await this.uploadsDb.findOne({\r\n      where: {\r\n        type: data.type,\r\n        businessId: ownerRole.businessId,\r\n        ownerId: ownerRole.ownerId,\r\n      },\r\n    });\r\n\r\n    if (upload) {\r\n      await this.uploadsDb.update(upload.id, {\r\n        url,\r\n      });\r\n      return {\r\n        message: 'Documento atualizado',\r\n      };\r\n    }\r\n\r\n    const create = this.uploadsDb.create({\r\n      businessId: ownerRole.businessId,\r\n      ownerId: ownerRole.ownerId,\r\n      type: data.type,\r\n      url,\r\n    });\r\n\r\n    await this.uploadsDb.save(create);\r\n\r\n    return {\r\n      message: 'Documento criado',\r\n    };\r\n  }\r\n\r\n  private async uploadToS3(\r\n    file: Express.Multer.File,\r\n    folder: string,\r\n  ): Promise<string> {\r\n    const fileName = `${folder}/${uuidv4()}-${file.originalname}`;\r\n    const params = {\r\n      Bucket: process.env.S3_BUCKET,\r\n      Key: fileName,\r\n      Body: file.buffer,\r\n      ContentType: file.mimetype,\r\n      ACL: 'public-read',\r\n    };\r\n\r\n    try {\r\n      const uploadResult = await this.s3.upload(params).promise();\r\n      return uploadResult.Location;\r\n    } catch (error) {\r\n      throw new BadRequestException(\r\n        'Erro ao fazer upload do arquivo para o S3',\r\n      );\r\n    }\r\n  }\r\n}\r\n"]}