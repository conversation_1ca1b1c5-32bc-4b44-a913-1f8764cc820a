{"version": 3, "file": "get-filter-pre-register.service.js", "sourceRoot": "/", "sources": ["modules/pre-register/services/get-filter-pre-register.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,uGAA6F;AAC7F,qCAAsE;AAK/D,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACtC,YAEU,aAA4C;QAA5C,kBAAa,GAAb,aAAa,CAA+B;IACnD,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAA6B;QACzC,MAAM,WAAW,GAAwC;YACvD,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;QACF,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,GAAG,IAAA,cAAI,EAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/D,WAAW,CAAC,eAAe,GAAG,IAAA,iBAAO,EAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACjC,WAAW,CAAC,SAAS,GAAG,IAAA,iBAAO,EAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACvC,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACjD,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;CACF,CAAA;AApCY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCACb,oBAAU;GAHxB,2BAA2B,CAoCvC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';\r\nimport { Between, FindOptionsWhere, Like, Repository } from 'typeorm';\r\n\r\nimport { GetFilterPreRegisterDto } from '../dto/get-filter-pre-register.dto';\r\n\r\n@Injectable()\r\nexport class GetFilterPreRegisterService {\r\n  constructor(\r\n    @InjectRepository(PreRegisterEntity)\r\n    private preRegisterDb: Repository<PreRegisterEntity>,\r\n  ) {}\r\n\r\n  async perform(data: GetFilterPreRegisterDto) {\r\n    const whereClause: FindOptionsWhere<PreRegisterEntity> = {\r\n      adviserId: data.adviserId,\r\n    };\r\n    if (data.status) {\r\n      whereClause.status = data.status;\r\n    }\r\n\r\n    if (data.name) {\r\n      whereClause.name = Like(`${data.name}%`);\r\n    }\r\n\r\n    if (data.valueMin !== undefined && data.valueMax !== undefined) {\r\n      whereClause.investmentValue = Between(data.valueMin, data.valueMax);\r\n    }\r\n\r\n    if (data.dateFrom && data.dateTo) {\r\n      whereClause.createdAt = Between(data.dateFrom, data.dateTo);\r\n    }\r\n\r\n    if (data.document) {\r\n      whereClause.document = data.document;\r\n    }\r\n\r\n    const preRegisters = await this.preRegisterDb.find({\r\n      where: whereClause,\r\n    });\r\n\r\n    return preRegisters;\r\n  }\r\n}\r\n"]}