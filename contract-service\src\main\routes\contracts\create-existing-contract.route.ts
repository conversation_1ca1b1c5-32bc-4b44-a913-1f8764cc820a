import { fastifyRouteAdapter } from "@/main/adapters";
import { makeCreateExistingContractController } from "@/main/factories/controllers/contracts/create-existing-contract-controller.factory";
import multipart from "@fastify/multipart";
import type { FastifyInstance } from "fastify";

export const createExistingContractRoute = async (app: FastifyInstance) => {
  await app.register(multipart, {
    limits: {
      fileSize: 100 * 1024 * 1024, // 100MB
      files: 5, // Máximo de 5 arquivos
    },
    attachFieldsToBody: true,
  });

  app.post(
    "/contracts/existing",
    {
      config: {
        rawBody: true,
      },
    },
    fastifyRouteAdapter(makeCreateExistingContractController())
  );
};
