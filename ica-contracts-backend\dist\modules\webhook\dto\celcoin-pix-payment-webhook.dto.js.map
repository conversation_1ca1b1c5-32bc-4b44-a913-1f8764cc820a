{"version": 3, "file": "celcoin-pix-payment-webhook.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/celcoin-pix-payment-webhook.dto.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface ICelcoinPixPaymentWebhookDto {\r\n  entity: 'pix-payment-in';\r\n  createTimestamp: string;\r\n  status: 'CONFIRMED';\r\n  body: {\r\n    amount: number;\r\n    debitParty: {\r\n      bank: number;\r\n      taxId: number;\r\n      name: string;\r\n      branch: number;\r\n      account: number;\r\n      accountType: string;\r\n    };\r\n    id: string;\r\n    endToEndId: string;\r\n    creditParty: {\r\n      bank: number;\r\n      taxId: number;\r\n      branch: number;\r\n      account: number;\r\n      accountType: string;\r\n      key: number;\r\n    };\r\n    initiationType: string;\r\n    transactionType: 'RECEIVEPIX';\r\n    urgency: string;\r\n    paymentType: string;\r\n    transactionIdentification: string;\r\n  };\r\n}\r\n"]}