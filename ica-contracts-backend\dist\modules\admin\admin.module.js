"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminModule = void 0;
const common_1 = require("@nestjs/common");
const shared_module_1 = require("../../shared/shared.module");
const investor_module_1 = require("../investor/investor.module");
const admin_controller_1 = require("./controller/admin.controller");
const dashboard_service_1 = require("./services/dashboard.service");
const edit_admin_service_1 = require("./services/edit-admin.service");
const get_admin_service_1 = require("./services/get-admin.service");
const get_admin_contracts_growth_chart_service_1 = require("./services/get-admin-contracts-growth-chart.service");
const change_password_service_1 = require("./services/change-password.service");
const email_entity_1 = require("../../shared/database/typeorm/entities/email.entity");
const business_entity_1 = require("../../shared/database/typeorm/entities/business.entity");
const owner_entity_1 = require("../../shared/database/typeorm/entities/owner.entity");
const contract_entity_1 = require("../../shared/database/typeorm/entities/contract.entity");
const typeorm_1 = require("@nestjs/typeorm");
const email_module_1 = require("../../shared/email-ng/email.module");
const list_contracts_service_1 = require("./services/list-contracts.service");
let AdminModule = class AdminModule {
};
exports.AdminModule = AdminModule;
exports.AdminModule = AdminModule = __decorate([
    (0, common_1.Module)({
        imports: [
            shared_module_1.SharedModule,
            investor_module_1.InvestorModule,
            email_module_1.EmailNgModule,
            typeorm_1.TypeOrmModule.forFeature([owner_entity_1.OwnerEntity, business_entity_1.BusinessEntity, email_entity_1.EmailEntity, contract_entity_1.ContractEntity]),
        ],
        providers: [
            get_admin_service_1.GetAdminService,
            dashboard_service_1.AdminDashboardService,
            edit_admin_service_1.EditAdminService,
            get_admin_contracts_growth_chart_service_1.GetAdminContractsGrowthChartService,
            change_password_service_1.ChangePasswordAdminService,
            list_contracts_service_1.ListContractsAdminService,
        ],
        controllers: [admin_controller_1.AdminController],
        exports: [],
    })
], AdminModule);
//# sourceMappingURL=admin.module.js.map