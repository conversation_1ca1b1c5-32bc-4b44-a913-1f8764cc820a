{"version": 3, "file": "response.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-all-brokers-with-active-contracts/response.dto.ts"], "names": [], "mappings": ";;;AAGA,MAAa,kBAAkB;CAM9B;AAND,gDAMC;AAED,MAAa,2CAA2C;CAKvD;AALD,kGAKC", "sourcesContent": ["import { PaginationMeta } from '../../helpers/pagination-meta.dto';\r\nimport { IPaginatedResult } from '../../helpers/pagination-query';\r\n\r\nexport class BrokerInfoResponse {\r\n  brokerId: string;\r\n  rank: number;\r\n  name: string;\r\n  avatar: string;\r\n  totalValue: number;\r\n}\r\n\r\nexport class GetAllBrokersWithActiveContractsResponseDto\r\n  implements IPaginatedResult<BrokerInfoResponse>\r\n{\r\n  data: BrokerInfoResponse[];\r\n  meta: PaginationMeta;\r\n}\r\n"]}