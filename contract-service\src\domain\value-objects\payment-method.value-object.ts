import { type Either, left, right } from '@/domain/shared/either'

const allowedMethods = ['pix', 'bank_transfer', 'boleto'] as const
type AllowedPaymentMethod = (typeof allowedMethods)[number]

export class PaymentMethod {
  private constructor(private readonly _method: AllowedPaymentMethod) {}

  static create(method: string): Either<Error, PaymentMethod> {
    if (!allowedMethods.includes(method as AllowedPaymentMethod)) {
      return left(new Error(`Invalid payment method: ${method}`))
    }

    return right(new PaymentMethod(method as AllowedPaymentMethod))
  }

  static pix(): PaymentMethod {
    return new PaymentMethod('pix')
  }

  static bankTransfer(): PaymentMethod {
    return new PaymentMethod('bank_transfer')
  }

  static boleto(): PaymentMethod {
    return new PaymentMethod('boleto')
  }

  get value(): AllowedPaymentMethod {
    return this._method
  }

  toString(): string {
    return this._method
  }

  equals(other: PaymentMethod): boolean {
    return this._method === other._method
  }
}
