"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixModule = void 0;
const common_1 = require("@nestjs/common");
const apis_module_1 = require("../../apis/apis.module");
const messaging_module_1 = require("../../messaging/messaging.module");
const shared_module_1 = require("../../shared/shared.module");
const account_transfer_limit_module_1 = require("../account-transfer-limit/account-transfer-limit.module");
const two_factor_auth_module_1 = require("../two-factor-auth/two-factor-auth.module");
const two_factor_auth_service_1 = require("../two-factor-auth/two-factor-auth.service");
const favorite_pix_controller_1 = require("./controllers/favorite-pix.controller");
const pix_controller_1 = require("./controllers/pix.controller");
const transaction_pix_controller_1 = require("./controllers/transaction-pix.controller");
const add_favorite_pix_service_1 = require("./service/add-favorite-pix.service");
const claim_cancel_pix_key_service_1 = require("./service/claim-cancel-pix-key.service");
const claim_confim_pix_key_service_1 = require("./service/claim-confim-pix-key.service");
const claim_get_pix_key_service_1 = require("./service/claim-get-pix-key.service");
const create_pix_key_service_1 = require("./service/create-pix-key.service");
const create_pix_qrcode_dynamic_service_1 = require("./service/create-pix-qrcode-dynamic.service");
const create_pix_qrcode_static_service_1 = require("./service/create-pix-qrcode-static.service");
const delete_favorite_pix_service_1 = require("./service/delete-favorite-pix.service");
const delete_pix_key_service_1 = require("./service/delete-pix-key.service");
const get_favorite_pix_account_service_1 = require("./service/get-favorite-pix-account.service");
const get_internal_pix_transaction_service_1 = require("./service/get-internal-pix-transaction.service");
const get_pix_key_external_service_1 = require("./service/get-pix-key-external.service");
const get_pix_key_service_1 = require("./service/get-pix-key.service");
const get_pix_participants_service_1 = require("./service/get-pix-participants.service");
const internal_transaction_service_1 = require("./service/internal-transaction.service");
const process_scheduled_pix_service_1 = require("./service/process-scheduled-pix.service");
const read_qrcode_pix_service_1 = require("./service/read-qrcode-pix.service");
const request_claim_pix_key_service_1 = require("./service/request-claim-pix-key.service");
const reversal_pix_service_1 = require("./service/reversal-pix.service");
const transaction_pix_key_service_1 = require("./service/transaction-pix-key.service");
const transaction_pix_manual_service_1 = require("./service/transaction-pix-manual.service");
const transaction_pix_status_service_1 = require("./service/transaction-pix-status.service");
let PixModule = class PixModule {
};
exports.PixModule = PixModule;
exports.PixModule = PixModule = __decorate([
    (0, common_1.Module)({
        controllers: [pix_controller_1.PixController, transaction_pix_controller_1.TransactionPixController, favorite_pix_controller_1.FavoritePixController],
        imports: [
            shared_module_1.SharedModule,
            apis_module_1.ApisModule,
            messaging_module_1.MessagingModule,
            account_transfer_limit_module_1.AccountTransferLimitModule,
            two_factor_auth_module_1.TwoFactorAuthModule,
        ],
        providers: [
            create_pix_key_service_1.CreatePixKeyService,
            get_pix_key_service_1.GetPixKeyService,
            get_pix_key_external_service_1.GetPixKeyExternalService,
            transaction_pix_key_service_1.TransactionPixKeyService,
            delete_pix_key_service_1.DeletePixKeyService,
            transaction_pix_status_service_1.TransactionPixStatusService,
            get_pix_participants_service_1.GetPixParticipantsService,
            get_internal_pix_transaction_service_1.GetInternalPixTransactionService,
            create_pix_qrcode_static_service_1.CreatePixQRCodeStaticService,
            internal_transaction_service_1.InternalTransactionService,
            request_claim_pix_key_service_1.RequestClaimPixKeyService,
            create_pix_qrcode_dynamic_service_1.CreatePixQRCodeDynamicService,
            claim_confim_pix_key_service_1.ClaimConfirmPixKeyService,
            read_qrcode_pix_service_1.ReadQRCodePixService,
            transaction_pix_manual_service_1.TransactionPixManualService,
            reversal_pix_service_1.ReversalPixService,
            claim_cancel_pix_key_service_1.ClaimCancelPixKeyService,
            claim_get_pix_key_service_1.ClaimGetPixKeyService,
            process_scheduled_pix_service_1.ProcessScheduledPix,
            add_favorite_pix_service_1.AddFavoritePixService,
            get_favorite_pix_account_service_1.GetFavoritePixAccountService,
            delete_favorite_pix_service_1.DeleteFavoritePixService,
            two_factor_auth_service_1.TwoFactorAuthService,
        ],
    })
], PixModule);
//# sourceMappingURL=pix.module.js.map