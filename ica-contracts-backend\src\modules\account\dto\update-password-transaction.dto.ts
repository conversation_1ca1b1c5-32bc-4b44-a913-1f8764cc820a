import { IsNotEmpty, IsString, Length, Matches } from 'class-validator';

export class UpdatePasswordTransactionDto {
  @IsNotEmpty()
  @IsString()
  accountNumber: string;

  @IsNotEmpty()
  @IsString()
  @Length(4, 4, {
    message: 'A senha de transação antiga deve conter exatamente 4 números.',
  })
  @Matches(/^\d{4}$/, {
    message: 'A senha de transação antiga deve conter apenas números.',
  })
  oldPassword: string;

  @IsNotEmpty()
  @IsString()
  @Length(4, 4, {
    message: 'A nova senha de transação deve conter exatamente 4 números.',
  })
  @Matches(/^\d{4}$/, {
    message: 'A nova senha de transação deve conter apenas números.',
  })
  newPassword: string;
}
