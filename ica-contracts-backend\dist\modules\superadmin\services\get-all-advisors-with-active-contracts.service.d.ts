import { C<PERSON> } from 'cache-manager';
import { Repository } from 'typeorm';
import { ContractAdvisorEntity } from 'src/shared/database/typeorm/entities/contract-advisor.entity';
import { GetAllAdvisorsWithActiveContractsQueryDto } from '../dto/get-all-advisors-with-active-contracts/query.dto';
import { GetAllAdvisorsWithActiveContractsResponseDto } from '../dto/get-all-advisors-with-active-contracts/response.dto';
export declare class GetAllAdvisorsWithActiveContractsService {
    private contractsAdvisorRepository;
    private cacheManager;
    constructor(contractsAdvisorRepository: Repository<ContractAdvisorEntity>, cacheManager: Cache);
    perform(query: GetAllAdvisorsWithActiveContractsQueryDto): Promise<GetAllAdvisorsWithActiveContractsResponseDto>;
}
