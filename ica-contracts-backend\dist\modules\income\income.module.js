"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncomeModule = void 0;
const common_1 = require("@nestjs/common");
const validate_admin_middleware_1 = require("../../shared/middlewares/validate-admin.middleware");
const shared_module_1 = require("../../shared/shared.module");
const income_controller_1 = require("./controllers/income.controller");
const delete_income_service_1 = require("./services/delete-income.service");
const income_detail_service_1 = require("./services/income-detail.service");
const list_incomes_service_1 = require("./services/list-incomes.service");
const save_income_service_1 = require("./services/save-income.service");
const update_income_service_1 = require("./services/update-income.service");
let IncomeModule = class IncomeModule {
    configure(consumer) {
        consumer
            .apply(validate_admin_middleware_1.ValidateAdminMiddleware)
            .forRoutes({ path: 'income', method: common_1.RequestMethod.POST }, { path: 'income', method: common_1.RequestMethod.GET }, { path: 'income/one', method: common_1.RequestMethod.GET }, { path: 'income/:incomeId', method: common_1.RequestMethod.PATCH }, { path: 'income', method: common_1.RequestMethod.DELETE });
    }
};
exports.IncomeModule = IncomeModule;
exports.IncomeModule = IncomeModule = __decorate([
    (0, common_1.Module)({
        imports: [shared_module_1.SharedModule],
        providers: [
            save_income_service_1.SaveIncomeService,
            list_incomes_service_1.ListIncomesService,
            income_detail_service_1.IncomeDetailService,
            update_income_service_1.UpdateIncomeService,
            delete_income_service_1.DeleteIncomeService,
        ],
        controllers: [income_controller_1.IncomeController],
    })
], IncomeModule);
//# sourceMappingURL=income.module.js.map