import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { Repository } from 'typeorm';
export declare class GetDashboardDataService {
    private contractRepository;
    private ownerRoleRelationEntity;
    constructor(contractRepository: Repository<ContractEntity>, ownerRoleRelationEntity: Repository<OwnerRoleRelationEntity>);
    perform(): Promise<{
        distributedIncome: number;
        scpWithdraws: number;
        p2pWithdraws: number;
        numberBrokers: number;
        numberAdvisors: number;
        p2pContractNumber: number;
        scpContractNumber: number;
        p2pContractAmount: number;
        scpContractAmount: number;
        activeQuotes: number;
        shareholder: number;
        activeInvestorsNumber: number;
    }>;
    private isContractDateValid;
    private isAddendumNotExpired;
}
