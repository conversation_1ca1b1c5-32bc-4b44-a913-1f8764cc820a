import { ForbiddenException, Injectable, NestMiddleware } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { NextFunction, Request, Response } from 'express';
import { decode } from 'jsonwebtoken';
import { Equal, Repository } from 'typeorm';

import { OwnerRoleRelationEntity } from '../database/typeorm/entities/owner-role-relation.entity';
import { IDecodeJwt } from '../interfaces/decode-jwt.interface';

@Injectable()
export class ValidateAdminMiddleware implements NestMiddleware {
  constructor(
    @InjectRepository(OwnerRoleRelationEntity)
    private perfilDb: Repository<OwnerRoleRelationEntity>,
  ) {}
  async use(req: Request, res: Response, next: NextFunction) {
    const token = req.headers.authorization.split(' ')[1];
    const decodeJwt = decode(token) as IDecodeJwt;

    const adviserId =
      req.body.adviserId ||
      req.body.roleId ||
      req.query.adviserId ||
      req.query.roleId;

    const owner = await this.perfilDb.findOne({
      relations: {
        role: true,
      },
      where: [
        {
          ownerId: Equal(decodeJwt.id),
          id: Equal(adviserId),
          role: [{ name: 'admin' }],
        },
        {
          businessId: Equal(decodeJwt.id),
          id: Equal(adviserId),
          role: [{ name: 'admin' }],
        },
      ],
    });

    if (!owner) {
      throw new ForbiddenException();
    }

    next();
  }
}
