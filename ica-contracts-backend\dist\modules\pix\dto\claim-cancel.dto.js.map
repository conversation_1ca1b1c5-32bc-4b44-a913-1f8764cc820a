{"version": 3, "file": "claim-cancel.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/claim-cancel.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA0E;AAC1E,+EAAqE;AAErE,MAAa,cAAc;CAQ1B;AARD,wCAQC;AALC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;2CACC;AAIZ;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,mCAAe,CAAC;;8CACA", "sourcesContent": ["import { IsDefined, IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { ClaimReasonEnum } from 'src/shared/enums/claim-reason.enum';\r\n\r\nexport class ClaimCancelDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  key: string;\r\n\r\n  @IsOptional()\r\n  @IsEnum(ClaimReasonEnum)\r\n  reason: ClaimReasonEnum;\r\n}\r\n"]}