import type { Contract } from '@/contexts/income-report/domain/entities/contract'
import type { IncomeReport } from '@/contexts/income-report/domain/entities/income-report'
import type { IncomeRepositoryRepository } from '@/contexts/income-report/domain/repositories/income-report.repository'

import prisma from '@/infrastructure/prisma/client'

export class PrismaIncomeReportRepository
  implements IncomeRepositoryRepository
{
  async save(incomeReport: IncomeReport): Promise<void> {
    await prisma.income_report.create({
      data: incomeReport,
    })
  }
  async update(incomeReport: IncomeReport): Promise<void> {
    await prisma.income_report.update({
      where: { id: incomeReport.id },
      data: {
        investor_id: incomeReport.investor_id,
        reference_year: incomeReport.reference_year,
        status: incomeReport.status,
        file_id: incomeReport.file_id,
      },
    })
  }

  async connectionContractToIncomeReport(
    report: IncomeReport,
    contracts: Contract[]
  ): Promise<void> {
    await Promise.all(
      contracts.map(contract =>
        prisma.income_reports_contracts.create({
          data: {
            contract_id: contract.id,
            income_report_id: report.id,
          },
        })
      )
    )
  }
}
