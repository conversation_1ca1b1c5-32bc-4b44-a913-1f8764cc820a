"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSingleBrokerWithActiveContractsService = void 0;
const cache_manager_1 = require("@nestjs/cache-manager");
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contract_entity_1 = require("../../../shared/database/typeorm/entities/contract.entity");
const typeorm_2 = require("typeorm");
const pagination_query_1 = require("../helpers/pagination-query");
const addendum_entity_1 = require("../../../shared/database/typeorm/entities/addendum.entity");
const contract_status_enum_1 = require("../../../shared/enums/contract-status.enum");
let GetSingleBrokerWithActiveContractsService = class GetSingleBrokerWithActiveContractsService {
    constructor(contractsDb, cacheManager) {
        this.contractsDb = contractsDb;
        this.cacheManager = cacheManager;
    }
    async perform(brokerId, query) {
        const cacheKey = `active-broker-contracts-${brokerId}-${JSON.stringify(query)}`;
        const cached = await this.cacheManager.get(cacheKey);
        if (cached)
            return cached;
        const { page, limit, skip } = pagination_query_1.PaginatedQueryHelper.getPaginationParams(query);
        const filters = {
            endContract: (0, typeorm_2.MoreThanOrEqual)(new Date()),
            status: contract_status_enum_1.ContractStatusEnum.ACTIVE,
        };
        if (query.dateTo || query.dateFrom) {
            const { start, end } = pagination_query_1.PaginatedQueryHelper.getDateRangeParams(query);
            filters.startContract = (0, typeorm_2.Between)(start, end);
        }
        const totalQuery = this.contractsDb
            .createQueryBuilder('contract')
            .innerJoin('contract.ownerRoleRelation', 'orr')
            .innerJoin('contract.investor', 'investor')
            .leftJoin('investor.owner', 'investorowner')
            .leftJoin('investor.business', 'investorbusiness')
            .leftJoin('contract.signataries', 'signataries')
            .leftJoinAndSelect('contract.addendum', 'addendum', 'addendum.status = :status', { status: addendum_entity_1.AddendumStatus.FULLY_SIGNED })
            .select('COUNT(DISTINCT contract.id)', 'count')
            .where('orr.id = :brokerId', { brokerId })
            .andWhere(filters);
        if (query.contractType) {
            totalQuery.andWhere('signataries.investmentModality = :contractType', {
                contractType: query.contractType,
            });
        }
        const { count } = await totalQuery.getRawOne();
        const total = Number(count);
        const activeBrokerContractsQuery = await this.contractsDb
            .createQueryBuilder('contract')
            .innerJoin('contract.ownerRoleRelation', 'orr')
            .innerJoin('contract.investor', 'investor')
            .leftJoin('investor.owner', 'investorowner')
            .leftJoin('investor.business', 'investorbusiness')
            .leftJoin('contract.signataries', 'signataries')
            .leftJoinAndSelect('contract.addendum', 'addendum', 'addendum.status = :status', { status: addendum_entity_1.AddendumStatus.FULLY_SIGNED })
            .select([
            'orr.id as brokerid',
            'investor.id as investorid',
            'COALESCE(investorowner.name, investorbusiness.companyName) as name',
            'COALESCE(investorowner.avatar, investorbusiness.avatar) as avatar',
            'COALESCE(investorowner.cpf , investorbusiness.cnpj) as document',
            'contract.createdAt as createdat',
            'SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0)) as totalcontractamount',
        ])
            .where('orr.id = :brokerId', { brokerId })
            .andWhere(filters);
        if (query.contractType) {
            activeBrokerContractsQuery.andWhere('signataries.investmentModality = :contractType', {
                contractType: query.contractType,
            });
        }
        const activeBrokerContractsQueryResult = await activeBrokerContractsQuery
            .groupBy('orr.id, investor.id, investorowner.name, investorbusiness.companyName, investorowner.avatar, investorbusiness.avatar, investorowner.cpf, investorbusiness.cnpj, contract.createdAt')
            .orderBy('createdat', 'DESC')
            .offset(skip)
            .limit(limit)
            .getRawMany();
        const totalCapturedQuery = this.contractsDb
            .createQueryBuilder('contract')
            .innerJoin('contract.ownerRoleRelation', 'orr')
            .leftJoin('contract.signataries', 'signataries')
            .leftJoin('contract.addendum', 'addendum', 'addendum.status = :status', {
            status: addendum_entity_1.AddendumStatus.FULLY_SIGNED,
        })
            .select('SUM(COALESCE(signataries.investmentValue, 0)) + SUM(COALESCE(addendum.value, 0))', 'total')
            .where('orr.id = :brokerId', { brokerId })
            .andWhere(filters);
        if (query.contractType) {
            totalCapturedQuery.andWhere('signataries.investmentModality = :contractType', {
                contractType: query.contractType,
            });
        }
        const { total: totalCaptured } = await totalCapturedQuery.getRawOne();
        const result = activeBrokerContractsQueryResult.map((broker) => ({
            investorId: broker.investorid,
            brokerId: broker.brokerid,
            name: broker.name,
            avatar: broker.avatar,
            document: broker.document,
            createdAt: broker.createdat,
            totalContractAmount: Number(broker.totalcontractamount),
            totalCaptured: Number(totalCaptured),
        }));
        const response = pagination_query_1.PaginatedQueryHelper.createPaginatedResponse(result, total, page, limit);
        await this.cacheManager.set(cacheKey, response, 60000);
        return response;
    }
};
exports.GetSingleBrokerWithActiveContractsService = GetSingleBrokerWithActiveContractsService;
exports.GetSingleBrokerWithActiveContractsService = GetSingleBrokerWithActiveContractsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __param(1, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [typeorm_2.Repository, Object])
], GetSingleBrokerWithActiveContractsService);
//# sourceMappingURL=get-single-broker-with-active-contracts.service.js.map