import { BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { AddressEntity } from 'src/shared/database/typeorm/entities/address.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { RoleEntity } from 'src/shared/database/typeorm/entities/role.entity';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { generatePassword } from 'src/shared/functions/generate-password';
import { Equal, Repository } from 'typeorm';

import { CreateAdminAccountDto } from '../dto/create-admin-account.dto';

export class CreateAdminAccountService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(OwnerEntity)
    private ownerDb: Repository<OwnerEntity>,
    @InjectRepository(AddressEntity)
    private addressDb: Repository<AddressEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private ownerRoleDb: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(RoleEntity)
    private roleDb: Repository<RoleEntity>,
  ) {}

  async perform(data: CreateAdminAccountDto) {
    const password = data.password?.trim() || generatePassword();
    const passwordEncrypted = await bcrypt.hash(password, 10);

    const accountExists = await this.accountDb.findOne({
      where: [
        {
          owner: {
            cpf: Equal(data.cpf),
          },
        },
        {
          business: {
            cnpj: Equal(data.cpf),
          },
        },
      ],
    });

    if (accountExists) {
      throw new BadRequestException('Ja existe uma conta com esse cpf');
    }

    const createdOwner = this.ownerDb.create({
      name: data.fullName.trim().toUpperCase(),
      cpf: data.cpf.trim(),
      email: data.email.toLowerCase(),
      phone: data.phoneNumber.trim(),
      motherName: data.motherName.trim().toUpperCase(),
      nickname: data.socialName.trim().toUpperCase(),
      dtBirth: data.birthDate,
      pep: String(data.pep),
      password: passwordEncrypted,
    });

    const owner = await this.ownerDb.save(createdOwner);

    await this.addressDb.save({
      owner,
      cep: data.address.cep,
      city: data.address.city,
      complement: data.address.complement,
      neighborhood: data.address.neighborhood,
      number: data.address.number,
      state: data.address.state.trim().toUpperCase(),
      street: data.address.street,
    });

    const createAccount = this.accountDb.create({
      owner,
      status: AccountStatusEnum.ACTIVE,
      type: 'physical',
      externalId: 'admin-account',
    });

    const saveAccount = await this.accountDb.save(createAccount);

    const getRole = await this.roleDb.findOne({
      where: {
        name: 'admin',
      },
    });

    const createOwnerRole = this.ownerRoleDb.create({
      ownerId: owner.id,
      roleId: getRole.id,
    });

    await this.ownerRoleDb.save(createOwnerRole);

    return {
      id: saveAccount.id,
    };
  }
}
