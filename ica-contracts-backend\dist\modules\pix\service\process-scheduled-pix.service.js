"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessScheduledPix = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const typeorm_1 = require("@nestjs/typeorm");
const date_fns_1 = require("date-fns");
const transaction_entity_1 = require("../../../shared/database/typeorm/entities/transaction.entity");
const transaction_status_enum_1 = require("../../../shared/enums/transaction-status-enum");
const typeorm_2 = require("typeorm");
let ProcessScheduledPix = class ProcessScheduledPix {
    constructor(transactionRepository) {
        this.transactionRepository = transactionRepository;
    }
    async handleCron() {
        const today = new Date();
        const transactionsCount = await this.transactionRepository.findAndCount({
            where: {
                status: transaction_status_enum_1.TransactionStatusEnum.SCHEDULED,
                transferDate: (0, typeorm_2.MoreThanOrEqual)((0, date_fns_1.startOfDay)(today)) &&
                    (0, typeorm_2.LessThanOrEqual)((0, date_fns_1.endOfDay)(today)),
            },
        });
        const limit = transactionsCount.length / 4;
        await this.transactionRepository.find({
            where: {
                status: transaction_status_enum_1.TransactionStatusEnum.SCHEDULED,
                transferDate: (0, typeorm_2.MoreThanOrEqual)((0, date_fns_1.startOfDay)(today)) &&
                    (0, typeorm_2.LessThanOrEqual)((0, date_fns_1.endOfDay)(today)),
            },
            take: transactionsCount.length > 100 ? limit : 100,
        });
    }
};
exports.ProcessScheduledPix = ProcessScheduledPix;
__decorate([
    (0, schedule_1.Cron)('0 */15 7-8 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProcessScheduledPix.prototype, "handleCron", null);
exports.ProcessScheduledPix = ProcessScheduledPix = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(transaction_entity_1.TransactionEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ProcessScheduledPix);
//# sourceMappingURL=process-scheduled-pix.service.js.map