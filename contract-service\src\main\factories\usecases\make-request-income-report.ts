import { RequestIncomeReportUseCase } from '@/contexts/income-report/application/usecases/request-income-report/request-income-report.usecase'
import { QueueNames } from '@/main/config/queue-names'
import { createQueueAdapter } from '../bullmq'

export function makeRequestIncomeReportUseCase() {
  const queueGateway = createQueueAdapter(QueueNames.INVESTOR_REPORT)

  const useCase = new RequestIncomeReportUseCase(queueGateway)

  return useCase
}
