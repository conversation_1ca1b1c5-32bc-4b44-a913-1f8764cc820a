import { PaginationMeta } from '../../helpers/pagination-meta.dto';
import { IPaginatedResult } from '../../helpers/pagination-query';
export declare class BrokerInfoResponse {
    brokerId: string;
    rank: number;
    name: string;
    avatar: string;
    totalValue: number;
}
export declare class GetAllBrokersWithActiveContractsResponseDto implements IPaginatedResult<BrokerInfoResponse> {
    data: BrokerInfoResponse[];
    meta: PaginationMeta;
}
