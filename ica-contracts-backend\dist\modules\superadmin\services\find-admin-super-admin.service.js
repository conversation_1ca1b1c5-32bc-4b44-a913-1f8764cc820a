"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetAdminSuperAdminService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const admin_entity_1 = require("../../../shared/database/typeorm/entities/admin.entity");
const typeorm_2 = require("typeorm");
let GetAdminSuperAdminService = class GetAdminSuperAdminService {
    constructor(adminRepository) {
        this.adminRepository = adminRepository;
    }
    async perform(input) {
        const admin = await this.adminRepository.findOne({
            where: { ownerId: input.ownerId },
            relations: { broker: { owner: true } },
        });
        if (!admin) {
            throw new common_1.NotFoundException(`Admin with ID ${input.ownerId} not found.`);
        }
        const response = {
            admin: {
                adminId: admin.id,
                ownerId: admin.ownerId,
                broker: [],
            },
        };
        for (const broker of admin.broker) {
            const brokerObj = {
                brokerId: broker.id,
                ownerId: broker.ownerId,
                name: broker.owner?.name || null,
                cpf: broker.owner?.cpf || null,
                phone: broker.owner?.phone || null,
                email: broker.owner?.email || null,
                createdAt: broker.owner?.createdAt || null,
            };
            response.admin.broker.push(brokerObj);
        }
        return response;
    }
};
exports.GetAdminSuperAdminService = GetAdminSuperAdminService;
exports.GetAdminSuperAdminService = GetAdminSuperAdminService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(admin_entity_1.AdminEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], GetAdminSuperAdminService);
//# sourceMappingURL=find-admin-super-admin.service.js.map