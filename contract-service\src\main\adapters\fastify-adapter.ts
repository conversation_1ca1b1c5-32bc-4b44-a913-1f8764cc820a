import type { HttpRequest, IController } from '@/presentation/http/protocols'
import type { MultipartFile } from '@fastify/multipart'
import type { FastifyReply, FastifyRequest } from 'fastify'

const isMultipartFile = (value: any): value is MultipartFile => {
  return (
    value &&
    typeof value === 'object' &&
    'filename' in value &&
    'mimetype' in value &&
    'toBuffer' in value
  )
}

const parseFormData = (fields: Record<string, any>): Record<string, any> => {
  const result: Record<string, any> = {}

  for (const [key, value] of Object.entries(fields)) {
    if (key.includes('[')) {
      // Processa campos aninhados
      const path = key.replace(/\]/g, '').split('[')
      let current = result

      for (let i = 0; i < path.length; i++) {
        const part = path[i]
        const isLast = i === path.length - 1

        if (isLast) {
          current[part] = value.value
        } else {
          if (!current[part]) {
            // Se for um índice numérico, cria um array
            current[part] = /^\d+$/.test(path[i + 1]) ? [] : {}
          }
          current = current[part]
        }
      }
    } else {
      // Campos simples
      result[key] = value.value
    }
  }

  return result
}

export const fastifyRouteAdapter = (controller: IController) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const rawBody = request.body as Record<string, any>

    // Separa arquivos e campos
    let body: Record<string, any>

    if (request.headers['content-type']?.includes('multipart/form-data')) {
      const files: Record<string, any> = {}
      const fields: Record<string, any> = {}

      for (const [key, value] of Object.entries(rawBody)) {
        if (isMultipartFile(value)) {
          files[key] = {
            filename: value.filename,
            mimetype: value.mimetype,
            buffer: await value.toBuffer(),
          }
        } else {
          fields[key] = value
        }
      }

      const parsedBody = parseFormData(fields)
      body = {
        ...parsedBody,
        ...files,
      }
    } else {
      body = rawBody
    }

    const httpRequest: HttpRequest = {
      body,
      params: request.params as Record<string, string>,
      query: request.query as Record<string, string>,
      headers: request.headers as Record<string, string>,
    }

    const httpResponse = await controller.handle(httpRequest)
    reply.status(httpResponse.statusCode).send(httpResponse.body)
  }
}
