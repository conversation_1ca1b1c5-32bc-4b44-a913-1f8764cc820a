{"version": 3, "file": "create-pix-qrcode-dynamic.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/create-pix-qrcode-dynamic.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yEAA0D;AAC1D,yDAAyC;AACzC,qDAQyB;AAEzB,MAAM,QAAQ;CAgBb;AAbC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;4CACQ;AAInB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sDACkB;AAI7B;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAGf,MAAM,KAAK;CAcV;AAXC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mCACG;AAKd;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,kCAAM,GAAE;;mCACK;AAKd;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,iCAAK,GAAE;;kCACK;AAGf,MAAa,yBAAyB;CA6BrC;AA7BD,8DA6BC;AAzBC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;4DACS;AAIlB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sDACC;AAIZ;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,gCAAc,GAAE;;yDACF;AAMf;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;8BACX,QAAQ;2DAAC;AAMnB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC;8BACX,KAAK;wDAAC;AAIb;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACG", "sourcesContent": ["import { Is<PERSON>NPJ, <PERSON><PERSON><PERSON> } from 'brazilian-class-validator';\r\nimport { Type } from 'class-transformer';\r\nimport {\r\n  IsDefined,\r\n  IsNumberString,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  IsUUID,\r\n  ValidateNested,\r\n} from 'class-validator';\r\n\r\nclass Merchant {\r\n  @IsDefined()\r\n  @IsString()\r\n  postalCode: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  city: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  merchantCategoryCode: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n}\r\n\r\nclass Payer {\r\n  @IsOptional()\r\n  @IsString()\r\n  name?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  @IsCNPJ()\r\n  cnpj?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  @IsCPF()\r\n  cpf?: string;\r\n}\r\n\r\nexport class CreatePixQRCodeDynamicDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsUUID()\r\n  accountId: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  key: string;\r\n\r\n  @IsDefined()\r\n  @IsNumberString()\r\n  amount: string;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Merchant)\r\n  merchant: Merchant;\r\n\r\n  @IsOptional()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Payer)\r\n  payer: Payer;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  info?: string;\r\n}\r\n"]}