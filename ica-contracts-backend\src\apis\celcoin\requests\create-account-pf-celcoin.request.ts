export interface ICreateAccountPFCelcoinRequest {
  clientCode: string;
  accountOnboardingType: string;
  documentNumber: string;
  phoneNumber: string;
  email: string;
  motherName: string;
  fullName: string;
  socialName: string;
  birthDate: string;
  isPoliticallyExposedPerson: boolean;
  address: {
    postalCode: string;
    street: string;
    number?: string;
    addressComplement: string;
    neighborhood: string;
    city: string;
    state: string;
    longitude?: string;
    latitude?: string;
  };
}
