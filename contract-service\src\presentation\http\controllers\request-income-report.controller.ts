import type { RequestIncomeReportUseCase } from '@/application/usecases';
import { badRequest, ok, serverError } from '../helpers/http-helper';
import type { HttpRequest, IController } from '../protocols';

export class RequestIncomeReportController implements IController {
  constructor(private useCase: RequestIncomeReportUseCase) {}

  async handle(request: HttpRequest<{ investorIds: string[]; year: number }>) {
    if (!request.body) {
      return badRequest(new Error('Body não informado'));
    }

    if (!request.body?.investorIds) {
      return badRequest(new Error('Investidores não informado'));
    }

    const currencyYear = new Date().getFullYear();
    if (request.body.year >= currencyYear) {
      return badRequest(
        new Error('O ano informado deve ser menor que o ano atual.'),
      );
    }

    const response = await this.useCase.execute(
      request.body?.year ?? 2024,
      request.body?.investorIds ?? [],
    );

    if (response.isLeft()) {
      return serverError(response.value);
    }

    return ok(response.value);
  }
}
