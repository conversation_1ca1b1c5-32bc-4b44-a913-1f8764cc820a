import { InvestmentContract } from '../entities/contracts';
import type { IncomeReport } from '../entities/reports';
import { Either } from '../shared';

export interface IIncomeReportRepository {
  save(report: IncomeReport): Promise<void>;
  update(report: IncomeReport): Promise<void>;
  findByInvestorAndYear(
    investorId: string,
    year: number,
  ): Promise<IncomeReport | null>;
  connectionContractToIncomeReport(report: IncomeReport): Promise<void>;
  findDetailsIncomeReport(
    investorId: string,
    year: number,
  ): Promise<Either<Error, InvestmentContract[]>>;
}
