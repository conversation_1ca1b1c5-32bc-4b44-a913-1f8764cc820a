import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { FavoritePixEntity } from 'src/shared/database/typeorm/entities/favorite-pix.entity';
import { Repository } from 'typeorm';
import { IGetFavoritePixAccountResponse } from '../response/get-favorite-pix-account.response';
export declare class GetFavoritePixAccountService {
    private accountDb;
    private favoritePixDb;
    constructor(accountDb: Repository<AccountEntity>, favoritePixDb: Repository<FavoritePixEntity>);
    perform(id: string): Promise<IGetFavoritePixAccountResponse[]>;
}
