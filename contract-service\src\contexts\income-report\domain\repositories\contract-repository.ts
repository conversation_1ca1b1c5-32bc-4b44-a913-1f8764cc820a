import type { Contract } from '../entities/contract';
import { IncomeReportEmailPayload } from '../entities/email-income-report';
import type { IncomeReport } from '../entities/income-report';

export interface ContractRepository {
  findByInvestorId(investorId: string): Promise<Contract[]>;
  findByIncomeReportId(
    investorId: string,
    year: number,
  ): Promise<IncomeReport | null>;
  findIncomeReportEmail(
    investorId: string,
    year: number,
  ): Promise<IncomeReportEmailPayload | null>;
}
