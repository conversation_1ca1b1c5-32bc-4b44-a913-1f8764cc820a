"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VinculateAdvisorBrokerWalletService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const owner_role_relation_entity_1 = require("../../../shared/database/typeorm/entities/owner-role-relation.entity");
const wallets_views_entity_1 = require("../../../shared/database/typeorm/entities/wallets-views.entity");
const roles_enum_1 = require("../../../shared/enums/roles.enum");
const typeorm_2 = require("typeorm");
let VinculateAdvisorBrokerWalletService = class VinculateAdvisorBrokerWalletService {
    constructor(walletDb, perfilDb) {
        this.walletDb = walletDb;
        this.perfilDb = perfilDb;
    }
    async perform(data) {
        const advisor = await this.perfilDb.findOne({
            where: {
                id: (0, typeorm_2.Equal)(data.advisorId),
                role: {
                    name: roles_enum_1.RolesEnum.ADVISOR,
                },
            },
            relations: {
                role: true,
            },
        });
        if (!advisor)
            throw new common_1.BadRequestException('Assessor nao encontrado');
        const broker = await this.perfilDb.findOne({
            where: {
                id: (0, typeorm_2.Equal)(data.brokerId),
                role: {
                    name: roles_enum_1.RolesEnum.BROKER,
                },
            },
            relations: {
                role: true,
            },
        });
        if (!broker)
            throw new common_1.BadRequestException('Broker nao encontrado');
        const wallet = await this.walletDb.findOne({
            where: {
                bottomId: (0, typeorm_2.Equal)(data.advisorId),
            },
        });
        if (wallet)
            throw new common_1.BadRequestException('Assessor ja está vinculado a um Broker');
        const createVinc = this.walletDb.create({
            upperId: data.brokerId,
            bottomId: data.advisorId,
        });
        await this.walletDb.save(createVinc);
    }
};
exports.VinculateAdvisorBrokerWalletService = VinculateAdvisorBrokerWalletService;
exports.VinculateAdvisorBrokerWalletService = VinculateAdvisorBrokerWalletService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(wallets_views_entity_1.WalletsViewsEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_role_relation_entity_1.OwnerRoleRelationEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], VinculateAdvisorBrokerWalletService);
//# sourceMappingURL=vinculate-advisor-broker-wallet.service.js.map