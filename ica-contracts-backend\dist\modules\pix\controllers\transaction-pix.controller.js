"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionPixController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../../shared/guards/jwt-auth.guard");
const get_internal_pix_transaction_dto_1 = require("../dto/get-internal-pix-transaction.dto");
const get_transaction_status_dto_1 = require("../dto/get-transaction-status.dto");
const internal_transaction_dto_1 = require("../dto/internal-transaction.dto");
const reversal_pix_dto_1 = require("../dto/reversal-pix.dto");
const transaction_pix_key_dto_1 = require("../dto/transaction-pix-key.dto");
const transaction_pix_manual_dto_1 = require("../dto/transaction-pix-manual.dto");
const get_internal_pix_transaction_service_1 = require("../service/get-internal-pix-transaction.service");
const internal_transaction_service_1 = require("../service/internal-transaction.service");
const reversal_pix_service_1 = require("../service/reversal-pix.service");
const transaction_pix_key_service_1 = require("../service/transaction-pix-key.service");
const transaction_pix_manual_service_1 = require("../service/transaction-pix-manual.service");
const transaction_pix_status_service_1 = require("../service/transaction-pix-status.service");
let TransactionPixController = class TransactionPixController {
    constructor(transactionPixKeyService, transactionPixManualService, transactionPixStatusService, getInternalPixTransactionService, internalTransactionService, reversalPixService) {
        this.transactionPixKeyService = transactionPixKeyService;
        this.transactionPixManualService = transactionPixManualService;
        this.transactionPixStatusService = transactionPixStatusService;
        this.getInternalPixTransactionService = getInternalPixTransactionService;
        this.internalTransactionService = internalTransactionService;
        this.reversalPixService = reversalPixService;
    }
    async transactionPixKey(request, body, twoFactorToken) {
        try {
            const data = await this.transactionPixKeyService.perform(body, request.user.id, false, twoFactorToken);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async transactionPixManual(request, body, twoFactorToken) {
        try {
            const data = await this.transactionPixManualService.perform(body, request.user.id, twoFactorToken);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async transactionStatus(request, query) {
        try {
            const data = await this.transactionPixStatusService.execute(query, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async getInternalPixTransaction(request, query) {
        try {
            const data = await this.getInternalPixTransactionService.execute(query, request.user.id);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async internalTransaction(request, data) {
        try {
            const ownerId = request.user.id;
            return this.internalTransactionService.perform(data, ownerId);
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
    async reversalPix(body) {
        try {
            const data = await this.reversalPixService.perform(body);
            return data;
        }
        catch (error) {
            throw new common_1.HttpException({ error: error.response || error.message }, error.status);
        }
    }
};
exports.TransactionPixController = TransactionPixController;
__decorate([
    (0, common_1.Post)('key'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('X-OTP-Token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, transaction_pix_key_dto_1.TransactionPixKeyDto, String]),
    __metadata("design:returntype", Promise)
], TransactionPixController.prototype, "transactionPixKey", null);
__decorate([
    (0, common_1.Post)('manual'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Headers)('X-OTP-Token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, transaction_pix_manual_dto_1.TransactionPixManualDto, String]),
    __metadata("design:returntype", Promise)
], TransactionPixController.prototype, "transactionPixManual", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, get_transaction_status_dto_1.TransactionStatusDto]),
    __metadata("design:returntype", Promise)
], TransactionPixController.prototype, "transactionStatus", null);
__decorate([
    (0, common_1.Get)('internal'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, get_internal_pix_transaction_dto_1.GetInternalPixTransactionlDto]),
    __metadata("design:returntype", Promise)
], TransactionPixController.prototype, "getInternalPixTransaction", null);
__decorate([
    (0, common_1.Post)('internal'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, internal_transaction_dto_1.InternalTransactionDto]),
    __metadata("design:returntype", Promise)
], TransactionPixController.prototype, "internalTransaction", null);
__decorate([
    (0, common_1.Post)('reversal'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [reversal_pix_dto_1.ReversalPixDto]),
    __metadata("design:returntype", Promise)
], TransactionPixController.prototype, "reversalPix", null);
exports.TransactionPixController = TransactionPixController = __decorate([
    (0, common_1.Controller)('pix/transaction'),
    __param(0, (0, common_1.Inject)(transaction_pix_key_service_1.TransactionPixKeyService)),
    __param(1, (0, common_1.Inject)(transaction_pix_manual_service_1.TransactionPixManualService)),
    __param(2, (0, common_1.Inject)(transaction_pix_status_service_1.TransactionPixStatusService)),
    __param(3, (0, common_1.Inject)(get_internal_pix_transaction_service_1.GetInternalPixTransactionService)),
    __param(4, (0, common_1.Inject)(internal_transaction_service_1.InternalTransactionService)),
    __param(5, (0, common_1.Inject)(reversal_pix_service_1.ReversalPixService)),
    __metadata("design:paramtypes", [transaction_pix_key_service_1.TransactionPixKeyService,
        transaction_pix_manual_service_1.TransactionPixManualService,
        transaction_pix_status_service_1.TransactionPixStatusService,
        get_internal_pix_transaction_service_1.GetInternalPixTransactionService,
        internal_transaction_service_1.InternalTransactionService,
        reversal_pix_service_1.ReversalPixService])
], TransactionPixController);
//# sourceMappingURL=transaction-pix.controller.js.map