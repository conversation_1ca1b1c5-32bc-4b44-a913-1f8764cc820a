"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContractLifecycleMonitoringModule = void 0;
const common_1 = require("@nestjs/common");
const shared_module_1 = require("../../shared/shared.module");
const contract_lifecycle_monitoring_controller_1 = require("./controller/contract-lifecycle-monitoring.controller");
const dashboard_service_1 = require("./services/dashboard.service");
const finalize_contract_event_service_1 = require("./services/finalize-contract-event.service");
const find_expiring_contracts_service_1 = require("./services/find-expiring-contracts.service");
const get_contracts_for_retention_service_1 = require("./services/get-contracts-for-retention.service");
const send_contract_to_retention_service_1 = require("./services/send-contract-to-retention.service");
const update_contract_from_retention_service_1 = require("./services/update-contract-from-retention.service");
let ContractLifecycleMonitoringModule = class ContractLifecycleMonitoringModule {
};
exports.ContractLifecycleMonitoringModule = ContractLifecycleMonitoringModule;
exports.ContractLifecycleMonitoringModule = ContractLifecycleMonitoringModule = __decorate([
    (0, common_1.Module)({
        imports: [shared_module_1.SharedModule],
        controllers: [contract_lifecycle_monitoring_controller_1.ContractLifecycleMonitoringController],
        providers: [
            find_expiring_contracts_service_1.FindExpiringContractsService,
            send_contract_to_retention_service_1.SendContractToRetentionService,
            get_contracts_for_retention_service_1.GetContractsForRetentionService,
            update_contract_from_retention_service_1.UpdateContractFromRetentionService,
            finalize_contract_event_service_1.FinalizeContractEventService,
            dashboard_service_1.DashboardService,
        ],
    })
], ContractLifecycleMonitoringModule);
//# sourceMappingURL=contract-lifecycle-monitoring.module.js.map