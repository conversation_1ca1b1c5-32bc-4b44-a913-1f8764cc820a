import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { TransactionStatusDto } from '../dto/get-transaction-status.dto';
export declare class TransactionPixStatusService {
    private accountDb;
    private transactionRepository;
    private celcoinService;
    constructor(accountDb: Repository<AccountEntity>, transactionRepository: Repository<TransactionEntity>, celcoinService: PixTransactionCelcoinService);
    execute(data: TransactionStatusDto, id: string): Promise<any>;
}
