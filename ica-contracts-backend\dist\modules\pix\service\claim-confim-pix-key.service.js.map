{"version": 3, "file": "claim-confim-pix-key.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/claim-confim-pix-key.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,6CAAmD;AACnD,gGAAsF;AACtF,6FAAmF;AACnF,+EAAqE;AACrE,+EAAqE;AACrE,mFAAwE;AACxE,qCAAqC;AAIrC,IAAa,yBAAyB,GAAtC,MAAa,yBAAyB;IACpC,YAEU,gBAA0C,EAE1C,iBAAsC;QAFtC,qBAAgB,GAAhB,gBAAgB,CAA0B;QAE1C,sBAAiB,GAAjB,iBAAiB,CAAqB;IAC7C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAsB,EAAE,OAAe;QACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE;gBACL;oBACE,GAAG,EAAE,KAAK,CAAC,GAAG;oBACd,MAAM,EAAE,sCAAgB,CAAC,OAAO;oBAChC,WAAW,EAAE,mCAAe,CAAC,IAAI;oBACjC,OAAO,EAAE,EAAE,OAAO,EAAE;iBACrB;gBACD;oBACE,GAAG,EAAE,KAAK,CAAC,GAAG;oBACd,MAAM,EAAE,sCAAgB,CAAC,OAAO;oBAChC,WAAW,EAAE,mCAAe,CAAC,IAAI;oBACjC,OAAO,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;iBACjC;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,0CAA0C,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAErD,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;YACxC,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,mCAAe,CAAC,iBAAiB;SAC1D,CAAC,CAAC;QAEH,MAAM,CAAC,WAAW,GAAG,mCAAe,CAAC,SAAS,CAAC;QAC/C,MAAM,CAAC,MAAM,GAAG,sCAAgB,CAAC,SAAS,CAAC;QAE3C,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,EAAE,MAAM,EAAE,uCAAuC,EAAE,CAAC;IAC7D,CAAC;CACF,CAAA;AA5CY,8DAAyB;oCAAzB,yBAAyB;IAEjC,WAAA,IAAA,0BAAgB,EAAC,6BAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,eAAM,EAAC,2CAAmB,CAAC,CAAA;qCADF,oBAAU;QAET,2CAAmB;GALrC,yBAAyB,CA4CrC", "sourcesContent": ["import { Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { ClaimCelcoinService } from 'src/apis/celcoin/services/claim-celcoin.service';\r\nimport { PixKeyEntity } from 'src/shared/database/typeorm/entities/pix-key.entity';\r\nimport { ClaimReasonEnum } from 'src/shared/enums/claim-reason.enum';\r\nimport { ClaimStatusEnum } from 'src/shared/enums/claim-status.enum';\r\nimport { PixKeyStatusEnum } from 'src/shared/enums/pix-key-status.enum';\r\nimport { Repository } from 'typeorm';\r\n\r\nimport { ClaimConfirmDto } from '../dto/claim-confirm.dto';\r\n\r\nexport class ClaimConfirmPixKeyService {\r\n  constructor(\r\n    @InjectRepository(PixKeyEntity)\r\n    private pixKeyRepository: Repository<PixKeyEntity>,\r\n    @Inject(ClaimCelcoinService)\r\n    private pixCelcoinService: ClaimCelcoinService,\r\n  ) {}\r\n\r\n  async execute(input: ClaimConfirmDto, ownerId: string): Promise<any> {\r\n    const pixkey = await this.pixKeyRepository.findOne({\r\n      where: [\r\n        {\r\n          key: input.key,\r\n          status: PixKeyStatusEnum.PENDENT,\r\n          claimStatus: ClaimStatusEnum.OPEN,\r\n          account: { ownerId },\r\n        },\r\n        {\r\n          key: input.key,\r\n          status: PixKeyStatusEnum.PENDENT,\r\n          claimStatus: ClaimStatusEnum.OPEN,\r\n          account: { businessId: ownerId },\r\n        },\r\n      ],\r\n    });\r\n\r\n    if (!pixkey) {\r\n      throw new NotFoundException('Chave pix não encontrada para sua conta.');\r\n    }\r\n\r\n    const claimMetada = JSON.parse(pixkey.claimMetadata);\r\n\r\n    await this.pixCelcoinService.claimConfirm({\r\n      claimId: claimMetada.claimId,\r\n      reason: input.reason || ClaimReasonEnum.DEFAULT_OPERATION,\r\n    });\r\n\r\n    pixkey.claimStatus = ClaimStatusEnum.CONFIRMED;\r\n    pixkey.status = PixKeyStatusEnum.COMPLETED;\r\n\r\n    await this.pixKeyRepository.save(pixkey);\r\n\r\n    return { status: 'Reivindicação confirmada com sucesso.' };\r\n  }\r\n}\r\n"]}