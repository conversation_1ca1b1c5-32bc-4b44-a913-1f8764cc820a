{"version": 3, "file": "consult-celcoin-webhook.service.js", "sourceRoot": "/", "sources": ["modules/webhook/services/consult-celcoin-webhook.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,oHAAyG;AAKlG,IAAM,4BAA4B,GAAlC,MAAM,4BAA4B;IACvC,YACmB,4BAA0D;QAA1D,iCAA4B,GAA5B,4BAA4B,CAA8B;IAC1E,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAA+B;QAC3C,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1D,CAAC;CACF,CAAA;AARY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;qCAGsC,8DAA4B;GAFlE,4BAA4B,CAQxC", "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { GetRegisteredWebhooksService } from 'src/apis/celcoin/services/get-registered-webhooks.service';\r\n\r\nimport { ConsultCelcoinWebhookDto } from '../dto/consult-celcoin-webhooks.dto';\r\n\r\n@Injectable()\r\nexport class ConsultCelcoinWebhookService {\r\n  constructor(\r\n    private readonly getRegisteredWebhooksService: GetRegisteredWebhooksService,\r\n  ) {}\r\n\r\n  async perform(input: ConsultCelcoinWebhookDto) {\r\n    return this.getRegisteredWebhooksService.perform(input);\r\n  }\r\n}\r\n"]}