{"version": 3, "file": "get-pix-key-external.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/get-pix-key-external.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAKwB;AACxB,6CAAmD;AACnD,oHAAyG;AACzG,6FAAoF;AACpF,+EAAqE;AACrE,uEAA6D;AAC7D,mDAA2C;AAC3C,qCAA4C;AAMrC,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAGnC,YAEE,SAA4C,EAE5C,cAAoD;QAF5C,cAAS,GAAT,SAAS,CAA2B;QAEpC,mBAAc,GAAd,cAAc,CAA8B;QAN9C,oBAAe,GAAgC,IAAI,GAAG,EAAE,CAAC;IAO9D,CAAC;IAEJ,KAAK,CAAC,OAAO,CACX,IAA0B,EAC1B,EAAU;QAEV,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAEnE,MAAM,UAAU,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QAEnD,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,sBAAa,CACrB;gBACE,OAAO,EAAE,oCAAoC;gBAC7C,KAAK,EAAE,mBAAmB;gBAC1B,UAAU,EAAE,GAAG;aAChB,EACD,GAAG,CACJ,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE3C,OAAO,IAAI,OAAO,CAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjE,IAAI,gBAAgB,GAAG,KAAK,CAAC;YAE7B,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;gBACxC,IAAI,gBAAgB,EAAE,CAAC;oBACrB,OAAO;gBACT,CAAC;gBAED,gBAAgB,GAAG,IAAI,CAAC;gBAExB,IAAI,CAAC;oBACH,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GACpB,MAAM,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC;wBACjD,aAAa,EAAE,OAAO,CAAC,MAAM;wBAC7B,GAAG,EAAE,IAAI,CAAC,GAAG;qBACd,CAAC,CAAC;oBAEL,MAAM,QAAQ,GAA+B;wBAC3C,UAAU,EAAE,MAAM,CAAC,UAAU;wBAC7B,GAAG,EAAE,MAAM,CAAC,GAAG;wBACf,OAAO,EAAE,2BAAW,CAAC,MAAM,CAAC,OAAO,CAAC;wBACpC,OAAO,EAAE;4BACP,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;4BAC7B,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;4BAC9B,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,WAAW;4BAChC,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,WAAW;yBACjC;wBACD,KAAK,EAAE;4BACL,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,cAAc;4BACrC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;4BACvB,IAAI,EAAE,mCAAe,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;yBACzC;qBACF,CAAC;oBAEF,eAAM,CAAC,IAAI,CACT,yBAAyB,OAAO,CAAC,EAAE,qBAAqB,MAAM,CAAC,GAAG,EAAE,CACrE,CAAC;oBACF,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC1B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBACxC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACpB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC1B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBACxC,MAAM,CAAC,IAAI,sBAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBACjD,CAAC;wBAAS,CAAC;oBACT,gBAAgB,GAAG,KAAK,CAAC;gBAC3B,CAAC;YACH,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,sBAAsB,CAAC,IAA0B,EAAE,aAAqB;QACtE,MAAM,UAAU,GAAG,GAAG,aAAa,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QAClD,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxD,aAAa,CAAC,UAAU,CAAC,CAAC;YAC1B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;CACF,CAAA;AAxGY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;qCADlB,oBAAU;QAEL,8DAA4B;GAP3C,wBAAwB,CAwGpC", "sourcesContent": ["import {\r\n  Inject,\r\n  Injectable,\r\n  NotFoundException,\r\n  HttpException,\r\n} from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { AccountTypeEnum } from 'src/shared/enums/account-type.enum';\r\nimport { KeyTypeEnum } from 'src/shared/enums/key-type.enum';\r\nimport { logger } from 'src/shared/logger';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { GetPixKeyExternalDto } from '../dto/get-pix-key-external.dto';\r\nimport { IGetPixKeyExternalResponse } from '../response/get-pix-key-external-response';\r\n\r\n@Injectable()\r\nexport class GetPixKeyExternalService {\r\n  private runningRequests: Map<string, NodeJS.Timeout> = new Map();\r\n\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @Inject(PixTransactionCelcoinService)\r\n    private celcoinService: PixTransactionCelcoinService,\r\n  ) {}\r\n\r\n  async perform(\r\n    data: GetPixKeyExternalDto,\r\n    id: string,\r\n  ): Promise<IGetPixKeyExternalResponse> {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    const requestKey = `${account.number}-${data.key}`;\r\n\r\n    if (this.runningRequests.has(requestKey)) {\r\n      throw new HttpException(\r\n        {\r\n          message: 'A requisição já está em andamento.',\r\n          error: 'Too Many Requests',\r\n          statusCode: 429,\r\n        },\r\n        429,\r\n      );\r\n    }\r\n\r\n    this.runningRequests.set(requestKey, null);\r\n\r\n    return new Promise<IGetPixKeyExternalResponse>((resolve, reject) => {\r\n      let isRequestPending = false;\r\n\r\n      const intervalId = setInterval(async () => {\r\n        if (isRequestPending) {\r\n          return;\r\n        }\r\n\r\n        isRequestPending = true;\r\n\r\n        try {\r\n          const { body: result } =\r\n            await this.celcoinService.getAccountExternalPixKey({\r\n              accountNumber: account.number,\r\n              key: data.key,\r\n            });\r\n\r\n          const response: IGetPixKeyExternalResponse = {\r\n            endToEndId: result.endtoEndId,\r\n            key: result.key,\r\n            keyType: KeyTypeEnum[result.keyType],\r\n            account: {\r\n              branch: result.account.branch,\r\n              number: result.account.account,\r\n              type: result.account.accountType,\r\n              bank: result.account.participant,\r\n            },\r\n            owner: {\r\n              document: result.owner.documentNumber,\r\n              name: result.owner.name,\r\n              type: AccountTypeEnum[result.owner.type],\r\n            },\r\n          };\r\n\r\n          logger.info(\r\n            `Consulta feita por id ${account.id} para a chave pix ${result.key}`,\r\n          );\r\n          clearInterval(intervalId);\r\n          this.runningRequests.delete(requestKey);\r\n          resolve(response);\r\n        } catch (error) {\r\n          clearInterval(intervalId);\r\n          this.runningRequests.delete(requestKey);\r\n          reject(new HttpException(error.response, 500));\r\n        } finally {\r\n          isRequestPending = false;\r\n        }\r\n      }, 1500);\r\n\r\n      this.runningRequests.set(requestKey, intervalId);\r\n    });\r\n  }\r\n\r\n  stopPerformingRequests(data: GetPixKeyExternalDto, accountNumber: string) {\r\n    const requestKey = `${accountNumber}-${data.key}`;\r\n    if (this.runningRequests.has(requestKey)) {\r\n      const intervalId = this.runningRequests.get(requestKey);\r\n      clearInterval(intervalId);\r\n      this.runningRequests.delete(requestKey);\r\n    }\r\n  }\r\n}\r\n"]}