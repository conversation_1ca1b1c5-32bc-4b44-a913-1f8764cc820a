import {
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  EditBrokerDto,
  EditBusinessDto,
} from 'src/modules/broker/dto/edit-broker.dto';
import { AddressEntity } from 'src/shared/database/typeorm/entities/address.entity';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { PhoneService } from 'src/shared/utils/phone/phone.service';
import { Repository, In } from 'typeorm';

@Injectable()
export class EditAdminService {
  constructor(
    @InjectRepository(OwnerRoleRelationEntity)
    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(OwnerEntity)
    private readonly ownerRepository: Repository<OwnerEntity>,
    @InjectRepository(BusinessEntity)
    private readonly businessRepository: Repository<BusinessEntity>,
    @InjectRepository(AddressEntity)
    private readonly addressRepository: Repository<AddressEntity>,
  ) {}

  async perform(
    { ownerRoleRelationId, owner, business, address }: EditBrokerDto,
    userId: string,
  ) {
    const hasPermission = await this.ownerRoleRelationRepository.findOne({
      relations: {
        role: true,
      },
      where: [
        {
          id: ownerRoleRelationId,
          ownerId: userId,
          role: {
            name: In([RolesEnum.ADMIN]),
          },
        },
        {
          businessId: userId,
          id: ownerRoleRelationId,
          role: { name: RolesEnum.ADMIN },
        },
        {
          ownerId: userId,
          role: {
            name: In([RolesEnum.SUPERADMIN]),
          },
        },
        { businessId: userId, role: { name: RolesEnum.SUPERADMIN } },
      ],
    });

    if (!hasPermission) {
      throw new ForbiddenException('Usuário não possui permissão');
    }

    const user = await this.ownerRoleRelationRepository.findOne({
      where: { id: ownerRoleRelationId },
      relations: { business: { address: true }, owner: { address: true } },
    });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    if (user.owner && owner) {
      await this.uploadOwner(owner, user.owner);
    }

    if (user.business && business) {
      await this.uploadBusiness(business, user.business);
    }

    if (address) {
      await this.uploadAddress(user.owner.id, address);
    }
  }

  private async uploadOwner(
    payload: {
      name?: string;
      document?: string;
      email?: string;
      phone?: string;
      motherName?: string;
      birthDate?: string;
      nickName?: string;
    },
    owner: OwnerEntity,
  ) {
    const [newName, newEmail, newNickname, newBirthDay, newDocument] =
      await Promise.all([
        await this.validateName(owner.id, payload.name),
        await this.validateEmail(owner.id, payload.email),
        await this.validateNickname(owner.id, payload.nickName),
        await this.validateBirthday(owner.id, payload.birthDate),
        await this.validateDocument(owner.id, payload.document),
      ]);

    await this.ownerRepository.update(owner.id, {
      name: newName,
      email: newEmail,
      nickname: newNickname,
      dtBirth: newBirthDay,
      cpf: newDocument,
      motherName: owner.motherName,
      phone: payload.phone
        ? PhoneService.formatPhoneNumber(payload.phone)
        : undefined,
    });
  }

  private async validateField(
    ownerId: string,
    newValue: string | undefined,
    fieldName: 'name' | 'email' | 'nickname' | 'cpf',
    errorMessage: { sameValue: string; duplicateValue: string },
  ): Promise<string | undefined> {
    if (!newValue) {
      return undefined;
    }

    const existingOwner = await this.ownerRepository.findOne({
      where: { id: ownerId },
      select: [fieldName], // Limita os campos retornados para otimizar a consulta
    });

    if (!existingOwner) {
      throw new NotFoundException('Proprietário não encontrado.');
    }

    if (existingOwner[fieldName] === newValue) {
      throw new UnprocessableEntityException(errorMessage.sameValue);
    }

    const valueAlreadyExists =
      (await this.ownerRepository.count({
        where: { [fieldName]: newValue },
      })) > 0;

    if (valueAlreadyExists) {
      throw new UnprocessableEntityException(errorMessage.duplicateValue);
    }

    return newValue;
  }

  private async validateBirthday(
    ownerId: string,
    newBirthday?: string,
  ): Promise<string | undefined> {
    if (!newBirthday) {
      return undefined;
    }

    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
    if (!datePattern.test(newBirthday)) {
      throw new UnprocessableEntityException(
        'O formato da data de aniversário é inválido. Use o formato YYYY-MM-DD.',
      );
    }

    const parsedDate = new Date(newBirthday);
    if (Number.isNaN(parsedDate.getTime())) {
      throw new UnprocessableEntityException(
        'A data de aniversário fornecida é inválida.',
      );
    }

    if (parsedDate >= new Date()) {
      throw new UnprocessableEntityException(
        'A data de aniversário deve ser anterior à data atual.',
      );
    }

    const existingOwner = await this.ownerRepository.findOne({
      where: { id: ownerId },
      select: ['dtBirth'], // Limita os campos retornados para otimizar a consulta
    });

    if (!existingOwner) {
      throw new NotFoundException('Usuário não encontrado.');
    }

    if (new Date(existingOwner.dtBirth).getTime() === parsedDate.getTime()) {
      throw new UnprocessableEntityException(
        'A nova data de aniversário é igual à atual.',
      );
    }

    return parsedDate.toISOString().split('T').at(0);
  }

  private async validateName(
    ownerId: string,
    newName?: string,
  ): Promise<string | undefined> {
    return this.validateField(ownerId, newName, 'name', {
      sameValue: 'O novo nome é igual ao nome atual.',
      duplicateValue: 'Já existe um usuário com esse nome.',
    });
  }

  private async validateEmail(
    ownerId: string,
    newEmail?: string,
  ): Promise<string | undefined> {
    return this.validateField(ownerId, newEmail, 'email', {
      sameValue: 'O novo e-mail é igual ao e-mail atual.',
      duplicateValue: 'Já existe um usuário com esse e-mail.',
    });
  }

  private async validateNickname(
    ownerId: string,
    newNickname?: string,
  ): Promise<string | undefined> {
    return this.validateField(ownerId, newNickname, 'nickname', {
      sameValue: 'O novo nickname é igual ao atual.',
      duplicateValue: 'Já existe um usuário com esse nickname.',
    });
  }

  private async validateDocument(
    ownerId: string,
    newDocument?: string,
  ): Promise<string | undefined> {
    return this.validateField(ownerId, newDocument?.replace(/\D/g, ''), 'cpf', {
      sameValue: 'O novo documento é igual ao atual.',
      duplicateValue: 'Já existe um usuário com esse documento.',
    });
  }

  private async validateAddress(
    ownerId: string,
    addressPayload: {
      cep: string;
      street: string;
      neighborhood: string;
      number: string;
      city: string;
      state: string;
      complement?: string;
    },
  ): Promise<AddressEntity> {
    const { cep, street, neighborhood, number, city, state, complement } =
      addressPayload;

    if (!cep || !street || !neighborhood || !number || !city || !state) {
      throw new UnprocessableEntityException(
        'Todos os campos obrigatórios do endereço devem ser preenchidos.',
      );
    }

    const cepPattern = /^\d{5}-\d{3}$/;
    if (!cepPattern.test(cep)) {
      throw new UnprocessableEntityException(
        'O formato do CEP é inválido. Use o formato 00000-000.',
      );
    }

    const existingAddress = await this.ownerRepository.findOne({
      where: { id: ownerId },
      relations: ['address'],
    });

    if (!existingAddress) {
      throw new NotFoundException('Usuário não encontrado.');
    }

    const updatedAddress = new AddressEntity();
    updatedAddress.ownerId = ownerId;
    updatedAddress.cep = cep;
    updatedAddress.street = street;
    updatedAddress.neighborhood = neighborhood;
    updatedAddress.number = number;
    updatedAddress.city = city;
    updatedAddress.state = state;
    updatedAddress.complement = complement;

    return updatedAddress;
  }

  private async uploadAddress(
    ownerId: string,
    addressPayload: {
      cep: string;
      street: string;
      neighborhood: string;
      number: string;
      city: string;
      state: string;
      complement?: string;
    },
  ): Promise<void> {
    const validatedAddress = await this.validateAddress(
      ownerId,
      addressPayload,
    );

    const existingAddress = await this.ownerRepository.findOne({
      where: { id: ownerId },
      relations: { address: true },
    });

    if (existingAddress.address.length > 0) {
      await this.addressRepository.update({ ownerId }, validatedAddress);
    } else {
      validatedAddress.ownerId = ownerId;
      await this.addressRepository.save(validatedAddress);
    }
  }

  private async uploadBusiness(
    payload: EditBusinessDto,
    business: BusinessEntity,
  ) {
    const [
      newCompanyName,
      newFantasyName,
      newCnpj,
      newEmail,
      newType,
      newSize,
      newDtOpening,
      // Add more fields as necessary
    ] = await Promise.all([
      this.validateBusinessField(
        business.id,
        payload.companyName,
        'companyName',
        {
          sameValue: 'O novo nome da empresa é igual ao atual.',
          duplicateValue: 'Já existe uma empresa com esse nome.',
        },
      ),
      this.validateBusinessField(
        business.id,
        payload.fantasyName,
        'fantasyName',
        {
          sameValue: 'O novo nome fantasia é igual ao atual.',
          duplicateValue: 'Já existe uma empresa com esse nome fantasia.',
        },
      ),
      this.validateBusinessField(
        business.id,
        payload.cnpj?.replace(/\D/g, ''),
        'cnpj',
        {
          sameValue: 'O novo CNPJ é igual ao atual.',
          duplicateValue: 'Já existe uma empresa com esse CNPJ.',
        },
      ),
      this.validateBusinessField(business.id, payload.email, 'email', {
        sameValue: 'O novo e-mail é igual ao atual.',
        duplicateValue: 'Já existe uma empresa com esse e-mail.',
      }),
      this.validateBusinessField(business.id, payload.type, 'type', {
        sameValue: 'O novo tipo é igual ao atual.',
        duplicateValue: '', // Assuming type doesn't need duplicate check
      }),
      this.validateBusinessField(business.id, payload.size, 'size', {
        sameValue: 'O novo porte é igual ao atual.',
        duplicateValue: '', // Assuming size doesn't need duplicate check
      }),
      this.validateDtOpening(business.id, payload.dtOpening),
      // Add more validations as necessary
    ]);

    await this.businessRepository.update(business.id, {
      companyName: newCompanyName,
      fantasyName: newFantasyName,
      cnpj: newCnpj,
      email: newEmail,
      type: newType,
      size: newSize,
      dtOpening: newDtOpening ? new Date(newDtOpening) : undefined,
      // Include other fields as necessary
    });
  }

  private async validateBusinessField(
    businessId: string,
    newValue: string | undefined,
    fieldName:
      | 'companyName'
      | 'fantasyName'
      | 'cnpj'
      | 'email'
      | 'type'
      | 'size',
    errorMessage: { sameValue: string; duplicateValue: string },
  ): Promise<string | undefined> {
    if (!newValue) {
      return undefined;
    }

    const existingBusiness = await this.businessRepository.findOne({
      where: { id: businessId },
      select: [fieldName],
    });

    if (!existingBusiness) {
      throw new NotFoundException('Empresa não encontrada.');
    }

    if (existingBusiness[fieldName] === newValue) {
      throw new UnprocessableEntityException(errorMessage.sameValue);
    }

    if (errorMessage.duplicateValue) {
      const valueAlreadyExists =
        (await this.businessRepository.count({
          where: { [fieldName]: newValue },
        })) > 0;

      if (valueAlreadyExists) {
        throw new UnprocessableEntityException(errorMessage.duplicateValue);
      }
    }

    return newValue;
  }

  private async validateDtOpening(
    businessId: string,
    newDtOpening?: string,
  ): Promise<string | undefined> {
    if (!newDtOpening) {
      return undefined;
    }

    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
    if (!datePattern.test(newDtOpening)) {
      throw new UnprocessableEntityException(
        'O formato da data de abertura é inválido. Use o formato YYYY-MM-DD.',
      );
    }

    const parsedDate = new Date(newDtOpening);
    if (Number.isNaN(parsedDate.getTime())) {
      throw new UnprocessableEntityException(
        'A data de abertura fornecida é inválida.',
      );
    }

    if (parsedDate >= new Date()) {
      throw new UnprocessableEntityException(
        'A data de abertura deve ser anterior à data atual.',
      );
    }

    const existingBusiness = await this.businessRepository.findOne({
      where: { id: businessId },
      select: ['dtOpening'],
    });

    if (!existingBusiness) {
      throw new NotFoundException('Empresa não encontrada.');
    }

    if (
      new Date(existingBusiness.dtOpening).getTime() === parsedDate.getTime()
    ) {
      throw new UnprocessableEntityException(
        'A nova data de abertura é igual à atual.',
      );
    }

    return parsedDate.toISOString().split('T').at(0);
  }
}
