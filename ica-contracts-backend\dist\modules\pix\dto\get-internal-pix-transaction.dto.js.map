{"version": 3, "file": "get-internal-pix-transaction.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/get-internal-pix-transaction.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsD;AAEtD,MAAa,6BAA6B;CAIzC;AAJD,sEAIC;AADC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;oEACW", "sourcesContent": ["import { IsDefined, IsString } from 'class-validator';\r\n\r\nexport class GetInternalPixTransactionlDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  transactionId: string;\r\n}\r\n"]}