{"version": 3, "file": "query.dto.js", "sourceRoot": "/", "sources": ["modules/superadmin/dto/get-single-advisor-with-active-contracts/query.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAkF;AAGlF,oFAAuE;AACvE,yDAAyC;AAEzC,MAAa,4CAA4C;IAAzD;QAcE,SAAI,GAAY,CAAC,CAAC;QAMlB,UAAK,GAAY,EAAE,CAAC;IAKtB,CAAC;CAAA;AAzBD,oGAyBC;AArBC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8EACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4EACK;AAMhB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;0EACU;AAMlB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,uBAAK,GAAE;;2EACY;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,qCAAgB,CAAC;;kFACM", "sourcesContent": ["import { IsEnum, IsInt, IsOptional, IsPositive, IsString } from 'class-validator';\r\n\r\nimport { IDateRangeQuery, IPaginationQuery } from '../../helpers/pagination-query';\r\nimport { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class GetSingleAdvisorsWithActiveContractsQueryDto\r\n  implements IDateRangeQuery, IPaginationQuery {\r\n  @IsOptional()\r\n  @IsString()\r\n  dateFrom?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  dateTo?: string;\r\n\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  page?: number = 1;\r\n\r\n  @IsOptional()\r\n  @IsPositive()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  limit?: number = 10;\r\n\r\n  @IsOptional()\r\n  @IsEnum(ContractTypeEnum)\r\n  contractType: ContractTypeEnum;\r\n}\r\n"]}