import { Cache } from 'cache-manager';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { Repository } from 'typeorm';
import { GetSingleBrokerWithActiveContractsQueryDto } from '../dto/get-single-broker-with-active-contracts/query.dto';
import { GetSinleBrokerWithActiveContractsResponseDto } from '../dto/get-single-broker-with-active-contracts/response.dto';
export declare class GetSingleBrokerWithActiveContractsService {
    private contractsDb;
    private cacheManager;
    constructor(contractsDb: Repository<ContractEntity>, cacheManager: Cache);
    perform(brokerId: string, query: GetSingleBrokerWithActiveContractsQueryDto): Promise<GetSinleBrokerWithActiveContractsResponseDto[] | import("../helpers/pagination-query").IPaginatedResult<{
        investorId: string;
        brokerId: string;
        name: string;
        avatar: string;
        document: string;
        createdAt: Date;
        totalContractAmount: number;
        totalCaptured: number;
    }>>;
}
