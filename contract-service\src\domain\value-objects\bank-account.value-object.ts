import { type Either, left, right } from '@/domain/shared/either'

export class BankAccount {
  private constructor(
    public readonly bank: string,
    public readonly agency: string,
    public readonly accountNumber: string,
    public readonly pixKey?: string
  ) {}

  static create(
    bank: string,
    agency: string,
    accountNumber: string,
    pixKey?: string
  ): Either<Error, BankAccount> {
    if (!bank || !agency || !accountNumber) {
      return right(new BankAccount('', '', '', ''))
    }

    // if (!/^\d+$/.test(agency)) {
    //   return left(new Error('Invalid agency number.'))
    // }

    // if (!/^\d+(-\d+)?$/.test(accountNumber)) {
    //   return left(new Error('Invalid account number format.'))
    // }

    return right(new BankAccount(bank, agency, accountNumber, pixKey))
  }

  get formatted(): string {
    return `${this.bank} - ${this.agency}/${this.accountNumber}`
  }

  equals(other: BankAccount): boolean {
    return (
      this.bank === other.bank &&
      this.agency === other.agency &&
      this.accountNumber === other.accountNumber &&
      this.pixKey === other.pixKey
    )
  }
}
