import { Inject, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IOwner } from 'src/apis/celcoin/requests/update-account-pj-celcoin.request';
import { AccountCelcoinService } from 'src/apis/celcoin/services/account-celcoin.service';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { Equal, Repository } from 'typeorm';

import { UpdateBusinessDto } from '../dto/update-account-pj.dto';

export class UpdateAccountPJService {
  constructor(
    @InjectRepository(BusinessEntity)
    private businessRepository: Repository<BusinessEntity>,
    @Inject(AccountCelcoinService)
    private accountCelcoin: AccountCelcoinService,
  ) {}

  async perform(input: UpdateBusinessDto) {
    const business = await this.businessRepository.findOne({
      where: {
        id: Equal(input.businessId),
      },
    });

    if (!business) {
      throw new NotFoundException('Empresa não encontrada.');
    }
    const owners: IOwner[] = input.owners.map((owner) => ({
      documentNumber: owner.document,
      address: {
        postalCode: owner.Address.cep,
        street: owner.Address.street,
        number: owner.Address.number,
        addressComplement: owner.Address.complement,
        neighborhood: owner.Address.neighborhood,
        city: owner.Address.city,
        state: owner.Address.state,
      },
      isPoliticallyExposedPerson: owner.pep,
    }));

    await this.accountCelcoin.updatePJ({
      DocumentNumber: business.cnpj,
      Account: input.accountNumber,
      businessEmail: input.email,
      contactNumber: input.phone,
      businessAddress: {
        addressComplement: input.Address.complement,
        city: input.Address.city,
        neighborhood: input.Address.neighborhood,
        number: input.Address.number,
        postalCode: input.Address.cep,
        state: input.Address.state,
        street: input.Address.street,
      },
      owners,
    });
    await this.businessRepository.save({
      ...business,
      address: [input.Address],
      email: input.email,
    });
  }
}
