{"version": 3, "file": "get-ted.service.js", "sourceRoot": "/", "sources": ["modules/ted/services/get-ted.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwC;AACxC,6CAAmD;AACnD,4FAAkF;AAClF,qGAA4F;AAC5F,qCAA4C;AAI5C,IAAa,aAAa,GAA1B,MAAa,aAAa;IACxB,YAEU,eAA8C,EAE9C,cAAiC;QAFjC,oBAAe,GAAf,eAAe,CAA+B;QAE9C,mBAAc,GAAd,cAAc,CAAmB;IACxC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAAe;QAC3B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE;gBACL,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,CAAC,EAAE,CAAC;aACnB;SACF,CAAC,CAAC;QAEH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YAChD,EAAE,EAAE,WAAW,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AArBY,sCAAa;wBAAb,aAAa;IAErB,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,eAAM,EAAC,uCAAiB,CAAC,CAAA;qCADD,oBAAU;QAEX,uCAAiB;GALhC,aAAa,CAqBzB", "sourcesContent": ["import { Inject } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { TedCelcoinService } from 'src/apis/celcoin/services/ted-celcoin.service';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { GetTedDto } from '../dto/get-ted.dto';\r\n\r\nexport class GetTedService {\r\n  constructor(\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepo: Repository<TransactionEntity>,\r\n    @Inject(TedCelcoinService)\r\n    private celcoinService: TedCelcoinService,\r\n  ) {}\r\n\r\n  async perform(data: GetTedDto) {\r\n    const transaction = await this.transactionRepo.findOne({\r\n      where: {\r\n        id: Equal(data.id),\r\n      },\r\n    });\r\n\r\n    const { body } = await this.celcoinService.getTed({\r\n      id: transaction.code,\r\n    });\r\n\r\n    return body;\r\n  }\r\n}\r\n"]}