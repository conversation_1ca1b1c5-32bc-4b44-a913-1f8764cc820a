{"version": 3, "file": "get-recent-teds.service.js", "sourceRoot": "/", "sources": ["modules/ted/services/get-recent-teds.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA2D;AAC3D,6CAAmD;AACnD,4FAAkF;AAClF,6FAAoF;AACpF,qGAA4F;AAC5F,qCAAkD;AAElD,IAAa,oBAAoB,GAAjC,MAAa,oBAAoB;IAC/B,YAEU,eAA8C,EAE9C,WAAsC,EAEtC,cAAiC;QAJjC,oBAAe,GAAf,eAAe,CAA+B;QAE9C,gBAAW,GAAX,WAAW,CAA2B;QAEtC,mBAAc,GAAd,cAAc,CAAmB;IACxC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAAe;QAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,OAAO,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,OAAO,CAAC,EAAE,CAAC;SACrE,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YAAE,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;QAEnE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACnD,KAAK,EAAE;gBACL,IAAI,EAAE,IAAA,cAAI,EAAC,MAAM,CAAC;gBAClB,SAAS,EAAE,OAAO,CAAC,EAAE;aACtB;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAC9B,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;YACrC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBAChD,EAAE,EAAE,WAAW,CAAC,IAAI;aACrB,CAAC,CAAC;YAEH,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;QAEF,OAAO;YACL,YAAY,EAAE,MAAM;YACpB,iBAAiB,EAAE,MAAM,CAAC,MAAM;SACjC,CAAC;IACJ,CAAC;CACF,CAAA;AAhDY,oDAAoB;+BAApB,oBAAoB;IAE5B,WAAA,IAAA,0BAAgB,EAAC,sCAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,eAAM,EAAC,uCAAiB,CAAC,CAAA;qCAHD,oBAAU;QAEd,oBAAU;QAEP,uCAAiB;GAPhC,oBAAoB,CAgDhC", "sourcesContent": ["import { Inject, NotFoundException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { TedCelcoinService } from 'src/apis/celcoin/services/ted-celcoin.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';\r\nimport { Equal, Like, Repository } from 'typeorm';\r\n\r\nexport class GetRecentTedsService {\r\n  constructor(\r\n    @InjectRepository(TransactionEntity)\r\n    private transactionRepo: Repository<TransactionEntity>,\r\n    @InjectRepository(AccountEntity)\r\n    private accountRepo: Repository<AccountEntity>,\r\n    @Inject(TedCelcoinService)\r\n    private celcoinService: TedCelcoinService,\r\n  ) {}\r\n\r\n  async perform(ownerId: string) {\r\n    const account = await this.accountRepo.findOne({\r\n      where: [{ ownerId: Equal(ownerId) }, { businessId: Equal(ownerId) }],\r\n    });\r\n\r\n    if (!account) throw new NotFoundException('Conta não encontrada.');\r\n\r\n    const transactions = await this.transactionRepo.find({\r\n      where: {\r\n        type: Like('TED%'),\r\n        accountId: account.id,\r\n      },\r\n      order: { createdAt: 'DESC' },\r\n      take: 5,\r\n    });\r\n\r\n    const output = await Promise.all(\r\n      transactions.map(async (transaction) => {\r\n        const { body } = await this.celcoinService.getTed({\r\n          id: transaction.code,\r\n        });\r\n\r\n        return {\r\n          id: body.id,\r\n          amount: body.amount,\r\n          clientCode: body.clientCode,\r\n          description: body.description,\r\n          creditParty: body.creditParty,\r\n          debitParty: body.debitParty,\r\n        };\r\n      }),\r\n    );\r\n\r\n    return {\r\n      transactions: output,\r\n      transactionsCount: output.length,\r\n    };\r\n  }\r\n}\r\n"]}