"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatistcModule = void 0;
const common_1 = require("@nestjs/common");
const shared_module_1 = require("../../shared/shared.module");
const statistic_controller_1 = require("./controllers/statistic.controller");
const update_statistic_service_1 = require("./services/update-statistic.service");
let StatistcModule = class StatistcModule {
};
exports.StatistcModule = StatistcModule;
exports.StatistcModule = StatistcModule = __decorate([
    (0, common_1.Module)({
        controllers: [statistic_controller_1.StatisticController],
        imports: [shared_module_1.SharedModule],
        providers: [update_statistic_service_1.UpdateStatisticService],
    })
], StatistcModule);
//# sourceMappingURL=statistic.module.js.map