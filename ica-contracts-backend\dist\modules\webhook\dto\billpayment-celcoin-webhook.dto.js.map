{"version": 3, "file": "billpayment-celcoin-webhook.dto.js", "sourceRoot": "/", "sources": ["modules/webhook/dto/billpayment-celcoin-webhook.dto.ts"], "names": [], "mappings": "", "sourcesContent": ["interface IBarCodeInfo {\r\n  digitable: string;\r\n}\r\n\r\ninterface ITag {\r\n  key: string;\r\n  value: string;\r\n}\r\n\r\ninterface IAuthenticationAPI {\r\n  bloco1: string;\r\n  bloco2: string;\r\n  blocoCompleto: string;\r\n}\r\n\r\ninterface IReceipt {\r\n  receiptformatted: string;\r\n}\r\n\r\ninterface IBasePayload {\r\n  entity: 'billpayment';\r\n  createTimestamp: string;\r\n  status: 'CONFIRMED' | 'ERROR';\r\n  body: {\r\n    amount: number;\r\n    id: string;\r\n    clientRequestId: number;\r\n    account: number;\r\n    barCodeInfo: IBarCodeInfo;\r\n    transactionIdAuthorize: number;\r\n  };\r\n}\r\n\r\ninterface IConfirmedBody extends IBasePayload {\r\n  status: 'CONFIRMED';\r\n  body: {\r\n    transactionId: number;\r\n    status: 'CONFIRMED';\r\n    createDate: string;\r\n    tags: ITag[];\r\n    authentication: number;\r\n    authenticationAPI: IAuthenticationAPI;\r\n    convenant: string;\r\n    isExpired: boolean;\r\n    receipt: IReceipt;\r\n    settleDate: string;\r\n  } & IBasePayload['body'];\r\n}\r\n\r\ninterface IErrorBody extends IBasePayload {\r\n  status: 'ERROR';\r\n  error: string;\r\n}\r\n\r\nexport type IBillPaymentCelcoinWebhookDto = IConfirmedBody | IErrorBody;\r\n"]}