import type { FastifyInstance } from 'fastify';
import { fastifyRouteAdapter } from '../adapters';
import { makeDetailsIncomeReportController } from '../factories/controllers/details-income-report-controler.factory';

export const detailsIncomeReportRoute = async (app: FastifyInstance) => {
  app.post(
    '/details-income-report',
    fastifyRouteAdapter(makeDetailsIncomeReportController()),
  );
};
