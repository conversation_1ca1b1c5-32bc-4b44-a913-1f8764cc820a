{"version": 3, "file": "add-favorite-pix.service.js", "sourceRoot": "/", "sources": ["modules/pix/service/add-favorite-pix.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+C;AAC/C,6CAAmD;AACnD,6FAAoF;AACpF,uGAA6F;AAC7F,qCAA4C;AAI5C,IAAa,qBAAqB,GAAlC,MAAa,qBAAqB;IAChC,YAEU,SAAoC,EAEpC,aAA4C;QAF5C,cAAS,GAAT,SAAS,CAA2B;QAEpC,kBAAa,GAAb,aAAa,CAA+B;IACnD,CAAC;IACJ,KAAK,CAAC,OAAO,CAAC,IAAuB,EAAE,EAAU;QAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,SAAS,EAAE;gBACT,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;aACZ;YACD,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACtB,EAAE,UAAU,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;gBACzB,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,EAAE,CAAC,EAAE;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,sBAAa,CACrB,4CAA4C,EAC5C,GAAG,CACJ,CAAC;QAEJ,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC9B,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAClC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAClC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YACtC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC9B,SAAS,EAAE,OAAO,CAAC,EAAE;SACtB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AAvCY,sDAAqB;gCAArB,qBAAqB;IAE7B,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,uCAAiB,CAAC,CAAA;qCADjB,oBAAU;QAEN,oBAAU;GALxB,qBAAqB,CAuCjC", "sourcesContent": ["import { HttpException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { FavoritePixEntity } from 'src/shared/database/typeorm/entities/favorite-pix.entity';\r\nimport { Equal, Repository } from 'typeorm';\r\n\r\nimport { AddFavoritePixDto } from '../dto/add-favorite-pix.dto';\r\n\r\nexport class AddFavoritePixService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(FavoritePixEntity)\r\n    private favoritePixDb: Repository<FavoritePixEntity>,\r\n  ) {}\r\n  async perform(data: AddFavoritePixDto, id: string) {\r\n    const account = await this.accountDb.findOne({\r\n      relations: {\r\n        business: true,\r\n        owner: true,\r\n      },\r\n      where: [\r\n        { ownerId: Equal(id) },\r\n        { businessId: Equal(id) },\r\n        { id: Equal(id) },\r\n      ],\r\n    });\r\n\r\n    if (!account)\r\n      throw new HttpException(\r\n        'Conta não existe ou não vinculada ao owner',\r\n        400,\r\n      );\r\n\r\n    const create = this.favoritePixDb.create({\r\n      alias: data.alias,\r\n      name: data.name,\r\n      accountBank: data.account.bank,\r\n      accountBranch: data.account.branch,\r\n      accountNumber: data.account.number,\r\n      accountDocument: data.account.document,\r\n      accountType: data.account.type,\r\n      accountId: account.id,\r\n    });\r\n\r\n    await this.favoritePixDb.save(create);\r\n  }\r\n}\r\n"]}