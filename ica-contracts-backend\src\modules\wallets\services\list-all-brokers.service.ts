import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Repository } from 'typeorm';

@Injectable()
export class ListAllBrokersService {
  constructor(
    @InjectRepository(WalletsViewsEntity)
    private walletsDb: Repository<WalletsViewsEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private perfilDb: Repository<OwnerRoleRelationEntity>,
  ) {}
  async perform() {
    const response = [];

    const brokers = await this.perfilDb.find({
      relations: {
        role: true,
        owner: {
          account: true,
        },
        business: {
          account: true,
        },
      },
      select: {
        owner: {
          name: true,
        },
        business: {
          companyName: true,
        },
      },
      where: {
        role: {
          name: RolesEnum.BROKER,
        },
      },
    });

    brokers.map((broker) => {
      if (broker.owner?.account[0] || broker.business?.account[0]) {
        response.push({
          name: broker.owner?.name || broker.business?.companyName,
          id: broker.id,
          accountId:
            broker.owner?.account[0].id || broker.business.account[0].id,
        });
      }
    });

    return response;
  }
}
