{"version": 3, "file": "create-pix-qrcode-static.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/create-pix-qrcode-static.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yDAAyC;AACzC,qDAOyB;AAEzB,MAAM,QAAQ;CAgBb;AAbC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;4CACQ;AAInB;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAIb;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sDACkB;AAI7B;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;sCACE;AAEf,MAAa,wBAAwB;CAkBpC;AAlBD,4DAkBC;AAfC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;qDACC;AAIZ;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wDACK;AAMhB;IAJC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,QAAQ,CAAC;8BACX,QAAQ;0DAAC;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACG", "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport {\r\n  IsDefined,\r\n  IsNumber,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\n\r\nclass Merchant {\r\n  @IsDefined()\r\n  @IsString()\r\n  postalCode: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  city: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  merchantCategoryCode: string;\r\n\r\n  @IsDefined()\r\n  @IsString()\r\n  name: string;\r\n}\r\nexport class CreatePixQRCodeStaticDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  key: string;\r\n\r\n  @IsDefined()\r\n  @IsNumber()\r\n  amount?: number;\r\n\r\n  @IsDefined()\r\n  @IsObject()\r\n  @ValidateNested()\r\n  @Type(() => Merchant)\r\n  merchant: Merchant;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  info?: string;\r\n}\r\n"]}