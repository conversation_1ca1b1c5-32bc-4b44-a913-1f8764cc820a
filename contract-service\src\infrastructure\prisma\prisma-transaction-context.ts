import type { ITransactionContext } from '@/application/interfaces/transaction-context'
import type { Prisma } from '@prisma/client'

export class PrismaTransactionContext implements ITransactionContext {
  private afterCommitActions: Array<() => Promise<void> | void> = []

  constructor(private readonly tx: Prisma.TransactionClient) {}

  // Em ORMs como o Prisma, o commit costuma ser gerenciado automaticamente
  // pelo método $transaction. Entretanto, esse método permite executar ações
  // pós-commit, caso seja necessário.
  async commit(): Promise<void> {
    // Executa ações registradas após o commit.
    for (const action of this.afterCommitActions) {
      await action()
    }
  }

  // Em caso de erro, o rollback será disparado automaticamente ao lançar exceção,
  // mas esse método pode ser útil para ações de limpeza ou log.
  async rollback(): Promise<void> {
    // Implemente ações de rollback, se necessário.
    // Na maioria dos casos, basta propagar a exceção.
  }

  registerAfterCommit(action: () => Promise<void> | void): void {
    this.afterCommitActions.push(action)
  }

  getClient(): Prisma.TransactionClient {
    return this.tx
  }
}
