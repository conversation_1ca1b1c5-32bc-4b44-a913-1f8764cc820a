{"version": 3, "file": "transaction-recharge.service.js", "sourceRoot": "/", "sources": ["modules/recharge/services/transaction-recharge.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAyE;AACzE,6CAAmD;AACnD,oHAAyG;AACzG,mIAAkH;AAClH,6FAAoF;AACpF,+FAAsF;AACtF,2FAAiF;AACjF,qCAAqC;AACrC,+BAA0B;AAKnB,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IACrC,YAEU,SAAoC,EAEpC,UAAsC,EAEtC,iBAA+C,EACtC,yBAAoD;QAL7D,cAAS,GAAT,SAAS,CAA2B;QAEpC,eAAU,GAAV,UAAU,CAA4B;QAEtC,sBAAiB,GAAjB,iBAAiB,CAA8B;QACtC,8BAAyB,GAAzB,yBAAyB,CAA2B;IACpE,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,IAA4B;QACxC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YACnD,KAAK,EAAE;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;aACZ;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,CAAC,CAAC;QAEtE,MAAM,IAAI,GAAG,IAAA,SAAE,GAAE,CAAC;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAC7D,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3B,WAAW,EAAE;gBACX,OAAO,EAAE,eAAe,CAAC,MAAM;aAChC;YACD,eAAe,EAAE,IAAI;YACrB,UAAU,EAAE;gBACV,OAAO,EAAE,eAAe;aACzB;YACD,WAAW,EAAE,SAAS;SACvB,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,SAAS,EAAE,eAAe,CAAC,EAAE;YAC7B,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,MAAM,EAAE,+CAAqB,CAAC,OAAO;YACrC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE;SAC7B,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEnC,MAAM,gBAAgB,GACpB,MAAM,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC;YACrD,SAAS,EAAE,eAAe,CAAC,EAAE;YAC7B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3B,IAAI,EAAE,UAAU;SACjB,CAAC,CAAC;QAEL,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF,CAAA;AArDY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAa,CAAC,CAAA;IAE/B,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;IAEhC,WAAA,IAAA,eAAM,EAAC,8DAA4B,CAAC,CAAA;qCAHlB,oBAAU;QAET,oBAAU;QAEH,8DAA4B;QACX,8DAAyB;GAR5D,0BAA0B,CAqDtC", "sourcesContent": ["import { BadRequestException, Inject, Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';\r\nimport { CreateTransactionsService } from 'src/apis/icainvest-credit/services/create-virtual-transaction.service';\r\nimport { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';\r\nimport { RechargeEntity } from 'src/shared/database/typeorm/entities/recharge.entity';\r\nimport { TransactionStatusEnum } from 'src/shared/enums/transaction-status-enum';\r\nimport { Repository } from 'typeorm';\r\nimport { v4 } from 'uuid';\r\n\r\nimport { TransactionRechargeDto } from '../dto/transaction-recharge.dto';\r\n\r\n@Injectable()\r\nexport class TransactionRechargeService {\r\n  constructor(\r\n    @InjectRepository(AccountEntity)\r\n    private accountDb: Repository<AccountEntity>,\r\n    @InjectRepository(RechargeEntity)\r\n    private rechargeDb: Repository<RechargeEntity>,\r\n    @Inject(PixTransactionCelcoinService)\r\n    private transactionPixApi: PixTransactionCelcoinService,\r\n    private readonly createTransactionsService: CreateTransactionsService,\r\n  ) {}\r\n\r\n  async perform(data: TransactionRechargeDto) {\r\n    const transferAccount = await this.accountDb.findOne({\r\n      where: {\r\n        id: data.id,\r\n      },\r\n    });\r\n\r\n    if (!transferAccount)\r\n      throw new BadRequestException('Conta não encontrada para recarga.');\r\n\r\n    const uuid = v4();\r\n\r\n    const transfer = await this.transactionPixApi.internalTransfer({\r\n      amount: Number(data.amount),\r\n      creditParty: {\r\n        account: transferAccount.number,\r\n      },\r\n      clientRequestId: uuid,\r\n      debitParty: {\r\n        account: '*************',\r\n      },\r\n      description: 'RECARGA',\r\n    });\r\n\r\n    const create = this.rechargeDb.create({\r\n      accountId: transferAccount.id,\r\n      value: data.amount,\r\n      status: TransactionStatusEnum.PENDENT,\r\n      externalId: transfer.body.id,\r\n    });\r\n\r\n    await this.rechargeDb.save(create);\r\n\r\n    const externalResponse =\r\n      await this.createTransactionsService.createTransaction({\r\n        accountId: transferAccount.id,\r\n        amount: Number(data.amount),\r\n        type: 'RECHARGE',\r\n      });\r\n\r\n    return externalResponse;\r\n  }\r\n}\r\n"]}