{"version": 3, "file": "validate-link-token-pre-register.middleware.js", "sourceRoot": "/", "sources": ["shared/middlewares/validate-link-token-pre-register.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,+CAAsC;AAG/B,IAAM,gCAAgC,GAAtC,MAAM,gCAAgC;IAC3C,gBAAe,CAAC;IAChB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;QACjD,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAe,CAAC;QAC1C,IAAA,qBAAM,EAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAEzB,IAAI,EAAE,CAAC;IACT,CAAC;CACF,CAAA;AATY,4EAAgC;2CAAhC,gCAAgC;IAD5C,IAAA,mBAAU,GAAE;;GACA,gCAAgC,CAS5C", "sourcesContent": ["import { Injectable, NestMiddleware } from '@nestjs/common';\r\nimport { NextFunction, Request, Response } from 'express';\r\nimport { verify } from 'jsonwebtoken';\r\n\r\n@Injectable()\r\nexport class ValidateLinkTokenOwnerMiddleware implements NestMiddleware {\r\n  constructor() {}\r\n  use(req: Request, res: Response, next: NextFunction) {\r\n    const secretKey = process.env.PRE_REGISTER_TOKEN;\r\n    const token = req.headers.token as string;\r\n    verify(token, secretKey);\r\n\r\n    next();\r\n  }\r\n}\r\n"]}