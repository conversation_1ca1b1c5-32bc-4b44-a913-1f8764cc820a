import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { addHours, format } from 'date-fns';
import { AccountCelcoinService } from 'src/apis/celcoin/services/account-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Equal, Repository } from 'typeorm';

import { UpdateAccountPfDTO } from '../dto/update-account-pf.dto';

@Injectable()
export class UpdateAccountPFService {
  constructor(
    @InjectRepository(AccountEntity)
    private readonly accountDb: Repository<AccountEntity>,
    @InjectRepository(OwnerEntity)
    private readonly ownerDb: Repository<OwnerEntity>,
    @Inject(AccountCelcoinService)
    private readonly apiCelcoin: AccountCelcoinService,
  ) {}

  async perform(data: UpdateAccountPfDTO): Promise<void> {
    const account = await this.accountDb.findOne({
      relations: {
        owner: {
          address: true,
        },
      },
      where: {
        id: Equal(data.id),
      },
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    await this.apiCelcoin.updatePF({
      Account: account.number,
      email: data.email || account.owner.email,
      phoneNumber: data.phone || account.owner.phone,
      address: data.address
        ? {
            addressComplement: data.address.complement,
            city: data.address.city,
            latitude: data.address.latitude,
            longitude: data.address.longitude,
            neighborhood: data.address.neighborhood,
            number: data.address.number,
            postalCode: data.address.cep,
            state: data.address.state,
            street: data.address.street,
          }
        : {
            addressComplement: account.owner.address[0].complement,
            city: account.owner.address[0].city,
            neighborhood: account.owner.address[0].neighborhood,
            number: account.owner.address[0].number,
            postalCode: account.owner.address[0].cep,
            state: account.owner.address[0].state,
            street: account.owner.address[0].street,
          },
      birthDate: data.birthDate
        ? format(addHours(data.birthDate, 3), 'dd-MM-yyyy')
        : format(account.owner.dtBirth, 'dd-MM-yyyy'),
    });

    await this.ownerDb.update(account.ownerId, {
      dtBirth: data.birthDate || account.owner.dtBirth,
    });
  }
}
