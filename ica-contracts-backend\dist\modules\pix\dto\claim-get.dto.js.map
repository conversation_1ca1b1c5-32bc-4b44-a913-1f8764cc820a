{"version": 3, "file": "claim-get.dto.js", "sourceRoot": "/", "sources": ["modules/pix/dto/claim-get.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAsD;AAEtD,MAAa,WAAW;CAIvB;AAJD,kCAIC;AADC;IAFC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;;wCACC", "sourcesContent": ["import { IsDefined, IsString } from 'class-validator';\r\n\r\nexport class ClaimGetDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  key: string;\r\n}\r\n"]}