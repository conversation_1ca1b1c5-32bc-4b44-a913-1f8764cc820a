import { TedCelcoinService } from 'src/apis/celcoin/services/ted-celcoin.service';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Repository } from 'typeorm';
import { GetTedDto } from '../dto/get-ted.dto';
export declare class GetTedService {
    private transactionRepo;
    private celcoinService;
    constructor(transactionRepo: Repository<TransactionEntity>, celcoinService: TedCelcoinService);
    perform(data: GetTedDto): Promise<{
        id: string;
        amount: number;
        clientCode: string;
        debitParty: {
            account: string;
            branch: string;
            taxId: string;
            name: string;
            accountType: string;
            personType: string;
            bank: string;
        };
        creditParty: {
            bank: string;
            account: string;
            branch: string;
            taxId: string;
            name: string;
            accountType: string;
            personType: string;
        };
        description: string;
        error?: {
            errorCode: string;
            message: string;
        };
    }>;
}
