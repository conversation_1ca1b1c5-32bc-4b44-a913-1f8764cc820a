{"version": 3, "file": "get-ted.dto.js", "sourceRoot": "/", "sources": ["modules/ted/dto/get-ted.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA8D;AAE9D,MAAa,SAAS;CAKrB;AALD,8BAKC;AADC;IAHC,IAAA,2BAAS,GAAE;IACX,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,GAAE;;qCACE", "sourcesContent": ["import { IsDefined, IsString, IsUUID } from 'class-validator';\r\n\r\nexport class GetTedDto {\r\n  @IsDefined()\r\n  @IsString()\r\n  @IsUUID()\r\n  id: string;\r\n}\r\n"]}