import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { IGetProfileResponse } from '../response/get-profile-response';

@Injectable()
export class GetProfileService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
  ) {}

  async perform(id: string): Promise<IGetProfileResponse> {
    const account = await this.accountDb.findOne({
      relations: {
        owner: {
          ownerRoleRelation: {
            role: true,
          },
        },
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
          ownerRoleRelation: {
            role: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    const roles: Array<{ name: string; roleId: string }> = [];
    if (account.type === 'physical') {
      account.owner.ownerRoleRelation.map((roleRelation) => {
        roles.push({
          name: roleRelation.role.name,
          roleId: roleRelation.id,
        });
      });
    } else {
      account.business.ownerRoleRelation.map((roleRelation) => {
        roles.push({
          name: roleRelation.role.name,
          roleId: roleRelation.id,
        });
      });
    }

    const isPhysical = account.type === 'physical';
    const document = isPhysical ? account.owner.cpf : account.business.cnpj;
    const name = isPhysical ? account.owner.name : account.business.companyName;

    return {
      id: account.id,
      document,
      name,
      roles,
    };
  }
}
