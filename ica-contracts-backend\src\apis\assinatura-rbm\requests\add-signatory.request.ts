export interface IAdicionarSignatarioRequest {
  id: string;

  cpfCnpj: string;
  nome: string;
  tipoAss: string;

  tipoAssComplemento?: string;
  grupoAssinaturaId?: string;
  ordemAssinatura?: string;
  email: string;
  celular?: string;
  linkAssinaturaEmail?: string;
  linkAssinaturaSMS?: string;
  dataNasc?: string;
  selfieNecessaria?: string;
  certificadoDigitalNecessario?: string;
  documentoFrenteNecessario?: string;
  documentoVersoNecessario?: string;
  selfieComDocumentoNecessario?: string;
  serproRealizarValidacaoFacial?: string;
  faceMatch?: string;
  mensagem?: string;
}
