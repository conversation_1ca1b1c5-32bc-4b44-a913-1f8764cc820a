"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuperAdminModule = void 0;
const cache_manager_1 = require("@nestjs/cache-manager");
const common_1 = require("@nestjs/common");
const shared_module_1 = require("../../shared/shared.module");
const advisor_module_1 = require("../advisor/advisor.module");
const broker_module_1 = require("../broker/broker.module");
const contract_module_1 = require("../contract/contract.module");
const investor_module_1 = require("../investor/investor.module");
const superadmin_controller_1 = require("./controllers/superadmin.controller");
const find_admin_super_admin_service_1 = require("./services/find-admin-super-admin.service");
const find_super_admin_service_1 = require("./services/find-super-admin.service");
const get_all_advisors_with_active_contracts_service_1 = require("./services/get-all-advisors-with-active-contracts.service");
const get_all_brokers_with_active_contracts_service_1 = require("./services/get-all-brokers-with-active-contracts.service");
const get_single_advisor_with_active_contracts_service_1 = require("./services/get-single-advisor-with-active-contracts.service");
const get_single_broker_with_active_contracts_service_1 = require("./services/get-single-broker-with-active-contracts.service");
const get_dashboard_data_service_1 = require("./services/get-dashboard-data.service");
const super_admin_edit_broker_service_1 = require("./services/super-admin-edit-broker.service");
const super_admin_edit_advisor_service_1 = require("./services/super-admin-edit-advisor.service");
const get_contracts_growth_chart_service_1 = require("./services/get-contracts-growth-chart.service");
const super_admin_edit_investor_service_1 = require("./services/super-admin-edit-investor.service");
let SuperAdminModule = class SuperAdminModule {
};
exports.SuperAdminModule = SuperAdminModule;
exports.SuperAdminModule = SuperAdminModule = __decorate([
    (0, common_1.Module)({
        imports: [shared_module_1.SharedModule, broker_module_1.BrokerModule, advisor_module_1.AdvisorModule, investor_module_1.InvestorModule, contract_module_1.ContractModule, cache_manager_1.CacheModule.register()],
        providers: [
            find_super_admin_service_1.SuperAdminService,
            find_admin_super_admin_service_1.GetAdminSuperAdminService,
            get_all_brokers_with_active_contracts_service_1.GetAllBrokersWithActiveContractsService,
            get_single_broker_with_active_contracts_service_1.GetSingleBrokerWithActiveContractsService,
            get_all_advisors_with_active_contracts_service_1.GetAllAdvisorsWithActiveContractsService,
            get_single_advisor_with_active_contracts_service_1.GetSingleAdvisorWithActiveContractsService,
            get_dashboard_data_service_1.GetDashboardDataService,
            super_admin_edit_broker_service_1.SuperAdminEditBrokerService,
            super_admin_edit_advisor_service_1.SuperAdminEditAdvisorService,
            get_contracts_growth_chart_service_1.GetContractsGrowthChartService,
            super_admin_edit_investor_service_1.SuperAdminEditInvestorService,
        ],
        controllers: [superadmin_controller_1.SuperAdminController],
        exports: [],
    })
], SuperAdminModule);
//# sourceMappingURL=superadmin.module.js.map