import { type Either, left, right } from '../shared'

export class CompanyType {
  private static readonly validTypes = [
    'MEI',
    'EI',
    'EIRELI',
    'LTDA',
    'SLU',
    'SA',
    'SS',
    'CONSORCIO',
  ] as const

  private constructor(
    private readonly value: (typeof CompanyType.validTypes)[number]
  ) {}

  static create(type: string): Either<Error, CompanyType> {
    const normalized = type.trim().toUpperCase()

    if (!CompanyType.validTypes.includes(normalized as any)) {
      return left(new Error(`Invalid company type: ${type}`))
    }

    return right(
      new CompanyType(normalized as (typeof CompanyType.validTypes)[number])
    )
  }

  static mei(): CompanyType {
    return new CompanyType('MEI')
  }

  static ei(): CompanyType {
    return new CompanyType('EI')
  }

  static eireli(): CompanyType {
    return new CompanyType('EIRELI')
  }

  static ltda(): CompanyType {
    return new CompanyType('LTDA')
  }

  static slu(): CompanyType {
    return new CompanyType('SLU')
  }

  static sa(): CompanyType {
    return new CompanyType('SA')
  }

  static ss(): CompanyType {
    return new CompanyType('SS')
  }

  static consorcio(): CompanyType {
    return new CompanyType('CONSORCIO')
  }

  getValue(): string {
    return this.value
  }

  toString(): string {
    return this.value
  }

  equals(other: CompanyType): boolean {
    return this.value === other.value
  }

  static values(): string[] {
    return [...CompanyType.validTypes]
  }
}
