import { Repository } from 'typeorm';
import { GetContractsByInvestorService } from 'src/modules/contract/services/get-contract-by-investor.service';
import { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { IEditInvestorSuperAdminDto } from 'src/modules/investor/dto/edit-investor-dto';
import { EditInvestorService } from 'src/modules/investor/services/edit-investor.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
export declare class SuperAdminEditInvestorService {
    private readonly ownerRoleRelationRepository;
    private readonly preRegisterRepository;
    private readonly accountRepository;
    private readonly getContractsByInvestorService;
    private readonly editInvestorService;
    constructor(ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>, preRegisterRepository: Repository<PreRegisterEntity>, accountRepository: Repository<AccountEntity>, getContractsByInvestorService: GetContractsByInvestorService, editInvestorService: EditInvestorService);
    perform(payload: IEditInvestorSuperAdminDto, userId: string): Promise<void>;
    updateBank: (payload: IEditInvestorSuperAdminDto) => Promise<{
        message: string;
    }>;
}
